<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>OAuth Authorization</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/axios/0.21.1/axios.min.js"></script>
</head>
<body class="bg-gradient-to-br from-blue-100 via-purple-100 to-pink-100 min-h-screen flex items-center justify-center">
<div class="bg-white/80 backdrop-blur-md shadow-2xl rounded-2xl p-8 max-w-3xl w-full border border-white/30">
  <h2 class="text-3xl font-extrabold mb-6 text-center text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600">OAuth Explorer</h2>

  <div class="mb-6">
    <h3 class="text-lg font-semibold mb-4 text-gray-700">Authorization</h3>
    <div class="grid grid-cols-3 gap-4">
      <button
          onclick="authorizeWithMotive()"
          class="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-bold py-3 px-4 rounded-lg transform transition duration-300 hover:scale-105 hover:shadow-lg"
      >
        Motive Auth
      </button>
      <button
          onclick="authorizeWithSamsara()"
          class="bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white font-bold py-3 px-4 rounded-lg transform transition duration-300 hover:scale-105 hover:shadow-lg"
      >
        Samsara Auth
      </button>
      <button
          onclick="authorizeWithQuickbooks()"
          class="bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white font-bold py-3 px-4 rounded-lg transform transition duration-300 hover:scale-105 hover:shadow-lg"
      >
        Quickbooks Auth
      </button>
    </div>
  </div>

  <div>
    <h3 class="text-lg font-semibold mb-4 text-gray-700">Sync Endpoints</h3>
    <div class="grid grid-cols-2 gap-4">
      <button
          onclick="fetchDrivers()"
          class="bg-gradient-to-r from-indigo-500 to-indigo-600 hover:from-indigo-600 hover:to-indigo-700 text-white font-bold py-3 px-4 rounded-lg transform transition duration-300 hover:scale-105 hover:shadow-lg"
      >
        Fetch Users
      </button>
      <button
          onclick="fetchVehicles()"
          class="bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 text-white font-bold py-3 px-4 rounded-lg transform transition duration-300 hover:scale-105 hover:shadow-lg"
      >
        Fetch User Company Equipments
      </button>
    </div>
  </div>

  <div id="result" class="mt-6 p-4 bg-white/50 backdrop-blur-sm rounded-lg max-h-64 overflow-auto border border-gray-200/50">
    <p class="text-gray-500 text-center">Results will appear here</p>
  </div>
</div>

<script>
  function authorizeWithMotive() {
      window.location.href = 'https://curiously-charming-firefly.ngrok-free.app/rest/eld/authorization/motive';
  }

  function authorizeWithSamsara() {
    window.location.href = 'https://curiously-charming-firefly.ngrok-free.app/rest/eld/authorization/samsara';
  }
  
  function authorizeWithQuickbooks() {
    window.location.href = 'https://curiously-charming-firefly.ngrok-free.app/rest/quickbooks/authorization';
  }

  function fetchDrivers() {
    axios.put('https://curiously-charming-firefly.ngrok-free.app/rest/eld/users/sync')
    .then(response => {
      document.getElementById('result').innerHTML =
          '<h4 class="text-lg font-semibold mb-2 text-indigo-700">Drivers:</h4><pre class="text-sm text-gray-800">' +
          JSON.stringify(response.data, null, 2) +
          '</pre>';
    })
    .catch(error => {
      document.getElementById('result').innerHTML =
          '<h4 class="text-lg font-semibold mb-2 text-red-600">Error:</h4><pre class="text-sm text-red-500">' +
          JSON.stringify(error.response ? error.response.data : error.message, null, 2) +
          '</pre>';
    });
  }

  function fetchVehicles() {
    axios.put('https://curiously-charming-firefly.ngrok-free.app/rest/eld/user_company_equipments/sync')
    .then(response => {
      document.getElementById('result').innerHTML =
          '<h4 class="text-lg font-semibold mb-2 text-purple-700">Vehicles:</h4><pre class="text-sm text-gray-800">' +
          JSON.stringify(response.data, null, 2) +
          '</pre>';
    })
    .catch(error => {
      document.getElementById('result').innerHTML =
          '<h4 class="text-lg font-semibold mb-2 text-red-600">Error:</h4><pre class="text-sm text-red-500">' +
          JSON.stringify(error.response ? error.response.data : error.message, null, 2) +
          '</pre>';
    });
  }
</script>
</body>
</html>
