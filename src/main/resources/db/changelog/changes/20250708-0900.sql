-- liquibase formatted sql

-- changeset theo:20250708-0900
create table qb_load_invoices
(
    load_invoice_id int          not null,
    qb_invoice_id   varchar(255) not null,
    primary key (load_invoice_id)
);

alter table qb_load_invoices
    add constraint uq_qb_load_invoices_load_invoice_id_qb_invoice_id
        unique (load_invoice_id, qb_invoice_id);

-- rollback drop table qb_load_invoices;

-- changeset theo:20250708-0901

CALL createEndpointAndPermissions('Users','GET','/quickbooks/invoices','Get all not Quickbooks posted Invoices','1,2');
