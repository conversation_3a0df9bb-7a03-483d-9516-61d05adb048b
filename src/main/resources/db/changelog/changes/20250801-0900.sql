-- liquibase formatted sql

-- changeset theo:20250801-0900

create table usda_regions
(
    id   int         not null primary key,
    type varchar(15) not null
);

insert into usda_regions (id, type) values (1, 'East');
insert into usda_regions (id, type) values (2, 'North Central');
insert into usda_regions (id, type) values (3, 'Rocky Mountain');
insert into usda_regions (id, type) values (4, 'South Central');
insert into usda_regions (id, type) values (5, 'West');

alter table states
    add column usda_region_id int null,
    add constraint fk_states_usda_region
        foreign key (usda_region_id) references usda_regions (id);

update states set usda_region_id = 1 where abbreviation in ('CT', 'DE', 'FL', 'GA', 'MA', 'MD', 'ME', 'NC', 'NH', 'NJ', 'NY', 'PA', 'RI', 'SC', 'VA', 'VT', 'WV');
update states set usda_region_id = 2 where abbreviation in ('IA', 'IL', 'IN', 'KS', 'KY', 'MI', 'MN', 'MO', 'ND', 'NE', 'OH', 'SD', 'TN', 'WI');
update states set usda_region_id = 3 where abbreviation in ('CO', 'ID', 'MT', 'UT', 'WY');
update states set usda_region_id = 4 where abbreviation in ('AL', 'AR', 'LA', 'MS', 'NM', 'OK', 'TX');
update states set usda_region_id = 5 where abbreviation in ('AZ', 'CA', 'NV', 'OR', 'WA');
