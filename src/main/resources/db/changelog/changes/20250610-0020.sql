-- liquibase formatted sql

-- changeset andreas :20250610-0020

UPDATE `api_keys`
SET `app_name` = 'sff-old'
WHERE (`api_key_id` = '3');
UPDATE `api_keys`
SET `app_name` = 'SSF',
    `enabled`  = '1'
WHERE (`api_key_id` = '19');

-- permissions for all endpoints
INSERT INTO `api_key_endpoints`(`api_key_id`,
                                `api_endpoint_id`)
SELECT k.api_key_id, e.api_endpoint_id
FROM api_keys k
         join api_endpoints e
where k.api_key_id in (19)
  and e.api_endpoint_id not in (select api_endpoint_id
                                from api_key_endpoints
                                where api_key_id in (19))
order by k.api_key_id, e.api_endpoint_id;
