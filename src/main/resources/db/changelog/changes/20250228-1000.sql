-- liquibase formatted sql

-- changeset andreas:20250228-1000
ALTER TABLE `load_assignments`
    ADD COLUMN `loading_ticket_file_id` INT NULL AFTER `loading_ticket_number`,
    ADD COLUMN `loading_ticket_number_ocr` varchar(50) NOT NULL DEFAULT '' AFTER `loading_ticket_file_id`,
    ADD COLUMN `loaded_weight_ocr` DOUBLE NULL AFTER `loaded_weight`,
    ADD COLUMN `loaded_volume_ocr` DOUBLE NULL AFTER `loaded_volume`,
    ADD COLUMN `unloading_ticket_file_id` INT NULL AFTER `unloading_ticket_number`,
    ADD COLUMN `unloading_ticket_number_ocr` varchar(50) NOT NULL DEFAULT '' AFTER `unloading_ticket_file_id`,
    ADD COLUMN `unload_weight_ocr` DOUBLE NULL AFTER `unload_weight`,
    ADD COLUMN `unload_volume_ocr` DOUBLE NULL AFTER `unload_volume`,
    ADD COLUMN `hauled_date_ocr` DATE NULL AFTER `hauled_date`
;



-- changeset andreas:20250229-0006
INSERT INTO `api_endpoints`
(`category`,`method`,`path`,`description`)
VALUES
    ('Files', 'GET', '/files/{int}/fields', 'Get the OCR fields'),
    ('Files', 'PUT', '/files/{int}/fields', 'Update the OCR fields')
;

INSERT INTO `api_key_endpoints`(
    `api_key_id`,
    `api_endpoint_id`
)
SELECT k.api_key_id, e.api_endpoint_id
FROM api_keys k
         join api_endpoints e
where k.api_key_id in (1,2)
  and (
        method = 'GET' and path = '/files/{int}/fields'
     or method = 'PUT' and path = '/files/{int}/fields'
  )
  and e.api_endpoint_id not in (
    select api_endpoint_id
    from api_key_endpoints
    where api_key_id in (1,2)
)
order by k.api_key_id, e.api_endpoint_id;
