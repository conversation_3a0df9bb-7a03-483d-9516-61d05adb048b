-- liquibase formatted sql

-- changeset andreas:20250226-2000

set sql_safe_updates = 0;

delete from api_endpoints
where path like '%washouts%'
    and path like '%comment%';

INSERT INTO `api_endpoints`
    (`category`,`method`,`path`,`description`)
VALUES
    ('Washouts', 'POST', '/washouts/{int}/comments', 'Post a washout comment'),
    ('Washouts', 'PUT', '/washouts/{int}/comments/{int}', 'Update Washout Comment'),
    ('Washouts', 'DELETE', '/washouts/{int}/comments/{int}', 'Delete a washout comment'),
    ('Washouts', 'POST', '/washouts/{int}/comments/{int}/approve', 'Approve a washout comment')
;

INSERT INTO `api_key_endpoints`(
    `api_key_id`,
    `api_endpoint_id`
)
SELECT k.api_key_id, e.api_endpoint_id
FROM api_keys k
         join api_endpoints e
where k.api_key_id in (1,2) -- MFA
  and (
    method = 'POST' and path = '/washouts/{int}/comments'
        or method = 'PUT' and path = '/washouts/{int}/comments/{int}'
        or method = 'DELETE' and path = '/washouts/{int}/comments/{int}'
        or method = 'POST' and path = '/washouts/{int}/comments/{int}/approve'
    )
  and e.api_endpoint_id not in (
    select api_endpoint_id
    from api_key_endpoints
    where api_key_id in (1,2) -- tms, mobile
)
order by k.api_key_id, e.api_endpoint_id;