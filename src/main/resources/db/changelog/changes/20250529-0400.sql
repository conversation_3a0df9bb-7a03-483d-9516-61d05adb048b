-- liquibase formatted sql

-- changeset andreas :20250529-0400

-- set the default menu to be the old for all existing users.
INSERT INTO user_interface_state (user_id, app_name, `key`, value, modified_date)
SELECT user_info.user_id,
       'bulkload_new',
       'menuSettings',
       '{"docked":true,"newMenu":false,"compactMenu":false}',
       NOW()
FROM user_info
ON DUPLICATE KEY UPDATE value         = VALUES(value),
                        modified_date = VALUES(modified_date);
