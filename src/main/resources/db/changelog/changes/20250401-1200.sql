-- liquibase formatted sql

-- changeset andreas:20250402-0700 endDelimiter:/

DROP PROCEDURE IF EXISTS createEndpointAndPermissions
/

CREATE PROCEDURE createEndpointAndPermissions(
    IN p_category VARCHAR(50),
    IN p_method VARCHAR(10),
    IN p_path VARCHAR(150),
    IN p_description VARCHAR(500),
    IN p_api_key_ids VARCHAR(255)  -- Comma-separated list of API key IDs
)
BEGIN
    DECLARE v_endpoint_id INT;
    DECLARE v_api_key_id INT;
    DECLARE v_pos INT;
    DECLARE v_next_pos INT;
    DECLARE v_api_key_list VARCHAR(255);

    -- First, try to find existing endpoint
    SELECT api_endpoint_id INTO v_endpoint_id
    FROM api_endpoints
    WHERE method = p_method AND path = p_path
    LIMIT 1;

    -- If endpoint doesn't exist, create it
    IF v_endpoint_id IS NULL THEN
        INSERT INTO api_endpoints
        (category, method, path, description)
        VALUES
        (p_category, p_method, p_path, p_description);

        SET v_endpoint_id = LAST_INSERT_ID();
    END IF;

    -- Handle API key permissions
    SET v_api_key_list = p_api_key_ids;
    SET v_pos = 1;

    -- Loop through comma-separated API key IDs
    WHILE v_api_key_list IS NOT NULL AND v_api_key_list != '' DO
        SET v_next_pos = LOCATE(',', v_api_key_list);
        
        IF v_next_pos > 0 THEN
            SET v_api_key_id = CAST(TRIM(SUBSTRING(v_api_key_list, 1, v_next_pos - 1)) AS UNSIGNED);
            SET v_api_key_list = SUBSTRING(v_api_key_list, v_next_pos + 1);
        ELSE
            SET v_api_key_id = CAST(TRIM(v_api_key_list) AS UNSIGNED);
            SET v_api_key_list = NULL;
        END IF;

        -- Insert API key permission if combination doesn't exist
        INSERT INTO api_key_endpoints
        (api_key_id, api_endpoint_id)
        SELECT v_api_key_id, v_endpoint_id
        FROM api_keys k
        WHERE k.api_key_id = v_api_key_id
        AND NOT EXISTS (
            SELECT 1
            FROM api_key_endpoints
            WHERE api_key_id = v_api_key_id
            AND api_endpoint_id = v_endpoint_id
        );

    END WHILE;

END


