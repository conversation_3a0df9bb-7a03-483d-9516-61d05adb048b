-- liquibase formatted sql

-- changeset theo:20250611-1700

create table qb_ab_companies
(
    ab_company_id  int         not null,
    qb_customer_id varchar(50) null,
    qb_vendor_id   varchar(50) null,
    constraint pk_qb_ab_companies primary key (ab_company_id),
    constraint fk_qb_ab_companies_ab_company
        foreign key (ab_company_id) references ab_companies (ab_company_id)
);

call CreateIndex('qb_ab_companies', 'idx_qb_customer_id', 'qb_customer_id');
call CreateIndex('qb_ab_companies', 'idx_qb_vendor_id', 'qb_vendor_id');

create table qb_load_assignment_surcharge_types
(
    id                                int auto_increment not null,
    user_company_id                   int                not null,
    charge_type                       varchar(20)        not null,
    load_assignment_surcharge_type_id int                null,
    qb_item_id                        varchar(50)        null,
    constraint pk_qb_last primary key (id),
    constraint fk_qb_last_user_company_id
        foreign key (user_company_id) references user_company (user_company_id),
    constraint fk_qb_last_load_assignment_surcharge_type_id
        foreign key (load_assignment_surcharge_type_id) references load_assignment_surcharge_types (load_assignment_surcharge_type_id),
    constraint uq_qb_last_load_assignment_surcharge_type_id unique (user_company_id, load_assignment_surcharge_type_id)
);

call CreateIndex('qb_load_assignment_surcharge_types', 'idx_qb_item_id', 'qb_item_id');

CALL createEndpointAndPermissions('QuickBooks', 'GET', '/quickbooks/authorization', 'Authorize with QuickBooks Api - Exposed', '1,2');
CALL createEndpointAndPermissions('QuickBooks', 'GET', '/oauth2/authorization/quickbooks', 'Authorize with QuickBooks Api', '1,2');
CALL createEndpointAndPermissions('QuickBooks', 'DELETE', '/quickbooks/authorization', 'Remove accounting provider authorization', '1,2');

CALL createEndpointAndPermissions('QuickBooks', 'GET', '/quickbooks/my-company', 'Get Quickbooks Company Info', '1,2');
CALL createEndpointAndPermissions('QuickBooks', 'GET', '/quickbooks/customers', 'Get all Quickbooks Customers', '1,2');
CALL createEndpointAndPermissions('QuickBooks', 'GET', '/quickbooks/items', 'Get all Quickbooks Items', '1,2');
CALL createEndpointAndPermissions('QuickBooks', 'GET', '/quickbooks/vendors', 'Get all Quickbooks Vendors', '1,2');
CALL createEndpointAndPermissions('QuickBooks', 'PUT', '/quickbooks/customers/{int}/link/{int}', 'Link Quickbooks Customer to AbCompany', '1,2');
CALL createEndpointAndPermissions('QuickBooks', 'PUT', '/quickbooks/items/{int}/link', 'Link Quickbooks Item to ChargeType', '1,2');
CALL createEndpointAndPermissions('QuickBooks', 'PUT', '/quickbooks/vendors/{int}/link/{int}', 'Link Quickbooks Vendor to AbCompany', '1,2');

set sql_safe_updates = 0;
delete
from api_endpoints
where api_endpoints.category = 'Accounting';
set sql_safe_updates = 1;

-- changeset theo:********-1700

CALL createEndpointAndPermissions('QuickBooks', 'DELETE', '/quickbooks/customers/{int}/unlink/{int}', 'Unlink Quickbooks Customer from AbCompany', '1,2');
CALL createEndpointAndPermissions('QuickBooks', 'DELETE', '/quickbooks/items/{int}/unlink', 'Unlink Quickbooks Item from ChargeType', '1,2');
CALL createEndpointAndPermissions('QuickBooks', 'DELETE', '/quickbooks/vendors/{int}/unlink/{int}', 'Unlink Quickbooks Vendor from AbCompany', '1,2');


