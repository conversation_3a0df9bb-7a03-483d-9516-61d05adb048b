-- liquibase formatted sql

-- changeset andreas:20250130

UPDATE `api_keys` SET `app_name` = 'M<PERSON>', `enabled` = '1' WHERE (`api_key_id` = '17');

DELETE FROM `api_key_integration_permissions`
 WHERE api_key_id = 17;

INSERT INTO `api_key_integration_permissions` (`api_key_id`, `user_company_id`)
    select 17, user_company_id
    from user_company
    where user_company_id = 1470;


INSERT INTO `api_key_endpoints`(
    `api_key_id`,
    `api_endpoint_id`
)
SELECT k.api_key_id, e.api_endpoint_id
FROM api_keys k
         join api_endpoints e
where k.api_key_id in (17) -- MFA
  and (
        method = 'GET' and path = '/integration/authorized_companies'
        or method = 'GET' and path = '/integration/authorized_users'
        or method = 'GET' and path = '/integration/{int}/address_book/companies'
        or method = 'POST' and path = '/integration/{int}/address_book/companies'
        or method = 'GET' and path = '/integration/{int}/address_book/companies/{int}'
        or method = 'PUT' and path = '/integration/{int}/address_book/companies/{int}'
        or method = 'DELETE' and path = '/integration/{int}/address_book/companies/{int}'
        or method = 'GET' and path = '/integration/{int}/address_book/users'
        or method = 'POST' and path = '/integration/{int}/address_book/users'
        or method = 'GET' and path = '/integration/{int}/address_book/users/{int}'
        or method = 'PUT' and path = '/integration/{int}/address_book/users/{int}'
        or method = 'DELETE' and path = '/integration/{int}/address_book/users/{int}'
        or method = 'GET' and path = '/address_book/user_types'
        or method = 'GET' and path = '/cities'
        or method = 'GET' and path = '/cities/name'
        or method = 'GET' and path = '/equipments'
        or method = 'POST' and path = '/integration/{int}/files'
        or method = 'GET' and path = '/integration/{int}/files/my_files'
        or method = 'GET' and path = '/integration/{int}/loads/assignments'
        or method = 'POST' and path = '/integration/{int}/loads'
        or method = 'POST' and path = '/integration/{int}/loads/complete'
        or method = 'GET' and path = '/products/categories'
    )
  and e.api_endpoint_id not in (
    select api_endpoint_id
    from api_key_endpoints
    where api_key_id in (17) -- MFA
  )
  order by k.api_key_id, e.api_endpoint_id;