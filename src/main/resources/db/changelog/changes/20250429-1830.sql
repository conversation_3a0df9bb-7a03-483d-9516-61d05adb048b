-- liquibase formatted sql

-- changeset john :20250429-1830

INSERT INTO `api_endpoints`
(`category`,`method`,`path`,`description`)
VALUES
    ('Agtrax Scale Tickets', 'GET', '/agtrax/unmatched_origin_tickets_admin', 'Get AgTrax unmatched origin tickets admin'),
    ('Agtrax Scale Tickets', 'GET', '/agtrax/unmatched_origin_tickets_admin/{int}', 'Get candidate assignments for agtrax origin ticket'),
    ('Agtrax Scale Tickets', 'PUT', '/agtrax/unmatched_origin_tickets_admin/{int}', 'Match an  origin agtrax ticket with a load assignment');

INSERT INTO `api_key_endpoints`(
    `api_key_id`,
    `api_endpoint_id`
)
SELECT k.api_key_id, e.api_endpoint_id
FROM api_keys k
         join api_endpoints e
where k.api_key_id in (1,2)
  and (
       method = 'GET' and path = '/agtrax/unmatched_origin_tickets_admin'
    or method = 'GET' and path = '/agtrax/unmatched_origin_tickets_admin/{int}'
    or method = 'PUT' and path = '/agtrax/unmatched_origin_tickets_admin/{int}'
    )
  and e.api_endpoint_id not in (
    select api_endpoint_id
    from api_key_endpoints
    where api_key_id in (1,2)
)
order by k.api_key_id, e.api_endpoint_id;