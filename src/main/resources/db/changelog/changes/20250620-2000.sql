-- liquibase formatted sql

-- changeset andreas:20250620-2000

ALTER TABLE `load_invoice_items`
    ADD COLUMN `load_assignment_surcharge_type_id` INT NULL DEFAULT NULL AFTER `is_surcharge`;

-- existing data
set sql_safe_updates = 0;

update load_invoice_items i
    inner join load_assignment_surcharge_types s on i.item_description = s.load_assignment_surcharge_type
set i.load_assignment_surcharge_type_id = s.load_assignment_surcharge_type_id
where i.is_surcharge = 1;

update load_invoice_items i
set i.load_assignment_surcharge_type_id = 10
where i.load_assignment_surcharge_type_id is null
  and i.is_surcharge = 1
  and i.item_description = 'Detention Fee';

update load_invoice_items i
set i.load_assignment_surcharge_type_id = 1
where i.load_assignment_surcharge_type_id is null
  and i.is_surcharge = 1
  and i.item_description like '%Fuel Surcharge (% percentage of load)';

update load_invoice_items i
set i.load_assignment_surcharge_type_id = 12
where i.load_assignment_surcharge_type_id is null
  and i.is_surcharge = 1
  and i.item_description like '%Fuel Surcharge (per loaded mile)';

set sql_safe_updates = 1;
