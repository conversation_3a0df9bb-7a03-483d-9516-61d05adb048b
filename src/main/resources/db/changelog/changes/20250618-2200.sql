-- liquibase formatted sql

-- changeset john:20250618-2200

ALTER TABLE `load_assignments`
    ADD COLUMN `agtrax_integration`            TINYINT(1) NOT NULL DEFAULT 0 AFTER `loading_ticket_file_id`,
    ADD COLUMN `has_unmatched_external_grades` TINYINT(1) NOT NULL DEFAULT 0 AFTER `agtrax_integration`
;

-- Add new endpoints for grades
call createEndpointAndPermissions('Grades', 'GET', '/grades', 'Get Bulkloads Grades', '1,2');
call createEndpointAndPermissions('Grades', 'POST', '/external_grades/{int}/link/{int}', 'Match a grade with an external grade', '1,2');