<#-- @ftlvariable name="header" type="String" -->
<#-- @ftlvariable name="footer" type="String" -->

<#-- @ftlvariable name="prodMode" type="boolean" -->
<#-- @ftlvariable name="log" type="org.slf4j.Logger" -->
<#-- @ftlvariable name="invoice" type="com.bulkloads.web.loadinvoice.domain.template.LoadInvoiceTemplateModel" -->
<#-- @ftlvariable name="isEmailContent" type="boolean" -->
<#-- @ftlvariable name="isVoid" type="boolean" -->
<#-- @ftlvariable name="message" type="String" -->


<#include "utils.ftl">

<#if isVoid>
  <!--
  <cfpdf
    action="addWatermark"
    source="#local.invoice.invoice_file_url#"
    image="#request.domain#/images/void.jpg"
    destination="#local.invoice.invoice_file_url#"
    overwrite="yes"
    foreground="yes">
  -->
</#if>


<#if !isEmailContent>
  <!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html lang="en">
<head>
  <title>INV: #<#if invoice.loadInvoiceId??>${intFormat(invoice.loadInvoiceId)}</#if></title>
    </#if>

  <style type="text/css">
      body {
          max-width: 900px;
          margin: 0 auto;
      }

      <#if !isEmailContent>
      @page {
          margin: 0.6in 0.6in 0.6in 0.6in;
      <#if header??>
          @top-center {
              content: '${header}';
          }
      </#if>
      <#if footer??>
          @bottom-center {
              content: '${footer}';
          }
      </#if>
      }

      </#if>

      .template_layout {
          font-family: Arial, Helvetica, sans-serif;
          font-size: 10px;
      }

      .template_layout .header {
          background-color: #e6e4e4;
          text-align: right;
      }

      .template_layout .rowheader {
          background-color: #e6e4e4;
      }

      .template_layout .text-left {
          text-align: left;
      }

      .template_layout .text-right {
          text-align: right;
      }

      table.template_layout {
          padding: 0;
          margin: 0;
          border-collapse: collapse;
      }

      table.template_layout td {
          text-align: left;
          vertical-align: center;
          border: 1px solid gray;
          padding: 3px 5px 3px 5px;
      }

      table.template_layout > tbody > tr > td {
          border: 0;
          padding: 0;
      }
  </style>

    <#if !isEmailContent>
</head>
<body>
</#if>


<#--
<#if !prodMode>
    <div style="text-align: center; color: #dbbd12; font-size: xx-large;font-weight: bold; text-transform: lowercase; font-variant: small-caps; text-decoration: underline;">
        Development mode
    </div>
</#if>
-->

<#if isEmailContent && message?has_content>
  <div style="padding:10px;color:red;">
    <strong>${message}</strong><br/><br/>
  </div>
</#if>


<table class="template_layout" width="95%" align="center"
       style="font-family:Arial, Helvetica, sans-serif;font-size:14px;border:0;margin-top:20px;
               <#if isEmailContent>max-width: 1024px;</#if>"
       border="0">
  <tbody>
  <tr>
    <td style="text-align:left;border:0;padding-top:8px;padding-bottom:15px;">
        <#if !isEmailContent>
          <!-- hiring company logo -->
            <#if invoice.companyLogoUrl?has_content >
              <img src="${invoice.companyLogoUrl?html}" border="0" style="max-height:80px;" alt="${invoice.companyName?html}"/>
            <#else >
              <div style="font-size:1.5em;font-weight:bold;">${invoice.companyName?html}</div>
                <#if invoice.city?has_content && invoice.state?has_content>
                  <div style="font-size:1.3em;font-weight:bold;">${invoice.city?html}, ${invoice.state?html}</div>
                </#if>
            </#if>
        </#if>
    </td>

    <td style="text-align:right;font-size:20px;font-weight:bold;color:#555654;">
      Inv: <#if invoice.loadInvoiceId??>${intFormat(invoice.loadInvoiceId)}<#else>[PREVIEW]</#if>
      <br/>
        <#if invoice.revisedFromLoadInvoiceId?has_content>
          <span style="color:red">(REVISED FROM #${intFormat(invoice.revisedFromLoadInvoiceId)})</span>
          <br/>
        </#if>

      Date: ${datetimeFormat(invoice.invoiceDate, "MMM dd, yyyy")}

      <div style="height:30px;"></div>
    </td>
  </tr>

  <tr>
    <td style="text-align:left;vertical-align:top;">
      <!-- dispatcher information -->
      <table style="width:95%;">
        <tr>
          <td class="header" style="vertical-align:top;">Company</td>
          <td>
              ${invoice.companyName?html}
              <#if (invoice.sffId?? && invoice.billToUserCompanyId?? && invoice.billToUserCompanyId == 43755)>
                <div style="font-size:18px;font-weight:bold;">
                    ${invoice.sffId}
                </div>
              </#if>
          </td>
        </tr>

        <tr>
          <td class="header">Contact</td>
          <td>${invoice.firstName?html} ${invoice.lastName?html}</td>
        </tr>

          <#if invoice.phone1?has_content>
            <tr>
              <td class="header">Phone</td>
              <td>${invoice.phone1?html}</td>
            </tr>
          </#if>

          <#if invoice.email?has_content>
            <tr>
              <td class="header">Dispatcher Email</td>
              <td>${invoice.email?html}</td>
            </tr>
          </#if>

          <#if invoice.accountingEmail?has_content>
            <tr>
              <td class="header">
                <b>BILLING Email</b>
              </td>
              <td>
                <b>${invoice.accountingEmail?html}</b>
              </td>
            </tr>
          </#if>

          <#if invoice.address?has_content>
            <tr>
              <td class="header">Mailing Address</td>
              <td>${invoice.address?html} ${invoice.city?html}, ${invoice.state?html} ${invoice.zip?html}</td>
            </tr>
          </#if>

      </table>
    </td>

    <td style="text-align:right;vertical-align:top;">
      <!-- Load info -->
      <table style="width:100%;vertical-align:top;">
        <tr>
          <td class="header" style="vertical-align:top;">Bill To</td>
          <td style="vertical-align:top;">
            <b>${invoice.billToCompanyName?html}</b>

              <#if invoice.billToFirstName?has_content || invoice.billToLastName?has_content>
                <br/>
                  ${invoice.billToFirstName?html} ${invoice.billToLastName?html}
              </#if>

              <#if invoice.billToAddress?has_content>
                <br/>
                  ${invoice.billToAddress?html}
              </#if>

              <#if invoice.billToLocation?has_content>
                <br/>
                  ${invoice.billToLocation?html}
              </#if>

              <#if invoice.billToPhone?has_content>
                <br/>
                  ${invoice.billToPhone?html}
              </#if>

              <#if invoice.billToEmail?has_content>
                <br/>
                  ${invoice.billToEmail?html}
              </#if>
          </td>
        </tr>
      </table>
    </td>
  </tr>

  <tr>
    <td colspan="2" style="padding-top:20px;">
      <!-- assignment information -->
      <table width="100%" cellpadding="2" cellspacing="0" style="font-size:12px;">
          <#assign items = invoice.getLoadInvoiceItems()>

          <#assign previousLoadAssignmentId = 0>
          <#assign singleLoadAmount = 0>
          <#assign uniqueAssignmentsCounter = 0>
          <#assign itemCounter = 0>


          <#list items as item>
              <#assign itemCounter = itemCounter + 1>
              <#assign loadAssignmentId = item.loadAssignment.loadAssignmentId>
              <#assign isUniqueAssignment = loadAssignmentId != previousLoadAssignmentId>
              <#if isUniqueAssignment>
                  <#assign singleLoadAmount = 0>
                  <#assign previousLoadAssignmentId = loadAssignmentId>
                  <#assign uniqueAssignmentsCounter = uniqueAssignmentsCounter + 1>
              </#if>

              <#assign singleLoadAmount = singleLoadAmount + item.itemAmount>

              <#if !item.getIsSurcharge()>

                <tr>
                  <td colspan="6" class="text-left">
                    <!-- basic info on load -->
                    <div style="font-size:14px;padding-bottom:15px;line-height:140%;">

                      <br/>
                      Hauled:
                      <span style="font-weight:bold;"><#if item.hauledDate?has_content>${dateFormat(item.hauledDate, "MM/dd/YY")}</#if></span>
                      <br/>

                        <#if invoice.hiringAbCompanyId?has_content && invoice.hiringAbCompanyId != invoice.billToAbCompanyId>
                          Hired by:
                          <br/>
                            ${invoice.hiringAbCompanyName?upper_case?html}
                            <#if invoice.hiringAbCompanyCity?has_content> - ${invoice.hiringAbCompanyCity?html}, ${invoice.hiringAbCompanyState?html}</#if>

                            <#if invoice.hiringAbUserFirstName?has_content
                            || invoice.hiringAbUserLastName?has_content
                            || invoice.hiringAbUserEmail?has_content>
                              <br/>
                                ${invoice.hiringAbUserFirstName?upper_case?html} ${invoice.hiringAbUserLastName?upper_case?html} - ${invoice.hiringAbUserEmail?lower_case}
                            </#if>
                          <br/>
                        </#if>

                        <#if item.previousLoadAssignmentNumber?has_content>
                          Load Rerouted #:
                          <s>${item.previousLoadAssignmentNumber?upper_case?html}</s>
                            ${item.loadAssignmentNumber?upper_case?html}
                        <#else>
                          Load #: ${item.loadAssignmentNumber?upper_case?html}
                        </#if>

                        <#if item.workOrderNumber?has_content>
                          <br/>
                          Work Order #: ${item.workOrderNumber?html}
                        </#if>

                        <#if item.hauledNotes?has_content>
                          <br/>
                          Notes: ${item.hauledNotes?upper_case?html}
                        </#if>
                    </div>
                  </td>
                </tr>

                <tr>
                  <td class="rowheader text-left">Origin</td>
                  <td class="rowheader text-left">Destination</td>
                  <td class="rowheader text-left">Cmdty</td>
                  <td class="rowheader text-left">Rate</td>
                  <td class="rowheader text-left">Bill Wt/Vol</td>
                  <td class="rowheader text-right">Amount</td>
                </tr>
                <tr>
                  <!-- Origin -->
                  <td class="aRow" style="height:45px;">

                    <!-- Reroute Origin-->
                      <#if item.isRerouted() && item.reroutePickupDrop == "pickup">
                          <#if item.pickupCity == "">
                            <span style="font-weight:bold;"><s>${item.pickupCity?html}, ${item.pickupState?html}</s></span>
                            <br/>
                          </#if>
                        <s>${item.pickupCompanyName?upper_case?html}</s>
                        <br/>
                          <#if item.rerouteCity?has_content>
                            <span style="font-weight:bold;">${item.rerouteCity?html}, ${item.rerouteState?html}</span>
                          </#if>
                          ${item.rerouteCompanyName?upper_case?html}

                      <#else>
                          <#if item.pickupCity?has_content>
                            <span style="font-weight:bold;">${item.pickupCity?html}, ${item.pickupState?html}</span>
                            <br/>
                          </#if>
                          ${item.pickupCompanyName?upper_case?html}
                      </#if>

                      <#if item.pickupNumber?has_content>
                        <br/>
                        #${item.pickupNumber?html}
                      </#if>

                      <#if item.loadingTicketNumber?has_content>
                        <br/>
                        Ticket: ${item.loadingTicketNumber}
                      </#if>

                      <#if item.loadedWeight?has_content || item.loadedVolume?has_content>
                        <br/>
                          ${item.calculateQuantityText(item.loadedWeight, item.loadedVolume)}
                      </#if>
                  </td>

                  <!-- destination -->
                  <td class="aRow">

                    <!-- Reroute Destination -->
                      <#if item.isRerouted() && item.reroutePickupDrop == "drop">
                          <#if item.dropCity?has_content>
                            <span style="font-weight:bold;"><s>${item.dropCity?html}, ${item.dropState?html}</s></span>
                            <br/>
                          </#if>
                        <s>${item.dropCompanyName?html}</s>
                        <br/>

                          <#if item.rerouteCity?has_content>
                            <span style="font-weight:bold;">${item.rerouteCity?html}, ${item.rerouteState?html}</span>
                          </#if>
                          ${item.rerouteCompanyName?upper_case?html}

                      <#else>
                          <#if item.dropCity?has_content>
                            <span style="font-weight:bold;">${item.dropCity?html}, ${item.dropState?html}</span>
                            <br/>
                          </#if>
                          ${item.dropCompanyName?html}
                      </#if>

                      <#if item.dropNumber?has_content>
                        <br/>
                        #${item.dropNumber?html}
                      </#if>

                      <#if item.unloadingTicketNumber?has_content>
                        <br/>
                        Ticket: ${item.unloadingTicketNumber?html}
                      </#if>

                      <#if item.unloadWeight?has_content || item.unloadVolume?has_content>
                        <br/>
                          ${item.calculateQuantityText(item.unloadWeight, item.unloadVolume)}
                      </#if>
                  </td>

                  <!-- commodity -->
                  <td class="aRow">
                      ${item.commodity?upper_case?html}
                  </td>

                  <!-- rate -->
                  <td class="aRow">
                      <#if item.previousBillRateMessage?has_content>
                        <s>${item.previousBillRateMessage}</s>
                        <br/>
                          ${item.billRateMessage}
                        <br/>
                      <#else>
                        <b>Rate:</b>
                          ${item.billRateMessage}
                        <br/>
                      </#if>

                      <#if item.bolNumber?has_content>
                        BOL: ${item.bolNumber?html}
                      </#if>
                  </td>

                  <!-- Bill Wt/Vol -->
                  <td class="aRow">
                      ${item.getBillWeightRateValue()}
                  </td>
                  <td class="aRow text-right">
                      <#if item.previousItemAmount?? && item.previousItemAmount != 0>
                        <s>${dollarFormat(item.previousItemAmount)}</s>
                        <br/>
                      </#if>
                      ${dollarFormat(item.itemAmount)}
                  </td>

                </tr>
              <#else>
                <tr>
                  <td class="aRow text-right" style="height:25px;font-weight:bold;" colspan="5">
                      ${item.itemDescription?html}
                  </td>
                  <td class="aRow text-right">
                      <#if item.previousItemAmount?? && item.previousItemAmount != 0>
                        <s>${dollarFormat(item.previousItemAmount)}</s>
                        <br/>
                      </#if>
                      ${dollarFormat(item.itemAmount)}
                  </td>
                </tr>
              </#if>


              <#if itemCounter == items?size || loadAssignmentId != items[itemCounter].loadAssignment.loadAssignmentId>
              <#-- single load total -->
                <tr>
                  <td class="rowheader text-right" style="font-weight:bold;font-size:1.2em;" colspan="5">Load Total</td>
                  <td class="rowheader text-right" style="font-weight:bold;font-size:1.2em;">
                      ${dollarFormat(singleLoadAmount)}
                  </td>
                </tr>
              </#if>

          </#list>

          <#-- all loads total -->
          <#if uniqueAssignmentsCounter gt 1>
            <tr>
              <td class="rowheader text-right" style="font-weight:bold;font-size:1.4em;padding:15px 5px 15px 0" colspan="5">
                Total (${uniqueAssignmentsCounter} loads)
              </td>
              <td class="rowheader text-right" style="font-weight:bold;font-size:1.4em;padding:15px 5px 15px 0">
                  ${dollarFormat(invoice.getInvoiceTotal())}
              </td>
            </tr>
          </#if>
        
      </table>
    </td>
  </tr>

  <!-- footer notes -->
  <#if invoice.invoiceFooterNotes?has_content>
    <tr>
      <td colspan="2" style="padding-top:20px;">
          ${invoice.invoiceFooterNotes?html}
      </td>
    </tr>
  </#if>
  </tbody>
</table>


<#if isEmailContent>
  <table class="template_layout" width="95%" align="center"
         cellpadding="20"
         style="font-family:Arial, Helvetica, sans-serif;font-size:14px;border:0;margin-top:20px; max-width: 1024px;">

    <tbody>
    <tr>
      <td align="left" style="height:45px; line-height:1.6em; padding-top:2em">
        <a href="${invoice.invoiceFileUrl}">
          <font color='green' size='+2'>View Invoice & Supporting Documents</font>
        </a>
        <br/>
      </td>

    <tr>
      <td align="left" style="height:45px; line-height:1.6em; padding-top:2em">

          <#list invoice.getLoadAssignmentIds() as loadAssignmentId>
              <#assign loadAssignment = invoice.getLoadAssignmentById(loadAssignmentId)>

              <#if (loadAssignment.assignmentFiles?size > 0) ||
              loadAssignment.createdByUserCompanyId != invoice.userCompanyId>

                --- Load #: ${loadAssignment.loadAssignmentNumber?upper_case} ---<br/>

                  <#if loadAssignment.previousLoadAssignmentNumber?has_content>
                    --- Reroute Load #: ${loadAssignment.previousLoadAssignmentNumber?upper_case?html} ---<br/>
                  </#if>

                  <#if loadAssignment.createdByUserCompanyId != invoice.userCompanyId>
                    - <a href="${loadAssignment.confirmationFileUrl}">View Confirmation Document</a><br/>
                  </#if>

                  <#assign counter = 1>
                  <#list loadAssignment.getAssignmentFiles() as assignmentFile>
                    - <a href="${assignmentFile.file.fileUrl}">View Supporting Document ${counter}</a>

                      <#if assignmentFile.file.fullFileUrl?has_content>
                        | <a href="${assignmentFile.file.fullFileUrl}"> View Original / No Filter</a>
                      </#if>
                    <br/>
                      <#assign counter++>

                  </#list>
              </#if>
          </#list>
      </td>
    </tr>

    </tbody>
  </table>

</#if>



<#if ! isEmailContent>
</body>
</html>
</#if>
