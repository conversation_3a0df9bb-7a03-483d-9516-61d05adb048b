package com.bulkloads.liquibase;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import liquibase.change.custom.CustomTaskChange;
import liquibase.database.Database;
import liquibase.database.jvm.JdbcConnection;
import liquibase.exception.CustomChangeException;
import liquibase.exception.SetupException;
import liquibase.exception.ValidationErrors;
import liquibase.logging.LogFactory;
import liquibase.logging.Logger;
import liquibase.resource.ResourceAccessor;
import net.iakovlev.timeshape.TimeZoneEngine;

public class TimezoneBackfillChangeLogTask implements CustomTaskChange {

  private static final String COUNT_QUERY = "SELECT COUNT(*) FROM %s WHERE latitude IS NOT NULL AND longitude IS NOT NULL AND timezone IS NULL";
  private static final String UPDATE_QUERY = "UPDATE %s SET timezone = ? WHERE %s = ?";
  private static final String SELECT_BATCH_QUERY =
      "SELECT %s, latitude, longitude FROM %s WHERE latitude IS NOT NULL AND longitude IS NOT NULL AND timezone IS NULL LIMIT %d";

  private ResourceAccessor resourceAccessor;
  private final Logger log;

  {
    LogFactory.getInstance();
    log = LogFactory.getLogger();
  }

  @Override
  public void execute(Database database) throws CustomChangeException {
    log.info("Starting timezone backfill for tables...");

    TimeZoneEngine timeZoneEngine = TimeZoneEngine.initialize();

    try {
      Connection conn = ((JdbcConnection) database.getConnection()).getUnderlyingConnection();

      // Backfill cities table
      backfillTableTimezones(conn, timeZoneEngine, "cities", "id");

      // Backfill ab_companies table
      backfillTableTimezones(conn, timeZoneEngine, "ab_companies", "ab_company_id");

      log.info("Timezone backfill completed for all tables");

    } catch (Exception e) {
      throw new CustomChangeException("Error during timezone backfill: " + e.getMessage(), e);
    }
  }

  @SuppressWarnings("checkstyle:AbbreviationAsWordInName")
  private void backfillTableTimezones(Connection conn, TimeZoneEngine timeZoneEngine,
                                      String tableName, String idColumn) throws SQLException {
    log.info("Starting timezone backfill for " + tableName + "...");

    PreparedStatement selectStmt = null;
    PreparedStatement updateStmt = null;
    ResultSet rs = null;

    try {
      // Get total count for progress reporting
      String query = String.format(COUNT_QUERY, tableName);
      PreparedStatement countStmt = conn.prepareStatement(query);
      ResultSet countRs = countStmt.executeQuery();
      countRs.next();
      int totalRecords = countRs.getInt(1);
      countRs.close();
      countStmt.close();

      log.info("Found " + totalRecords + " records to process in " + tableName);

      // Prepare statements
      final PreparedStatement finalUpdateStmt = conn.prepareStatement(
          String.format(UPDATE_QUERY, tableName, idColumn)
      );
      updateStmt = finalUpdateStmt;

      // Process in batches
      final int BATCH_SIZE = 5000;
      int totalProcessed = 0;
      int updated = 0;
      int failed = 0;

      while (totalProcessed < totalRecords) {
        // Get batch of records with lat/long
        selectStmt = conn.prepareStatement(
            String.format(SELECT_BATCH_QUERY, idColumn, tableName, BATCH_SIZE)
        );

        rs = selectStmt.executeQuery();
        int batchCount = 0;
        int batchUpdated = 0;

        while (rs.next()) {
          batchCount++;
          final long id = rs.getLong(idColumn);
          final double latitude = rs.getDouble("latitude");
          final double longitude = rs.getDouble("longitude");

          try {
            timeZoneEngine.query(latitude, longitude).ifPresent(zoneId -> {
              try {
                finalUpdateStmt.setString(1, zoneId.getId());
                finalUpdateStmt.setLong(2, id);
                finalUpdateStmt.addBatch();
                // log.info(String.format("Updating %s ID %d with timezone %s", tableName, id, zoneId.getId()));
              } catch (SQLException e) {
                log.severe("Error preparing update for " + tableName + " ID " + id + ": " + e.getMessage());
              }
            });
          } catch (Exception e) {
            log.severe("Error querying timezone for " + tableName + " ID " + id + ": " + e.getMessage());
            failed++;
          }
        }

        // Execute batch update
        if (batchCount > 0) {
          int[] updateCounts = updateStmt.executeBatch();
          for (int count : updateCounts) {
            if (count > 0) {
              updated += count;
              batchUpdated += count;
            }
          }
          updateStmt.clearBatch();

          // Close result set and select statement for this batch
          rs.close();
          selectStmt.close();

          // Progress report - only after actual updates
          double percentComplete = (double) updated / totalRecords * 100;
          log.info(String.format("Processed %d/%d records (%.2f%%) in %s. Updated: %d, Failed: %d",
              updated + failed, totalRecords, percentComplete, tableName, updated, failed));
        }

        // Track total processed based on actual updates and failures
        totalProcessed += batchUpdated + (batchCount - batchUpdated > 0 ? failed : 0);

        // If we got fewer records than the batch size, we're done
        if (batchCount < BATCH_SIZE) {
          break;
        }
      }

      log.info(tableName + " timezone backfill completed. Updated: " + updated + ", Failed: " + failed);

    } finally {
      if (rs != null && !rs.isClosed()) {
        rs.close();
      }
      if (selectStmt != null && !selectStmt.isClosed()) {
        selectStmt.close();
      }
      if (updateStmt != null) {
        updateStmt.close();
      }
    }
  }

  @Override
  public String getConfirmationMessage() {
    return "Timezone backfill for all tables completed successfully";
  }

  @Override
  public void setUp() throws SetupException {
    // No setup needed
  }

  @Override
  public void setFileOpener(ResourceAccessor resourceAccessor) {
    this.resourceAccessor = resourceAccessor;
  }

  @Override
  public ValidationErrors validate(Database database) {
    return new ValidationErrors();
  }

}
