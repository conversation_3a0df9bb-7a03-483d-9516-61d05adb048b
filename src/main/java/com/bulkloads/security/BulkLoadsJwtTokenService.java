package com.bulkloads.security;

import static java.util.Objects.nonNull;
import java.util.Date;
import java.util.Map;
import java.util.Optional;
import org.springframework.stereotype.Service;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.JwtBuilder;
import io.jsonwebtoken.Jwts;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RequiredArgsConstructor
public class BulkLoadsJwtTokenService {

  private final JwtProperties jwtProperties;

  public String generateAccessToken(final String subject,
                                    final Map<String, String> claims) {
    return generateAccessToken(subject, claims, jwtProperties.getExpiration());
  }

  public String generateAccessToken(final String subject,
                                    final Map<String, String> claims,
                                    final long expirationInMillis) {
    final JwtBuilder jwtBuilder = Jwts
        .builder()
        .setSubject(subject)
        .setExpiration(new Date(System.currentTimeMillis() + expirationInMillis));

    if (nonNull(claims) && !claims.isEmpty()) {
      claims.forEach(jwtBuilder::claim);
    }

    return jwtBuilder
        .signWith(jwtProperties.getKey())
        .compact();
  }

  public Claims getClaimsFromJwtToken(String token) {
    return Jwts.parserBuilder()
        .setSigningKey(jwtProperties.getKey())
        .build()
        .parseClaimsJws(token)
        .getBody();
  }

  public Optional<String> getSubjectFromJwtToken(String token) {
    if (token == null) {
      return Optional.empty();
    }
    return Optional.ofNullable(getClaimsFromJwtToken(token)).map(Claims::getSubject);
  }

}
