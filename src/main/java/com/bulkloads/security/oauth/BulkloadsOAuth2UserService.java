package com.bulkloads.security.oauth;

import static java.util.Collections.emptyList;
import java.util.Map;
import com.bulkloads.common.UserUtil;
import org.springframework.security.oauth2.client.userinfo.OAuth2UserRequest;
import org.springframework.security.oauth2.client.userinfo.OAuth2UserService;
import org.springframework.security.oauth2.core.user.DefaultOAuth2User;
import org.springframework.security.oauth2.core.user.OAuth2User;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class BulkloadsOAuth2UserService implements OAuth2UserService<OAuth2UserRequest, OAuth2User> {

  public static final String USER_ID = "userId";

  @Override
  public OAuth2User loadUser(final OAuth2UserRequest userRequest) {
    final int userId = UserUtil.getUserIdOrThrow();
    log.debug("OAuth2 login: {}", userId);
    return new DefaultOAuth2User(emptyList(), Map.of(USER_ID, userId), USER_ID);
  }
}