package com.bulkloads.security.oauth;

import static com.bulkloads.config.AppConstants.AccountingProviderId.QUICKBOOKS;
import static com.bulkloads.security.oauth.QuickBooksRealmIdCaptureFilter.REALM_ID_PARAMETER;
import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;

import java.util.List;
import com.bulkloads.exception.BulkloadsException;
import org.springframework.jdbc.core.JdbcOperations;
import org.springframework.security.core.Authentication;
import org.springframework.security.oauth2.client.JdbcOAuth2AuthorizedClientService;
import org.springframework.security.oauth2.client.OAuth2AuthorizedClient;
import org.springframework.security.oauth2.client.registration.ClientRegistrationRepository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.util.WebUtils;
import jakarta.servlet.http.HttpServletRequest;

public class BulkloadsJdbcOAuth2AuthorizedClientService extends JdbcOAuth2AuthorizedClientService {

  public static final String LOAD_ALL_SQL = "SELECT * FROM oauth2_authorized_client";
  public static final String LOAD_ALL_ELD_SQL = "SELECT * FROM oauth2_authorized_client where client_registration_id in ('motive', 'samsara')";
  public static final String SAVE_OAUTH2_METADATA_SQL = """
      INSERT INTO oauth2_authorized_client_metadata (client_registration_id, principal_name, realm_id)
      VALUES (?, ?, ?)
      """;

  public BulkloadsJdbcOAuth2AuthorizedClientService(final JdbcOperations jdbcOperations,
                                                    final ClientRegistrationRepository clientRegistrationRepository) {
    super(jdbcOperations, clientRegistrationRepository);
  }

  @Override
  @Transactional
  public void saveAuthorizedClient(final OAuth2AuthorizedClient authorizedClient, final Authentication principal) {
    super.saveAuthorizedClient(authorizedClient, principal);

    final String registrationId = authorizedClient.getClientRegistration().getRegistrationId();
    if (QUICKBOOKS.equals(registrationId)) {
      final String realmId = extractRealmId();
      if (nonNull(realmId)) {
        final String principalName = principal.getName();
        jdbcOperations.update(SAVE_OAUTH2_METADATA_SQL, registrationId, principalName, realmId);
      }
    }
  }

  public List<OAuth2AuthorizedClient> findAll() {
    return jdbcOperations.query(LOAD_ALL_SQL, authorizedClientRowMapper);
  }

  public List<OAuth2AuthorizedClient> findAllEld() {
    return jdbcOperations.query(LOAD_ALL_ELD_SQL, authorizedClientRowMapper);
  }

  private String extractRealmId() {
    final RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
    if (isNull(requestAttributes)) {
      throw new BulkloadsException("No request attributes available");
    }
    HttpServletRequest request = ((ServletRequestAttributes) requestAttributes).getRequest();
    return (String) WebUtils.getSessionAttribute(request, REALM_ID_PARAMETER);
  }
}
