package com.bulkloads.security;

import static com.bulkloads.config.AppConstants.Header.X_API_KEY;

import java.util.Optional;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class SecurityUtils {

  public static Optional<String> getApiKey() {
    final ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
    if (attributes == null) {
      return Optional.empty();
    }
    return Optional.ofNullable(attributes.getRequest().getHeader(X_API_KEY));
  }
}
