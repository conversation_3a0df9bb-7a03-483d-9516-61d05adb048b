package com.bulkloads.security.api.dto;

import java.util.Optional;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class CheckinRequest {

  private Optional<String> deviceId;
  private Optional<String> notificationToken;
  private Optional<String> device;
  private Optional<String> model;
  private Optional<String> osversion;
  private Optional<String> appversion;
  private Optional<Double> latitude;
  private Optional<Double> longitude;

}
