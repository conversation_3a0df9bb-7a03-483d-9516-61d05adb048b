package com.bulkloads.security.api;

import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.Map;
import com.bulkloads.config.AppProperties;
import com.bulkloads.security.AuthService;
import com.bulkloads.security.api.dto.CheckinRequest;
import com.bulkloads.security.api.dto.GoogleSigninResponse;
import com.bulkloads.security.api.dto.LoginResponse;
import com.bulkloads.security.api.dto.LogoutRequest;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.MapperFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.lang3.NotImplementedException;
import org.apache.http.client.utils.URIBuilder;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.reactive.function.client.WebClient;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Positive;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequestMapping("/rest")
@Tag(name = "Auth")
@RequiredArgsConstructor
@Validated
public class AuthController {

  private static final ObjectMapper MAPPER = new ObjectMapper()
      .configure(MapperFeature.ACCEPT_CASE_INSENSITIVE_PROPERTIES, true);

  private final AuthService authService;
  private final WebClient webClient;
  private final AppProperties props;

  @Operation(summary = "Login",
      description = "The OAuth2 token url. Submit a username/password to obtain an access_token. "
                    + "Pass the token in the header as Authorization: Bearer [access_token] to /checkin or for other secure urls")
  @PostMapping(value = "/login", consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
  public LoginResponse login(
      @Parameter(name = "grant_type", description = "use the value ''password''", required = true)
      @RequestParam(value = "grant_type", defaultValue = "password") final String grantType,
      @Parameter(name = "username", description = "Username", required = true)
      @RequestParam @NotBlank(message = "The username is required") final String username,
      @Parameter(name = "password", description = "Password", required = true)
      @RequestParam @NotBlank(message = "The password is required") final String password,
      @Parameter(name = "scope", description = "leave blank")
      @RequestParam(required = false) final String scope) {

    final String jwtToken = authService.authenticate(username, password);

    return LoginResponse.builder()
        .accessToken(jwtToken)
        .build();
  }

  @Operation(summary = "Login As User",
      description = "Login As User, only for Admin accounts")
  @PostMapping("/loginas")
  public String loginas(
      @Parameter(name = "user_id", description = "The Id of the User to login as", required = true)
      @Positive @RequestParam(value = "user_id") final Integer userId) {
    throw new NotImplementedException();
  }

  @Operation(summary = "Checkin",
      description = "Call this url when opening the app, changing locations or logging in. It will return the user's info as well as a refreshed access_token")
  @PostMapping("/checkin")
  public String checkin(
      @Parameter(name = "body", description = "User details", required = true)
      @Valid @RequestBody final CheckinRequest checkinRequest) {
    throw new NotImplementedException();
  }

  @Operation(summary = "logout",
      description = "Call this url to disable notifications for the device")
  @PostMapping("/logout")
  public String logout(
      @Parameter(name = "body", description = "User details", required = true)
      @Valid @RequestBody final LogoutRequest logoutRequest) {
    throw new NotImplementedException();
  }

  @GetMapping("/signin/google")
  public ResponseEntity<GoogleSigninResponse> google(@RequestParam("id_token") String idToken) throws URISyntaxException {

    URI uri = new URIBuilder(props.getDomainUrl() + "/cfc/sociallogin.cfc")
        .addParameter("method", "handlegooglesignin")
        .addParameter("id_token", idToken)
        .addParameter("rememberme", String.valueOf(0))
        .build();

    ResponseEntity<String> cfResp = webClient.get()
        .uri(uri)
        .exchangeToMono(r -> r.toEntity(String.class))
        .block();

    GoogleSigninResponse signinResponse = buildGoogleSigninResponse(cfResp.getBody());

    return ResponseEntity
        .status(cfResp.getStatusCode())
        .body(signinResponse);
  }

  @Data
  @NoArgsConstructor
  @AllArgsConstructor
  public static class ColdfusionLoginResult {

    private String status;

    @JsonProperty("login_result")
    private LoginResult loginResult;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LoginResult {

      @JsonProperty("AUTH_TOKEN")
      private String authToken;

      @JsonProperty("ERRORS")
      private Map<String, Object> errors;

      @JsonProperty("ERRORSTATUS")
      private int errorStatus;
    }
  }

  private GoogleSigninResponse buildGoogleSigninResponse(String cfResponseBody) {

    try {
      final ColdfusionLoginResult coldfusionLoginResult = MAPPER.readValue(cfResponseBody, ColdfusionLoginResult.class);
      return new GoogleSigninResponse(coldfusionLoginResult.loginResult.getAuthToken());

    } catch (IOException e) {
      log.warn("Unable to parse ColdFusion sign-in response: {}", cfResponseBody, e);
      return new GoogleSigninResponse(null);
    }
  }

}
