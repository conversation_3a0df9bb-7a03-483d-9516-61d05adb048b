package com.bulkloads.fmcsa.view;

import com.bulkloads.fmcsa.model.FmcsaCity;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class FmcsaCityResponse {

  private int id;
  private String country;
  private String language;
  private String regIso2;
  private String state;
  private String region2;
  private String region3;
  private String region4;
  private String zip;
  private String city;
  private String area1;
  private String area2;
  private Double latitude;
  private Double longitude;
  private String tz;
  private String utc;
  private String dst;

  public static FmcsaCityResponse fromFmcsaCity(FmcsaCity c) {
    return FmcsaCityResponse.builder()
        .id(c.getId())
        .country(c.getCountry())
        .language(c.getLanguage())
        .regIso2(c.getRegIso2())
        .state(c.getState())
        .region2(c.getRegion2())
        .region3(c.getRegion3())
        .region4(c.getRegion4())
        .zip(c.getZip())
        .city(c.getCity())
        .area1(c.getArea1())
        .area2(c.getArea2())
        .latitude(c.getLatitude())
        .longitude(c.getLongitude())
        .tz(c.getTz())
        .utc(c.getUtc())
        .dst(c.getDst())
        .build();
  }
}
