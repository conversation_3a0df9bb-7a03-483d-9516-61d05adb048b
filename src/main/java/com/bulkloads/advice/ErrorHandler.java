package com.bulkloads.advice;

import static com.bulkloads.common.StringUtil.toSnakeCase;
import static com.bulkloads.config.AppConstants.BULKLOADS_ERROR_MAIL_ADDRESS;
import static com.bulkloads.config.AppConstants.NO_REPLY_EMAIL_ADDRESS;
import static com.bulkloads.config.AppConstants.Templates.ERROR;
import static java.util.Objects.nonNull;
import java.net.InetAddress;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;
import com.bulkloads.common.UserUtil;
import com.bulkloads.common.api.ApiErrorResponse;
import com.bulkloads.exception.BulkloadsExceptionUtils;
import com.bulkloads.exception.ValidationException;
import com.bulkloads.web.infra.email.EmailService;
import com.bulkloads.web.infra.email.domain.EmailDetails;
import com.bulkloads.web.infra.template.TemplateService;
import com.fasterxml.jackson.databind.exc.InvalidFormatException;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.info.GitProperties;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.authentication.AuthenticationCredentialsNotFoundException;
import org.springframework.security.core.AuthenticationException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import org.springframework.web.multipart.MaxUploadSizeExceededException;
import org.springframework.web.servlet.resource.NoResourceFoundException;
import jakarta.persistence.EntityNotFoundException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.ConstraintViolationException;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Order(Ordered.HIGHEST_PRECEDENCE)
@RestControllerAdvice
public class ErrorHandler {

  public static final String ERROR_EMAIL_SUBJECT_TEMPLATE = "[%s] Something went wrong. %s %s";

  private final EmailService emailService;
  private final TemplateService templateService;

  private final String hostname;
  private final List<String> errorEmailAddresses;

  private final String commitHash;

  @SneakyThrows
  public ErrorHandler(final EmailService emailService,
                      final TemplateService templateService,
                      @Value("${bulkloads.mailing.error-email-addresses}") final List<String> errorEmailAddresses,
                      final GitProperties gitProperties) {
    this.emailService = emailService;
    this.templateService = templateService;
    this.hostname = InetAddress.getLocalHost().getHostName();
    this.errorEmailAddresses = errorEmailAddresses;
    this.commitHash = gitProperties.getCommitId();
  }

  @ExceptionHandler(ValidationException.class)
  @ResponseStatus(HttpStatus.BAD_REQUEST)
  public ApiErrorResponse validationExceptionHandler(final ValidationException exception) {
    log.trace("{}: ", exception.getClass().getSimpleName(), exception);
    final Map<String, String> errors = collectValidationErrorByField(exception);
    final String message = String.join(" ", exception.getErrors().values());
    return ApiErrorResponse.builder().message(message).errors(errors).build();
  }

  @ExceptionHandler(MethodArgumentNotValidException.class)
  @ResponseStatus(HttpStatus.BAD_REQUEST)
  public ApiErrorResponse handleMethodArgumentNotValidException(final MethodArgumentNotValidException exception) {
    log.debug("{}: ", exception.getClass().getSimpleName(), exception);

    final Map<String, String> errors = new HashMap<>();

    exception.getBindingResult().getAllErrors().forEach(error -> {

      String fieldName = toSnakeCase(error.getObjectName());

      if (error instanceof FieldError fieldError) {
        fieldName = toSnakeCase((fieldError).getField());
      }
      final String errorMessage = error.getDefaultMessage();
      errors.put(fieldName, errorMessage);
    });

    final String message = String.join(" ", errors.values());
    return ApiErrorResponse.builder().message(message).errors(errors).build();
  }

  @ExceptionHandler(MethodArgumentTypeMismatchException.class)
  @ResponseStatus(HttpStatus.BAD_REQUEST)
  public ApiErrorResponse handleMethodArgumentTypeMismatchException(final MethodArgumentTypeMismatchException exception) {
    log.debug("{}: ", exception.getClass().getSimpleName(), exception);
    final String message = inferErrorMessage(exception.getRequiredType());
    final Map<String, String> errors = Map.of(exception.getName(), message);
    return ApiErrorResponse.builder().message(message).errors(errors).build();
  }

  @ExceptionHandler({HttpMessageNotReadableException.class, ConstraintViolationException.class})
  @ResponseStatus(HttpStatus.BAD_REQUEST)
  public ApiErrorResponse handleBadRequest(final Exception exception) {
    log.debug("{}: ", exception.getClass().getSimpleName(), exception);

    final String message;
    final String fieldName;
    if (exception.getCause() instanceof InvalidFormatException ife) {
      message = inferErrorMessage(ife.getTargetType());
      fieldName = ife.getPath().get(0).getFieldName();
    } else {
      message = exception.getMessage();
      fieldName = "error";
    }

    final Map<String, String> errors = Map.of(fieldName, message);
    return ApiErrorResponse.builder().message(message).errors(errors).build();
  }

  @ExceptionHandler(AuthenticationException.class)
  @ResponseStatus(HttpStatus.UNAUTHORIZED)
  public ApiErrorResponse handleAuthenticationException(final AuthenticationException exception) {
    log.debug("{}: ", exception.getClass().getSimpleName(), exception);
    String message = exception.getMessage();
    if (exception instanceof AuthenticationCredentialsNotFoundException) {
      message = "Permission denied. Authentication header missing.";
    }
    return ApiErrorResponse.builder().message(message).build();
  }

  @ExceptionHandler(AccessDeniedException.class)
  @ResponseStatus(HttpStatus.FORBIDDEN)
  public ApiErrorResponse handleAuthenticationException(final AccessDeniedException exception) {
    log.debug("{}: ", exception.getClass().getSimpleName(), exception);
    final String message = exception.getMessage();
    return ApiErrorResponse.builder().message(message).build();
  }

  @ExceptionHandler({NoResourceFoundException.class, EntityNotFoundException.class})
  @ResponseStatus(HttpStatus.NOT_FOUND)
  public ApiErrorResponse handleNoResourceFound(final Exception exception) {
    log.warn("{}: ", exception.getClass().getSimpleName(), exception);
    final String message = exception.getMessage();
    return ApiErrorResponse.builder().message(message).build();
  }

  @ExceptionHandler(MaxUploadSizeExceededException.class)
  @ResponseStatus(HttpStatus.BAD_REQUEST)
  public ApiErrorResponse handleMaxUploadSizeExceededException(final MaxUploadSizeExceededException exception) {
    log.debug("{}: ", exception.getClass().getSimpleName(), exception);
    final String message = "File size exceeds maximum allowed size of 64MB";
    final Map<String, String> errors = Map.of("file", message);
    return ApiErrorResponse.builder().message(message).errors(errors).build();
  }

  @ExceptionHandler(Exception.class)
  @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
  public ApiErrorResponse handleException(final Exception exception, final HttpServletRequest request) {
    final String uuid = UUID.randomUUID().toString();
    log.error("Something went wrong, uuid: {}", uuid, exception);

    final EmailDetails emailDetails = buildErrorEmail(exception, uuid, request);
    emailService.sendEmail(emailDetails);

    final String message = "Something went wrong, please contact support.";
    return ApiErrorResponse.builder().uuid(uuid).message(message).build();
  }

  private EmailDetails buildErrorEmail(final Exception exception,
                                       final String uuid,
                                       final HttpServletRequest request) {
    return EmailDetails
        .builder()
        .fromEmail(BULKLOADS_ERROR_MAIL_ADDRESS)
        .subject(getSubject(request))
        .message(getMessage(exception, uuid, request))
        .toEmails(errorEmailAddresses)
        .replyToEmail(NO_REPLY_EMAIL_ADDRESS)
        .failTo(NO_REPLY_EMAIL_ADDRESS)
        .build();
  }

  private String getSubject(final HttpServletRequest request) {
    return ERROR_EMAIL_SUBJECT_TEMPLATE.formatted(hostname, request.getMethod(), request.getRequestURI());
  }

  private String getMessage(final Exception exception, final String uuid, final HttpServletRequest request) {
    final String stackTrace = ExceptionUtils.getStackTrace(exception);
    final String causedByChain = BulkloadsExceptionUtils.getCausedByChainMessage(stackTrace);
    final List<String> headers = Collections.list(request.getHeaderNames()).stream()
        .map(headerName -> headerName + ": " + request.getHeader(headerName)).toList();
    final int userId = UserUtil.getUserId().orElse(0);
    final int userCompanyId = UserUtil.getUserCompanyId().orElse(0);
    final String body = getRequestBody(request);
    final List<String> queryParams = request.getParameterMap().entrySet().stream()
        .map(entry -> entry.getKey() + ": " + String.join(", ", entry.getValue())).toList();

    final ErrorTemplateModel errorTemplateModel = ErrorTemplateModel.builder()
        .uuid(uuid)
        .causedByChain(causedByChain)
        .userId(userId)
        .userCompanyId(userCompanyId)
        .headers(headers)
        .queryParams(queryParams)
        .body(body)
        .stacktrace(stackTrace)
        .commitHash(commitHash)
        .build();

    return templateService.processFromTemplateFile(ERROR, errorTemplateModel);
  }

  @SneakyThrows
  private String getRequestBody(final HttpServletRequest request) {
    if (nonNull(request.getContentType()) && request.getContentType().contains(MediaType.MULTIPART_FORM_DATA_VALUE)) {
      return "form-data";
    }
    try {
      return request.getReader().lines().collect(Collectors.joining(System.lineSeparator()));
    } catch (Exception e) {
      log.info("Failed to read request body", e);
      return "Failed to read request body";
    }
  }

  private Map<String, String> collectValidationErrorByField(final ValidationException exception) {
    return exception.getErrors().entrySet().stream()
        .collect(Collectors
            .toMap(entry -> toSnakeCase(entry.getKey()), Map.Entry::getValue, (a, b) -> b, HashMap::new));
  }

  private String inferErrorMessage(final Class<?> clazz) {
    final String message;
    if (LocalDate.class.equals(clazz)) {
      message = "Invalid format. Should be yyyy-MM-dd.";
    } else if (LocalTime.class.equals(clazz)) {
      message = "Invalid format. Should be HH:mm or HH:mm:ss.";
    } else if (Instant.class.equals(clazz)) {
      message = "Invalid format. Should be in UTC yyyy-MM-dd'T'HH:mm:ss.SSS'Z'.";
    } else if (Number.class.isAssignableFrom(clazz)) {
      message = "Enter a valid number";
    } else if (Boolean.class.equals(clazz)) {
      message = "Enter true/false";
    } else {
      message = "Invalid parameter type. Expected type: " + clazz.getSimpleName();
    }
    return message;
  }

}
