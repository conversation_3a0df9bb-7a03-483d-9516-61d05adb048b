package com.bulkloads.web.commodity.service.dto;

import java.time.Instant;
import java.util.List;
import com.bulkloads.common.jackson.CsvSerializer;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;

@Data
public class CommodityResponse {

  private Integer commodityId;
  private String commodity;
  private String externalCommodityId;
  private String commodityAbbr;
  private String colorCode;
  private String defRateType;
  private Integer rateProductCategoryId;
  private String rateProductCategory;

  @JsonSerialize(using = CsvSerializer.class)
  private List<String> equipmentIds;
  @JsonSerialize(using = CsvSerializer.class)
  private List<String> equipmentNames;

  private String defaultBillWeightUse;
  private Instant modifiedDate;
  private Boolean deleted;
}
