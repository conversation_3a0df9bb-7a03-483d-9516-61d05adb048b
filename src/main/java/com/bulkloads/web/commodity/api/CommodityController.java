package com.bulkloads.web.commodity.api;

import static com.bulkloads.config.AppConstants.UserRole.ROLE_USER;
import com.bulkloads.common.api.ApiResponse;
import com.bulkloads.web.commodity.service.CommodityService;
import com.bulkloads.web.commodity.service.dto.CommodityRequest;
import com.bulkloads.web.commodity.service.dto.CommodityResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Positive;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequestMapping(path = "/rest/commodities")
@Tag(name = "Commodities")
@CrossOrigin(origins = "*", maxAge = 3600)
@RequiredArgsConstructor
@Validated
@PreAuthorize("hasRole('" + ROLE_USER + "')")
public class CommodityController {

  private final CommodityService commodityService;

  @Operation(summary = "Create Commodity")
  @PostMapping
  public ApiResponse<CommodityResponse, Integer> createCommodity(
      @Valid @RequestBody final CommodityRequest dto) {
    final CommodityResponse response = commodityService.create(dto);

    return ApiResponse.<CommodityResponse, Integer>builder()
        .key(response.getCommodityId())
        .data(response)
        .message("Commodity created")
        .build();
  }

  @Operation(summary = "Update commodity")
  @PutMapping("/{commodity_id}")
  public ApiResponse<CommodityResponse, Integer> updateCommodity(
      @PathVariable("commodity_id") @Positive final int commodityId,
      @Valid @RequestBody final CommodityRequest dto) {
    final CommodityResponse response = commodityService.update(commodityId, dto);
    return ApiResponse.<CommodityResponse, Integer>builder()
        .key(response.getCommodityId())
        .data(response)
        .message("Commodity updated")
        .build();
  }

  @Operation(summary = "Delete Commodity")
  @DeleteMapping("/{commodity_id}")
  public ApiResponse<CommodityResponse, Integer> deleteCommodity(
      @PathVariable("commodity_id") @Positive final int commodityId) {
    commodityService.deleteById(commodityId);
    return ApiResponse.<CommodityResponse, Integer>builder()
        .key(commodityId)
        .message("Commodity deleted")
        .build();
  }

}
