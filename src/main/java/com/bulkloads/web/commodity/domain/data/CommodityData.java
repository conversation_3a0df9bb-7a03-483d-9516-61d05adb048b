package com.bulkloads.web.commodity.domain.data;

import java.util.List;
import java.util.Optional;
import com.bulkloads.web.rate.domain.entity.RateProductCategory;
import lombok.Data;

@Data
public class CommodityData {

  private Optional<String> commodity;
  private Optional<String> externalCommodityId;
  private Optional<String> commodityAbbr;
  private Optional<String> colorCode;
  private Optional<String> defRateType;
  private Optional<RateProductCategory> rateProductCategory;
  private Optional<List<String>> equipmentIds;
  private Optional<List<String>> equipmentNames;
  private Optional<String> defaultBillWeightUse;
}
