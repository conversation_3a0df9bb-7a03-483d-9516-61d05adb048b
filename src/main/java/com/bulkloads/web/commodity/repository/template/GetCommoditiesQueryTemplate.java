package com.bulkloads.web.commodity.repository.template;

import org.intellij.lang.annotations.Language;

public class GetCommoditiesQueryTemplate {

  @Language("SQL")
  public static final String GET_COMMODITIES_QUERY_TEMPLATE = """
          SELECT
              c.commodity_id,
              c.commodity,
              c.external_commodity_id,
              c.commodity_abbr,
              c.color_code,
              c.def_rate_type,
              c.rate_product_category_id,
              r.rate_product_category,
              c.equipment_ids,
              c.equipment_names,
              c.default_bill_weight_use,
              c.modified_date,
              c.deleted
          from commodities c
              left join rate_product_categories r using(rate_product_category_id)
          <% params.put("userCompanyId", userCompanyId) %>
          where c.user_company_id = :userCompanyId

          <% if (paramExistsAdd("commodityId")) { %>
              and c.commodity_id = :commodityId
          <% } else { %>
              <% if (!paramIsTrue("includeDeleted")) { %>
                  and c.deleted = 0
              <% } %>
          <% } %>

          <% if (paramExistsAdd("externalCommodityId")) { %>
              and c.external_commodity_id = :externalCommodityId
          <% } %>

          <% if (paramExistsAdd("commodity")) { %>
              and c.commodity = :commodity
          <% } %>

          <% if (paramExists("term")) { %>
              <% var wildTerms = term.split("\\s+") %>
              <% for (int i = 0; i < wildTerms.length; i++) { %>
                  <% params.put("wildTerms_"+i, "%"+wildTerms[i]+"%") %>
                  AND commodity LIKE :<% print("wildTerms_" + i) %>
              <% } %>
          <% } %>

          <% if (paramExistsAdd("lastModifiedDate")) { %>
              and c.modified_date >= :lastModifiedDate
          <% } %>

          order by c.commodity

          <% if (paramExistsAdd("limit")) { %>
              LIMIT
              <% if (paramExistsAdd("skip")) { %>
               :skip,
              <% } %>
              :limit
          <% } %>
      """;


}
