package com.bulkloads.web.commodity.repository;

import static com.bulkloads.common.Converters.instantToSql;
import static com.bulkloads.web.commodity.repository.template.GetCommoditiesQueryTemplate.GET_COMMODITIES_QUERY_TEMPLATE;
import java.time.Instant;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.bulkloads.common.jpa.nativejpa.JpaNativeQueryService;
import com.bulkloads.web.commodity.service.dto.CommodityResponse;
import com.bulkloads.web.commodity.service.dto.transformer.CommodityResponseTransformer;
import org.springframework.stereotype.Repository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Repository
@RequiredArgsConstructor
public class CommodityQueryRepositoryImpl implements CommodityQueryRepository {

  private final JpaNativeQueryService jpaNativeQueryService;
  private final CommodityResponseTransformer commodityResponseTransformer;

  public List<CommodityResponse> getCommodities(final int userCompanyId,
                                                final String term,
                                                final Integer commodityId,
                                                final Instant lastModifiedDate,
                                                final boolean includeDeleted) {
    Map<String, Object> queryParams = new HashMap<>();
    queryParams.put("userCompanyId", userCompanyId);
    queryParams.put("term", term);
    queryParams.put("commodityId", commodityId);
    queryParams.put("lastModifiedDate", instantToSql(lastModifiedDate));
    queryParams.put("includeDeleted", includeDeleted);

    return jpaNativeQueryService.query(GET_COMMODITIES_QUERY_TEMPLATE, queryParams, commodityResponseTransformer);
  }

  public CommodityResponse getCommodity(final int userCompanyId, final Integer commodityId) {
    final Map<String, Object> queryParams = new HashMap<>();
    queryParams.put("userCompanyId", userCompanyId);
    queryParams.put("commodityId", commodityId);

    return jpaNativeQueryService.queryForObject(GET_COMMODITIES_QUERY_TEMPLATE, queryParams, commodityResponseTransformer);
  }

}
