package com.bulkloads.web.commodity.repository;

import java.util.Optional;
import com.bulkloads.web.commodity.domain.entity.Commodity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface CommodityRepository
    extends JpaRepository<Commodity, Integer>, CommodityQueryRepository {

  Optional<Commodity> findByCommodityAndUserCompanyUserCompanyIdAndDeletedFalse(final String commodity,
                                                                 final int userCompanyId);

  Optional<Commodity> findByCommodityIdAndUserCompanyUserCompanyId(final int commodityId,
                                                                   final int userCompanyId);

  Optional<Commodity> findByUserCompanyUserCompanyIdAndExternalCommodityId(
      Integer userCompanyId,
      String externalCommodityId
  );

  @Query("""
          select c from Commodity c
          where
              c.commodity = :#{#commodity}
              and c.deleted = false
              and c.userCompany.userCompanyId = :#{#userCompanyId}
              and ( :#{#commodityId} is null or c.commodityId <> :#{#commodityId} )
      """)
  Commodity findDuplicate(
      @Param("userCompanyId") final Integer userCompanyId,
      @Param("commodity") final String commodity,
      @Param("commodityId") final Integer commodityId
  );

}
