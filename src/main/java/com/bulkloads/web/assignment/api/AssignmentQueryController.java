package com.bulkloads.web.assignment.api;

import static com.bulkloads.config.AppConstants.UserRole.ROLE_USER;
import java.util.List;
import com.bulkloads.web.assignment.api.dto.AssignmentSearchParams;
import com.bulkloads.web.assignment.mapper.AssignmentMapper;
import com.bulkloads.web.assignment.service.AssignmentService;
import com.bulkloads.web.assignment.service.dto.AssignmentBookingListResponse;
import com.bulkloads.web.assignment.service.dto.AssignmentTicketItemResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.PositiveOrZero;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequestMapping("/rest/loads")
@Tag(name = "Load Assignments (Shipper)")
@CrossOrigin(origins = "*", maxAge = 3600)
@RequiredArgsConstructor
@Validated
public class AssignmentQueryController {

  private final AssignmentService assignmentService;
  private final AssignmentMapper assignmentMapper;

  @PreAuthorize("hasRole('" + ROLE_USER + "')")
  @GetMapping("/my_loads/assignments")
  public List<AssignmentBookingListResponse> getAssignments(

      AssignmentSearchParams searchParams,

      @Parameter(description = "The sort order. Values are case-insensitive. Allowed values are Newest,Oldest,Delivered Date Desc,Loads Asc - Oldest. "
                               + "Defaults to `Loads Asc - Oldest` meaning load_id asc, load_assignment_id asc.")
      @RequestParam(value = "order", required = false) String order,

      @Parameter(description = "the number of records to skip, defaults to 0")
      @PositiveOrZero(message = "Must be 0 or positive")
      @RequestParam(value = "skip", defaultValue = "0") int skip,

      @Parameter(description = "the number of records to return, defaults to 100")
      @PositiveOrZero(message = "Must be 0 or positive")
      @RequestParam(value = "limit", defaultValue = "100") int limit) {

    return assignmentService.getAssignments(
        assignmentMapper.paramsToRequest(searchParams),
        order,
        skip,
        limit);
  }

  @PreAuthorize("hasRole('" + ROLE_USER + "')")
  @GetMapping("/my_loads/{load_id}/assignments/{load_assignment_id}/tickets")
  public List<AssignmentTicketItemResponse> getAssignmentTickets(
      @Parameter(description = "load_id")
      @PathVariable("load_id") int loadId,
      @Parameter(description = "load_assignment_id")
      @PathVariable("load_assignment_id") int loadAssignmentId) {

    return assignmentService.getAssignmentTickets(loadAssignmentId);
  }

}
