package com.bulkloads.web.assignment.api;

import static com.bulkloads.common.mui.model.QueryParams.getFieldInfoFromClass;
import static com.bulkloads.config.AppConstants.UserRole.ROLE_USER;
import java.util.List;
import java.util.Map;
import com.bulkloads.common.api.TotalResponse;
import com.bulkloads.common.mui.model.FieldInfo;
import com.bulkloads.common.mui.model.QueryParams;
import com.bulkloads.web.assignment.service.AssignmentService;
import com.bulkloads.web.assignment.service.dto.AssignmentByCarrierV2Response;
import com.bulkloads.web.assignment.service.dto.AssignmentByDestinationV2Response;
import com.bulkloads.web.assignment.service.dto.AssignmentByDriverV2Response;
import com.bulkloads.web.assignment.service.dto.AssignmentByOriginV2Response;
import com.bulkloads.web.assignment.service.dto.AssignmentV2ListResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.constraints.PositiveOrZero;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequestMapping("/rest/v2/")
@Tag(name = "Load Assignments (Shipper)")
@CrossOrigin(origins = "*", maxAge = 3600)
@RequiredArgsConstructor
@Validated
public class AssignmentV2QueryController {

  private static final Map<String, FieldInfo> ASSIGNMENT_BY_DESTINATION_LIST_FIELD_NAME_CACHE =
      getFieldInfoFromClass(AssignmentByDestinationV2Response.class);

  private static final Map<String, FieldInfo> ASSIGNMENT_BY_ORIGIN_LIST_FIELD_NAME_CACHE =
      getFieldInfoFromClass(AssignmentByOriginV2Response.class);

  private static final Map<String, FieldInfo> ASSIGNMENT_BY_CARRIER_LIST_FIELD_NAME_CACHE =
      getFieldInfoFromClass(AssignmentByCarrierV2Response.class);

  private static final Map<String, FieldInfo> ASSIGNMENT_BY_DRIVER_LIST_FIELD_NAME_CACHE =
      getFieldInfoFromClass(AssignmentByDriverV2Response.class);

  private static final Map<String, FieldInfo> ASSIGNMENT_V2_LIST_FIELD_NAME_CACHE =
      getFieldInfoFromClass(AssignmentV2ListResponse.class);

  private final AssignmentService assignmentService;

  @PreAuthorize("hasRole('" + ROLE_USER + "')")
  @GetMapping("/assignments/totals/by_destination")
  public List<AssignmentByDestinationV2Response> getAssignmentsByDestination(
      @RequestParam(name = "active", required = false) Boolean active,
      @RequestParam(name = "user_ids", required = false, defaultValue = "") String userIds,
      HttpServletRequest request) {

    return assignmentService.getAssignmentsByDestination(
        active,
        userIds,
        new QueryParams(request.getQueryString(), ASSIGNMENT_BY_DESTINATION_LIST_FIELD_NAME_CACHE));
  }

  @PreAuthorize("hasRole('" + ROLE_USER + "')")
  @GetMapping("/assignments/totals/by_origin")
  public List<AssignmentByOriginV2Response> getAssignmentsByOrigin(
      @RequestParam(name = "active", required = false) Boolean active,
      @RequestParam(name = "user_ids", required = false, defaultValue = "") String userIds,
      HttpServletRequest request) {

    return assignmentService.getAssignmentsByOrigin(
        active,
        userIds,
        new QueryParams(request.getQueryString(), ASSIGNMENT_BY_ORIGIN_LIST_FIELD_NAME_CACHE));
  }

  @PreAuthorize("hasRole('" + ROLE_USER + "')")
  @GetMapping("/assignments/totals/by_carrier")
  public List<AssignmentByCarrierV2Response> getAssignmentsByCarrier(
      @RequestParam(name = "active", required = false) Boolean active,
      @RequestParam(name = "user_ids", required = false, defaultValue = "") String userIds,
      HttpServletRequest request) {

    return assignmentService.getAssignmentsByCarrier(
        active,
        userIds,
        new QueryParams(request.getQueryString(), ASSIGNMENT_BY_CARRIER_LIST_FIELD_NAME_CACHE));
  }

  @PreAuthorize("hasRole('" + ROLE_USER + "')")
  @GetMapping("/assignments/totals/by_driver")
  public List<AssignmentByDriverV2Response> getAssignmentsByDriver(
      @RequestParam(name = "active", required = false) Boolean active,
      @RequestParam(name = "user_ids", required = false, defaultValue = "") String userIds,
      HttpServletRequest request) {

    return assignmentService.getAssignmentsByDriver(
        active,
        userIds,
        new QueryParams(request.getQueryString(), ASSIGNMENT_BY_DRIVER_LIST_FIELD_NAME_CACHE));
  }

  @PreAuthorize("hasRole('" + ROLE_USER + "')")
  @GetMapping("/assignments/new_shares")
  public List<AssignmentV2ListResponse> getAssignmentNewShares() {

    return assignmentService.getAssignmentNewSharesV2();
  }

  @PreAuthorize("hasRole('" + ROLE_USER + "')")
  @GetMapping("/assignments")
  public List<AssignmentV2ListResponse> getAssignments(

      @RequestParam(name = "active", required = false) Boolean active,
      @RequestParam(name = "user_ids", required = false, defaultValue = "") String userIds,
      @Parameter(description = "for the Load Group tab")
      @RequestParam(name = "load_id", required = false) Integer loadId,
      @Parameter(description = "For the Origin tab. Includes rerouted loads.")
      @RequestParam(name = "final_pickup_ab_company_id", required = false) Integer finalPickupAbCompanyId,
      @Parameter(description = "For the Destination tab. Includes rerouted loads.")
      @RequestParam(name = "final_drop_ab_company_id", required = false) Integer finalDropAbCompanyId,
      @Parameter(description = "For the Carriers tab")
      @RequestParam(name = "to_ab_company_id", required = false) Integer toAbCompanyId,
      @Parameter(description = "For the Company Drivers tab. If to_user_id is passed, the to_ab_user_id must be null")
      @RequestParam(name = "to_ab_user_id", required = false) Integer toAbUserId,
      @RequestParam(name = "to_user_id", required = false) Integer toUserId,
      HttpServletRequest request,

      @Parameter(description = "the number of records to skip, defaults to 0")
      @PositiveOrZero(message = "Must be 0 or positive")
      @RequestParam(value = "skip", defaultValue = "0") int skip,

      @Parameter(description = "the number of records to return, defaults to 100")
      @PositiveOrZero(message = "Must be 0 or positive")
      @RequestParam(value = "limit", defaultValue = "100") int limit) {

    return assignmentService.getAssignmentsV2(
        active,
        userIds,
        loadId,
        finalPickupAbCompanyId,
        finalDropAbCompanyId,
        toAbCompanyId,
        toAbUserId,
        toUserId,
        new QueryParams(request.getQueryString(), ASSIGNMENT_V2_LIST_FIELD_NAME_CACHE),
        skip,
        limit);
  }

  @PreAuthorize("hasRole('" + ROLE_USER + "')")
  @GetMapping("/assignments/totals")
  public TotalResponse getAssignmentTotals(
      HttpServletRequest request,
      @RequestParam(name = "active", required = false) Boolean active,
      @RequestParam(name = "user_ids", required = false, defaultValue = "") String userIds) {

    return assignmentService.getAssignmentTotalsV2(
        active, userIds,
      new QueryParams(request.getQueryString(), ASSIGNMENT_V2_LIST_FIELD_NAME_CACHE)

    );
  }

}
