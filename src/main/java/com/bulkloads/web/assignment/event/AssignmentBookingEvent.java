package com.bulkloads.web.assignment.event;

import java.util.List;
import java.util.Set;
import com.bulkloads.web.common.event.DomainEvent;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor(access = AccessLevel.PROTECTED)
public class AssignmentBookingEvent implements DomainEvent {

  public AssignmentBookingEvent(final List<Integer> loadAssignmentIds, final String action) {
    this(loadAssignmentIds, action, Set.of());
  }

  private List<Integer> loadAssignmentIds;
  private String action;
  private Set<String> affectedFields;
}
