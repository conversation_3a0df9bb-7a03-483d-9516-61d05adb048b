package com.bulkloads.web.assignment.event;

import static com.bulkloads.config.AppConstants.AssignmentAction.ASSIGNMENT_UPDATE;
import java.util.List;
import java.util.Set;
import lombok.Getter;

@Getter
public class AssignmentUpdatedEvent extends AssignmentBookingEvent {

  public AssignmentUpdatedEvent(final List<Integer> loadAssignmentId, final Set<String> affectedFields) {
    super(loadAssignmentId, ASSIGNMENT_UPDATE, affectedFields);
  }
}
