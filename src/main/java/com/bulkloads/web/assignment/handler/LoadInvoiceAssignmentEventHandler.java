package com.bulkloads.web.assignment.handler;

import com.bulkloads.web.assignment.service.AssignmentService;
import com.bulkloads.web.loadinvoice.event.LoadInvoiceCreatedEvent;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
@RequiredArgsConstructor
public class LoadInvoiceAssignmentEventHandler {

  final AssignmentService assignmentService;

  @Order(10)
  @TransactionalEventListener(
      classes = LoadInvoiceCreatedEvent.class,
      phase = TransactionPhase.BEFORE_COMMIT
  )
  public void handleInvoiceCreatedEvent(final LoadInvoiceCreatedEvent event) {
    log.debug("handling {}", event);
    assignmentService.updateLoadInvoiceIds(event.getLoadAssignmentIds(), event.getLoadInvoiceId());
  }
}
