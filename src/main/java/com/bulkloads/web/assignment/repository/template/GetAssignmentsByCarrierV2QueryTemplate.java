package com.bulkloads.web.assignment.repository.template;

import org.intellij.lang.annotations.Language;

public class GetAssignmentsByCarrierV2QueryTemplate {

  @Language("SQL")
  public static final String GET_ASSIGNMENTS_BY_CARRIER_QUERY_TEMPLATE = """
      <% params.put("c_id", c_id) %>
      
      SELECT
          la.to_ab_company_id,
          to_c.company_name as to_company_name,
          count(*) as number_of_loads
      
      FROM load_assignments la
          inner join loads l on l.load_id = la.load_id
          inner join ab_companies to_c on la.to_ab_company_id = to_c.ab_company_id
      
      WHERE la.deleted = 0
          and l.deleted = 0
          and la.user_company_id = :c_id
          <% if (paramExistsAdd("active")) { %>
            AND l.active = :active
          <% } %>
          <% if (paramExistsAdd("user_ids")) { %>
            AND l.user_id in (:user_ids)
          <% } %>
   
      group by la.to_ab_company_id
      
      <% if (paramExists("having_clause")) {
        print(" having " + having_clause)
      } %>
      
      <% if (paramExists("order_by_clause")) {
        print(" order by " + order_by_clause + ", la.to_ab_company_id")
      } else {
        print(" order by la.to_ab_company_id")
      } %>
      
      """;
}
