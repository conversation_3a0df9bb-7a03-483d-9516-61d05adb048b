package com.bulkloads.web.assignment.repository;

import java.util.List;
import com.bulkloads.common.api.TotalResponse;
import com.bulkloads.common.mui.model.QueryParams;
import com.bulkloads.web.assignment.service.dto.AssignmentBookingListResponse;
import com.bulkloads.web.assignment.service.dto.AssignmentByCarrierV2Response;
import com.bulkloads.web.assignment.service.dto.AssignmentByDestinationV2Response;
import com.bulkloads.web.assignment.service.dto.AssignmentByDriverV2Response;
import com.bulkloads.web.assignment.service.dto.AssignmentByOriginV2Response;
import com.bulkloads.web.assignment.service.dto.AssignmentSearchRequest;
import com.bulkloads.web.assignment.service.dto.AssignmentV2ListResponse;

public interface AssignmentQueryRepository {

  List<AssignmentBookingListResponse> getAssignments(
      Boolean count,
      List<Integer> uIds,
      Integer cId,
      AssignmentSearchRequest assignmentSearchRequest,
      String orderBy,
      Integer skip,
      Integer limit
                                                    );

  List<AssignmentByDestinationV2Response> findAssignmentsByDestination(
      final int cId,
      final Boolean active,
      final List<Integer> userIds,
      final QueryParams queryParams);

  List<AssignmentByOriginV2Response> findAssignmentsByOrigin(
      final int cId,
      final Boolean active,
      final List<Integer> userIds,
      final QueryParams queryParams);

  List<AssignmentByCarrierV2Response> findAssignmentsByCarrier(
      final int cId,
      final Boolean active,
      final List<Integer> userIds,
      final QueryParams queryParams);


  List<AssignmentByDriverV2Response> findAssignmentsByDriver(
      final int cId,
      final Boolean active,
      final List<Integer> userIds,
      final QueryParams queryParams);

  List<AssignmentV2ListResponse> findAssignmentNewSharesV2(
      final int cId);

  List<AssignmentV2ListResponse> findAssignmentsV2(
      final int cId,
      final Boolean active,
      final List<Integer> userIds,
      final Integer loadId,
      final Integer finalPickupAbCompanyId,
      final Integer finalDropAbCompanyId,
      final Integer toAbCompanyId,
      final Integer toAbUserId,
      final Integer toUserId,
      final QueryParams queryParams,
      int skip,
      int limit);

  TotalResponse findAssignmentTotalsV2(
      final int cId,
      final Boolean active,
      final List<Integer> userIds,
      final QueryParams queryParams);
}
