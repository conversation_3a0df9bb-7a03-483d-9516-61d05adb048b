package com.bulkloads.web.assignment.repository.template;

import org.intellij.lang.annotations.Language;

public class GetAssignmentsQueryTemplate {

  @Language("SQL")
  public static final String GET_ASSIGNMENTS_QUERY_TEMPLATE = """
      SELECT
          <% if (paramIsTrue("count")) { %>
              count(*) as count
          <% } else { %>
            -- load_assignments
            la.load_id,
            la.to_load_id,
            la.load_assignment_id,
            la.assignment_status,
            la.assigned_date,
            la.created_date,
            la.dispatched_date,
            la.loading_date,
            la.enroute_date,
            la.unloading_date,
            la.delivered_date,
            la.completed_date,
            la.pickup_number,
            la.pickup_notes,
            la.drop_number,
            la.drop_notes,
            la.work_order_number,
            la.inside_notes,
            la.personal_message,
            la.loaded_weight,
            la.loaded_volume,
            la.unload_weight,
            la.unload_volume,
            la.loading_ticket_number,
            la.unloading_ticket_number,
            la.bol_number,
            la.load_assignment_number,
            la.hauled_notes,
            la.scheduled_hauled_date,
            la.scheduled_pickup_date,
            pickup_c.timezone as pickup_timezone,
            la.scheduled_drop_date,
            drop_c.timezone as drop_timezone,
            la.hauled_date,
            la.mileage,
            la.number_of_files,
            la.total_files_size,
            la.needs_attention,
            la.truck_user_company_equipment_id,
            la.trailer_user_company_equipment_id,
      
            case
                when la.truck_user_company_equipment_id is null then ''
                else trim(concat(
                    ifnull(equipment_trucks.external_equipment_id, ''), ' - ',
                    ifnull(equipment_trucks.model_year, ''),
                    ' ',
                    ifnull(equipment_trucks.make, '')
                ))
            end as truck_user_company_equipment,
      
            case
                when la.trailer_user_company_equipment_id is null then ''
                else trim(concat(
                    ifnull(equipment_trailers.external_equipment_id, ''), ' - ',
                    ifnull(equipment_trailers.model_year, ''),
                    ' ',
                    ifnull(equipment_trailers.make, '')
                ))
            end as trailer_user_company_equipment,
      
            la.original_rate,
            la.original_rate_type,
            la.original_rate_visible,
            la.original_rate_percentage,
      
            la.rate,
            la.rate_type,
            la.est_weight,
            la.est_volume,
            la.est_miles,
            la.est_hours,
            la.est_quantity,
            la.est_subtotal,
            la.est_surcharges,
            la.est_total,
            la.est_rate_per_mile,
            la.bill_weight_use,
            la.bill_weight,
            la.bill_volume,
            la.bill_miles,
            la.bill_hours,
            la.bill_quantity,
            la.bill_subtotal,
            la.bill_surcharges,
            la.bill_total,
            la.bill_rate_per_mile,
      
            -- payments
            la.payment,
            la.payment_notes,
            la.payment_date,
            la.payment_approved,
            la.payment_approved_date,
            la.paid,
            la.paid_date,
            la.to_payment,
            la.to_payment_notes,
            la.to_payment_date,
            la.to_paid,
            la.to_paid_date,
      
            -- confirmation fields
            la.confirmation_file_id,
            la.confirmation_file_code,
            conf_files.file_url as confirmation_file_url,
            conf_files.thumb_url as confirmation_thumb_url,
            la.confirmation_sent_date,
            la.confirmation_sent_method,
            la.confirmation_revised_date,
            la.confirmation_opened_date,
            la.confirmation_confirmed_date,
            la.confirmation_confirmed_method,
            la.confirmation_email_status,
            la.confirmation_to_ab_user_ids,
            la.confirmation_cc_others,
      
            -- load fields
            l.active,
            l.rate_product_category_id,
            l.rate_product_category as product,
            l.commodity_id,
            l.lo_commodity,
            l.ship_from,
            l.ship_to,
            l.is_hazmat,
            l.estimated_miles,
            l.load_bearing,
            l.load_bearing_direction,
            l.lo_contract_number,
            l.post_date,
            l.user_id as dispatcher_user_id,
            l.equipment_names,
            l.load_access,
            l.number_of_loads,
            l.number_of_delivered_loads,
            l.number_of_available_loads,
            l.is_managed,
      
            pickup_c.ab_company_id as pickup_ab_company_id,
            pickup_c.company_name as pickup_company_name,
            pickup_c.census_num as pickup_census_num,
            pickup_c.mc_num as pickup_mc_num,
      
            pickup_c.address as pickup_address,
            pickup_c.city as pickup_city,
            pickup_c.state as pickup_state,
            pickup_c.zip as pickup_zip,
            pickup_c.country as pickup_country,
            pickup_c.location as pickup_location,
            pickup_c.latitude as pickup_latitude,
            pickup_c.longitude as pickup_longitude,
            pickup_c.company_phone as pickup_company_phone,
            pickup_c.receiving_hours as pickup_receiving_hours,
            pickup_c.directions as pickup_directions,
            pickup_c.company_notes as pickup_company_notes,
            pickup_c.appt_required as pickup_appt_required,
            l.pickup_po,
      
            drop_c.ab_company_id as drop_ab_company_id,
            drop_c.company_name as drop_company_name,
            drop_c.census_num as drop_census_num,
            drop_c.mc_num as drop_mc_num,
      
            drop_c.address as drop_address,
            drop_c.city as drop_city,
            drop_c.state as drop_state,
            drop_c.zip as drop_zip,
            drop_c.country as drop_country,
            drop_c.location as drop_location,
            drop_c.latitude as drop_latitude,
            drop_c.longitude as drop_longitude,
      
            drop_c.company_phone as drop_company_phone,
            drop_c.receiving_hours as drop_receiving_hours,
            drop_c.directions as drop_directions,
            drop_c.company_notes as drop_company_notes,
            drop_c.appt_required as drop_appt_required,
            l.drop_po,
      
            l.originals_required,
            l.washout_required,
            l.required_file_type_ids,
            l.required_file_types,
            l.required_files_note,
      
            -- created userCompany
            la.created_by_user_id,
            la.created_by_user_company_id,
      
            -- hiring userCompany
            la.hiring_ab_user_id,
            la.user_id,
            ifnull(u.first_name, hiring_abu.first_name) as first_name,
            ifnull(u.last_name, hiring_abu.last_name) as last_name,
            ifnull(u.email, hiring_abu.email) as email,
            ifnull(u.cell_phone, hiring_abu.phone_1) as phone_1,
      
            la.hiring_ab_company_id,
            la.user_company_id,
            ifnull(c.company_name, hiring_abc.company_name) as company_name,
            ifnull(c.user_types, hiring_abc.user_types) as user_types,
      
            c.avg_rating,
            c.rating_count,
            c.company_logo_url,
            c.company_logo_thumb_url,
      
            -- hired userCompany
            la.to_ab_user_id,
            la.to_user_id,
      
            ifnull(to_abu.first_name, to_u.first_name) as to_first_name,
            ifnull(to_abu.last_name, to_u.last_name) as to_last_name,
            ifnull(to_abu.email, to_u.email) as to_email,
            ifnull(to_abu.phone_1, to_u.cell_phone) as to_phone_1,
      
            la.to_ab_company_id,
            la.to_user_company_id,
            ifnull(to_abc.company_name, to_c.company_name) as to_company_name,
            ifnull(to_abc.user_types, to_c.user_types) as to_user_types,
      
            to_c.avg_rating as to_avg_rating,
            to_c.rating_count as to_rating_count,
            to_c.company_logo_url as to_company_logo_url,
            to_c.company_logo_thumb_url as to_company_logo_thumb_url,
      
            -- bill to userCompany
            la.bill_to_ab_company_id,
            bill_to_abc.company_name as bill_to_company_name,
            la.bill_to_ab_user_id,
            bill_to_abu.first_name as bill_to_first_name,
            bill_to_abu.last_name as bill_to_last_name,
            bill_to_abu.email as bill_to_email,
      
            -- sharing fields
            la.shared_with_hiring_company,
            la.shared_with_hiring_company_response,
            la.shared_with_hired_company,
            la.shared_with_hired_company_response,
            (la.load_id is null and la.shared_with_hiring_company = 1 and la.shared_with_hiring_company_response = 'Pending') as new_share,
      
            -- chain fields
            la.child_load_assignment_id,
            la.parent_load_assignment_id,
            la.chain_load_assignment_id,
      
            -- invoice_fields
            la.auto_invoice,
            la.ready_to_invoice,
            ifnull(lb.ready_to_invoice, 0) as parent_ready_to_invoice,
            la.load_invoice_id,
            la.load_invoice_id is not null as invoiced,
            lb.load_invoice_id is not null as parent_invoiced,
      
            li.invoice_date,
            li.email_status as invoice_email_status,
            li.email_status_date as invoice_email_status_date,
            lif.file_url as invoice_file_url,
            lif.thumb_url as invoice_thumb_url,
            li.bill_to_user_id,
            li.bill_to_user_company_id,
            ifnull(li.archived,0) as archived,
            ifnull(li.bill_to_archived,0) as bill_to_archived,
      
            lipf.file_url as settlement_file_url,
      
            -- geo fields
            la.geo_share_location,
            la.geo_request_status,
            la.geo_request_date,
            la.geo_tracking_enabled,
            la.geo_tracking_stop_date,
            la.geo_tracking_until,
            la.geo_latitude,
            la.geo_longitude,
            la.geo_speed,
            la.geo_heading,
            la.geo_updated_date,
      
            -- external fields
            l.external_load_id,
            pickup_c.external_ab_company_id as pickup_external_ab_company_id,
            drop_c.external_ab_company_id as drop_external_ab_company_id,
            cast(null as char) as hiring_external_ab_company_id,
            cast(null as char) as hiring_external_ab_user_id,
            to_abc.external_ab_company_id as to_external_ab_company_id,
            to_abu.external_ab_user_id as to_external_ab_user_id,
            bill_to_abc.external_ab_company_id as bill_to_external_ab_company_id,
            bill_to_abu.external_ab_user_id as bill_to_external_ab_user_id,
      
            -- parent booking number of files
            lb.number_of_files as parent_number_of_files,
      
            -- exports
            le.export_date,
      
            -- offers
            la.booked_from_offer_recipient_id,
      
            -- parent hiring userCompany
            parent_hiring_abc.company_name as parent_hiring_company_name,
            l.lo_rate,
            l.lo_rate_type,
      
            0 as is_booking,
            la.is_rerouted,
            la.is_reassigned,
            la.is_intra_company,
            la.is_driver,
      
            la.modified_date,
      
            -- Reroutes, reroute request
            la.reroute_request,
            la.reroute_request_reason,
            la.reroute_request_date,
            -- Reroutes, rerouted start
            la.reroute_date,
            la.reroute_by_user_id,
            la.reroute_reason,
            la.reroute_contract_id,
            la.reroute_contract_number,
            la.previous_load_assignment_number,
            la.previous_pickup_number,
            la.previous_drop_number,
            la.previous_work_order_number,
            la.previous_rate,
            la.previous_rate_type,
            la.previous_bill_subtotal,
            la.previous_bill_surcharges,
            la.previous_bill_total,
            la.previous_bill_rate_per_mile,
            la.reroute_pickup_drop,
            -- reroute_c rerouted to userCompany
            reroute_c.ab_company_id as reroute_ab_company_id,
            reroute_c.external_ab_company_id as reroute_external_ab_company_id,
            reroute_c.company_name as reroute_company_name,
            reroute_c.census_num as reroute_census_num,
            reroute_c.mc_num as reroute_mc_num,
      
            reroute_c.address as reroute_address,
            reroute_c.city as reroute_city,
            reroute_c.state as reroute_state,
            reroute_c.zip as reroute_zip,
            reroute_c.country as reroute_country,
            reroute_c.location as reroute_location,
            reroute_c.latitude as reroute_latitude,
            reroute_c.longitude as reroute_longitude,
      
            reroute_c.company_phone as reroute_company_phone,
            reroute_c.receiving_hours as reroute_receiving_hours,
            reroute_c.directions as reroute_directions,
            reroute_c.company_notes as reroute_company_notes,
            reroute_c.appt_required as reroute_appt_required,
            -- l.reroute_po,
      
            -- deletes
            la.to_deleted,
            la.deleted_message,
            la.to_deleted_message,
            ifnull(lb.deleted, 0) as parent_deleted,
            lb.deleted_message as parent_deleted_message,
      
            <% if (paramIsTrue("include_deleted")) { %>
                l.deleted OR la.deleted as deleted
            <% } else { %>
                0 as deleted
            <% } %>
      
            <% if (paramIsTrue("with_files")) { %>
                ,ifnull(la_files.files_json, JSON_ARRAY()) as files
            <% } %>
      
            -- unique fields
            -- ifnull(to_abu.first_name, to_u.first_name) as to_ab_first_name,
            -- ifnull(to_abu.last_name, to_u.last_name) as to_ab_last_name,
            -- ifnull(to_abu.email, to_u.email) as to_ab_emai
                       -- ifnull(to_abu.phone_1, to_u.cell_phone) as to_ab_phone_1
      
            -- NON api fields
            , u.accounting_email
            , l.contract_id
          <% } %>
      FROM load_assignments la
      
          inner join loads l on l.load_id = ifnull(la.load_id,la.to_load_id) -- needed for new shared assignments  that don't have a load_id yet
          left join ab_companies pickup_c on l.pickup_ab_company_id = pickup_c.ab_company_id
          left join ab_companies drop_c on l.drop_ab_company_id = drop_c.ab_company_id
          left join ab_companies parent_hiring_abc on l.hiring_ab_company_id = parent_hiring_abc.ab_company_id
          left join files conf_files on la.confirmation_file_id = conf_files.file_id
      
          -- reroutes
          left join ab_companies reroute_c on la.reroute_ab_company_id = reroute_c.ab_company_id
      
          -- hiring ab
          left join ab_users hiring_abu on la.hiring_ab_user_id = hiring_abu.ab_user_id
          left join ab_companies hiring_abc on la.hiring_ab_company_id = hiring_abc.ab_company_id
          -- left join user_info bl_u on hiring_abu.bl_user_id = bl_u.user_id   extra condition for shares
      
          -- hiring user_info
          left join user_info u on u.user_id = la.user_id
          left join user_company c on u.user_company_id = c.user_company_id
      
          -- hired ab
          left join ab_users to_abu on la.to_ab_user_id = to_abu.ab_user_id
          left join ab_companies to_abc on la.to_ab_company_id = to_abc.ab_company_id
      
          -- hired user_info
          left join user_info to_u on to_u.user_id = la.to_user_id
          left join user_company to_c on to_u.user_company_id = to_c.user_company_id
      
          -- bill_to userCompany
          left join ab_companies bill_to_abc on bill_to_abc.ab_company_id = la.bill_to_ab_company_id
          left join ab_users bill_to_abu on bill_to_abu.ab_user_id = la.bill_to_ab_user_id
      
          -- invoices
          left join load_invoices li on la.load_invoice_id = li.load_invoice_id
          left join files lif on li.invoice_file_id = lif.file_id
      
          -- incoming invoice payments
          left join load_invoice_payments lip on li.load_invoice_payment_id = lip.load_invoice_payment_id
          left join files lipf on lip.settlement_file_id = lipf.file_id
      
          -- parent booking
          left join load_assignments lb on la.parent_load_assignment_id = lb.load_assignment_id
      
          -- exports
          left join load_assignment_exports le on la.load_assignment_export_id = le.load_assignment_export_id
      
          -- user company equipments
          left join user_company_equipments equipment_trucks on la.truck_user_company_equipment_id = equipment_trucks.user_company_equipment_id
          left join user_company_equipments equipment_trailers on la.trailer_user_company_equipment_id = equipment_trailers.user_company_equipment_id
      
          -- files
          <% if (paramIsTrue("with_files")) { %>
              left join (
                  select
                      laf.load_assignment_id, JSON_ARRAYAGG(json_object(
                          'file_id', ff.file_id,
                          'number_of_pages', ff.number_of_pages,
                          'caption', ff.caption,
                          'file_url', ff.file_url,
                          'thumb_url', ff.thumb_url,
                          'mime_type', ff.mime_type,
                          'is_image', ff.is_image,
                          'is_audio', ff.is_audio,
                          'size', ff.size,
                          'extension', ff.extension,
                          'filename', ff.`filename`
                      )) as files_json
                  from load_assignment_files laf
                      left join files ff on ff.file_id = laf.file_id
                  group by laf.load_assignment_id
              ) as la_files on la_files.load_assignment_id = la.load_assignment_id
          <% } %>
      
      
      WHERE 1=1
      
          -- when syncing include deleted
          <% if (paramIsTrue("include_deleted")) { %>
              AND l.deleted = 0
              AND la.deleted = 0
          <% } %>
      
          <% if (paramExistsAdd("c_id")) { %>
              AND la.user_company_id = :c_id
          <% } %>
      
          <% if (paramExistsAdd("to_ab_user_id")) { %>
              and la.hiring_ab_user_id = :to_ab_user_id
          <% } %>
      
          <% if (paramExistsAdd("active")) { %>
            AND l.active = :active
          <% } %>
      
      
          <% if (!paramExists("new_shares")) { %>
              -- both
              AND (
                  -- assignments in Dispatch
                  la.load_id is not NULL
      
                  <% if (tms_active == true) { %>
                      and l.active = 1
                  <% } %>
      
                  -- user filter on loads
                  <% if (paramExistsAdd("u_ids")) { %>
                      AND l.user_id in (:u_ids)
                  <% } %>
      
                  <% if (paramExistsAdd("c_id")) { %>
                      -- eg. in viewloadassignmentconfirmationpdf the c_id is not passed, only the conf_file_id & code
                      OR
                      -- assignments in incoming invoices
                      la.load_invoice_id is not null
                      AND li.email_status_date is not null
      
                      and li.bill_to_user_company_id = :c_id
                      <% if (tms_active == true) { %>
                          AND li.bill_to_archived = 0
                      <% } %>
                  <% } %>
      
                  <% if (tms_active == false) { %>
                      OR
                      -- new shares
                          la.load_id is null
                      and la.shared_with_hiring_company = 1
                      -- and la.confirmation_sent_date is not null
                      and la.shared_with_hiring_company_response <> 'Dismissed'
                      -- user filter on assignments
                      <% if (paramExistsAdd("u_ids")) { %>
                          AND la.user_id in (:u_ids)
                      <% } %>
                  <% } %>
              )
      
          <% } else if (new_shares == false) { %>
              -- assignments in Dispatch
              and la.load_id is not NULL
              -- user filter on loads
              <% if (paramExistsAdd("u_ids")) { %>
                AND l.user_id in (:u_ids)
              <% } %>
      
          <% } else if (new_shares == true) { %>
              -- new shares
              and la.load_id is null
              and la.shared_with_hiring_company = 1
              -- and la.confirmation_sent_date is not null
              and la.shared_with_hiring_company_response <> 'Dismissed'
              -- user filter on assignments
              <% if (paramExistsAdd("u_ids")) { %>
                AND la.user_id in (:u_ids)
              <% } %>
          <% } %>
      
      
          <% if (paramExists("completed")) { %>
              <% if (completed) { %>
                AND la.assignment_status = 'Completed'
              <% } else { %>
                AND la.assignment_status <> 'Completed'
              <% } %>
          <% } %>
      
      
          <% if (paramExists("unassigned")) { %>
              <% if (unassigned) { %>
                  AND la.assignment_status = 'Unassigned'
              <% } else { %>
                  AND la.assignment_status <> 'Unassigned'
              <% } %>
          <% } %>
      
          <% if (paramExistsAdd("assignment_status")) { %>
              AND la.assignment_status = :assignment_status
          <% } %>
      
      
          <% if (paramExists("invoiced")) { %>
              <% if (invoiced) { %>
                  AND la.load_invoice_id is not null
              <% } else { %>
                  AND la.load_invoice_id is null
              <% } %>
          <% } %>
      
          <% if (paramExists("bill_to_archived")) { %>
              <% if (bill_to_archived) { %>
                  AND li.bill_to_archived = 1
              <% } else { %>
                  AND li.bill_to_archived = 0
              <% } %>
          <% } %>
      
          -- invoiced load
          <% if (paramExistsAdd("load_invoice_id")) { %>
              and la.load_invoice_id = :load_invoice_id
          <% } %>
      
          <% if (paramExists("intra_company")) { %>
            <% if (intra_company) { %>
              AND la.is_intra_company = 1
            <% } else { %>
              AND la.is_intra_company = 0
            <% } %>
          <% } %>
      
          <% if (paramExists("payment_approved")) { %>
            <% if (payment_approved) { %>
              AND la.payment_approved = 1
            <% } else { %>
              AND la.payment_approved = 0
            <% } %>
          <% } %>
      
          <% if (paramExists("paid")) { %>
            <% if (paid) { %>
              AND la.paid = 1
            <% } else { %>
              AND la.paid = 0
            <% } %>
          <% } %>
      
          <% if (paramExistsAdd("last_modified_date")) { %>
              and la.modified_date >= :last_modified_date
          <% } %>
      
          <% if (paramExistsAdd("load_id")) { %>
              AND la.load_id = :load_id
          <% } %>
      
          <% if (paramExistsAdd("to_ab_company_id")) { %>
              AND la.to_ab_company_id = :to_ab_company_id
          <% } %>
      
          <% if (paramExistsAdd("to_ab_user_id")) { %>
              AND la.to_ab_user_id = :to_ab_user_id
          <% } %>
      
          <% if (paramExistsAdd("confirmation_email_queue_id")) { %>
              AND la.confirmation_email_queue_id = :confirmation_email_queue_id
          <% } %>
      
          <% if (paramExistsAdd("load_assignment_id")) { %>
              AND la.load_assignment_id = :load_assignment_id
          <% } %>
      
          <% if (paramExistsAdd("load_assignment_ids")) { %>
              AND la.load_assignment_id in (:load_assignment_ids)
          <% } %>
      
          <% if (paramExistsAdd("confirmation_file_id")) { %>
              AND la.confirmation_file_id = :confirmation_file_id
          <% } %>
      
          <% if (paramExistsAdd("confirmation_file_code")) { %>
              AND la.confirmation_file_code = :confirmation_file_code
          <% } %>
      
          -- radius search
          <% if (paramExistsAdd("radius")) { %>
      
            <% if ( paramExists("pickup_latitude") && paramExists("pickup_longitude")) { %>
              <% params.put("pickup_latitude", pickup_latitude) %>
              <% params.put("pickup_longitude", pickup_longitude) %>
      
              AND st_distance_sphere(
                      point(:pickup_longitude, :pickup_latitude),
                      point(pickup_c.longitude, pickup_c.latitude))/1609.34
                      <= :radius
            <% } %>
      
            <% if (paramExists("drop_latitude") &&  paramExists("drop_longitude")) { %>
              <% params.put("drop_longitude", drop_longitude) %>
              <% params.put("drop_latitude", drop_latitude) %>
      
              AND st_distance_sphere(
                      point(:drop_longitude, :drop_latitude),
                      point(ifnull(reroute_c.longitude, drop_c.longitude), ifnull(reroute_c.latitude, drop_c.latitude)))/1609.34
                      <= :radius
            <% } %>
      
          <% } %>
      
      
          -- shipping dates search. loads range must be fully included in the search range
          <% if (paramExistsAdd("ship_from")) { %>
              AND l.ship_from >= :ship_from
          <% } %>
      
          <% if (paramExistsAdd("ship_to")) { %>
              AND l.ship_from >= :ship_from
          <% } %>
      
          <% if (paramExistsAdd("lo_contract_number")) { %>
              AND l.lo_contract_number = :lo_contract_number
          <% } %>
      
          <% if (paramExistsAdd("load_assignment_number")) { %>
              AND la.load_assignment_number = :load_assignment_number
          <% } %>
      
          <% if (paramExists("search_term")) { %>
              <% params.put("search_term_param", search_term + "%") %>
              AND (
                  l.lo_contract_number like :search_term_param
                  or la.load_assignment_number like :search_term_param
              )
          <% } %>
      
          -- for origin/destination facilities
          <% if (paramExistsAdd("pickup_ab_company_id")) { %>
              AND pickup_c.ab_company_id = :pickup_ab_company_id
          <% } %>
      
          <% if (paramExistsAdd("drop_ab_company_id")) { %>
              AND drop_c.ab_company_id = :drop_ab_company_id
          <% } %>
      
          -- Sorting
          <% if (!paramIsTrue("count")) { %>
            <% if (paramExists("order_by")) { %>
              ORDER BY <% print(order_by) %>
            <% } %>
      
            <% if (paramExistsAdd("limit")) { %>
              LIMIT
              <% if (paramExistsAdd("skip")) { %>
                  :skip,
              <% } %>
              :limit
            <% } %>
          <% } %>
      """;
}
