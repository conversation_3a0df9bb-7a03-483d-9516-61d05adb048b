package com.bulkloads.web.assignment.repository.template;

import org.intellij.lang.annotations.Language;

public class GetAssignmentsByOriginV2QueryTemplate {

  /*
    params:
      - c_id
      - u_ids
      - active

      - order_by
      - limit
      - skip
  */
  @Language("SQL")
  public static final String GET_ASSIGNMENTS_BY_ORIGIN_QUERY_TEMPLATE = """
      <% params.put("c_id", c_id) %>
      
      select
          ab_company_id,
          company_name,
          city,
          state,
          sum(number_of_assigned_loads) as number_of_assigned_loads,
          sum(number_of_loads) as number_of_loads
      FROM
      (
          SELECT
              abc.ab_company_id,
              abc.company_name,
              abc.city,
              abc.state,
              sum(la.assignment_status <> 'Unassigned') as number_of_assigned_loads,
              count(*) as number_of_loads
          FROM load_assignments la
              inner join loads l on l.load_id = la.load_id
              inner join ab_companies abc on l.pickup_ab_company_id = abc.ab_company_id
          WHERE la.deleted = 0
              and l.deleted = 0
              and la.user_company_id = :c_id
              <% if (paramExistsAdd("active")) { %>
                AND l.active = :active
              <% } %>
              <% if (paramExistsAdd("user_ids")) { %>
                AND l.user_id in (:user_ids)
              <% } %>

              and (la.is_rerouted = 0 OR la.reroute_pickup_drop <> 'pickup')
          group by abc.ab_company_id

          UNION ALL -- rerouted loads

          SELECT
              abc.ab_company_id,
              abc.company_name,
              abc.city,
              abc.state,
              sum(la.assignment_status <> 'Unassigned') as number_of_assigned_loads,
              count(*) as number_of_loads

          FROM load_assignments la
              inner join loads l on l.load_id = la.load_id
              inner join ab_companies abc on la.reroute_ab_company_id = abc.ab_company_id

          WHERE la.deleted = 0
              and l.deleted = 0
              and la.user_company_id = :c_id
              <% if (paramExistsAdd("active")) { %>
                AND l.active = :active
              <% } %>
              <% if (paramExistsAdd("user_ids")) { %>
                AND l.user_id in (:user_ids)
              <% } %>

              and (la.is_rerouted = 1 AND la.reroute_pickup_drop = 'pickup')
          group by abc.ab_company_id
      ) a
      group by ab_company_id

      <% if (paramExists("having_clause")) {
        print(" having " + having_clause)
      } %>
      
      <% if (paramExists("order_by_clause")) {
        print(" order by " + order_by_clause + ", ab_company_id")
      } else {
        print(" order by ab_company_id")
      } %>
      
      """;
}
