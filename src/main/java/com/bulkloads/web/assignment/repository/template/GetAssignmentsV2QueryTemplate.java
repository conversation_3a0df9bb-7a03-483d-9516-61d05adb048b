package com.bulkloads.web.assignment.repository.template;

public class GetAssignmentsV2QueryTemplate {

  /*
    params:
      - count
      - c_id
      - active
      - user_ids
      - load_id
      - final_pickup_ab_company_id
      - final_drop_ab_company_id
      - to_ab_company_id
      - to_ab_user_id
      - to_user_id
      - skip
      - limit
  */
  public static final String GET_ASSIGNMENTS_QUERY_TEMPLATE_V2 = """
      <% params.put("c_id", c_id) %>
      
      SELECT
          la.load_assignment_id,
          l.load_id,
          la.assignment_status,
          la.deleted,
          la.to_deleted,
          parent_deleted,
      
          -- hired company
          ifnull(to_abc.company_name, to_c.company_name) as to_company_name,
          trim(concat(ifnull(to_abu.first_name, to_u.first_name), ' ', ifnull(to_abu.last_name, to_u.last_name))) as to_full_name,
          la.scheduled_hauled_date,
          la.scheduled_pickup_date,
          pickup_c.timezone as pickup_timezone,
          la.scheduled_drop_date,
          drop_c.timezone as drop_timezone,
          la.hauled_date,
      
          l.lo_commodity,
          l.rate_product_category,
      
          l.ship_from,
          l.ship_to,
      
          -- rate
          la.rate,
          la.rate_type,
          rt.rate_type_text,
          rt.rate_type_text_abbr,
      
          la.est_rate_per_mile,
      
          -- pickup
          pickup_c.company_name as pickup_company_name,
          pickup_c.city as pickup_city,
          pickup_c.state as pickup_state,
      
          -- drop
          drop_c.company_name as drop_company_name,
          drop_c.city as drop_city,
          drop_c.state as drop_state,
      
          -- reroute
          la.is_rerouted,
          la.reroute_pickup_drop,
          reroute_c.company_name as reroute_company_name,
          reroute_c.city as reroute_city,
          reroute_c.state as reroute_state,
      
          -- reroute request
          la.reroute_request,
          la.reroute_request_reason,
          la.reroute_request_date,
      
          l.estimated_miles,
          parent_hiring_abc.company_name as parent_hiring_company_name,
          la.load_assignment_number,
          l.lo_contract_number,
          la.work_order_number,
          la.bol_number,
          la.loading_ticket_number,
          la.unloading_ticket_number,
          la.pickup_number,
          la.drop_number,
      
          la.load_invoice_id,
          parent_load_invoice_id,
          ifnull(parent_ready_to_invoice, 0) as parent_ready_to_invoice,
      
          la.confirmation_file_id,
          la.confirmation_confirmed_date,
          la.confirmation_opened_date,
          la.confirmation_sent_date,
      
          la.number_of_files,
          parent_number_of_files,
      
          la.loaded_volume,
          la.loaded_weight,
      
          la.unload_volume,
          la.unload_weight,
      
          la.bill_volume,
          la.bill_weight,
          la.bill_weight_use,
          la.bill_quantity,
          (CASE
            WHEN la.rate_type IN ('gallon', 'liter') THEN la.unload_volume
            ELSE la.unload_weight
           END -
           CASE
             WHEN la.rate_type IN ('gallon', 'liter') THEN la.loaded_volume
             ELSE la.loaded_weight
           END) AS weight_discrepancy,
          la.bill_total as payable,
          parent_bill_total as receivable,
          parent_bill_total - la.bill_total as total_net_profit,
          case
            when la.truck_user_company_equipment_id is null then ''
            else trim(concat(
              ifnull(equipment_trucks.external_equipment_id, ''), ' - ',
              ifnull(equipment_trucks.model_year, ''),
              ' ',
              ifnull(equipment_trucks.make, '')
              ))
          end as truck_user_company_equipment,
      
          case
            when la.trailer_user_company_equipment_id is null then ''
            else trim(concat(
              ifnull(equipment_trailers.external_equipment_id, ''), ' - ',
              ifnull(equipment_trailers.model_year, ''),
              ' ',
              ifnull(equipment_trailers.make, '')
              ))
          end as trailer_user_company_equipment
      
      FROM load_assignments la
          inner join loads l on l.load_id = la.load_id
          inner join ab_companies pickup_c on l.pickup_ab_company_id = pickup_c.ab_company_id
          inner join ab_companies drop_c on l.drop_ab_company_id = drop_c.ab_company_id
          left join ab_companies parent_hiring_abc on l.hiring_ab_company_id = parent_hiring_abc.ab_company_id
          left join rate_types rt on la.rate_type = rt.rate_type
      
          -- reroutes
          left join ab_companies reroute_c on la.reroute_ab_company_id = reroute_c.ab_company_id
      
          -- hired ab
          left join ab_users to_abu on la.to_ab_user_id = to_abu.ab_user_id
          left join ab_companies to_abc on la.to_ab_company_id = to_abc.ab_company_id
      
          -- hired user_info
          left join user_info to_u on to_u.user_id = la.to_user_id
          left join user_company to_c on to_u.user_company_id = to_c.user_company_id
      
          -- parent booking
          -- left join load_assignments lb on la.parent_load_assignment_id = lb.load_assignment_id
          left join (
            select
              load_assignment_id,
              load_invoice_id as parent_load_invoice_id,
              ready_to_invoice as parent_ready_to_invoice,
              number_of_files as parent_number_of_files,
              bill_total as parent_bill_total,
              deleted as parent_deleted
            from load_assignments
          ) lb on la.parent_load_assignment_id = lb.load_assignment_id
      
          left join user_company_equipments equipment_trucks on la.truck_user_company_equipment_id = equipment_trucks.user_company_equipment_id
          left join user_company_equipments equipment_trailers on la.trailer_user_company_equipment_id = equipment_trailers.user_company_equipment_id
      
      WHERE l.deleted = 0
          AND la.deleted = 0
          AND la.user_company_id = :c_id
      
          <% if (paramExistsAdd("active")) { %>
            AND l.active = :active
          <% } %>
      
          <% if (paramExistsAdd("user_ids")) { %>
              AND l.user_id in (:user_ids)
          <% } %>
      
          <% if (paramExistsAdd("load_id")) { %>
              AND la.load_id = :load_id
          <% } %>
      
          -- for origin/destination facilities
          <% if (paramExistsAdd("final_pickup_ab_company_id")) { %>
              AND (
                (la.is_rerouted = 0 OR la.reroute_pickup_drop <> 'pickup') AND pickup_c.ab_company_id = :final_pickup_ab_company_id
                OR
                la.is_rerouted and la.reroute_pickup_drop = 'pickup' and reroute_c.ab_company_id = :final_pickup_ab_company_id
              )
          <% } %>
      
          <% if (paramExistsAdd("final_drop_ab_company_id")) { %>
              AND (
                (la.is_rerouted = 0 OR la.reroute_pickup_drop <> 'drop') AND drop_c.ab_company_id = :final_drop_ab_company_id
                OR
                la.is_rerouted and la.reroute_pickup_drop = 'drop' and reroute_c.ab_company_id = :final_drop_ab_company_id
              )
          <% } %>
      
          <% if (paramExistsAdd("to_ab_company_id")) { %>
              AND la.to_ab_company_id = :to_ab_company_id
          <% } %>
      
          -- by Company Driver tab, either to_ab_user_id or to_user_id
          <% if (paramExistsAdd("to_ab_user_id")) { %>
              AND la.to_ab_user_id = :to_ab_user_id
              and la.is_driver = 1
          <% } else if (paramExistsAdd("to_user_id")) { %>
              AND la.to_user_id = :to_user_id
              and la.to_ab_user_id is null
          <% } %>
      
          <% if (paramExists("having_clause")) {
            print(" having " + having_clause)
          } %>
      
          <% if (!paramIsTrue("count")) { %>
      
            -- Sorting
            <% if (paramExists("order_by_clause")) { %>
              <% print(" order by " + order_by_clause + ", la.load_assignment_id") %>
            <% } else { %>
              order by natural_sort(la.load_assignment_number) asc, la.load_assignment_id
            <% } %>
      
            <% if (paramExistsAdd("limit")) { %>
              LIMIT
              <% if (paramExistsAdd("skip")) { %>
                  :skip,
              <% } %>
              :limit
            <% } %>
          <% } %>
      """;
}
