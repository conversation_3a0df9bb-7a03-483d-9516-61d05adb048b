package com.bulkloads.web.assignment.repository.template;

public class GetBookingsV2QueryTemplate {

  public static final String GET_BOOKINGS_RTI_QUERY_TEMPLATE_V2 = """
      <% params.put("c_id", c_id) %>
      
      SELECT
          <% if (paramIsTrue("count")) { %>
              ifnull(count(*), 0) as count
          <% } else { %>
      
          TRIM(BOTH '-' FROM
              CONCAT_WS('-',
                  NULLIF(la.bill_to_ab_user_id, ''),
                  NULLIF(la.bill_to_ab_company_id, ''),
                  NULLIF(la.user_id, ''),
                  NULLIF(la.user_company_id, '')
              )
          ) AS bill_to_group_id,
      
          la.load_assignment_id,
          l.load_id,
      
            if(la.bill_to_ab_company_id is null, c.company_name, bill_to_abc.company_name) as bill_to_company_name,
      
            trim(concat(
              if(la.bill_to_ab_company_id is null, u.first_name, bill_to_abu.first_name),
              ' ',
              if(la.bill_to_ab_company_id is null, u.last_name, bill_to_abu.last_name)
              )) as bill_to,
      
            if(la.bill_to_ab_company_id is null, u.email, bill_to_abu.email) as bill_to_email,
      
            la.load_assignment_number,
            l.lo_commodity,
      
            trim(concat(pickup_c.city, ', ', pickup_c.state)) as pickup_city_state,
            trim(concat(drop_c.city, ', ', drop_c.state)) as drop_city_state,
            -- reroute
            la.is_rerouted,
            la.reroute_pickup_drop,
            trim(concat(reroute_c.city, ', ', reroute_c.state)) as reroute_city_state,
      
            la.rate,
            la.rate_type,
            rt.rate_type_text,
            rt.rate_type_text_abbr,
      
            la.hauled_date,
            la.loaded_weight,
            la.unload_weight,
      
            la.bill_subtotal,
            la.bill_surcharges,
            la.bill_total
        <% } %>
      
      from load_assignments la
      
        -- guests do not invoice. previously ifnull(la.to_load_id,la.load_id)
        inner join loads l on l.load_id = la.to_load_id
      
        left join ab_companies pickup_c on l.pickup_ab_company_id = pickup_c.ab_company_id
        left join ab_companies drop_c on l.drop_ab_company_id = drop_c.ab_company_id
      
        left join user_info u on u.user_id = la.user_id
        left join user_company c on u.user_company_id = c.user_company_id
      
        left join ab_companies bill_to_abc on bill_to_abc.ab_company_id = la.bill_to_ab_company_id
        left join ab_users bill_to_abu on bill_to_abu.ab_user_id = la.bill_to_ab_user_id
      
        left join rate_types rt on la.rate_type = rt.rate_type
      
        -- reroutes
        left join ab_companies reroute_c on la.reroute_ab_company_id = reroute_c.ab_company_id
      
      where 1=1
        and l.deleted = 0
        and la.to_deleted = 0
        and l.user_company_id = :c_id
        and la.load_invoice_id is null
        and la.ready_to_invoice = 1
      
      <% if (paramExists("having_clause")) {
        print(" having " + having_clause)
      } %>
      
      <% if (paramExists("order_by_clause")) { %>
        <% print(" order by " + order_by_clause + ", la.load_assignment_id desc") %>
      <% } else { %>
        order by la.load_assignment_id desc
      <% } %>
      
      <% if (paramExistsAdd("limit")) { %>
        limit
        <% if (paramExistsAdd("skip")) { %>
         :skip,
        <% } %>
        :limit
      <% } %>
      """;
}
