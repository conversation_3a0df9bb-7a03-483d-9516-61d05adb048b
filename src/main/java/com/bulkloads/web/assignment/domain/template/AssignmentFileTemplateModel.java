package com.bulkloads.web.assignment.domain.template;

import java.time.Instant;
import com.bulkloads.web.file.domain.template.FileTemplateModel;
import lombok.Builder;
import lombok.Value;

@Value
@Builder(toBuilder = true)
public class AssignmentFileTemplateModel {

  FileTemplateModel file;
  Instant dateAdded;
  Integer fileOrder;
  Boolean fieldsExtracted;
  Integer fieldsExtractedByUserId;
  Instant fieldsExtractedDate;
  Boolean needsAttention;
  Instant needsAttentionDate;
  Integer needsAttentionByUserId;
  String attentionNote;
  Boolean approved;
  Integer approvedByUserId;
  Instant approvedDate;
  <PERSON><PERSON><PERSON> deleted;
  Instant deletedDate;
}
