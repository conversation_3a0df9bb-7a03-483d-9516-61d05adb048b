package com.bulkloads.web.assignment.domain.entity;

import java.io.Serializable;
import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import jakarta.validation.constraints.NotNull;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Embeddable
@EqualsAndHashCode
public class SurchargeId implements Serializable {

  @NotNull
  @Column(name = "load_assignment_surcharge_id")
  private Integer loadAssignmentSurchargeId;

  @NotNull
  @Column(name = "load_assignment_id")
  private Integer loadAssignmentId;

  @NotNull
  @Column(name = "load_assignment_surcharge_type_id")
  private Integer surchargeTypeId;
}