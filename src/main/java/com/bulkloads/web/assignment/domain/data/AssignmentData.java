package com.bulkloads.web.assignment.domain.data;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;
import com.bulkloads.web.addressbook.abcompany.domain.entity.AbCompany;
import com.bulkloads.web.addressbook.abuser.domain.entity.AbUser;
import com.bulkloads.web.companyequipment.domain.entity.UserCompanyEquipment;
import com.bulkloads.web.load.domain.entity.Load;
import com.bulkloads.web.rate.domain.entity.RateType;
import com.bulkloads.web.user.domain.entity.User;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class AssignmentData {

  private Optional<String> assignmentStatus;
  private Optional<User> toUser;
  private Optional<AbCompany> toAbCompany;
  private Optional<AbUser> toAbUser;
  private Optional<Integer> numberOfLoads;
  private Optional<String> pickupNotes;
  private Optional<String> dropNotes;
  private Optional<String> workOrderNumber;
  private Optional<String> insideNotes;
  private Optional<String> personalMessage;
  private Optional<Boolean> sharedWithHiredCompany;
  private Optional<Integer> bookedFromOfferRecipientId;
  private Optional<BigDecimal> originalRate;
  private Optional<RateType> originalRateType;
  private Optional<BigDecimal> originalRatePercentage;
  private Optional<Boolean> originalRateVisible;
  private Optional<BigDecimal> rate;
  private Optional<RateType> rateType;
  private Optional<Double> estWeight;
  private Optional<Double> estVolume;
  private Optional<BigDecimal> estMiles;
  private Optional<BigDecimal> estHours;
  private Optional<Boolean> sendConfirmation;
  private Optional<String> confirmationSentMethod;
  private Optional<List<AbUser>> confirmationToAbUsers;
  private Optional<List<String>> confirmationCcOthers;
  private Optional<List<AssignmentSurchargeData>> surcharges;
  private Optional<Double> loadedWeight;
  private Optional<Double> loadedVolume;
  private Optional<Double> unloadWeight;
  private Optional<Double> unloadVolume;
  private Optional<String> loadingTicketNumber;
  private Optional<String> unloadingTicketNumber;
  private Optional<String> bolNumber;
  private Optional<String> hauledNotes;
  private Optional<LocalDate> scheduledHauledDate;
  private Optional<Instant> scheduledPickupDate;
  private Optional<Instant> scheduledDropDate;
  private Optional<LocalDate> hauledDate;
  private Optional<String> billWeightUse;
  private Optional<BigDecimal> billMiles;
  private Optional<BigDecimal> billHours;
  private Optional<BigDecimal> payment;

  private Optional<String> paymentNotes;
  private Optional<Boolean> paymentApproved;
  private Optional<Boolean> paid;
  private Optional<List<AssignmentFileData>> files;
  private Optional<Load> load;

  private Optional<Integer> loadAssignmentId;
  private Optional<String> loadAssignmentNumber;
  private Optional<String> pickupNumber;
  private Optional<String> dropNumber;

  private Optional<UserCompanyEquipment> truckUserCompanyEquipment;
  private Optional<UserCompanyEquipment> trailerUserCompanyEquipment;
}
