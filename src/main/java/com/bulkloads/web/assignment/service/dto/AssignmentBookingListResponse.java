package com.bulkloads.web.assignment.service.dto;

import java.time.Instant;
import java.time.LocalDate;
import java.util.List;
import com.bulkloads.common.jackson.CsvSerializer;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;

@Data
public class AssignmentBookingListResponse {

  private Integer loadAssignmentId;
  private Boolean active;
  private String assignmentStatus;
  private Instant assignedDate;
  private Double avgRating;
  private Double billHours;
  private Double billMiles;
  private Double billQuantity;
  private Double billRatePerMile;
  private Double billSubtotal;
  private Double billSurcharges;
  private Integer billToAbCompanyId;
  private String billToCompanyName;
  private Integer billToAbUserId;
  private String billToFirstName;
  private String billToLastName;
  private String billToEmail;
  private Double billTotal;
  private Double billWeight;
  private Double billVolume;
  private String billWeightUse;
  private Double payment;
  private String paymentNotes;
  private Instant paymentDate;
  private Integer paymentApproved;
  private Instant paymentApprovedDate;
  private Integer paid;
  private Instant paidDate;
  private Double toPayment;
  private String toPaymentNotes;
  private Instant toPaymentDate;
  private Integer toPaid;
  //  private String toPaidNotes;
  private Instant toPaidDate;
  private String bolNumber;
  private String loadAssignmentNumber;
  private String hauledNotes;
  private LocalDate scheduledHauledDate;
  private Instant scheduledPickupDate;
  private String pickupTimezone;
  private Instant scheduledDropDate;
  private String dropTimezone;
  private LocalDate hauledDate;
  private String companyName;
  private String companyLogoUrl;
  private String companyLogoThumbUrl;
  private Instant completedDate;
  private Instant confirmationConfirmedDate;
  private String confirmationConfirmedMethod;
  private String confirmationEmailStatus;
  private Integer confirmationFileId;
  private String confirmationFileUrl;
  private Instant confirmationOpenedDate;
  private Instant confirmationSentDate;
  private String confirmationSentMethod;
  private String confirmationThumbUrl;
  private String confirmationCcOther;
  private String confirmationToAbUserIds;
  private Integer createdByUserId;
  private Integer createdByUserCompanyId;
  private Instant createdDate;
  private String deletedMessage;
  private Instant deliveredDate;
  private Instant dispatchedDate;
  private Integer dispatcherUserId;
  private Integer dropAbCompanyId;
  private String dropAddress;
  private Integer dropApptRequired;
  private Integer dropCensusNum;
  private String dropCity;
  private String dropCompanyName;
  private String dropCompanyPhone;
  private String dropCountry;
  private String dropDirections;
  private Double dropLatitude;
  private String dropLocation;
  private Double dropLongitude;
  private String dropMcNum;
  private String dropNotes;
  private String dropNumber;
  private String dropPo;
  private String dropReceivingHours;
  private String dropState;
  private String dropZip;
  private String email;
  private Instant enrouteDate;
  private String equipmentNames;
  private Double estHours;
  private Double estMiles;
  private Double estQuantity;
  private Double estRatePerMile;
  private Double estSubtotal;
  private Double estSurcharges;
  private Double estTotal;
  private Double estWeight;
  private Double estVolume;
  private Double estimatedMiles;
  private String firstName;
  private Integer hiringAbCompanyId;
  private Integer hiringAbUserId;
  private String workOrderNumber;
  private String insideNotes;
  private String personalMessage;
  private Integer isHazmat;
  private String lastName;
  private Integer commodityId;
  private String loCommodity;
  private String loContractNumber;
  private Double loRate;
  private String loRateType;
  private String loadAccess;
  private String loadBearingDirection;
  private Double loadBearing;
  private Integer loadId;
  private Double loadedVolume;
  private Double loadedWeight;
  private Instant loadingDate;
  private String loadingTicketNumber;
  private Double mileage;
  private Integer numberOfFiles;
  private Integer totalFilesSize;
  private Integer needsAttention;
  private Integer numberOfLoads;
  private Integer numberOfDeliveredLoads;
  private Integer numberOfAvailableLoads;
  private Double originalRate;
  private String originalRateType;
  private Double originalRatePercentage;
  private Integer originalRateVisible;
  private Integer originalsRequired;
  @JsonProperty("phone_1")
  private String phone1;
  private Integer pickupAbCompanyId;
  private String pickupAddress;
  private Integer pickupApptRequired;
  private Integer pickupCensusNum;
  private String pickupCity;
  private String pickupCompanyName;
  private String pickupCompanyPhone;
  private String pickupCountry;
  private String pickupDirections;
  private Double pickupLatitude;
  private String pickupLocation;
  private Double pickupLongitude;
  private String pickupMcNum;
  private String pickupNotes;
  private String pickupNumber;
  private String pickupPo;
  private String pickupReceivingHours;
  private String pickupState;
  private String pickupZip;
  private Instant postDate;
  private String product;
  private Integer rateProductCategoryId;
  private String rateType;
  private Double rate;
  private Integer ratingCount;
  private String rerouteReason;

  private String sharedWithHiredCompanyResponse;
  private Integer sharedWithHiredCompany;
  private String sharedWithHiringCompanyResponse;
  private Integer sharedWithHiringCompany;
  private Integer newShare;
  private Integer childLoadAssignmentId;
  private Integer parentLoadAssignmentId;
  private Integer chainLoadAssignmentId;
  private Boolean autoInvoice;
  private Boolean readyToInvoice;
  private Boolean parentReadyToInvoice;
  private Boolean invoiced;
  private Boolean parentInvoiced;
  private Boolean parentDeleted;
  private Integer loadInvoiceId;
  private Instant invoiceDate;
  private String invoiceEmailStatus;
  private Instant invoiceEmailStatusDate;
  private String invoiceFileUrl;
  private String invoiceThumbUrl;
  private String settlementFileUrl;
  private Integer billToUserId;
  private Integer billToUserCompanyId;
  private Integer archived;
  private Integer billToArchived;
  private LocalDate shipFrom;
  private LocalDate shipTo;
  private Integer toAbCompanyId;
  private Integer toAbUserId;
  private Double toAvgRating;
  private String toCompanyName;
  private String toCompanyLogoUrl;
  private String toCompanyLogoThumbUrl;
  private String toEmail;
  private Boolean toDeleted;
  private String toDeletedMessage;
  private String toFirstName;
  private String toLastName;
  private Integer toLoadId;
  @JsonProperty("to_phone_1")
  private String toPhone1;
  private Integer toRatingCount;
  private Integer toUserCompanyId;
  private Integer toUserId;
  private String toUserTypes;
  private Double unloadWeight;
  private Double unloadVolume;
  private Instant unloadingDate;
  private String unloadingTicketNumber;
  private Integer userCompanyId;
  private Integer userId;
  private String userTypes;
  private Integer washoutRequired;

  @JsonSerialize(using = CsvSerializer.class)
  private List<Integer> requiredFileTypeIds;
  @JsonSerialize(using = CsvSerializer.class)
  private List<String> requiredFileTypes;

  private String requiredFilesNote;
  private Integer geoShareLocation;
  private String geoRequestStatus;
  private Instant geoRequestDate;
  private Boolean geoTrackingEnabled;
  private Instant geoTrackingStopDate;
  private String geoTrackingUntil;
  private Double geoLatitude;
  private Double geoLongitude;
  private Double geoSpeed;
  private Double geoHeading;
  private Instant geoUpdatedDate;
  private String externalLoadId;
  private String pickupExternalAbCompanyId;
  private String dropExternalAbCompanyId;
  private String hiringExternalAbCompanyId;
  private String hiringExternalAbUserId;
  private String toExternalAbCompanyId;
  private String toExternalAbUserId;
  private String billToExternalAbCompanyId;
  private String billToExternalAbUserId;
  private String parentHiringCompanyName;
  private String parentDeletedMessage;
  private Integer isReassigned;
  private Integer isIntraCompany;
  private Integer isDriver;
  private Integer isBooking;
  private Integer isManaged;
  private Instant modifiedDate;
  private Boolean deleted;
  private Instant exportDate;
  private Integer parentNumberOfFiles;
  private Integer isRerouted;
  private Boolean rerouteRequest;
  private String rerouteRequestReason;
  private Instant rerouteRequestDate;
  private Instant rerouteDate;
  private Integer rerouteByUserId;
  private Integer rerouteContractId;
  private String rerouteContractNumber;
  private String previousLoadAssignmentNumber;
  private String previousPickupNumber;
  private String previousDropNumber;
  private String previousWorkOrderNumber;
  private Double previousRate;
  private String previousRateType;
  private Double previousBillSubtotal;
  private Double previousBillSurcharges;
  private Double previousBillTotal;
  private Double previousBillRatePerMile;
  private String reroutePickupDrop;
  private Integer rerouteAbCompanyId;
  private String rerouteExternalAbCompanyId;
  private String rerouteCompanyName;
  private Integer rerouteCensusNum;
  private String rerouteMcNum;
  private String rerouteAddress;
  private String rerouteCity;
  private String rerouteState;
  private String rerouteZip;
  private String rerouteCountry;
  private String rerouteLocation;
  private Double rerouteLatitude;
  private Double rerouteLongitude;
  private String rerouteCompanyPhone;
  private String rerouteReceivingHours;
  private String rerouteDirections;
  private String rerouteCompanyNotes;
  private Boolean rerouteApptRequired;
  private Integer truckUserCompanyEquipmentId;
  private Integer trailerUserCompanyEquipmentId;
  private String truckUserCompanyEquipment;
  private String trailerUserCompanyEquipment;

}
