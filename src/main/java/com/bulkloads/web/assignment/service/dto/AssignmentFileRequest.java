package com.bulkloads.web.assignment.service.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Value;
import lombok.extern.jackson.Jacksonized;

@Value
@Builder
@Jacksonized
public class AssignmentFileRequest {

  @NotNull
  @Schema(name = "file_id", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Integer fileId;

  @Schema(name = "needs_attention", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  boolean needsAttention;

  @Schema(name = "attention_note", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  String attentionNote;
}
