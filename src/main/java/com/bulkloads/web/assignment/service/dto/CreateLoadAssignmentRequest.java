package com.bulkloads.web.assignment.service.dto;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import lombok.Builder;
import lombok.Value;
import lombok.extern.jackson.Jacksonized;

@Value
@Builder
@Jacksonized
public class CreateLoadAssignmentRequest {

  @Schema(name = "assignment_status", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<String> assignmentStatus;
  @Schema(name = "to_user_id", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<Integer> toUserId;
  @Schema(name = "to_ab_company_id", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<Integer> toAbCompanyId;
  @Schema(name = "to_ab_user_id", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<Integer> toAbUserId;
  @Schema(name = "number_of_loads", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<Integer> numberOfLoads;
  @Schema(name = "pickup_notes", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<String> pickupNotes;
  @Schema(name = "drop_notes", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<String> dropNotes;
  @Schema(name = "inside_notes", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<String> insideNotes;
  @Schema(name = "personal_message", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<String> personalMessage;
  @Schema(name = "shared_with_hired_company", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<Boolean> sharedWithHiredCompany;
  @Schema(name = "booked_from_offer_recipient_id", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<Integer> bookedFromOfferRecipientId;
  @Schema(name = "original_rate", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<BigDecimal> originalRate;
  @Schema(name = "original_rate_type", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<String> originalRateType;
  @Schema(name = "original_rate_percentage", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<BigDecimal> originalRatePercentage;
  @Schema(name = "original_rate_visible", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<Boolean> originalRateVisible;
  @Schema(name = "rate", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<BigDecimal> rate;
  @Schema(name = "rate_type", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<String> rateType;
  @Schema(name = "est_weight", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<Double> estWeight;
  @Schema(name = "est_volume", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<Double> estVolume;
  @Schema(name = "est_miles", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<BigDecimal> estMiles;
  @Schema(name = "est_hours", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<BigDecimal> estHours;
  @Schema(name = "send_confirmation", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<Boolean> sendConfirmation;
  @Schema(name = "confirmation_sent_method", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<String> confirmationSentMethod;
  @Schema(name = "confirmation_to_ab_user_ids", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<String> confirmationToAbUserIds;
  @Schema(name = "confirmation_cc_others", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<String> confirmationCcOthers;
  @Valid
  @Schema(name = "surcharges", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<List<@Valid AssignmentSurchargeRequest>> surcharges;
  @Schema(name = "loaded_weight", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<Double> loadedWeight;
  @Schema(name = "loaded_volume", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<Double> loadedVolume;
  @Schema(name = "unload_weight", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<Double> unloadWeight;
  @Schema(name = "unload_volume", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<Double> unloadVolume;
  @Schema(name = "loading_ticket_number", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<String> loadingTicketNumber;
  @Schema(name = "unloading_ticket_number", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<String> unloadingTicketNumber;
  @Schema(name = "bol_number", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<String> bolNumber;
  @Schema(name = "hauled_notes", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<String> hauledNotes;
  @Schema(name = "scheduled_hauled_date", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<LocalDate> scheduledHauledDate;
  @Schema(name = "hauled_date", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<LocalDate> hauledDate;
  @Schema(name = "bill_weight_use", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<String> billWeightUse;
  @Schema(name = "bill_miles", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<BigDecimal> billMiles;
  @Schema(name = "bill_hours", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<BigDecimal> billHours;
  @Schema(name = "payment", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<BigDecimal> payment;
  @Schema(name = "payment_notes", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<String> paymentNotes;
  @Schema(name = "payment_approved", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<Boolean> paymentApproved;
  @Schema(name = "paid", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<Boolean> paid;
  @Valid
  @Schema(name = "truck_user_company_equipment_id", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<Integer> truckUserCompanyEquipmentId;
  @Schema(name = "trailer_user_company_equipment_id", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<Integer> trailerUserCompanyEquipmentId;

  @Schema(name = "files", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<List<@Valid AssignmentFileRequest>> files;

  @Valid
  @Schema(name = "assignments", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<List<@Valid CreateLoadAssignmentSubRequest>> assignments;
}
