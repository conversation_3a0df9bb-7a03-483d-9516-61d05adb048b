package com.bulkloads.web.assignment.service.dto;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import com.bulkloads.common.mui.ExcludeFromMuiQuery;
import com.bulkloads.common.mui.NaturalSort;
import lombok.Builder;
import lombok.Value;

@Value
@Builder
public class AssignmentV2ListResponse {

  Integer loadAssignmentId;
  Integer loadId;
  String assignmentStatus;
  Boolean deleted;
  Boolean toDeleted;
  Boolean parentDeleted;
  String toCompanyName;
  String toFullName;
  LocalDate scheduledHauledDate;
  Instant scheduledPickupDate;
  String pickupTimezone;
  Instant scheduledDropDate;
  String dropTimezone;
  LocalDate hauledDate;
  String loCommodity;
  String rateProductCategory;
  LocalDate shipFrom;
  LocalDate shipTo;
  BigDecimal rate;
  String rateType;
  String rateTypeText;
  String rateTypeTextAbbr;
  BigDecimal estRatePerMile;
  String pickupCompanyName;
  String pickupCity;
  String pickupState;
  String dropCompanyName;
  String dropCity;
  String dropState;
  Boolean isRerouted;
  String reroutePickupDrop;
  String rerouteCompanyName;
  String rerouteCity;
  String rerouteState;
  Boolean rerouteRequest;
  String rerouteRequestReason;
  LocalDate rerouteRequestDate;
  Double estimatedMiles;
  String parentHiringCompanyName;
  @NaturalSort
  String loadAssignmentNumber;
  String loContractNumber;
  @NaturalSort
  String workOrderNumber;
  String bolNumber;
  String loadingTicketNumber;
  String unloadingTicketNumber;
  @NaturalSort
  String pickupNumber;
  @NaturalSort
  String dropNumber;
  Integer loadInvoiceId;
  Integer parentLoadInvoiceId;
  Boolean parentReadyToInvoice;

  @ExcludeFromMuiQuery
  Integer confirmationFileId;
  LocalDate confirmationConfirmedDate;
  LocalDate confirmationOpenedDate;
  LocalDate confirmationSentDate;
  Integer numberOfFiles;
  Integer parentNumberOfFiles;
  Double loadedVolume;
  Double loadedWeight;
  Double unloadVolume;
  Double unloadWeight;
  Double billVolume;
  Double billWeight;
  String billWeightUse;
  Double billQuantity;
  Double weightDiscrepancy;
  BigDecimal payable;
  BigDecimal receivable;
  BigDecimal totalNetProfit;
  String truckUserCompanyEquipment;
  String trailerUserCompanyEquipment;
}
