package com.bulkloads.web.assignment.service.dto;

import java.util.Optional;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Value;
import lombok.extern.jackson.Jacksonized;

@Value
@Builder
@Jacksonized
public class CancelAssignmentRequest {

  @Schema(name = "deleted_message", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<String> deletedMessage;

}
