package com.bulkloads.web.assignment.service.dto;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Builder;
import lombok.Value;

@Value
@Builder
public class AssignmentSearchRequest {
  Integer loadId;
  Integer loadAssignmentId;
  List<Integer> loadAssignmentIds;
  List<Integer> userIds;
  List<Integer> userGroupIds;
  Integer loadInvoiceId;
  Integer confirmationFileId;
  String confirmationFileCode;
  Integer toAbCompanyId;
  Integer toAbUserId;
  Integer confirmationEmailQueueId;
  Boolean active;
  Boolean tmsActive;
  Boolean completed;
  Boolean delivered;
  String assignmentStatus;
  Boolean unassigned;
  Boolean invoiced;
  Boolean billToArchived;
  Boolean intraCompany;
  Boolean newShares;
  Integer pickupAbCompanyId;
  Integer dropAbCompanyId;
  Boolean paymentApproved;
  Boolean paid;
  Double pickupLatitude;
  Double pickupLongitude;
  Double dropLatitude;
  Double dropLongitude;
  Double radius;
  LocalDate shipFrom;
  LocalDate shipTo;
  String loContractNumber;
  String loadAssignmentNumber;
  String searchTerm;
  LocalDateTime lastModifiedDate;
  Boolean includeDeleted;
  Boolean withFiles;
}