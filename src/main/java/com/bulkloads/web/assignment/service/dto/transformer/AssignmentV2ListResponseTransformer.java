package com.bulkloads.web.assignment.service.dto.transformer;

import com.bulkloads.common.jpa.nativejpa.QueryParts;
import com.bulkloads.web.assignment.service.dto.AssignmentV2ListResponse;
import org.hibernate.query.TupleTransformer;
import org.springframework.stereotype.Component;
import lombok.RequiredArgsConstructor;

@Component
@RequiredArgsConstructor
public class AssignmentV2ListResponseTransformer implements TupleTransformer<AssignmentV2ListResponse> {

  @Override
  public AssignmentV2ListResponse transformTuple(Object[] columns, String[] aliases) {
    QueryParts parts = new QueryParts(columns, aliases);

    return AssignmentV2ListResponse.builder()
        .loadAssignmentId(parts.asInteger("load_assignment_id"))
        .loadId(parts.asInteger("load_id"))
        .assignmentStatus(parts.asString("assignment_status"))
        .deleted(parts.asBoolean("deleted"))
        .toDeleted(parts.asBoolean("to_deleted"))
        .parentDeleted(parts.asBoolean("parent_deleted"))
        .toCompanyName(parts.asString("to_company_name"))
        .toFullName(parts.asString("to_full_name"))
        .scheduledHauledDate(parts.asLocalDate("scheduled_hauled_date"))
        .scheduledPickupDate(parts.asInstant("scheduled_pickup_date"))
        .pickupTimezone(parts.asString("pickup_timezone"))
        .scheduledDropDate(parts.asInstant("scheduled_drop_date"))
        .dropTimezone(parts.asString("drop_timezone"))
        .hauledDate(parts.asLocalDate("hauled_date"))
        .loCommodity(parts.asString("lo_commodity"))
        .rateProductCategory(parts.asString("rate_product_category"))
        .shipFrom(parts.asLocalDate("ship_from"))
        .shipTo(parts.asLocalDate("ship_to"))
        .rate(parts.asBigDecimal("rate"))
        .rateType(parts.asString("rate_type"))
        .rateTypeText(parts.asString("rate_type_text"))
        .rateTypeTextAbbr(parts.asString("rate_type_text_abbr"))
        .estRatePerMile(parts.asBigDecimal("est_rate_per_mile"))
        .pickupCompanyName(parts.asString("pickup_company_name"))
        .pickupCity(parts.asString("pickup_city"))
        .pickupState(parts.asString("pickup_state"))
        .dropCompanyName(parts.asString("drop_company_name"))
        .dropCity(parts.asString("drop_city"))
        .dropState(parts.asString("drop_state"))
        .isRerouted(parts.asBoolean("is_rerouted"))
        .reroutePickupDrop(parts.asString("reroute_pickup_drop"))
        .rerouteCompanyName(parts.asString("reroute_company_name"))
        .rerouteCity(parts.asString("reroute_city"))
        .rerouteState(parts.asString("reroute_state"))
        .rerouteRequest(parts.asBoolean("reroute_request"))
        .rerouteRequestReason(parts.asString("reroute_request_reason"))
        .rerouteRequestDate(parts.asLocalDate("reroute_request_date"))
        .estimatedMiles(parts.asDouble("estimated_miles"))
        .parentHiringCompanyName(parts.asString("parent_hiring_company_name"))
        .loadAssignmentNumber(parts.asString("load_assignment_number"))
        .loContractNumber(parts.asString("lo_contract_number"))
        .workOrderNumber(parts.asString("work_order_number"))
        .bolNumber(parts.asString("bol_number"))
        .loadingTicketNumber(parts.asString("loading_ticket_number"))
        .unloadingTicketNumber(parts.asString("unloading_ticket_number"))
        .pickupNumber(parts.asString("pickup_number"))
        .dropNumber(parts.asString("drop_number"))
        .loadInvoiceId(parts.asInteger("load_invoice_id"))
        .parentLoadInvoiceId(parts.asInteger("parent_load_invoice_id"))
        .parentReadyToInvoice(parts.asBoolean("parent_ready_to_invoice"))
        .confirmationFileId(parts.asInteger("confirmation_file_id"))
        .confirmationConfirmedDate(parts.asLocalDate("confirmation_confirmed_date"))
        .confirmationOpenedDate(parts.asLocalDate("confirmation_opened_date"))
        .confirmationSentDate(parts.asLocalDate("confirmation_sent_date"))
        .numberOfFiles(parts.asInteger("number_of_files"))
        .parentNumberOfFiles(parts.asInteger("parent_number_of_files"))
        .loadedVolume(parts.asDouble("loaded_volume"))
        .loadedWeight(parts.asDouble("loaded_weight"))
        .unloadVolume(parts.asDouble("unload_volume"))
        .unloadWeight(parts.asDouble("unload_weight"))
        .billVolume(parts.asDouble("bill_volume"))
        .billWeight(parts.asDouble("bill_weight"))
        .billWeightUse(parts.asString("bill_weight_use"))
        .billQuantity(parts.asDouble("bill_quantity"))
        .weightDiscrepancy(parts.asDouble("weight_discrepancy"))
        .payable(parts.asBigDecimal("payable"))
        .receivable(parts.asBigDecimal("receivable"))
        .totalNetProfit(parts.asBigDecimal("total_net_profit"))
        .truckUserCompanyEquipment(parts.asString("truck_user_company_equipment"))
        .trailerUserCompanyEquipment(parts.asString("trailer_user_company_equipment"))
        .build();
  }
}
