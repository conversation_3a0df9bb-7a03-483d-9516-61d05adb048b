package com.bulkloads.web.assignment.service.template;

import static com.bulkloads.config.AppConstants.AssignmentAction.BOOKING_CREATE;
import static com.bulkloads.config.AppConstants.AssignmentAction.BOOKING_UPDATE;
import static com.bulkloads.config.AppConstants.Templates.ASSIGNMENT_CREATED;
import static java.util.Objects.nonNull;
import java.util.List;
import com.bulkloads.web.load.domain.template.LoadAssignmentTemplateModel;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class CreateBookingTemplateBuilder extends AssignmentTemplateBuilder {

  @Override
  public String getEmailTemplateName() {
    return ASSIGNMENT_CREATED;
  }

  @Override
  public String getEmailTitle(final LoadAssignmentTemplateModel model, final String titleTextInput) {
    final String rateMessage = model.getRateMediumFormat();
    final int assignmentsSize = model.getAssignments().size();
    final String lan = assignmentsSize == 1
        ? "(" + model.getLoadAssignmentNumber() + ")"
        : "(" + model.getAssignments().get(0).getLoadAssignmentNumber() + " thru "
          + model.getAssignments().get(assignmentsSize - 1).getLoadAssignmentNumber() + ")";

    final String drop = StringUtils.hasText(model.getDropCity())
        ? model.getDropCity() + ", " + model.getDropState()
        : model.getDropCompanyName();

    String titleText = model.getIsIntraCompany() ? "Assigned Load" : "Confirm Load";
    if (StringUtils.hasText(titleTextInput)) {
      titleText = titleTextInput;
    }

    String prefix = nonNull(model.getEditDate()) ? "REVISED " : "";

    return prefix + titleText + " " + lan + ": " + model.getPickupCity() + ", "
           + model.getPickupState() + " to " + drop + " " + rateMessage;
  }

  @Override
  public String getSmsContent(final LoadAssignmentTemplateModel model) {
    final String smsSuffix = getSmsSuffix(model);
    final StringBuilder smsContent = new StringBuilder();
    final int recordCount = model.getAssignments().size();

    smsContent.append("[DO NOT REPLY]");

    if (StringUtils.hasText(model.getPersonalMessage())) {
      smsContent.append(" ").append(model.getPersonalMessage()).append(" ");
    }

    smsContent.append(model.getCompanyName())
        .append(" has assigned you ")
        .append(recordCount)
        .append(" LOAD")
        .append(recordCount == 1 ? "" : "S")
        .append(" ")
        .append(smsSuffix);

    return smsContent.toString();
  }

  @Override
  public String getNotificationTitle(final LoadAssignmentTemplateModel model) {
    final String revisedText = nonNull(model.getEditDate()) ? "REVISED " : "";
    final int recordCount = model.getAssignments().size();
    final String lan =
        "("
        + (recordCount == 1
            ? model.getLoadAssignmentNumber()
            : model.getAssignments().get(0).getLoadAssignmentNumber() + " thru " + model.getAssignments().get(recordCount - 1).getLoadAssignmentNumber())
        + ")";
    final String action = model.getIsIntraCompany() ? "Assigned" : "Confirm";
    return revisedText + action + " Load " + lan;
  }

  @Override
  public String getNotificationContent(final LoadAssignmentTemplateModel model) {
    return model.getFullName() + " has assigned you " + model.getAssignments().size() + " Load(s): "
           + model.getPickupDrop() + " " + model.getRateMediumFormat();
  }

  @Override
  public List<String> getSupportedActions() {
    return List.of(BOOKING_CREATE, BOOKING_UPDATE);
  }
}
