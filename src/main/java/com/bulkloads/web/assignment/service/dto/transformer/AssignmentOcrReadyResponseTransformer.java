package com.bulkloads.web.assignment.service.dto.transformer;

import com.bulkloads.common.jpa.nativejpa.QueryParts;
import com.bulkloads.web.assignment.service.dto.AssignmentOcrReadyResponse;
import org.hibernate.query.TupleTransformer;
import org.springframework.stereotype.Component;

@Component
public class AssignmentOcrReadyResponseTransformer implements TupleTransformer<AssignmentOcrReadyResponse> {

  @Override
  public AssignmentOcrReadyResponse transformTuple(Object[] tuple, String[] aliases) {
    QueryParts parts = new QueryParts(tuple, aliases);

    return AssignmentOcrReadyResponse.builder()
        .loadAssignmentId(parts.asInteger("load_assignment_id"))
        .agtraxIntegration(parts.asBoolean("agtrax_integration"))
        .hasUnmatchedExternalGrades(parts.asBoolean("has_unmatched_external_grades"))
        .companyName(parts.asString("company_name"))
        .toCompanyName(parts.asString("to_company_name"))
        .toFirstName(parts.asString("to_first_name"))
        .toLastName(parts.asString("to_last_name"))
        .toPhone(parts.asString("to_phone"))
        .pickupCompanyName(parts.asString("pickup_company_name"))
        .pickupCity(parts.asString("pickup_city"))
        .pickupState(parts.asString("pickup_state"))
        .dropCompanyName(parts.asString("drop_company_name"))
        .dropCity(parts.asString("drop_city"))
        .dropState(parts.asString("drop_state"))
        .loCommodity(parts.asString("lo_commodity"))
        .assignedDate(parts.asInstant("assigned_date"))
        .deliveredDate(parts.asInstant("delivered_date"))
        .assignmentStatus(parts.asString("assignment_status"))
        .originFileUrl(parts.asString("origin_file_url"))
        .destinationFileUrl(parts.asString("destination_file_url"))
        .originFileIsUnreadable(parts.asBoolean("origin_file_is_unreadable"))
        .destinationFileIsUnreadable(parts.asBoolean("destination_file_is_unreadable"))
        .build();
  }
}