package com.bulkloads.web.assignment.mapper;

import static java.util.Objects.isNull;
import java.util.List;
import java.util.Optional;
import java.util.stream.IntStream;
import com.bulkloads.web.assignment.domain.data.AssignmentFileData;
import com.bulkloads.web.assignment.domain.entity.AssignmentFile;
import com.bulkloads.web.assignment.service.dto.AssignmentFileRequest;
import com.bulkloads.web.contracts.mapper.CommonMapper;
import com.bulkloads.web.file.domain.entity.File;
import com.bulkloads.web.file.repository.FileRepository;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.springframework.beans.factory.annotation.Autowired;

@Mapper(componentModel = "spring",
    nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
    nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
    unmappedTargetPolicy = org.mapstruct.ReportingPolicy.IGNORE,
    uses = CommonMapper.class)
public abstract class AssignmentFileMapper {

  @Autowired
  FileRepository fileRepository;

  @Mapping(target = "id.fileId", source = "file.fileId")
  public abstract AssignmentFile dataToEntity(final AssignmentFileData data);

  public abstract void dataToEntity(final AssignmentFileData data, @MappingTarget final AssignmentFile entity);

  protected abstract List<AssignmentFile> dataToEntity(final List<AssignmentFileData> data);

  public Optional<List<AssignmentFileData>> mapOptionalFileRequestListToOptionalFileList(
      final Optional<List<AssignmentFileRequest>> lafrs) {

    if (isNull(lafrs)) {
      return null;
    }

    if (lafrs.isEmpty()) {
      return Optional.empty();
    }

    final List<AssignmentFileRequest> requests = lafrs.get();
    List<Integer> ids = requests.stream().map(AssignmentFileRequest::getFileId).toList();

    final List<File> files = fileRepository.findAllById(ids);

    final List<AssignmentFileData> data = IntStream.range(0, files.size()).mapToObj(i -> {
      final File file = files.get(i);
      final AssignmentFileRequest asr = requests.get(i);
      return new AssignmentFileData()
          .setFile(file)
          .setNeedsAttention(asr.isNeedsAttention())
          .setAttentionNote(asr.getAttentionNote());
    }).toList();
    return Optional.of(data);
  }
}
