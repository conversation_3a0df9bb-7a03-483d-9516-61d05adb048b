package com.bulkloads.web.integration.agtrax;

import static com.bulkloads.common.StringUtil.asCsString;
import static com.bulkloads.common.validation.ValidationUtils.isEmpty;
import java.net.URI;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import com.bulkloads.config.AppProperties;
import com.bulkloads.exception.BulkloadsException;
import com.bulkloads.web.file.service.dto.OcrResponse;
import org.apache.http.client.utils.URIBuilder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@Transactional
@RequiredArgsConstructor
public class AgTraxInternalService {

  private static final String UUID = "ad350820-433c-4932-a135-f7f9929ecf81";

  private final AppProperties appProperties;
  private final WebClient webClient;

  public CompletableFuture<Void> repostScaleTicketsByLoadAssignmentIdsAsync(List<Integer> loadAssignmentIds) {
    return CompletableFuture.runAsync(() -> {
      try {
        repostScaleTicketsByLoadAssignmentIds(loadAssignmentIds);
      } catch (Exception e) {
        log.error("Error calling repostScaleTicketsByLoadAssignmentIdsAsync for : {}", loadAssignmentIds, e);
        throw new RuntimeException(e);
      }
    });
  }

  private void repostScaleTicketsByLoadAssignmentIds(List<Integer> loadAssignmentIds) {
    String targetUrl = appProperties.getDomainUrl() + "/rest/integration/agtrax/repost";
    try {
      URI uri = new URIBuilder(targetUrl)
          .addParameter("uuid", UUID)
          .addParameter("load_assignment_ids", asCsString(loadAssignmentIds))
          .build();

      OcrResponse response = webClient.post()
          .uri(uri)
          .header("Content-Type", "application/json")
          .bodyValue(Map.of())
          .retrieve()
          .bodyToMono(OcrResponse.class)
          .block();

      if (isEmpty(response)) {
        log.error("Error calling repostScaleTicketsByLoadAssignmentIdsAsync for : {}", loadAssignmentIds);
        throw new BulkloadsException("Error calling OCR service");
      }
      log.debug("Response from OCR service: [{}]", response.getMessage());

    } catch (WebClientResponseException e) {
      log.error("Error calling repostScaleTicketsByLoadAssignmentIdsAsync for : {}", loadAssignmentIds, e);
      throw new BulkloadsException(e);
    } catch (Exception e) {
      log.error("Unexpected error calling repostScaleTicketsByLoadAssignmentIdsAsync for : {}", loadAssignmentIds, e);
      throw new BulkloadsException(e);
    }
  }

}