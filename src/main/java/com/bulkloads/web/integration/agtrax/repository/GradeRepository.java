package com.bulkloads.web.integration.agtrax.repository;

import java.util.List;
import java.util.Optional;
import com.bulkloads.web.integration.agtrax.domain.entity.Grade;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface GradeRepository extends JpaRepository<Grade, Integer> {
  
  Optional<Grade> findByGradeCode(String gradeCode);
  
  Optional<Grade> findByGradeName(String gradeName);
  
  List<Grade> findByDeletedFalse();
}
