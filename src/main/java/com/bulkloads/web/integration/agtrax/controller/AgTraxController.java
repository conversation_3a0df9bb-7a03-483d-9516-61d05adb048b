package com.bulkloads.web.integration.agtrax.controller;

import static com.bulkloads.config.AppConstants.UserRole.ROLE_SITE_ADMIN;
import java.util.List;
import com.bulkloads.web.integration.agtrax.dto.CandidateAssignmentResponse;
import com.bulkloads.web.integration.agtrax.dto.UnmatchedOriginTicketResponse;
import com.bulkloads.web.integration.agtrax.service.AgTraxService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;

@RestController
@RequestMapping("/rest/agtrax")
@Tag(name = "AgTrax Scale Tickets")
@CrossOrigin(origins = "*", maxAge = 3600)
@RequiredArgsConstructor
public class AgTraxController {

  private final AgTraxService agTraxService;

  @Operation(summary = "Get unmatched origin tickets admin")
  @GetMapping("/unmatched_origin_tickets_admin")
  @PreAuthorize("hasRole('" + ROLE_SITE_ADMIN + "')")
  public List<UnmatchedOriginTicketResponse> getUnmatchedOriginTicketsAdmin() {
    return agTraxService.getUnmatchedOriginTicketsAdmin();
  }

  @Operation(summary = "Get candidate assignments for agtrax origin ticket")
  @GetMapping("/unmatched_origin_tickets_admin/{agtrax_scale_ticket_id}")
  @PreAuthorize("hasRole('" + ROLE_SITE_ADMIN + "')")
  public List<CandidateAssignmentResponse> getCandidateAssignments(
          @PathVariable("agtrax_scale_ticket_id") Integer agtraxScaleTicketId) {
    return agTraxService.getCandidateAssignments(agtraxScaleTicketId);
  }
}