package com.bulkloads.web.integration.agtrax.domain.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "agtrax_scale_ticket_factors")
@Getter
@Setter
public class AgTraxScaleTicketFactor {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "agtrax_scale_ticket_factor_id", nullable = false)
  private Integer agtraxScaleTicketFactorId;

  @Column(name = "agtrax_scale_ticket_id", nullable = false)
  private Integer agtraxScaleTicketId;

  @Size(max = 45)
  @Column(name = "factor_code", nullable = false, length = 45)
  private String factorCode = "";

  @Size(max = 255)
  @Column(name = "factor_description", nullable = false, length = 255)
  private String factorDescription = "";

  @Size(max = 45)
  @Column(name = "factor_value", nullable = false, length = 45)
  private String factorValue = "";

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "agtrax_scale_ticket_id", referencedColumnName = "agtrax_scale_ticket_id", insertable = false, updatable = false)
  private AgTraxScaleTicket agtraxScaleTicket;
}