package com.bulkloads.web.integration.agtrax.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Value;

@Value
@Builder
public class CandidateAssignmentResponse {
  @JsonProperty("load_assignment_id")
  Integer loadAssignmentId;

  @JsonProperty("load_id")
  Integer loadId;

  @JsonProperty("loading_ticket_number")
  String loadingTicketNumber;

  @JsonProperty("pickup_company_name")
  String pickupCompanyName;

  @JsonProperty("drop_company_name")
  String dropCompanyName;

  @JsonProperty("carrier_company_name")
  String carrierCompanyName;
}