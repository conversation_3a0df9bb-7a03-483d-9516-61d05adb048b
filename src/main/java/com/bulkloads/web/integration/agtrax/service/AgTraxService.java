package com.bulkloads.web.integration.agtrax.service;

import static com.bulkloads.web.file.service.FileOcrService.OCR_FIELD_GROSS_WEIGHT;
import static com.bulkloads.web.file.service.FileOcrService.OCR_FIELD_TARE_WEIGHT;
import static com.bulkloads.web.file.service.FileOcrService.OCR_FIELD_TICKET_DATE;
import static com.bulkloads.web.file.service.FileOcrService.OCR_FIELD_TICKET_NUMBER;
import static com.bulkloads.web.file.service.FileService.LOADING_TICKET_FILE_TYPE_ID;
import java.io.IOException;
import java.nio.file.Path;
import java.time.Instant;
import java.util.AbstractMap;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import com.bulkloads.config.AppConstants;
import com.bulkloads.exception.BulkloadsException;
import com.bulkloads.security.ImpersonationService;
import com.bulkloads.web.assignment.domain.entity.Assignment;
import com.bulkloads.web.assignment.repository.AssignmentRepository;
import com.bulkloads.web.assignment.service.AssignmentService;
import com.bulkloads.web.assignment.service.dto.AssignmentFileRequest;
import com.bulkloads.web.assignment.service.dto.UpdateLoadAssignmentRequest;
import com.bulkloads.web.aws.service.AmazonS3Service;
import com.bulkloads.web.file.domain.entity.File;
import com.bulkloads.web.file.domain.entity.FileField;
import com.bulkloads.web.file.domain.entity.FileFieldDefinition;
import com.bulkloads.web.file.repository.FileFieldDefinitionRepository;
import com.bulkloads.web.file.repository.FileRepository;
import com.bulkloads.web.file.service.FileService;
import com.bulkloads.web.file.service.dto.FileResponse;
import com.bulkloads.web.file.service.dto.LocalFileRequest;
import com.bulkloads.web.file.util.ImageUtils;
import com.bulkloads.web.integration.agtrax.domain.entity.AgTraxScaleTicket;
import com.bulkloads.web.integration.agtrax.domain.entity.AgTraxScaleTicketFactor;
import com.bulkloads.web.integration.agtrax.domain.entity.ExternalGrade;
import com.bulkloads.web.integration.agtrax.domain.entity.Grade;
import com.bulkloads.web.integration.agtrax.dto.CandidateAssignmentResponse;
import com.bulkloads.web.integration.agtrax.dto.UnmatchedOriginTicketResponse;
import com.bulkloads.web.integration.agtrax.repository.AgTraxRepository;
import com.bulkloads.web.integration.agtrax.repository.ExternalGradeRepository;
import com.bulkloads.web.integration.agtrax.repository.GradeRepository;
import com.bulkloads.web.user.domain.entity.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.server.ResponseStatusException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RequiredArgsConstructor
public class AgTraxService {
  @Autowired
  private AgTraxRepository agTraxRepository;
  @Autowired
  private AssignmentRepository loadAssignmentRepository;
  @Autowired
  private ExternalGradeRepository externalGradeRepository;
  @Autowired
  private GradeRepository gradeRepository;
  @Autowired
  private FileRepository fileRepository;
  @Autowired
  private FileFieldDefinitionRepository fileFieldDefinitionRepository;
  @Autowired
  private FileService fileService;
  @Autowired
  private AssignmentService assignmentService;
  @Autowired
  private ImpersonationService impersonationService;

  public List<UnmatchedOriginTicketResponse> getUnmatchedOriginTicketsAdmin() {
    return agTraxRepository.getUnmatchedOriginTicketsAdmin();
  }

  public List<CandidateAssignmentResponse> getCandidateAssignments(Integer agtraxScaleTicketId) {
    // First get the scale ticket details
    Optional<AgTraxScaleTicket> ticketOpt = agTraxRepository.findByAgtraxScaleTicketIdAndDeletedFalse(agtraxScaleTicketId);
    if (ticketOpt.isEmpty()) {
      throw new ResponseStatusException(HttpStatus.NOT_FOUND, "Scale ticket not found");
    }

    AgTraxScaleTicket ticket = ticketOpt.get();

    // Try to find candidate assignments by ticket number first
    List<CandidateAssignmentResponse> candidates = agTraxRepository.findCandidateAssignmentsByTicketNumber(
        ticket.getUserCompanyId(),
        ticket.getTicketNumber()
    );

    // If no candidates found by ticket number, try by company IDs
    if (candidates.isEmpty() && ticket.getBranchId() != null
        && ticket.getDestinationId() != null && ticket.getHaulerId() != null) {
      candidates = agTraxRepository.findCandidateAssignmentsByCompanies(
          ticket.getUserCompanyId(),
          ticket.getBranchId(),
          ticket.getDestinationId(),
          ticket.getHaulerId()
      );
    }

    return candidates;
  }

  @Transactional
  public boolean linkAssignmentToScaleTicket(Integer assignmentId) throws IOException {
    if (assignmentId == null) {
      throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Assignment ID is required");
    }

    // Find the assignment
    Assignment assignment = loadAssignmentRepository.findById(assignmentId)
        .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND,
            "Assignment not found with ID: " + assignmentId));

    String loadingTicketNumber = assignment.getLoadingTicketNumber();
    // use the company from the assignment
    if (assignment.getUserCompany() == null) {
      throw new ResponseStatusException(HttpStatus.BAD_REQUEST, 
          "Assignment does not have an associated user company");
    }
    Integer userCompanyId = assignment.getUserCompany().getUserCompanyId();

    // Check if there's an existing scale ticket linked to this assignment
    Optional<AgTraxScaleTicket> existingLinkedTicket = agTraxRepository
        .findByUserCompanyIdAndDeletedFalseAndLoadAssignmentId(
            userCompanyId, assignmentId);

    if (existingLinkedTicket.isPresent()) {
      AgTraxScaleTicket linkedTicket = existingLinkedTicket.get();

      // If loading ticket number hasn't changed, no action needed
      if (loadingTicketNumber != null && loadingTicketNumber.equals(linkedTicket.getTicketNumber())) {
        log.debug("Assignment {} already linked to correct scale ticket {}", assignmentId, loadingTicketNumber);
        return true;
      }

      unlinkAssignmentToScaleTicket(assignmentId, linkedTicket, assignment);
    }

    log.info("Linking assignment {} to scale ticket with number {}", assignmentId, loadingTicketNumber);

    log.debug("Looking for scale ticket with loadingTicketNumber='{}', userCompanyId='{}'",
        loadingTicketNumber, userCompanyId);

    // Find the scale ticket
    Optional<AgTraxScaleTicket> scaleTicketOpt = agTraxRepository.findByTicketNumberAndUserCompanyIdAndDeletedFalseAndLoadAssignmentIdIsNull(
        loadingTicketNumber, userCompanyId);

    if (scaleTicketOpt.isEmpty()) {
      log.warn("No scale ticket found for ticket number: {}", loadingTicketNumber);
      return false;
    }

    AgTraxScaleTicket scaleTicket = scaleTicketOpt.get();

    // Link the assignment to the scale ticket
    scaleTicket.setLoadAssignmentId(assignmentId);
    scaleTicket.setModifiedDate(Instant.now());

    // Set the agtrax_integration flag to true in the assignment
    assignment.setAgtraxIntegration(true);

    List<FileField> fileFieldsToSave = getFileFieldsFromFactors(scaleTicket);

    // see if there are any unmatched grades except these 4: OCR_FIELD_TICKET_NUMBER, OCR_FIELD_TICKET_DATE, OCR_FIELD_GROSS_WEIGHT, OCR_FIELD_TARE_WEIGHT
    boolean hasUnmatchedExternalGrades = fileFieldsToSave.stream()
        .anyMatch(field -> field.getGradeId() == null
            && !field.getFieldName().equalsIgnoreCase(OCR_FIELD_TICKET_NUMBER)
            && !field.getFieldName().equalsIgnoreCase(OCR_FIELD_TICKET_DATE)
            && !field.getFieldName().equalsIgnoreCase(OCR_FIELD_GROSS_WEIGHT)
            && !field.getFieldName().equalsIgnoreCase(OCR_FIELD_TARE_WEIGHT)
        );
    assignment.setHasUnmatchedExternalGrades(hasUnmatchedExternalGrades);

    // Get the loading ticket file
    File loadingTicketFile;
    if (assignment.getLoadingTicketFileId() != null) {
      loadingTicketFile = fileRepository.findById(assignment.getLoadingTicketFileId())
          .orElse(null);
    } else {
      log.warn("No loading ticket file found for assignment: {}", assignmentId);

      // Try to create an image from the scale ticket data
      // Create image and get the new file ID
      Integer fileId = createFileFromText(assignmentId, fileFieldsToSave);
      log.info("Created image from scale ticket data: {}", fileId);

      updateAssignmentWithGeneratedOriginFile(assignment, fileId);

      loadAssignmentRepository.flush();
      assignment = loadAssignmentRepository.findById(assignment.getLoadAssignmentId())
          .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND,
              "Assignment not found with ID: " + assignmentId));

      loadingTicketFile = fileRepository.findById(assignment.getLoadingTicketFileId())
          .orElse(null);
    }

    overwriteFileFields(loadingTicketFile, fileFieldsToSave);

    // approve file

    log.info("Successfully processed {} factors for scale ticket ID {}",
        fileFieldsToSave.size(), scaleTicket.getAgtraxScaleTicketId());
    return true;
  }

  private void unlinkAssignmentToScaleTicket(Integer assignmentId, AgTraxScaleTicket linkedTicket, Assignment assignment) {
    // Unlink the scale ticket
    linkedTicket.setLoadAssignmentId(null);
    linkedTicket.setModifiedDate(Instant.now());
    assignment.setAgtraxIntegration(false);
    assignment.setHasUnmatchedExternalGrades(false);

    // Clear file fields from loading ticket file if it exists
    if (assignment.getLoadingTicketFileId() != null) {
      File loadingTicketFile = fileRepository.findById(assignment.getLoadingTicketFileId())
          .orElse(null);

      // Remove the loading ticket file from assignment using AssignmentService
      List<AssignmentFileRequest> files = assignment.getAssignmentFiles().stream()
          .filter(f -> !f.getFile().getFileId().equals(assignment.getLoadingTicketFileId()))
          .map(f -> AssignmentFileRequest.builder()
              .fileId(f.getFile().getFileId())
              .build())
          .collect(Collectors.toList());

      UpdateLoadAssignmentRequest updateRequest = UpdateLoadAssignmentRequest.builder()
          .files(Optional.of(files))
          .build();

      assignmentService.update(assignment.getLoadAssignmentId(), updateRequest);
    }
  }

  private void overwriteFileFields(File loadingTicketFile, List<FileField> fileFieldsToSave) {
    // Now that we have a file, associate all the previously created file fields with it
    if (loadingTicketFile != null) {
      // Clear existing fields and add new ones
      List<FileField> previousFields = loadingTicketFile.getFileFields();
      for (FileField field : previousFields) {
        field.setFile(null);
      }
      loadingTicketFile.getFileFields().clear();
      fileRepository.saveAndFlush(loadingTicketFile);

      // For each file field we created earlier, update it with the file reference
      for (FileField fileField : fileFieldsToSave) {
        fileField.setFile(loadingTicketFile);
      }
      // Add all fields to the file
      loadingTicketFile.getFileFields().addAll(fileFieldsToSave);

      // Save the file with its updated fields
      fileRepository.save(loadingTicketFile);
    }
  }

  private void updateAssignmentWithGeneratedOriginFile(Assignment assignment, Integer fileId) {
    // Update the assignment with the new file ID using AssignmentService
    // Gather existing ids and add new one
    List<AssignmentFileRequest> files = assignment.getAssignmentFiles().stream()
        .map(f ->
            AssignmentFileRequest.builder()
                .fileId(f.getFile().getFileId())
                .build()
        )
        .collect(Collectors.toList());
    files.add(AssignmentFileRequest.builder()
        .fileId(fileId)
        .build()
    );

    UpdateLoadAssignmentRequest updateRequest = UpdateLoadAssignmentRequest.builder()
        .files(Optional.of(files))
        .build();

    User user = assignment.getUser();
    impersonationService.impersonate(user.getUserId());
    assignmentService.update(assignment.getLoadAssignmentId(), updateRequest);

  }

  private List<FileField> getFileFieldsFromFactors(AgTraxScaleTicket scaleTicket) {
    // Get the factors for this scale ticket
    List<AgTraxScaleTicketFactor> factors = agTraxRepository.findFactorsByAgtraxScaleTicketId(scaleTicket.getAgtraxScaleTicketId());
    log.debug("Found {} factors for scale ticket {}", factors.size(), scaleTicket.getAgtraxScaleTicketId());

    // Create a list to hold file fields we'll create
    List<FileField> fileFieldsToSave = new ArrayList<>();
    
    // Track processed factor codes to avoid duplicates
    Set<String> processedFactorCodes = new HashSet<>();

    // add non-grade fields
    fileFieldsToSave.add(createFileField(OCR_FIELD_TICKET_NUMBER, "Ticket Number", scaleTicket.getTicketNumber(), null));
    fileFieldsToSave.add(createFileField(OCR_FIELD_TICKET_DATE, "Ticket Date", scaleTicket.getDate(), null));
    fileFieldsToSave.add(createFileField(OCR_FIELD_GROSS_WEIGHT, "Gross", scaleTicket.getGrossPounds(), null));
    fileFieldsToSave.add(createFileField(OCR_FIELD_TARE_WEIGHT, "Tare", scaleTicket.getTarePounds(), null));

    // Process each factor
    for (AgTraxScaleTicketFactor factor : factors) {
      String factorCode = factor.getFactorCode();
      String factorDescription = factor.getFactorDescription();
      String factorValue = factor.getFactorValue();

      if (factorCode == null || factorValue == null) {
        continue;
      }

      // Skip if we've already processed this factor code
      if (processedFactorCodes.contains(factorCode)) {
        log.debug("Skipping duplicate factor code: {}", factorCode);
        continue;
      }
      processedFactorCodes.add(factorCode);

      // Try to find matching external grade
      Optional<ExternalGrade> existingExternalGrade = externalGradeRepository
          .findByExternalGradeCodeAndUserCompanyId(factorCode, scaleTicket.getUserCompanyId());

      Grade grade;

      if (existingExternalGrade.isPresent()) {
        // Use existing external grade
        ExternalGrade externalGrade = existingExternalGrade.get();
        if (externalGrade.getGradeId() != null) {
          grade = gradeRepository.findById(externalGrade.getGradeId()).orElse(null);
        } else {
          grade = null;
        }
      } else {
        grade = null;
        // Create new external grade
        ExternalGrade newExternalGrade = new ExternalGrade();
        newExternalGrade.setUserCompanyId(scaleTicket.getUserCompanyId());
        newExternalGrade.setExternalGradeCode(factorCode);
        newExternalGrade.setExternalGradeDescription(factorDescription);
        externalGradeRepository.save(newExternalGrade);

        log.debug("Created new external grade for factor code: {}", factorCode);
      }

      // Create file field
      FileField fileField;

      if (grade != null) {
        // Case 1: Grade has been matched
        // Get field definition from file_field_definitions table based on grade_id
        FileFieldDefinition fieldDefinition = fileFieldDefinitionRepository.findByGradeId(grade.getGradeId())
            .orElseThrow(() -> new BulkloadsException("Grade " + grade.getGradeName() + " has no file field definition"));

        // Use values from field definition
        fileField = createFileField(fieldDefinition.getFieldName(), fieldDefinition.getFieldLabel(), factorValue, grade.getGradeId());

      } else {
        // Case 2: External grade was created (or no grade match)
        fileField = createFileField(factorCode, factorDescription + " (No Match)", factorValue, null);
      }

      // Add to our list to save later
      fileFieldsToSave.add(fileField);
    }

    return fileFieldsToSave;

  }

  public Integer createFileFromText(Integer assignmentId, List<FileField> fileFields) throws IOException {
    if (assignmentId == null) {
      log.error("Cannot create image from text: assignmentId is null");
      return null;
    }

    // Find the assignment
    Assignment assignment = loadAssignmentRepository.findById(assignmentId)
        .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND,
            "Assignment not found with ID: " + assignmentId));

    // Convert file fields to key-value pairs using AbstractMap.SimpleEntry
    List<Map.Entry<String, String>> keyValuePairs = fileFields.stream()
        .filter(field -> field.getFieldLabel() != null && field.getFieldValue() != null)
        .map(field -> new AbstractMap.SimpleEntry<>(field.getFieldLabel(), field.getFieldValue()))
        .collect(Collectors.toList());

    if (keyValuePairs.isEmpty()) {
      log.warn("No fields found for assignment: {}", assignmentId);
      return null;
    }

    // Create image from key-value pairs
    String title = "Loading Ticket: " + assignment.getLoadingTicketNumber();

    Path outputPath = AppConstants.Paths.TEMP.resolve("Origin_ticket_" + assignmentId + ".png");
    Path generatedFile = ImageUtils.createTextImage(title, keyValuePairs, outputPath);

    // Create a file in the system from the generated image
    LocalFileRequest localFileRequest = LocalFileRequest.builder()
        .localPath(outputPath)
        .caption("Generated ticket image for assignment " + assignmentId)
        .fileTypeId(LOADING_TICKET_FILE_TYPE_ID)
        .ocrProcessed(true)
        .build();

    // Save the file to S3 and create a record in the database
    FileResponse fileResponse = fileService.createFromLocalFile(
        localFileRequest,
        false,
        AmazonS3Service.USER_FILES);

    if (fileResponse == null || fileResponse.getFileId() == null) {
      log.error("Failed to create file from image for assignment {}", assignmentId);
      return null;
    }

    return fileResponse.getFileId();
  }

  private FileField createFileField(String fieldName, String fieldLabel, Object value, Integer gradeId) {
    FileField fileField = new FileField();
    fileField.setFieldName(fieldName);
    fileField.setFieldLabel(fieldLabel);
    fileField.setFieldType("string");
    fileField.setFieldValue(value != null ? value.toString() : "");
    fileField.setConfidence(1.0);
    fileField.setGradeId(gradeId);
    return fileField;
  }

}
