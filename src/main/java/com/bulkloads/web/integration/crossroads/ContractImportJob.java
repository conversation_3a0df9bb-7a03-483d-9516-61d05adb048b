package com.bulkloads.web.integration.crossroads;

import java.io.File;
import java.io.FileInputStream;
import java.time.Instant;
import java.util.List;
import java.util.stream.Stream;
import com.bulkloads.config.AppProperties;
import com.bulkloads.security.ImpersonationService;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
//@Profile("scheduler")
@Component
@RequiredArgsConstructor
public class ContractImportJob {

  private static final String CROSSROADS_IMPORT_DIR = "/crossroads";
  private static final int CROSSROADS_USER_ID = 87079; // Lex Carlson or CROSSROADS CO-OP. Test id: 119334;

  private final AppProperties appProperties;
  private final ContractImportService contractImportService;
  private final ImpersonationService impersonationService;

  @Scheduled(fixedDelayString = "10000")
  public void processFiles() {
    // log.info("Starting file processing job");
    String sourceDir = appProperties.getFtpRoot() + CROSSROADS_IMPORT_DIR + (appProperties.isProdMode() ? "/prod" : "/test");
    File inputDir = new File(sourceDir);
    File[] files = inputDir.listFiles();

    if (files == null) {
      return;
    }

    List<File> filesToProcess = Stream.of(files)
        .filter(file -> !file.isDirectory() && file.toString().endsWith(".xlsx"))
        .toList();

    if (filesToProcess.isEmpty()) {
      return;
    }


    log.info("{} files to process:", filesToProcess.size());

    // only if we have files to process do we impersonate the user
    impersonationService.impersonate(CROSSROADS_USER_ID);

    for (File file : filesToProcess) {
      try {

        log.info("Importing Contracts to Crossroads: Start {}", file.getName());

        // process file
        FileInputStream excelFile = new FileInputStream(file);
        final ContractImportResponse result = contractImportService.importContractsFromExcel(excelFile);

        log.info("Importing Contracts to Crossroads: {}/{} imported", result.getSuccessRecords(), result.getTotalRecords());

        // log results
        // move file to archive dir, if it already exists, it will be overwritten
        final File archiveDir = new File(sourceDir + "/archive");
        if (!archiveDir.exists()) {
          archiveDir.mkdirs();
        }
        final File archivedFile = new File(archiveDir, file.getName());
        if (archivedFile.exists()) {
          archivedFile.delete();
        }
        file.renameTo(archivedFile);

        // write import response to archive dir
        String now = Instant.now().toString().replace(":", "-").substring(0, 19) + "Z";
        final File reportFile = new File(archiveDir, file.getName().replace(".xlsx", "-report-" + now + ".xlsx"));
        log.debug("Writing report to: {}", reportFile.getName());
        result.saveProcessedFile(reportFile.getAbsolutePath());

      } catch (Exception e) {
        log.error("Error processing file: {}", file.getName(), e);
      }
    }

  }
}

