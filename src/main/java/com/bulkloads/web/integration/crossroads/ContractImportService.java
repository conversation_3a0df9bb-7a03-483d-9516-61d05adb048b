package com.bulkloads.web.integration.crossroads;

import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import com.bulkloads.config.AppConstants;
import com.bulkloads.config.AppProperties;
import com.bulkloads.exception.ValidationException;
import com.bulkloads.util.ExcelTable;
import com.bulkloads.web.addressbook.abcompany.service.AbCompanyService;
import com.bulkloads.web.addressbook.abcompany.service.dto.AbCompanyRequest;
import com.bulkloads.web.addressbook.abcompany.service.dto.AbCompanyResponse;
import com.bulkloads.web.contracts.service.ContractService;
import com.bulkloads.web.contracts.service.dto.ContractRequest;
import com.bulkloads.web.contracts.service.dto.ContractResponse;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class ContractImportService {

  public static final String P_S_O = "P/S/O";
  public static final String CONTRACT_NO = "Contract No";
  public static final String STATUS = "Status";
  public static final String ID = "Id";
  public static final String NAME = "Name";
  public static final String DESCRIPTION = "Description";
  public static final String TRADER = "Trader";
  public static final String CT_UOM = "Ct UOM";
  public static final String LOADS = "Loads";
  public static final String QUANTITY = "Quantity";
  //public static final String BAL_LOADS = "Bal  Loads";
  //public static final String BALANCE = "Balance";
  public static final String START_DATE = "Start Date";
  public static final String END_DATE = "End Date";
  public static final String WEIGHTS = "Weights";
  public static final String PRODUCT_ORIGIN = "Product Origin";

  public static final List<String> HEADERS = List.of(
      P_S_O,
      CONTRACT_NO,
      STATUS,
      ID,
      NAME,
      DESCRIPTION,
      TRADER,
      CT_UOM,
      LOADS,
      QUANTITY,
      //BAL_LOADS,
      //BALANCE,
      START_DATE,
      END_DATE,
      WEIGHTS,
      PRODUCT_ORIGIN
  );

  private final ContractService contractService;
  private final AbCompanyService abCompanyService;
  private final AppProperties appProperties;
  // private final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("M/d/yyyy");

  public ContractImportResponse importContractsFromExcel(InputStream excelFile) throws IOException {

    ContractImportResponse result = new ContractImportResponse(HEADERS);

    ExcelTable excelTable = new ExcelTable(excelFile);

    excelTable.filterByHeaders(HEADERS);

    List<Map<String, XSSFCell>> tableData = excelTable.getData();
    for (Map<String, XSSFCell> row : tableData) {

      List<XSSFCell> rowAsList = rowMapToList(row);
      ContractRequest contractRequest = transform(row);

      try {
        ContractResponse response = contractService.create(contractRequest);
        // success
        // https://local.bulkloads.com/tms/contracts/1135/edit
        final String url = appProperties.getDomainUrl() + "/tms/contracts/" + response.getContractId() + "/edit";
        result.addRecord(rowAsList, true, url, "Success", response.getContractId());

      } catch (ValidationException e) {
        // fail
        result.addRecord(rowAsList, false, "", e.getMessage(), null);
      }

    }

    return result;
  }

  private List<XSSFCell> rowMapToList(Map<String, XSSFCell> row) {
    // get the data in a list from the columns in the headers
    List<XSSFCell> filteredRow = new ArrayList<>();
    for (String header : HEADERS) {
      filteredRow.add(row.get(header));
    }
    return filteredRow;
  }

  private ContractRequest transform(Map<String, XSSFCell> excelRow) {

    AbCompanyResponse pickupCompanyResponse = createPickUpAbCompany(excelRow);

    return ContractRequest.builder()
        .buySell(transformBuySell(excelRow.get(P_S_O)))
        .contractNumber(transformToString(excelRow.get(CONTRACT_NO)))
        .pickupAbCompanyId(Optional.of(pickupCompanyResponse.getAbCompanyId()))
        .commodity(transformToString(excelRow.get(DESCRIPTION)))
        .contactInfo(transformToString(excelRow.get(TRADER)))
        .rateType(transformRateType(excelRow.get(CT_UOM)))
        .freightRateType(transformRateType(excelRow.get(CT_UOM)))
        .numberOfLoads(transformToInteger(excelRow.get(LOADS)))
        .quantity(transformToBigDecimal(excelRow.get(QUANTITY)))
        //.remainingNumberOfLoads(transformToInteger(excelRow.get(BAL_LOADS)))
        //.remainingQuantity(transformToBigDecimal(excelRow.get(BALANCE)))
        .shipFrom(transformToDate(excelRow.get(START_DATE)))
        .shipTo(transformToDate(excelRow.get(END_DATE)))
        .notes(Optional.of("Weights: " + (excelRow.get(WEIGHTS)).toString()))
        .build();
  }

  private Optional<String> transformBuySell(XSSFCell cell) {
    String value = cell.getRawValue();
    return Optional.of("Sales".equalsIgnoreCase(value) ? "Sell" : "Buy");
  }

  private Optional<String> transformRateType(XSSFCell cell) {
    String value = cell.toString();
    if (value == null) {
      return Optional.empty();
    }
    String blValue;
    switch (value) {
      case "TON":
        blValue = AppConstants.RateType.TWO_K;
        break;
      case "CWT":
        blValue = AppConstants.RateType.HUNDRED;
        break;
      case "BU":
        blValue = AppConstants.RateType.BUSHEL_60;
        break;
      default:
        blValue = null;
    }

    return Optional.ofNullable(blValue);
  }

  private Optional<String> transformToString(XSSFCell cell) {
    String value;
    if (cell.getCellType() == CellType.STRING) {
      value = cell.getStringCellValue();
    } else {
      value = cell.getRawValue();
    }

    return Optional.ofNullable(value);
  }

  private Optional<LocalDate> transformToDate(XSSFCell cell) {
    String value = cell.getRawValue();
    if (value == null) {
      return Optional.empty();
    }

    long daysSince1900 = Long.parseLong(value);
    if (daysSince1900 < 1) {
      return Optional.empty();
    }

    LocalDate date = null;
    try {
      date = LocalDate.of(1900, 1, 1).plusDays(daysSince1900 - 2);
    } catch (DateTimeParseException e) {
      System.err.println("Invalid date format: " + value);
    }

    return Optional.ofNullable(date);
  }

  private Optional<Integer> transformToInteger(XSSFCell cell) {
    String value = cell.getRawValue();
    return Optional.ofNullable(value == null ? null : Double.valueOf(value).intValue());
  }

  private Optional<BigDecimal> transformToBigDecimal(XSSFCell cell) {
    String value = cell.getRawValue();
    return Optional.ofNullable(value == null ? null : new BigDecimal(value));
  }

  private AbCompanyResponse createPickUpAbCompany(Map<String, XSSFCell> excelRow) {
    AbCompanyRequest abCompanyRequest = AbCompanyRequest.builder()
        .companyName(transformToString(excelRow.get(NAME)))
        .externalAbCompanyId(transformToString(excelRow.get(ID)))
        .location(transformToString(excelRow.get(PRODUCT_ORIGIN)))
        .userTypeIds(Optional.of(String.valueOf(AppConstants.UserTypeId.PICKUP_DROP_FACILITY)))
        .validateLocation(false)
        .build();
    return abCompanyService.createOrGetExisting(abCompanyRequest);
  }

}
