package com.bulkloads.web.integration.general.service;

import static com.bulkloads.common.validation.ValidationUtils.exists;
import static com.bulkloads.common.validation.ValidationUtils.existsAndIsNotEmpty;
import static java.util.Objects.isNull;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import com.bulkloads.common.UserUtil;
import com.bulkloads.exception.ValidationException;
import com.bulkloads.web.addressbook.abcompany.service.dto.AbCompanyResponse;
import com.bulkloads.web.commodity.service.dto.CommodityResponse;
import com.bulkloads.web.common.dto.CreateOrUpdateResult;
import com.bulkloads.web.contracts.domain.entity.Contract;
import com.bulkloads.web.contracts.repository.ContractRepository;
import com.bulkloads.web.contracts.service.ContractService;
import com.bulkloads.web.contracts.service.dto.ContractRequest;
import com.bulkloads.web.contracts.service.dto.ContractResponse;
import com.bulkloads.web.integration.general.mapper.IntegrationMapper;
import com.bulkloads.web.integration.general.repository.IntegrationContractRepository;
import com.bulkloads.web.integration.general.service.dto.IntegrationAbCompanyRequest;
import com.bulkloads.web.integration.general.service.dto.IntegrationContractRequest;
import com.bulkloads.web.integration.general.service.dto.IntegrationContractResponse;
import com.bulkloads.web.integration.general.service.dto.IntegrationContractSearchRequest;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RequiredArgsConstructor
public class IntegrationContractService {

  private final ContractService contractService;
  private final IntegrationAbCompanyService integrationAbCompanyService;
  private final IntegrationCommodityService integrationCommodityService;
  private final IntegrationMapper integrationMapper;
  private final ContractRepository contractRepository;
  private final IntegrationContractRepository integrationContractRepository;

  @Transactional(readOnly = true)
  public List<IntegrationContractResponse> getContracts(IntegrationContractSearchRequest searchRequest) {
    final Integer companyId = UserUtil.getUserCompanyIdOrThrow();

    return integrationContractRepository.findContracts(
            companyId,
            searchRequest.getExternalContractId(),
            searchRequest.getBuySell(),
            searchRequest.getContractStatus(),
            searchRequest.getLastModifiedDate(),
            PageRequest.of(searchRequest.getSkip(), searchRequest.getLimit())
        ).getContent()
        .stream()
        .map(integrationMapper::toIntegrationContractResponse)
        .toList();
  }

  @Transactional
  public CreateOrUpdateResult<ContractResponse> createUpdateContract(IntegrationContractRequest request) {
    Map<String, String> errors = new HashMap<>();

    // Handle Origin Company
    Optional<Integer> pickupAbCompanyId = null;
    if (exists(request.getOrigin())) {
      try {
        final IntegrationAbCompanyRequest integrationAbCompanyRequest = request.getOrigin().get();
        if (isNull(integrationAbCompanyRequest)) {
          pickupAbCompanyId = Optional.empty();
        } else {
          AbCompanyResponse originResponse = integrationAbCompanyService.createUpdateFacility(request.getOrigin().get());
          pickupAbCompanyId = Optional.of(originResponse.getAbCompanyId());
        }
      } catch (ValidationException e) {
        e.getErrors().forEach((key, value) ->
            errors.put("origin." + key, value));
      }
    }

    // Handle Destination Company
    Optional<Integer> dropAbCompanyId = null;
    if (exists(request.getDestination())) {
      try {
        if (isNull(request.getDestination().get())) {
          dropAbCompanyId = Optional.empty();
        } else {
          AbCompanyResponse destResponse = integrationAbCompanyService.createUpdateFacility(request.getDestination().get());
          dropAbCompanyId = Optional.of(destResponse.getAbCompanyId());
        }
      } catch (ValidationException e) {
        e.getErrors().forEach((key, value) ->
            errors.put("destination." + key, value));
      }
    }

    // Handle Commodity
    Optional<Integer> commodityId = null;
    if (exists(request.getCommodity())) {
      try {
        if (isNull(request.getCommodity().get())) {
          commodityId = Optional.empty();
        } else {
          CommodityResponse commodityResponse = integrationCommodityService.createUpdateCommodity(request.getCommodity().get());
          commodityId = Optional.of(commodityResponse.getCommodityId());
        }
      } catch (ValidationException e) {
        e.getErrors().forEach((key, value) ->
            errors.put("commodity." + key, value));
      }
    }

    if (!errors.isEmpty()) {
      throw new ValidationException(mapValidationErrors(errors));
    }

    // Use mapper to create ContractRequest
    ContractRequest contractRequest = integrationMapper.toContractRequest(
        request,
        pickupAbCompanyId,
        dropAbCompanyId,
        commodityId
    );

    try {
      // Create/Update Contract
      Integer existingId = null;
      if (existsAndIsNotEmpty(request.getExternalContractId())) {
        Optional<Contract> existingContractOpt = contractRepository.findByExternalContractIdAndUserCompanyUserCompanyIdAndDeletedFalse(
            request.getExternalContractId().get(),
            UserUtil.getUserCompanyIdOrThrow()
        );
        if (existingContractOpt.isPresent()) {
          existingId = existingContractOpt.get().getContractId();
        }
      }

      if (isNull(existingId)) {
        ContractResponse response = contractService.create(contractRequest);
        return CreateOrUpdateResult.of(response, CreateOrUpdateResult.Operation.CREATED);
      } else {
        ContractResponse response = contractService.update(existingId, contractRequest);
        return CreateOrUpdateResult.of(response, CreateOrUpdateResult.Operation.UPDATED);
      }

    } catch (ValidationException e) {
      throw new ValidationException(mapValidationErrors(e.getErrors()));
    }
  }

  @Transactional
  public List<String> closeContracts(List<String> externalContractIds) {
    int userCompanyId = UserUtil.getUserCompanyIdOrThrow();
    List<Contract> contracts = contractRepository.findAllByExternalContractIdInAndUserCompanyUserCompanyIdAndDeletedFalse(
        externalContractIds,
        userCompanyId
    );

    if (contracts.isEmpty()) {
      throw new ValidationException("external_contract_ids", "No contracts found for the given external IDs in your company");
    }

    contracts.forEach(contract ->
        contractService.setClosed(contract.getContractId()));

    return contracts.stream()
        .map(Contract::getExternalContractId)
        .toList();
  }

  @Transactional
  public List<String> openContracts(List<String> externalContractIds) {
    int userCompanyId = UserUtil.getUserCompanyIdOrThrow();
    List<Contract> contracts = contractRepository.findAllByExternalContractIdInAndUserCompanyUserCompanyIdAndDeletedFalse(
        externalContractIds,
        userCompanyId
    );

    if (contracts.isEmpty()) {
      throw new ValidationException("external_contract_ids", "No contracts found for the given external IDs in your company");
    }

    contracts.forEach(contract ->
        contractService.setOpened(contract.getContractId()));

    return contracts.stream()
        .map(Contract::getExternalContractId)
        .toList();
  }

  private Map<String, String> mapValidationErrors(Map<String, String> errors) {
    Map<String, String> mappedErrors = new HashMap<>();

    errors.forEach((key, value) -> {
      String mappedKey = switch (key) {
        case "shipFrom" -> "shipFromDate";
        case "shipTo" -> "shipToDate";
        default -> key;
      };
      mappedErrors.put(mappedKey, value);
    });

    // for the rateType and freightRateType, we need a better validation message because there is no form
    if (errors.containsKey("rate_type")) {
      mappedErrors.put("rateType", "Allowed values are 100, 1000, 2000, 2204.62, 32, 45, 48, 50, 56, 60, gallon, liter. "
          + "They correspond to: per cwt (100 lbs), per metric ton (kg), per ton, per metric ton (lbs), per bushel (32 lbs), per bushel (45 lbs), per bushel "
          + "(48 lbs), per bushel (50 lbs), per bushel (56 lbs), per bushel (60 lbs), per gallon, per liter.");
    }

    if (errors.containsKey("freight_rate_type")) {
      mappedErrors.put("freightRateType", "Allowed values are 100, 1000, 2000, 2204.62, 32, 45, 48, 50, 56, 60, gallon, liter, flat, hour, mile. "
          + "They correspond to: per cwt (100 lbs), per metric ton (kg), per ton, per metric ton (lbs), per bushel (32 lbs), per bushel (45 lbs), per bushel "
          + "(48 lbs), per bushel (50 lbs), per bushel (56 lbs), per bushel (60 lbs), per gallon, per liter, flat rate, per hour, per mile.");
    }

    return mappedErrors;
  }
}