package com.bulkloads.web.integration.general.service.dto;

import lombok.Builder;
import lombok.Value;

@Value
@Builder
public class IntegrationAbCompanyResponse {
  String externalAbCompanyId;
  String companyName;
  String address;
  String location;
  String city;
  String state;
  String zip;
  String country;
  Double latitude;
  Double longitude;
  Boolean apptRequired;
  String receivingHours;
  String directions;
  String companyNotes;
}