package com.bulkloads.web.integration.general.service.dto;

import java.time.Instant;
import org.springdoc.core.annotations.ParameterObject;
import io.swagger.v3.oas.annotations.Parameter;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.PositiveOrZero;
import lombok.Data;

@Data
@ParameterObject
public class IntegrationContractSearchRequest {

  @Parameter(name = "external_contract_id", description = "External contract ID to search for")
  String externalContractId;

  @Pattern(regexp = "(?i)buy|sell", message = "Buy or Sell")
  @Parameter(name = "buy_sell", description = "Get Buy or Sell contracts ('Buy', 'Sell')")
  String buySell;

  @Pattern(regexp = "(?i)open|closed", message = "Open or Closed")
  @Parameter(name = "contract_status", description = "open|closed")
  String contractStatus;

  @Parameter(name = "last_modified_date",
      description = "Records that were edited after this date. UTC datetime in ISO8601 format e.g. 2022-05-03T18:31:38.480Z")
  Instant lastModifiedDate;

  @Parameter(description = "the number of records to skip, defaults to 0")
  @PositiveOrZero(message = "Must be 0 or positive")
  Integer skip = 0;

  @Parameter(description = "the number of records to return, defaults to 100")
  @PositiveOrZero(message = "Must be positive or zero")
  Integer limit = 100;
}
