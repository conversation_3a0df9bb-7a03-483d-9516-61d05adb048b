package com.bulkloads.web.integration.general.api;

import java.util.List;
import com.bulkloads.web.city.service.CityService;
import com.bulkloads.web.city.service.dto.CityResponse;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;

@RestController
@RequiredArgsConstructor
@Tag(name = "Cities")
@RequestMapping("/rest/integration/cities")
public class IntegrationCityController {

  private final CityService cityService;

  @Operation(summary = "Get the cities")
  @GetMapping
  public List<CityResponse> getCities(
      @Parameter(description = "The search term.")
      @RequestParam(value = "term", required = false) String term,
      @RequestParam(value = "include_states", required = false) Boolean includeStates
  ) {
    return cityService.getCities(
        term,
        includeStates
    );
  }

  @Operation(summary = "Get the city names")
  @GetMapping("/name")
  public List<String> getCityNames(
      @Parameter(description = "The search term.")
      @RequestParam(value = "term") String term
  ) {
    return cityService.getCityNames(term);
  }
}