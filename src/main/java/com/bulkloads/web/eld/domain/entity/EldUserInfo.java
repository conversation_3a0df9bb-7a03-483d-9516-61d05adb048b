package com.bulkloads.web.eld.domain.entity;

import java.time.Instant;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(name = "eld_user_info")
public class EldUserInfo {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Integer id;

  @Column(name = "eld_provider_id")
  private String eldProviderId = "";

  @Column(name = "external_id")
  private String externalId = "";

  @Column(name = "first_name")
  private String firstName = "";

  @Column(name = "last_name")
  private String lastName = "";

  @Column(name = "email")
  private String email = "";

  @Column(name = "username")
  private String username = "";

  @Column(name = "notes")
  private String notes = "";

  @Column(name = "status")
  private String status = "";

  @Column(name = "phone_1")
  private String phone1 = "";

  @Column(name = "timezone")
  private String timezone = "";

  @Column(name = "updated_at")
  private Instant updatedAt;

  @Column(name = "user_id")
  private Integer userId;

  @Column(name = "ab_user_id")
  private Integer abUserId;

  @Column(name = "sync_user_id")
  private Integer syncUserId;

  @Column(name = "user_company_id")
  private Integer userCompanyId;

  @Column(name = "is_active")
  private Boolean active;
}
