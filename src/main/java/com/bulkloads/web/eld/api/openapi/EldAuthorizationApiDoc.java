package com.bulkloads.web.eld.api.openapi;

import java.io.IOException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

@Tag(name = "ELD Authorization API")
public interface EldAuthorizationApiDoc {

  @Operation(summary = "Authorize with ELD provider")
  void authorizeProvider(@Parameter(name = "provider_id", description = "The id of the provider", required = true)
                         final String providerId,
                         final HttpServletRequest request,
                         final HttpServletResponse response) throws IOException;

  @Operation(summary = "Remove ELD provider authorization")
  void deleteAuthorization();
}
