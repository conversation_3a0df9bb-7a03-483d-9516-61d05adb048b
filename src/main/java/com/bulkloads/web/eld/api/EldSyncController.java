package com.bulkloads.web.eld.api;

import static com.bulkloads.config.AppConstants.UserRole.ROLE_USER;

import com.bulkloads.web.eld.api.openapi.EldSyncApiDoc;
import com.bulkloads.web.eld.service.EldSyncService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import lombok.RequiredArgsConstructor;

@RestController
@RequestMapping("/rest/eld")
@RequiredArgsConstructor
@PreAuthorize("hasRole('" + ROLE_USER + "')")
public class EldSyncController implements EldSyncApiDoc {

  private final EldSyncService service;

  @PutMapping("/users/sync")
  public void syncDrivers() {
    service.synchronizeDrivers();
  }

  @PutMapping("/user_company_equipments/sync")
  public void syncVehicles() {
    service.synchronizeUserCompanyEquipments();
  }

}
