package com.bulkloads.web.eld.api.openapi;

import java.util.List;
import com.bulkloads.web.eld.api.dto.EldUserInfoResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;

@Tag(name = "ELD UserInfo API")
public interface EldUserInfoQueryApiDoc {

  @Operation(summary = "Find ELD User Info by Id")
  EldUserInfoResponse findOne(
      @Parameter(name = "eld_user_info_id", description = "The Id of the ELD User Info", required = true)
      final int eldUserInfoId);

  @Operation(summary = "Find all ELD User Info")
  List<EldUserInfoResponse> findAllByProviderIdAndUserCompanyId();
}
