package com.bulkloads.web.eld.api;

import static com.bulkloads.config.AppConstants.UserRole.ROLE_USER;

import java.util.List;
import com.bulkloads.web.eld.api.dto.EldUserCompanyEquipmentResponse;
import com.bulkloads.web.eld.api.openapi.EldUserCompanyEquipmentQueryApiDoc;
import com.bulkloads.web.eld.service.EldUserCompanyEquipmentService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import lombok.RequiredArgsConstructor;

@RestController
@RequestMapping("/rest/eld/user_company_equipments")
@RequiredArgsConstructor
@PreAuthorize("hasRole('" + ROLE_USER + "')")
public class EldUserCompanyEquipmentQueryController implements EldUserCompanyEquipmentQueryApiDoc {

  private final EldUserCompanyEquipmentService service;

  @GetMapping(value = "/{eld_user_company_equipment_id}")
  public EldUserCompanyEquipmentResponse findOne(@PathVariable("eld_user_company_equipment_id") final int eldUserCompanyEquipmentId) {
    return service.findById(eldUserCompanyEquipmentId);
  }

  @GetMapping
  public List<EldUserCompanyEquipmentResponse> findAllByProviderIdAndUserCompanyId() {
    return service.findAllByProviderIdAndUserCompanyId();
  }
}
