package com.bulkloads.web.eld.mapper;

import java.util.List;
import com.bulkloads.web.contracts.mapper.CommonMapper;
import com.bulkloads.web.eld.api.dto.EldUserInfoResponse;
import com.bulkloads.web.eld.domain.entity.EldUserInfo;
import com.bulkloads.web.infra.eld.dto.EldDriverDto;
import com.bulkloads.web.infra.eld.dto.EldDriverHosDto;
import com.bulkloads.web.user.domain.entity.UserHosHistory;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;

@Mapper(componentModel = "spring",
    nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
    nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
    unmappedTargetPolicy = org.mapstruct.ReportingPolicy.IGNORE,
    uses = CommonMapper.class)
public abstract class EldUserInfoMapper {

  public abstract EldUserInfoResponse map(final EldUserInfo entity);

  @Mapping(target = "id", ignore = true)
  @Mapping(target = "active", constant = "true")
  public abstract EldUserInfo map(final EldDriverDto dto);

  public List<EldUserInfoResponse> map(final List<EldUserInfo> entities) {
    return entities.stream().map(this::map).toList();
  }

  @Mapping(target = "id", ignore = true)
  @Mapping(target = "eldProviderId", ignore = true)
  @Mapping(target = "externalId", ignore = true)
  @Mapping(target = "syncUserId", ignore = true)
  @Mapping(target = "userId", ignore = true)
  @Mapping(target = "userCompanyId", ignore = true)
  @Mapping(target = "active", constant = "true")
  public abstract void map(final EldDriverDto dto, @MappingTarget final EldUserInfo entity);

  @Mapping(target = "id", ignore = true)
  @Mapping(target = "createdOn", ignore = true)
  @Mapping(source = "dto.eldProviderId", target = "providerId")
  public abstract UserHosHistory map(final EldDriverHosDto dto, final int userId);
}
