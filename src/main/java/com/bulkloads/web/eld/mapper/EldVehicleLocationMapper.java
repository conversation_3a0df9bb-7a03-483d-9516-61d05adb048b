package com.bulkloads.web.eld.mapper;

import java.util.List;
import com.bulkloads.web.companyequipment.domain.entity.UserCompanyEquipmentGeoHistory;
import com.bulkloads.web.contracts.mapper.CommonMapper;
import com.bulkloads.web.eld.api.dto.UserCompanyEquipmentLocationResponse;
import com.bulkloads.web.infra.eld.dto.EldVehicleLocationDto;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;

@Mapper(componentModel = "spring",
    nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
    nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
    unmappedTargetPolicy = org.mapstruct.ReportingPolicy.IGNORE,
    uses = CommonMapper.class)
public abstract class EldVehicleLocationMapper {

  public abstract List<UserCompanyEquipmentLocationResponse> map(final List<EldVehicleLocationDto> eldDriverHosDtos);

  public abstract UserCompanyEquipmentGeoHistory map(final EldVehicleLocationDto eldVehicleLocationDto);
}
