package com.bulkloads.web.eld.service;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import com.bulkloads.common.UserUtil;
import com.bulkloads.exception.ValidationException;
import com.bulkloads.web.addressbook.abuser.domain.entity.AbUser;
import com.bulkloads.web.addressbook.abuser.repository.AbUserRepository;
import com.bulkloads.web.eld.api.dto.EldUserInfoResponse;
import com.bulkloads.web.eld.domain.entity.EldUserInfo;
import com.bulkloads.web.eld.mapper.EldUserInfoMapper;
import com.bulkloads.web.eld.repository.EldUserInfoRepository;
import com.bulkloads.web.infra.eld.dto.EldDriverDto;
import com.bulkloads.web.infra.eld.dto.EldDriverHosDto;
import com.bulkloads.web.setting.service.SettingService;
import com.bulkloads.web.user.domain.entity.User;
import com.bulkloads.web.user.domain.entity.UserHosHistory;
import com.bulkloads.web.user.repository.UserHosHistoryRepository;
import com.bulkloads.web.user.service.UserService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import lombok.RequiredArgsConstructor;

@Service
@Transactional
@RequiredArgsConstructor
public class EldUserInfoService {

  private final EldUserInfoRepository eldUserInfoRepository;
  private final EldUserInfoMapper eldUserInfoMapper;
  private final UserHosHistoryRepository userHosHistoryRepository;
  private final UserService userService;
  private final AbUserRepository abUserRepository;
  private final SettingService settingService;

  public EldUserInfoResponse findById(final int eldUserInfoId) {
    return eldUserInfoMapper.map(eldUserInfoRepository.getReferenceById(eldUserInfoId));
  }

  public List<EldUserInfoResponse> findAllByProviderIdAndUserCompanyId() {
    final int userCompanyId = UserUtil.getUserCompanyIdOrThrow();
    final String providerId = settingService.getEldProviderId();

    final List<EldUserInfo> entities = eldUserInfoRepository.findByEldProviderIdAndUserCompanyId(providerId, userCompanyId);
    return eldUserInfoMapper.map(entities);
  }

  public List<String> findExternalIdByUserId(final List<Integer> userIds) {
    final int userCompanyId = UserUtil.getUserCompanyIdOrThrow();
    final String providerId = settingService.getEldProviderId();

    final List<EldUserInfo> entities = CollectionUtils.isEmpty(userIds)
        ? eldUserInfoRepository.findByEldProviderIdAndUserCompanyIdAndActiveIsTrue(providerId, userCompanyId)
        : eldUserInfoRepository.findByEldProviderIdAndUserCompanyIdAndActiveIsTrueAndUserIdIn(providerId, userCompanyId, userIds);

    return entities
        .stream()
        .map(EldUserInfo::getExternalId)
        .toList();
  }

  public void linkToUser(final int eldUserInfoId, final int userId) {
    final User user = userService.findById(userId);
    final EldUserInfo eldUser = eldUserInfoRepository.getReferenceById(eldUserInfoId);

    if (!eldUser.getUserCompanyId().equals(user.getUserCompany().getUserCompanyId())) {
      throw new ValidationException("user_company_id", "The user company does not match the eld user company");
    }

    eldUser.setUserId(userId);
  }

  public void linkToAbUser(final int eldUserInfoId, final int abUserId) {
    final AbUser abUser = abUserRepository.getReferenceById(abUserId);
    final EldUserInfo eldUser = eldUserInfoRepository.getReferenceById(eldUserInfoId);

    if (!eldUser.getUserCompanyId().equals(abUser.getUserCompany().getUserCompanyId())) {
      throw new ValidationException("user_company_id", "The user company does not match the eld user company");
    }

    eldUser.setAbUserId(abUserId);
  }

  public void unlink(final int eldUserInfoId) {
    final EldUserInfo entity = eldUserInfoRepository.getReferenceById(eldUserInfoId);
    entity.setUserId(null);
    entity.setAbUserId(null);
  }

  public void syncUsers(final String providerId, final List<EldDriverDto> eldDriverDtos) {
    final int userCompanyId = UserUtil.getUserCompanyIdOrThrow();

    final Map<String, EldUserInfo> eldUserInfoByExternalId = collectEldUserInfoByProviderIdAndCompanyId(providerId, userCompanyId);

    List<EldUserInfo> newToBeSaved = new ArrayList<>();
    final Set<String> dtoExternalIds = new HashSet<>();

    eldDriverDtos.forEach(dto -> {
      final String dtoExternalId = dto.externalId();
      final EldUserInfo entity = eldUserInfoByExternalId.get(dtoExternalId);
      dtoExternalIds.add(dtoExternalId);
      if (entity != null) {
        eldUserInfoMapper.map(dto, entity);
      } else {
        final EldUserInfo newEntity = eldUserInfoMapper.map(dto);
        newToBeSaved.add(newEntity);
      }
    });

    if (!eldUserInfoByExternalId.isEmpty() && !eldUserInfoByExternalId.keySet().containsAll(dtoExternalIds)) {
      eldUserInfoByExternalId.values().stream()
          .filter(e -> !dtoExternalIds.contains(e.getExternalId()))
          .forEach(e -> e.setActive(false));
    }

    eldUserInfoRepository.saveAll(newToBeSaved);
  }

  public void syncDriverHos(final List<EldDriverHosDto> dtos) {
    final Map<String, Integer> userIdByExternalId = collectMatchedUserIdByExternalId();

    final List<UserHosHistory> toBeSaved = dtos
        .stream()
        .map(dto -> {
          final String dtoExternalId = dto.externalId();
          final Integer userId = userIdByExternalId.get(dtoExternalId);
          return eldUserInfoMapper.map(dto, userId);
        }).toList();

    userHosHistoryRepository.saveAll(toBeSaved);
  }

  public List<String> findMatchedExternalIdsByProviderIdAndUserCompanyId() {
    final int userCompanyId = UserUtil.getUserCompanyIdOrThrow();
    final String providerId = settingService.getEldProviderId();

    return eldUserInfoRepository.findByEldProviderIdAndUserCompanyIdAndUserIdNotNull(providerId, userCompanyId)
        .stream()
        .map(EldUserInfo::getExternalId)
        .toList();
  }

  private Map<String, Integer> collectMatchedUserIdByExternalId() {
    return eldUserInfoRepository
        .findAllByUserIdNotNull()
        .stream()
        .collect(Collectors.toMap(EldUserInfo::getExternalId, EldUserInfo::getUserId));
  }

  private Map<String, EldUserInfo> collectEldUserInfoByProviderIdAndCompanyId(final String providerId, final int userCompanyId) {
    return eldUserInfoRepository
        .findByEldProviderIdAndUserCompanyId(providerId, userCompanyId)
        .stream()
        .collect(Collectors.toMap(EldUserInfo::getExternalId, e -> e));
  }
}
