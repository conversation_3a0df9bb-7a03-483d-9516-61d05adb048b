package com.bulkloads.web.city.domain.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "states")
@Getter
@Setter
public class State {

  public static final String DEFAULT_COLOR_CODE = "FFFFFF";

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "state_id")
  private Integer stateId;

  @Column(name = "state")
  private String state = "";

  @Column(name = "abbreviation")
  private String abbreviation = "";

  @Column(name = "country")
  private String country = "";

  @Column(name = "region")
  private Integer region;

  @Column(name = "Latitude")
  private Double latitude;

  @Column(name = "Longitude")
  private Double longitude;

  @Column(name = "color_code")
  private String colorCode = DEFAULT_COLOR_CODE;
}