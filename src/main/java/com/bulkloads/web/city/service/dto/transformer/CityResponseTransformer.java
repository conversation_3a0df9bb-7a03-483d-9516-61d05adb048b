package com.bulkloads.web.city.service.dto.transformer;

import com.bulkloads.common.jpa.nativejpa.QueryParts;
import com.bulkloads.web.city.service.dto.CityResponse;
import org.hibernate.query.TupleTransformer;
import org.springframework.stereotype.Component;
import lombok.RequiredArgsConstructor;

@Component
@RequiredArgsConstructor
public class CityResponseTransformer implements TupleTransformer<CityResponse> {

  @Override
  public CityResponse transformTuple(Object[] columns, String[] aliases) {
    QueryParts parts = new QueryParts(columns, aliases);

    CityResponse response = new CityResponse();
    response.setName(parts.asString("name"));
    response.setZip(parts.asString("zip"));
    response.setCity(parts.asString("city"));
    response.setCountry(parts.asString("country"));
    response.setFreq(parts.asInteger("freq"));
    response.setState(parts.asString("state"));
    response.setLatitude(parts.asDouble("latitude"));
    response.setLongitude(parts.asDouble("longitude"));
    return response;

  }
}
