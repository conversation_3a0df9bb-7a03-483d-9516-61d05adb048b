package com.bulkloads.web.city.service;

import java.util.List;
import com.bulkloads.web.city.repository.CityRepository;
import com.bulkloads.web.city.service.dto.CityResponse;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RequiredArgsConstructor
public class CityService {

  private final CityRepository cityRepository;

  public List<CityResponse> getCities(
      final String term,
      final Boolean includeStates
  ) {
    return cityRepository.getCities(term, includeStates);
  }

  public List<String> getCityNames(
      final String term
  ) {
    return cityRepository.getCityNames(term)
        .stream()
        .map(CityResponse::getName)
        .toList();
  }

  public List<CityResponse> getCityNearMe(
      final double latitude,
      final double longitude
  ) {
    return cityRepository.getCityNearMe(longitude, latitude);
  }

}
