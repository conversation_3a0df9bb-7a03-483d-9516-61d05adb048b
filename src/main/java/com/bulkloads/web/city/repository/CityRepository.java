package com.bulkloads.web.city.repository;

import com.bulkloads.web.city.domain.entity.City;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.ListCrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface CityRepository extends ListCrudRepository<City, Integer>, CityQueryRepository {

  @Query(value = """
          SELECT c.*, s.state as stateFull
          FROM cities c
              inner join states s on c.state = s.abbreviation
          WHERE (c.name = :term OR c.zip = :term)
          LIMIT 1
      """, nativeQuery = true)
  City getCityZip(@Param("term") final String term);

  @Query("SELECT c FROM City c WHERE c.state = :originState AND c.city = :originCity AND c.zip = ''")
  City getCityByStateAndCity(
      @Param("originState") final String originState,
      @Param("originCity") final String originCity
  );
}
