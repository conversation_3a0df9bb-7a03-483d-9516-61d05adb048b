package com.bulkloads.web.companyequipment.mapper;

import static com.bulkloads.common.Converters.asList;

import java.util.List;
import java.util.Optional;
import com.bulkloads.common.UserUtil;
import com.bulkloads.web.addressbook.abuser.domain.entity.AbUser;
import com.bulkloads.web.addressbook.abuser.repository.AbUserRepository;
import com.bulkloads.web.companyequipment.domain.data.UserCompanyEquipmentData;
import com.bulkloads.web.companyequipment.domain.entity.UserCompanyEquipment;
import com.bulkloads.web.companyequipment.domain.entity.UserCompanyEquipmentFile;
import com.bulkloads.web.companyequipment.domain.entity.UserCompanyEquipmentGeoHistory;
import com.bulkloads.web.companyequipment.domain.entity.UserCompanyEquipmentLog;
import com.bulkloads.web.companyequipment.service.dto.UserCompanyEquipmentLogResponse;
import com.bulkloads.web.companyequipment.service.dto.UserCompanyEquipmentRequest;
import com.bulkloads.web.companyequipment.service.dto.UserCompanyEquipmentResponse;
import com.bulkloads.web.contracts.mapper.CommonMapper;
import com.bulkloads.web.eld.api.dto.UserCompanyEquipmentLocationResponse;
import com.bulkloads.web.file.service.dto.FileResponse;
import com.bulkloads.web.user.domain.entity.User;
import com.bulkloads.web.user.repository.UserRepository;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.springframework.beans.factory.annotation.Autowired;

@Mapper(componentModel = "spring",
    nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
    nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
    unmappedTargetPolicy = org.mapstruct.ReportingPolicy.IGNORE,
    uses = CommonMapper.class)
public abstract class UserCompanyEquipmentMapper {

  @Autowired
  UserRepository userRepository;

  @Autowired
  AbUserRepository abUserRepository;

  @Autowired
  UserCompanyEquipmentLogMapper userCompanyEquipmentLogMapper;

  @Mapping(source = "defaultAssignedUserId", target = "defaultAssignedUser")
  @Mapping(source = "defaultAssignedAbUserId", target = "defaultAssignedAbUser")
  public abstract UserCompanyEquipmentData requestToData(UserCompanyEquipmentRequest request);

  @Mapping(source = "userCompanyEquipmentFiles", target = "files")
  @Mapping(source = "defaultAssignedUser", target = "defaultAssignedUserId")
  @Mapping(source = "defaultAssignedAbUser", target = "defaultAssignedAbUserId")
  @Mapping(source = "userCompanyEquipmentLogs", target = "userCompanyEquipmentLogs")
  public abstract UserCompanyEquipmentResponse entityToResponse(final UserCompanyEquipment e);

  public abstract void dataToEntity(UserCompanyEquipmentData data, @MappingTarget UserCompanyEquipment e);

  @Mapping(source = "defaultAssignedUser", target = "defaultAssignedUser")
  @Mapping(source = "defaultAssignedAbUser", target = "defaultAssignedAbUser")
  public abstract UserCompanyEquipment saveExisting(UserCompanyEquipment e);

  public abstract UserCompanyEquipmentLocationResponse map(final UserCompanyEquipmentGeoHistory userCompanyEquipmentGeoHistory);

  Integer userToUserId(User user) {
    if (user == null) {
      return null;
    }
    return user.getUserId();
  }

  Integer userToAbUserId(AbUser abUser) {
    if (abUser == null) {
      return null;
    }
    return abUser.getAbUserId();
  }

  User userToUserShallow(User user) {
    if (user == null) {
      return null;
    }
    User shallowUser = new User();
    shallowUser.setUserId(user.getUserId());

    return shallowUser;
  }

  Optional<User> userIdOptToUserOpt(Optional<Integer> userId) {
    if (userId == null) {
      return null;
    } else if (userId.isEmpty()) {
      return Optional.empty();
    }

    return userRepository.findByUserIdAndUserCompanyUserCompanyId(userId.get(), UserUtil.getUserCompanyIdOrThrow());
  }

  Optional<AbUser> abUserIdOptToAbUserOpt(Optional<Integer> abUserId) {
    if (abUserId == null) {
      return null;
    } else if (abUserId.isEmpty()) {
      return Optional.empty();
    }

    return abUserRepository.findAllByAbUserIdAndUserCompanyUserCompanyId(abUserId.get(), UserUtil.getUserCompanyIdOrThrow());
  }

  public List<FileResponse> userCompanyEquipmentFileListToFileResponseList(final List<UserCompanyEquipmentFile> files) {
    if (files == null) {
      return null;
    }
    return files
        .stream()
        // .filter(e -> !e.getDeleted())
        .map(f -> FileResponse.fromFile(f.getFile()))
        .collect(asList());
  }

  public List<UserCompanyEquipmentLogResponse> userCompanyEquipmentLogListToUserCompanyEquipmentLogResponseList(List<UserCompanyEquipmentLog> logs) {
    return logs
        .stream()
        // .filter(e -> !e.getDeleted())
        .map(e -> userCompanyEquipmentLogMapper.entityToResponse(e))
        .collect(asList());
  }

}
