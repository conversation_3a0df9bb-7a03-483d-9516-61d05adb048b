package com.bulkloads.web.companyequipment.api;


import static com.bulkloads.config.AppConstants.UserPermission.MANAGE_EQUIPMENTS;
import com.bulkloads.common.api.ApiResponse;
import com.bulkloads.web.companyequipment.service.UserCompanyEquipmentLogService;
import com.bulkloads.web.companyequipment.service.dto.UserCompanyEquipmentLogRequest;
import com.bulkloads.web.companyequipment.service.dto.UserCompanyEquipmentLogResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Positive;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequestMapping("/rest/companies/equipments")
@Tag(name = "Company Equipments")
@CrossOrigin(origins = "*", maxAge = 3600)
@RequiredArgsConstructor
@Validated
@PreAuthorize("hasAuthority('" + MANAGE_EQUIPMENTS + "')")
public class UserCompanyEquipmentLogController {

  private final UserCompanyEquipmentLogService userCompanyEquipmentLogService;

  @Operation(summary = "Create equipment maintenance log")
  @PostMapping("{user_company_equipment_id}/logs")
  public ApiResponse<UserCompanyEquipmentLogResponse, Integer> create(
      @Positive(message = "user_company_equipment_id id should be positive")
      @PathVariable("user_company_equipment_id") final int userCompanyEquipmentId,
      @Valid @RequestBody UserCompanyEquipmentLogRequest request) {

    UserCompanyEquipmentLogResponse response = userCompanyEquipmentLogService.create(
        userCompanyEquipmentId, request);

    return ApiResponse.<UserCompanyEquipmentLogResponse, Integer>builder()
        .key(response.getUserCompanyEquipmentLogId())
        .data(response)
        .message("The equipment maintenance log has been added")
        .build();
  }


  @Operation(summary = "Update company equipment maintenance log")
  @PutMapping("{user_company_equipment_id}/logs/{user_company_equipment_log_id}")
  public ApiResponse<UserCompanyEquipmentLogResponse, Integer> update(
      @Positive(message = "user_company_equipment_id id should be positive")
      @PathVariable("user_company_equipment_id") final int userCompanyEquipmentId,
      @Positive(message = "user_company_equipment_log_id id should be positive")
      @PathVariable("user_company_equipment_log_id") final int userCompanyEquipmentLogId,
      @Valid @RequestBody UserCompanyEquipmentLogRequest userCompanyEquipmentLogRequest) {

    final UserCompanyEquipmentLogResponse response = userCompanyEquipmentLogService.update(
        userCompanyEquipmentId,
        userCompanyEquipmentLogId,
        userCompanyEquipmentLogRequest);

    return ApiResponse.<UserCompanyEquipmentLogResponse, Integer>builder()
        .key(response.getUserCompanyEquipmentLogId())
        .data(response)
        .message("The equipment maintenance log has been updated")
        .build();
  }

  @Operation(summary = "Delete company equipment maintenance log")
  @DeleteMapping("{user_company_equipment_id}/logs/{user_company_equipment_log_id}")
  public ApiResponse<UserCompanyEquipmentLogResponse, Integer> remove(
      @Positive(message = "user_company_equipment_id id should be positive")
      @PathVariable("user_company_equipment_id") final int userCompanyEquipmentId,
      @Positive(message = "user_company_equipment_log_id id should be positive")
      @PathVariable("user_company_equipment_log_id") final int userCompanyEquipmentLogId) {

    userCompanyEquipmentLogService.remove(userCompanyEquipmentId, userCompanyEquipmentLogId);

    return ApiResponse.<UserCompanyEquipmentLogResponse, Integer>builder()
        .message("Equipment maintenance log deleted")
        .build();
  }


}
