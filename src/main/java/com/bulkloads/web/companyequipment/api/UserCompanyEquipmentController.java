package com.bulkloads.web.companyequipment.api;


import static com.bulkloads.config.AppConstants.UserPermission.MANAGE_EQUIPMENTS;
import com.bulkloads.common.api.ApiResponse;
import com.bulkloads.web.companyequipment.service.UserCompanyEquipmentService;
import com.bulkloads.web.companyequipment.service.dto.UserCompanyEquipmentRequest;
import com.bulkloads.web.companyequipment.service.dto.UserCompanyEquipmentResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Positive;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequestMapping("/rest/companies/equipments")
@Tag(name = "Company Equipments")
@CrossOrigin(origins = "*", maxAge = 3600)
@RequiredArgsConstructor
@Validated
@PreAuthorize("hasAuthority('" + MANAGE_EQUIPMENTS + "')")
public class UserCompanyEquipmentController {

  private final UserCompanyEquipmentService userCompanyEquipmentService;

  @Operation(summary = "Create equipment for company")
  @PostMapping
  public ApiResponse<UserCompanyEquipmentResponse, Integer> create(
      @Valid @RequestBody final UserCompanyEquipmentRequest request) {

    final UserCompanyEquipmentResponse response = userCompanyEquipmentService.create(request);

    return ApiResponse.<UserCompanyEquipmentResponse, Integer>builder()
        .key(response.getUserCompanyEquipmentId())
        .data(response)
        .message("The equipment has been added to your company")
        .build();
  }

  @Operation(summary = "Update company equipment")
  @PutMapping("{user_company_equipment_id}")
  public ApiResponse<UserCompanyEquipmentResponse, Integer> update(
      @Positive(message = "user_company_equipment_id id should be positive")
      @PathVariable("user_company_equipment_id") final int userCompanyEquipmentId,
      @Valid @RequestBody final UserCompanyEquipmentRequest userCompanyEquipmentRequest) {

    final UserCompanyEquipmentResponse response = userCompanyEquipmentService.update(userCompanyEquipmentId, userCompanyEquipmentRequest);

    return ApiResponse.<UserCompanyEquipmentResponse, Integer>builder()
        .key(response.getUserCompanyEquipmentId())
        .data(response)
        .message("The equipment has been updated")
        .build();
  }

  @Operation(summary = "Delete company equipment")
  @DeleteMapping("{user_company_equipment_id}")
  public ApiResponse<UserCompanyEquipmentResponse, Integer> remove(
      @Positive(message = "user_company_equipment_id id should be positive")
      @PathVariable("user_company_equipment_id") final int userCompanyEquipmentId) {

    userCompanyEquipmentService.remove(userCompanyEquipmentId);

    return ApiResponse.<UserCompanyEquipmentResponse, Integer>builder()
        .message("Equipment deleted")
        .build();
  }
}
