package com.bulkloads.web.companyequipment.repository;

import java.util.Optional;
import com.bulkloads.web.companyequipment.domain.entity.UserCompanyEquipmentLog;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface UserCompanyEquipmentLogRepository extends
    JpaRepository<UserCompanyEquipmentLog, Integer> {

  @Query("""
      select ucel from UserCompanyEquipmentLog ucel
        join ucel.userCompanyEquipment
        join ucel.userCompanyEquipmentLogFiles
      where
         ucel.userCompanyEquipment.userCompany.userCompanyId = :userCompanyId
         and ucel.userCompanyEquipmentLogId = :userCompanyEquipmentLogId
      """)
  Optional<UserCompanyEquipmentLog> getUserCompanyEquipmentLog(
      Integer userCompanyId,
      Integer userCompanyEquipmentLogId);

}
