package com.bulkloads.web.companyequipment.domain.data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;
import com.bulkloads.web.file.domain.entity.File;
import lombok.Data;

@Data
public class UserCompanyEquipmentLogData {

  private String logType;
  private LocalDate logDate;
  private Integer mileage;
  private String notes;
  private BigDecimal expense;

  private Optional<List<File>> files;
}
