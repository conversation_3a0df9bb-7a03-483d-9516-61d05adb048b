package com.bulkloads.web.companyequipment.domain;

import static com.bulkloads.common.validation.ValidationUtils.existsAndIsNotEmpty;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import com.bulkloads.common.BaseDomainService;
import com.bulkloads.common.UserUtil;
import com.bulkloads.common.validation.Result;
import com.bulkloads.common.validation.ValidationMethod;
import com.bulkloads.exception.BulkloadsException;
import com.bulkloads.web.companyequipment.domain.data.UserCompanyEquipmentLogData;
import com.bulkloads.web.companyequipment.domain.entity.UserCompanyEquipment;
import com.bulkloads.web.companyequipment.domain.entity.UserCompanyEquipmentLog;
import com.bulkloads.web.companyequipment.domain.entity.UserCompanyEquipmentLogFile;
import com.bulkloads.web.companyequipment.domain.entity.UserCompanyEquipmentLogFileId;
import com.bulkloads.web.companyequipment.mapper.UserCompanyEquipmentLogMapper;
import com.bulkloads.web.file.domain.entity.File;
import com.bulkloads.web.file.service.FileService;
import com.bulkloads.web.user.domain.entity.User;
import com.bulkloads.web.user.service.UserService;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
@RequiredArgsConstructor
public class UserCompanyEquipmentLogDomainService extends BaseDomainService<UserCompanyEquipmentLog> {

  private final UserService userService;
  private final UserCompanyEquipmentLogMapper userCompanyEquipmentLogMapper;
  private final FileService fileService;

  public Result<UserCompanyEquipmentLog> create(
      UserCompanyEquipment owningUserCompanyEquipment,
      UserCompanyEquipmentLogData data) {

    Objects.requireNonNull(owningUserCompanyEquipment);
    Objects.requireNonNull(owningUserCompanyEquipment.getUserCompanyEquipmentId());

    UserCompanyEquipmentLog userCompanyEquipmentLog = new UserCompanyEquipmentLog();
    userCompanyEquipmentLog.setUserCompanyEquipment(owningUserCompanyEquipment);

    return validate(userCompanyEquipmentLog, null, data, ValidationMethod.CREATE);
  }

  public Result<UserCompanyEquipmentLog> update(
      UserCompanyEquipment owningUserCompanyEquipment,
      UserCompanyEquipmentLog userCompanyEquipmentLog,
      UserCompanyEquipmentLogData dto) {

    Objects.requireNonNull(owningUserCompanyEquipment);
    Objects.requireNonNull(owningUserCompanyEquipment.getUserCompanyEquipmentId());
    Objects.requireNonNull(userCompanyEquipmentLog);
    Objects.requireNonNull(userCompanyEquipmentLog.getUserCompanyEquipmentLogId());

    UserCompanyEquipmentLog existing = userCompanyEquipmentLogMapper.saveExisting(userCompanyEquipmentLog);

    if (!owningUserCompanyEquipment.getUserCompanyEquipmentId()
        .equals(userCompanyEquipmentLog.getUserCompanyEquipment().getUserCompanyEquipmentId())) {
      throw new BulkloadsException("UserCompanyEquipmentLog does not belong to UserCompanyEquipment");
    }

    return validate(userCompanyEquipmentLog, existing, dto, ValidationMethod.UPDATE);
  }

  public Result<UserCompanyEquipmentLog> remove(
      UserCompanyEquipment userCompanyEquipment,
      UserCompanyEquipmentLog userCompanyEquipmentLog) {

    if (!userCompanyEquipment.getUserCompanyEquipmentId()
        .equals(userCompanyEquipmentLog.getUserCompanyEquipment().getUserCompanyEquipmentId())) {
      throw new BulkloadsException("UserCompanyEquipmentLog does not belong to UserCompanyEquipment");
    }

    userCompanyEquipment.getUserCompanyEquipmentLogs().remove(userCompanyEquipmentLog);
    userCompanyEquipmentLog.markDeletedBy(userService.getLoggedInUser().getUserId());
    return new Result<>(userCompanyEquipmentLog);
  }

  @Override
  public void validateDataAndMapToEntity(
      Result<UserCompanyEquipmentLog> result,
      UserCompanyEquipmentLog entity,
      UserCompanyEquipmentLog existing,
      Object data,
      ValidationMethod method) {

    UserCompanyEquipmentLogData dto = (UserCompanyEquipmentLogData) data;

    final User user = userService.getLoggedInUser();
    if (method == ValidationMethod.CREATE) {
      entity.markCreatedBy(user.getUserId());
    } else {
      entity.markModifiedBy(user.getUserId());
    }

    validateFiles(result, entity, dto);
  }

  void validateFiles(Result<UserCompanyEquipmentLog> result,
                     UserCompanyEquipmentLog entity,
                     UserCompanyEquipmentLogData data) {

    if (existsAndIsNotEmpty(data.getFiles())) {

      entity.getUserCompanyEquipmentLogFiles().clear();
      List<File> files = data.getFiles().get();

      if (!files.isEmpty()) {

        List<UserCompanyEquipmentLogFile> newFiles = new ArrayList<>();
        for (int i = 0; i < files.size(); i++) {
          newFiles.add(buildEquipmentLogFile(entity, files.get(i), i + 1));
        }

        if (!fileService.isFilesOwnershipValid(files, newFiles.size())) {
          result.addError("files", "One or more of the files passed is not allowed to by added by your account");
          return;
        }

        entity.getUserCompanyEquipmentLogFiles().addAll(newFiles);
      }
    }
  }

  private static @NotNull UserCompanyEquipmentLogFile buildEquipmentLogFile(UserCompanyEquipmentLog entity, File file, int order) {
    UserCompanyEquipmentLogFile newUserCompanyEquipmentLogFile = new UserCompanyEquipmentLogFile();
    newUserCompanyEquipmentLogFile.markCreatedBy(UserUtil.getUserIdOrThrow());
    newUserCompanyEquipmentLogFile.setUserCompanyEquipmentLog(entity);
    newUserCompanyEquipmentLogFile.setFile(file);
    newUserCompanyEquipmentLogFile.setFileOrder(order);
    UserCompanyEquipmentLogFileId fileId = new UserCompanyEquipmentLogFileId(entity.getUserCompanyEquipmentLogId(), file.getFileId());

    newUserCompanyEquipmentLogFile.setUserCompanyEquipmentLogFileId(fileId);
    return newUserCompanyEquipmentLogFile;
  }

  @Override
  public void mapToEntityAuto(Object data, UserCompanyEquipmentLog e) {
    userCompanyEquipmentLogMapper.dataToEntity((UserCompanyEquipmentLogData) data, e);
  }

  @Override
  public void validateEntity(Result<UserCompanyEquipmentLog> result, UserCompanyEquipmentLog entity) {
  }

}
