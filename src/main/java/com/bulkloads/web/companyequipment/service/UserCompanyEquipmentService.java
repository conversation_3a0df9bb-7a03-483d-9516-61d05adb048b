package com.bulkloads.web.companyequipment.service;

import static com.bulkloads.common.Converters.asList;

import java.util.List;
import java.util.Optional;
import com.bulkloads.common.UserUtil;
import com.bulkloads.common.validation.Result;
import com.bulkloads.exception.ValidationException;
import com.bulkloads.web.companyequipment.domain.UserCompanyEquipmentDomainService;
import com.bulkloads.web.companyequipment.domain.data.UserCompanyEquipmentData;
import com.bulkloads.web.companyequipment.domain.entity.EquipmentStatus;
import com.bulkloads.web.companyequipment.domain.entity.EquipmentType;
import com.bulkloads.web.companyequipment.domain.entity.UserCompanyEquipment;
import com.bulkloads.web.companyequipment.domain.entity.UserCompanyEquipmentGeoHistory;
import com.bulkloads.web.companyequipment.mapper.UserCompanyEquipmentMapper;
import com.bulkloads.web.companyequipment.repository.UserCompanyEquipmentGeoHistoryRepository;
import com.bulkloads.web.companyequipment.repository.UserCompanyEquipmentRepository;
import com.bulkloads.web.companyequipment.service.dto.UserCompanyEquipmentListResponse;
import com.bulkloads.web.companyequipment.service.dto.UserCompanyEquipmentRequest;
import com.bulkloads.web.companyequipment.service.dto.UserCompanyEquipmentResponse;
import com.bulkloads.web.eld.api.dto.UserCompanyEquipmentLocationResponse;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class UserCompanyEquipmentService {

  private final UserCompanyEquipmentRepository userCompanyEquipmentRepository;
  private final UserCompanyEquipmentGeoHistoryRepository userCompanyEquipmentGeoHistoryRepository;
  private final UserCompanyEquipmentMapper userCompanyEquipmentMapper;
  private final UserCompanyEquipmentDomainService userCompanyEquipmentDomainService;

  @Transactional
  public UserCompanyEquipmentResponse create(UserCompanyEquipmentRequest request) {
    UserCompanyEquipmentData data = userCompanyEquipmentMapper.requestToData(request);
    Result<UserCompanyEquipment> result = userCompanyEquipmentDomainService.create(data);
    UserCompanyEquipment userCompanyEquipmentToSave = result.orElseThrow();
    UserCompanyEquipment userCompanyEquipment = userCompanyEquipmentRepository.save(userCompanyEquipmentToSave);
    return userCompanyEquipmentMapper.entityToResponse(userCompanyEquipment);
  }

  @Transactional
  public UserCompanyEquipmentResponse update(int userCompanyEquipmentId, UserCompanyEquipmentRequest request) {
    UserCompanyEquipment userCompanyEquipmentToUpdate = findUserCompanyEquipmentById(userCompanyEquipmentId);
    UserCompanyEquipmentData data = userCompanyEquipmentMapper.requestToData(request);
    Result<UserCompanyEquipment> result = userCompanyEquipmentDomainService.update(userCompanyEquipmentToUpdate, data);
    UserCompanyEquipment userCompanyEquipmentToSave = result.orElseThrow();
    UserCompanyEquipment userCompanyEquipment = userCompanyEquipmentRepository.save(userCompanyEquipmentToSave);
    return userCompanyEquipmentMapper.entityToResponse(userCompanyEquipment);
  }

  @Transactional
  public void remove(int userCompanyEquipmentId) {
    UserCompanyEquipment userCompanyEquipment = findUserCompanyEquipmentById(userCompanyEquipmentId);
    final Result<UserCompanyEquipment> result = userCompanyEquipmentDomainService.remove(userCompanyEquipment);
    userCompanyEquipmentRepository.save(result.orElseThrow());
  }

  public UserCompanyEquipmentResponse getUserCompanyEquipmentById(int userCompanyEquipmentId) {
    UserCompanyEquipment userCompanyEquipmentById = userCompanyEquipmentRepository.getUserCompanyEquipmentById(
        UserUtil.getUserCompanyIdOrThrow(),
        userCompanyEquipmentId);

    return userCompanyEquipmentMapper.entityToResponse(userCompanyEquipmentById);
  }

  public List<UserCompanyEquipmentLocationResponse> getUserCompanyEquipmentCurrentLocations(final List<Integer> equipmentIds) {

    final List<UserCompanyEquipmentGeoHistory> latestLocations = userCompanyEquipmentGeoHistoryRepository
        .findLatestLocationsByUserCompanyEquipmentIds(equipmentIds);

    return latestLocations.stream()
        .map(userCompanyEquipmentMapper::map)
        .toList();
  }

  public List<UserCompanyEquipmentListResponse> getUserCompanyEquipments(
      EquipmentType equipmentType,
      String externalEquipmentId,
      String insuranceCompany,
      String licensePlateNumber,
      String make,
      String model,
      EquipmentStatus status,
      String trailerType,
      String vin,
      String year) {

    List<UserCompanyEquipment> userCompanyEquipmentList = userCompanyEquipmentRepository.getUserCompanyEquipments(
        UserUtil.getUserCompanyIdOrThrow(), equipmentType, externalEquipmentId, insuranceCompany, licensePlateNumber,
        make, model, status, trailerType, vin, year);

    return userCompanyEquipmentList
        .stream()
        .map(userCompanyEquipmentMapper::entityToResponse)
        .collect(asList());
  }

  UserCompanyEquipment findUserCompanyEquipmentById(final int userCompanyEquipmentId) {

    return Optional.ofNullable(
            userCompanyEquipmentRepository.getUserCompanyEquipmentById(
                UserUtil.getUserCompanyIdOrThrow(),
                userCompanyEquipmentId))
        .orElseThrow(
            () -> new ValidationException("user_company_equipment_id", "You are not allowed to update this equipment"));
  }
}
