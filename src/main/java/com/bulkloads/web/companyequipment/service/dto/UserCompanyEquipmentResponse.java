package com.bulkloads.web.companyequipment.service.dto;

import java.util.List;
import com.bulkloads.web.file.service.dto.FileResponse;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class UserCompanyEquipmentResponse extends UserCompanyEquipmentListResponse {

  List<UserCompanyEquipmentLogResponse> userCompanyEquipmentLogs;
  List<FileResponse> files;
}
