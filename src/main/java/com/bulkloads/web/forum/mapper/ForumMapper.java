package com.bulkloads.web.forum.mapper;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import com.bulkloads.web.contracts.mapper.CommonMapper;
import com.bulkloads.web.forum.domain.data.ForumData;
import com.bulkloads.web.forum.domain.data.ForumFileData;
import com.bulkloads.web.forum.domain.data.ReplyData;
import com.bulkloads.web.forum.domain.entity.ForumPost;
import com.bulkloads.web.forum.domain.entity.ForumPostType;
import com.bulkloads.web.forum.domain.entity.ForumReply;
import com.bulkloads.web.forum.domain.entity.ForumUpdateRequest;
import com.bulkloads.web.forum.repository.ForumPostTypeRepository;
import com.bulkloads.web.forum.repository.ForumReplyRepository;
import com.bulkloads.web.forum.service.dto.ForumFileRequest;
import com.bulkloads.web.forum.service.dto.ForumRequest;
import com.bulkloads.web.forum.service.dto.ReplyRequest;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.springframework.beans.factory.annotation.Autowired;

@Mapper(componentModel = "spring",
    nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
    nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
    unmappedTargetPolicy = org.mapstruct.ReportingPolicy.IGNORE,
    uses = CommonMapper.class)
public abstract class ForumMapper {

  @Autowired
  private ForumPostTypeRepository forumPostTypeRepository;

  @Autowired
  private ForumReplyRepository forumReplyRepository;

  @Mapping(source = "postTypeId", target = "postType")
  public abstract ForumData requestToData(final ForumRequest request);

  @Mapping(source = "postTypeId", target = "postType")
  public abstract ForumData requestToData(final ForumUpdateRequest request);


  @Mapping(source = "parentReplyId", target = "parentReply")
  public abstract ReplyData replyToData(final ReplyRequest request);


  @Mapping(source = "files", target = "files", ignore = true)
  public abstract void dataToEntity(final ForumData data, @MappingTarget final ForumPost forumPost);

  @Mapping(source = "files", target = "files", ignore = true)
  public abstract void replyDataToEntity(final ReplyData data, @MappingTarget final ForumReply forumReply);



  public  Optional<List<ForumFileData>> mapFileList(Optional<List<ForumFileRequest>> fileMap) {

    return fileMap.map(fileList -> fileList.stream()
            .map(this::mapFile)
            .collect(Collectors.toCollection(ArrayList::new)));
  }

  public abstract ForumFileData mapFile(ForumFileRequest fileMap);


  public ForumPostType mapPostTypeIdToForumPostType(Integer postTypeId) {
    if (postTypeId == null) {
      return null;
    }
    return forumPostTypeRepository.findById(postTypeId).orElse(null);
  }

//  Maps a parent reply ID to a ForumReply entity.
//  parentReplyId an Optional containing the parent reply ID
//  an Optional containing the ForumReply entity, or empty if not found
  protected Optional<ForumReply> mapParentReplyById(Optional<Integer> parentReplyId) {
    return parentReplyId.flatMap(forumReplyRepository::findById);
  }
}
