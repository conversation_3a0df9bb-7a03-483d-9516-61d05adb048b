package com.bulkloads.web.forum.service.utility;

import static com.bulkloads.common.StringUtil.htmlToText;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import com.bulkloads.common.UserUtil;
import com.bulkloads.web.forum.domain.entity.ForumPost;
import com.bulkloads.web.forum.domain.entity.ForumReply;
import com.bulkloads.web.forum.domain.entity.SiteUpdate;
import com.bulkloads.web.forum.repository.SiteUpdateRepository;
import com.bulkloads.web.infra.email.entity.EmailCategory;
import com.bulkloads.web.infra.email.repository.UserEmailCategoryRepository;
import com.bulkloads.web.notification.service.NotificationService;
import com.bulkloads.web.notification.service.dto.NotificationRequest;
import com.bulkloads.web.user.domain.entity.User;
import com.bulkloads.web.user.repository.UserRepository;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class AnnouncePost {

  private final UserRepository userRepository;
  private final NotificationService notificationService;
  private final SiteUpdateRepository siteUpdateRepository;
  private final UserEmailCategoryRepository userEmailCategoryRepository;

  // Create a fixed thread pool
  private final ExecutorService executorService = Executors.newFixedThreadPool(5);

  // Make announcements of posts
  public boolean announcePost(ForumPost forumPost, List<ForumReply> forumReplies) {
    executorService.submit(() -> announcePostPrivate(forumPost, forumReplies));
    return true;
  }

  @SuppressWarnings("checkstyle:VariableDeclarationUsageDistance")
  private void announcePostPrivate(ForumPost forumPost, List<ForumReply> forumReplies) {
    boolean isReply = !forumReplies.isEmpty();

    calculateTimeDifference();

    // Sanitize the forum content and strip HTML from the title
    String sanitizedContent = htmlToText(forumPost.getForumContent());
    String strippedTitle = htmlToText(forumPost.getForumTitle());

    if (!isReply) {
      // Create the activity message, limited to 175 characters
      String activityMessage = String.format("%s - %s", strippedTitle, sanitizedContent);
      activityMessage = activityMessage.substring(0, Math.min(activityMessage.length(), 175)) + "...";

      // Create and send a notification
      NotificationRequest notificationRequest = new NotificationRequest();
      notificationRequest.setBody(activityMessage);
      notificationService.create(notificationRequest);
    }

    // Get postTypeId from forumPost
    int postTypeId = forumPost.getPostType().getPostTypeId();

    if (postTypeId != 9) {
      // Create a new SiteUpdate
      SiteUpdate siteUpdate = new SiteUpdate();

      // Create description from title and content
      String description = strippedTitle + " - " + sanitizedContent;

      // Convert description to uppercase and limit to 200 characters
      String siteDescription = description.toUpperCase().substring(0, Math.min(description.length(), 200));
      siteUpdate.setSiteUpdateDescription(siteDescription);
      siteUpdate.setSiteUpdatePosted(Instant.now());
      siteUpdate.setSiteUpdatePostId(siteUpdate.getSiteUpdatePostId());
      siteUpdate.setSiteUpdatesTypeId(5);
      siteUpdate.setSiteId(1);

      // Save the site update
      siteUpdateRepository.save(siteUpdate);
    }

    // Send new post notifications after saving site update
    Integer emailCategoryId = new EmailCategory().getEmailCategoryId();
    Integer siteId = forumPost.getSiteId();
    Integer userId = UserUtil.getUserId().orElse(null);

    // Ensure emailCategoryId is numeric
    if (emailCategoryId != null) {
      // Get new post notification recipients based on email category
      List<Integer> newPostNotifRecipientsIds = userEmailCategoryRepository.findNewPostNotificationRecipients(emailCategoryId, siteId, userId);

      // Process notifications for each recipient
      if (!newPostNotifRecipientsIds.isEmpty()) {
        String titlePrefix = "New " + forumPost.getPostType().getPostTypeId();

        // Remove 's' from the end of the title for post types (10, 11, 12)
        List<Integer> postTypesToModify = Arrays.asList(10, 11, 12);
        if (postTypesToModify.contains(forumPost.getPostType().getPostTypeId())) {
          titlePrefix = titlePrefix.substring(0, titlePrefix.length() - 1);
        }
        // Create push title
        String forumTitle = forumPost.getForumTitle();
        String pushTitle = titlePrefix + ": " + forumTitle.substring(0, Math.min(forumTitle.length(), 50));
        // Create push body, strip HTML
        String forumContent = htmlToText(forumPost.getForumContent());
        String pushBody = forumContent.substring(0, Math.min(forumContent.length(), 200))
                          + (forumContent.length() > 200 ? "..." : "");
        // Push data to send with the notification
        Map<String, Object> pushData = new HashMap<>();
        pushData.put("forum_id", forumPost.getForumId());
        pushData.put("notification_type", emailCategoryId);

        for (Integer recipientId : newPostNotifRecipientsIds) {
          // Create the NotificationRequest
          NotificationRequest notificationRequest = new NotificationRequest();
          notificationRequest.setUserId(recipientId);
          notificationRequest.setTitle(pushTitle);
          notificationRequest.setBody(pushBody);
          notificationRequest.setPushData(pushData);
          notificationRequest.setEmailCategoryId(emailCategoryId);
          // Send the notification
          notificationService.create(notificationRequest);
        }
      }
    } else {
      List<User> forumSubscribers = userRepository.findForumMembers(
          forumPost.getSiteId(),
          forumReplies.get(0).getForumPost().getForumId(),
          forumReplies.get(0).getUser().getUserId()
      );

      String strippedReplyTitle = htmlToText(forumPost.getForumTitle());
      String strippedReplyContent = htmlToText(forumReplies.get(0).getContent());

      // Create the email message
      StringBuilder emailMessage = new StringBuilder();
      emailMessage.append("A new message has been added to a forum topic you are subscribed to!<br><br>")
        .append("<strong>Posted by:</strong> ")
        .append(!forumReplies.get(0).getUser().getFirstName().isEmpty()
          ? forumReplies.get(0).getUser().getFirstName() + " " + forumReplies.get(0).getUser().getLastName()
          : forumReplies.get(0).getUser().getUserCompany().getCompanyName())
        .append("<br><br>")
        .append("<strong>In reply to:</strong> ").append(strippedReplyTitle).append("<br><br>")
        .append("<strong>New Message:</strong> ").append(strippedReplyContent).append("<br><br>")
          .append("<a href=\"%s/%s\">View this forum message</a><br>");

      // Prepare push notification data
      String pushTitle = String.format("%s says:", forumReplies.get(0).getUser().getFirstName().isEmpty()
          ? forumReplies.get(0).getUser().getUserCompany().getCompanyName()
          : forumReplies.get(0).getUser().getFirstName() + " " + forumReplies.get(0).getUser().getLastName());
      String pushBody = String.format("%s in %s", sanitizedContent, strippedTitle);

      Map<String, Object> pushData = new HashMap<>();
      pushData.put("forum_id", forumReplies.get(0).getForumPost().getForumId());
      pushData.put("reply_id", forumReplies.get(0).getReplyId());
      pushData.put("notification_type", "Forum Messages");

// Create the push data
      Map<String, Object> pushNoteData = new HashMap<>();
      pushNoteData.put("forum_id", forumPost.getForumId());
      pushNoteData.put("notification_type", emailCategoryId);
      pushNoteData.put("site_id", forumPost.getSiteId()); // Include the site ID here

// Create the NotificationRequest
      // TODO send push = 1
      NotificationRequest notificationRequest = new NotificationRequest();
      notificationRequest.setUserId(userId);
      notificationRequest.setTitle(pushTitle);
      notificationRequest.setBody(pushBody);
      notificationRequest.setPushData(pushData);
      notificationRequest.setEmailCategoryId(6);

// Send the notification
      notificationService.create(notificationRequest);

    }
  }


  public static void calculateTimeDifference() {
    Instant baseTime = Instant.parse("1969-12-31T16:00:00Z");
    Instant currentTime = Instant.now();
    ChronoUnit.SECONDS.between(baseTime, currentTime);
  }
}
