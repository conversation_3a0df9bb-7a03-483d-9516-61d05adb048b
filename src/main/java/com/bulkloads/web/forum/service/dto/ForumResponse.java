package com.bulkloads.web.forum.service.dto;

import java.time.Instant;
import java.time.LocalDate;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Value;

@Value
@Builder
@JsonAutoDetect(
    fieldVisibility = JsonAutoDetect.Visibility.ANY,
    getterVisibility = JsonAutoDetect.Visibility.NONE,
    isGetterVisibility = JsonAutoDetect.Visibility.NONE
)
public class ForumResponse {

  Integer forumId;
  Integer postTypeId;
  String postType;
  String forumTitle;
  String forumContent;
  String thumbDir;
  String thumbPath;
  String thumbUrl;
  Instant dateAdded;
  Instant lastPost;
  Integer views;
  Integer likes;
  Integer dislikes;
  Integer messageCount;
  Integer premiumOnly;
  Integer isfeatured;
  Integer showInDays;
  LocalDate showDate;
  String forumLink;
  String youtubeVideoId;
  <PERSON>ole<PERSON> approved;
  Integer active;
  String alias;
  String forumUrl;
  Integer userId;
  String firstName;
  String lastName;
  String email;
  @SuppressWarnings("checkstyle:MemberName")
  @JsonProperty("i_like")
  Integer iLike;
  @SuppressWarnings("checkstyle:MemberName")
  @JsonProperty("i_dislike")
  Integer iDislike;
  @SuppressWarnings("checkstyle:MemberName")
  @JsonProperty("i_subscribe")
  Integer iSubscribe;
  String avatarThumbUrl;
  List<String> links;
  String city;
  String state;
  String zip;
  String country;
  @JsonProperty("lat")
  Double latitude;
  @JsonProperty("long")
  Double longitude;
  Double distance;
}
