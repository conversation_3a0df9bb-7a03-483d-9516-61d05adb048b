package com.bulkloads.web.forum.service.dto.transformer;

import com.bulkloads.common.jpa.nativejpa.QueryParts;
import com.bulkloads.web.forum.service.dto.ForumFileResponse;
import org.hibernate.query.TupleTransformer;
import org.springframework.stereotype.Component;
import lombok.RequiredArgsConstructor;

@Component
@RequiredArgsConstructor
public class QueryForumFilesResponseTransformer implements TupleTransformer<ForumFileResponse> {

  @Override
  public ForumFileResponse transformTuple(Object[] columns, String[] aliases) {
    QueryParts parts = new QueryParts(columns, aliases);
    return ForumFileResponse.builder()
      .fileUrl(parts.asString("file_url"))
      .thumbUrl(parts.asString("thumb_url"))
      .caption(parts.asString("caption"))
      .build();
  }
}
