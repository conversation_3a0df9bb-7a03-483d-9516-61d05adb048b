package com.bulkloads.web.forum.service;

import static com.bulkloads.common.StringUtil.extractLinksFromHtml;
import static com.bulkloads.common.StringUtil.htmlToText;
import static com.bulkloads.common.validation.ValidationUtils.exists;
import static com.bulkloads.common.validation.ValidationUtils.isEmpty;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import com.bulkloads.common.UserUtil;
import com.bulkloads.common.validation.Result;
import com.bulkloads.config.AppProperties;
import com.bulkloads.exception.ValidationException;
import com.bulkloads.web.forum.domain.ForumDomainService;
import com.bulkloads.web.forum.domain.ForumReplyDomainService;
import com.bulkloads.web.forum.domain.data.ForumData;
import com.bulkloads.web.forum.domain.data.ReplyData;
import com.bulkloads.web.forum.domain.entity.ForumPost;
import com.bulkloads.web.forum.domain.entity.ForumReply;
import com.bulkloads.web.forum.domain.entity.ForumSubscription;
import com.bulkloads.web.forum.domain.entity.ForumSubscriptionId;
import com.bulkloads.web.forum.domain.entity.ForumUpdateRequest;
import com.bulkloads.web.forum.mapper.ForumMapper;
import com.bulkloads.web.forum.repository.ForumPostTypeRepository;
import com.bulkloads.web.forum.repository.ForumReplyRepository;
import com.bulkloads.web.forum.repository.ForumRepository;
import com.bulkloads.web.forum.repository.ForumSubscriptionRepository;
import com.bulkloads.web.forum.service.dto.ForumDetailResponse;
import com.bulkloads.web.forum.service.dto.ForumFileResponse;
import com.bulkloads.web.forum.service.dto.ForumRequest;
import com.bulkloads.web.forum.service.dto.ForumResponse;
import com.bulkloads.web.forum.service.dto.ForumTotalResponse;
import com.bulkloads.web.forum.service.dto.PostTypesResponse;
import com.bulkloads.web.forum.service.dto.ReplyRequest;
import com.bulkloads.web.forum.service.dto.ReplyResponse;
import com.bulkloads.web.forum.service.utility.AnnouncePost;
import com.bulkloads.web.forum.service.utility.HandleOpinion;
import com.bulkloads.web.infra.email.EmailService;
import com.bulkloads.web.infra.email.domain.EmailDetails;
import com.bulkloads.web.user.domain.entity.User;
import com.bulkloads.web.user.service.UserService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import jakarta.persistence.EntityNotFoundException;
import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class ForumService {

  private final ForumRepository forumRepository;
  private final ForumPostTypeRepository forumPostTypeRepository;
  private final ForumMapper forumMapper;
  private final ForumDomainService forumDomainService;
  private final AnnouncePost announcePost;
  private final ForumReplyRepository forumReplyRepository;
  private final ForumSubscriptionRepository forumSubscriptionRepository;
  private final ForumReplyDomainService forumReplyDomainService;
  private final HandleOpinion handleOpinion;
  private final AppProperties appProperties;
  private final EmailService emailService;
  private final UserService userService;

  public List<ForumResponse> getForum(String postTypeIds,
                                      String term,
                                      Double latitude,
                                      Double longitude,
                                      Double radius,
                                      Integer skip,
                                      Integer limit) {

    Integer userId = UserUtil.getUserId().orElse(null);

    final boolean isSiteAdmin = UserUtil.isSiteAdmin();

    boolean includePremiumPosts = false;
    if (UserUtil.getUserId().isPresent()) {
      User user = userService.getLoggedInUser();
      includePremiumPosts = user.isPro();
    }

    return forumRepository.getForum(userId, isSiteAdmin, includePremiumPosts, postTypeIds, term, latitude, longitude, radius, skip, limit);
  }

  public ForumDetailResponse getForumById(Integer forumId) {
    Integer userId = UserUtil.getUserId().orElse(null);
    final boolean isSiteAdmin = UserUtil.isSiteAdmin();

    boolean includePremiumPosts = false;
    if (UserUtil.getUserId().isPresent()) {
      User user = userService.getLoggedInUser();
      includePremiumPosts = user.isPro();
    }

    ForumDetailResponse response = forumRepository.queryPostsDetail(forumId, userId, isSiteAdmin);
    if (response == null) {
      throw new EntityNotFoundException("Forum details not found");
    }

    if (!includePremiumPosts && response.getPremiumOnly() == 1) {
      throw new ValidationException("premium_only", "This Forum Post contains Load Details which are only viewable with a Pro Membership.");
    }

    List<String> forumLinks = extractLinksFromHtml(response.getForumContent());
    response.setLinks(forumLinks);
    response.setForumContentHtml(response.getForumContent());
    // remove html except videos and training videos
    if (response.getPostTypeId() != 9 && response.getPostTypeId() != 11) {
      response.setForumContent(htmlToText(response.getForumContent()));
    }

    List<ReplyResponse> replyDetails = forumRepository.queryRepliesDetail(forumId, userId, isSiteAdmin, includePremiumPosts, null);

    if (replyDetails != null && !replyDetails.isEmpty()) {
      for (ReplyResponse reply : replyDetails) {

        List<String> links = extractLinksFromHtml(reply.getContent());
        reply.setLinks(links);
        reply.setContentHtml(reply.getContent());
        reply.setContent(htmlToText(reply.getContent()));

        reply.setFiles(new ArrayList<>());

        List<ForumFileResponse> fileResponses = forumRepository.queryForumFiles(null, reply.getReplyId()).stream()
            .map(file -> new ForumFileResponse(file.getFileUrl(), file.getThumbUrl(), file.getCaption()))
            .collect(Collectors.toList());

        if (!fileResponses.isEmpty()) {
          reply.setFiles(fileResponses);
        }
      }
    }

    List<ForumFileResponse> forumFileResponses = forumRepository.queryForumFiles(forumId, null).stream()
        .map(file -> new ForumFileResponse(file.getFileUrl(), file.getThumbUrl(), file.getCaption()))
        .collect(Collectors.toList());
    response.setFiles(forumFileResponses);

    response.setReplies(replyDetails);

    // update the views
    forumRepository.updateViews(forumId);

    return response;
  }

  public ForumDetailResponse getForumDetails(String alias) {
    if (isEmpty(alias)) {
      throw new ValidationException("alias", "Alias is required");
    }
    List<ForumPost> forums = forumRepository.findByAlias(alias);

    if (forums.isEmpty()) {
      throw new EntityNotFoundException("Forum post not found by alias: " + alias);
    } else if (forums.size() > 1) {
      throw new EntityNotFoundException("Multiple forum posts found by alias: " + alias);
    }

    ForumPost forum = forums.get(0);
    Integer forumId = forum.getForumId();
    return getForumById(forumId);
  }

  public List<PostTypesResponse> getForumPostTypes() {
    return forumPostTypeRepository.findAllPostTypes();
  }

  public ForumTotalResponse getForumTotals(String postTypeIds, String term) {
    Integer userId = UserUtil.getUserId().orElse(null);
    final boolean isSiteAdmin = UserUtil.isSiteAdmin();
    boolean premiumOnly = false;
    return forumRepository.getForumTotals(userId,
        isSiteAdmin,
        premiumOnly,
        postTypeIds,
        term);
  }

  @Transactional
  public Integer createForum(ForumRequest forumRequest) {
    ForumData data = forumMapper.requestToData(forumRequest);
    Result<ForumPost> result = forumDomainService.create(data);
    ForumPost forumPost = result.orElseThrow();
    forumRepository.save(forumPost);
    Integer forumId = forumPost.getForumId();

    if (forumPost.getIsFeatured()) {
      forumRepository.unsetFeaturedPostsExcept(forumId);
    }

    // Subscription
    if (exists(forumRequest.getISubscribe())) {
      if (forumRequest.getISubscribe().get()) {
        forumSubscribe(forumId);
      } else {
        forumUnsubscribe(forumId);
      }
    }

    notifyAdminForPosts(forumPost);

    announcePost.announcePost(forumPost, Collections.emptyList());

    return forumPost.getForumId();
  }

  @Transactional
  public Integer updateForum(Integer forumId, ForumUpdateRequest request) {
    ForumPost forumToUpdate = forumRepository.findById(forumId)
        .orElseThrow(() -> new EntityNotFoundException("Forum post not found"));
    ForumData data = forumMapper.requestToData(request);
    Result<ForumPost> result = forumDomainService.update(forumToUpdate, data);
    ForumPost forumPost = result.orElseThrow();
    forumRepository.save(forumPost);

    return forumId;
  }

  @Transactional
  public Integer createReply(Integer forumId, ReplyRequest replyRequest) {
    ForumPost forumPost = forumRepository.findById(forumId)
        .orElseThrow(() -> new EntityNotFoundException("Forum post not found"));

    ReplyData replyData = forumMapper.replyToData(replyRequest);

    replyData.setForumPost(forumPost);

    Result<ForumReply> result = forumReplyDomainService.createReply(replyData);
    ForumReply savedReply = result.orElseThrow();

    forumPost.setLastReply(Instant.now());

    savedReply.setForumPost(forumPost);

    forumReplyRepository.save(savedReply);

    if (exists(replyRequest.getISubscribe())) {
      if (replyRequest.getISubscribe().get()) {
        forumSubscribe(forumId);
      } else {
        forumUnsubscribe(forumId);
      }
    }

    notifyAdminForReply(savedReply);

    return savedReply.getReplyId();
  }

  public void forumLike(Integer forumId, Integer replyId) {
    handleOpinion.handleOpinion(forumId, replyId, 1); // 1 for like
  }

  public void forumDislike(Integer forumId, Integer replyId) {
    handleOpinion.handleOpinion(forumId, replyId, 0); // 0 for dislike
  }

  public void forumUndoLikeDislike(Integer forumId, Integer replyId) {
    handleOpinion.handleOpinion(forumId, replyId, null);
  }

  @Transactional
  public void forumSubscribe(Integer forumId) {
    final int userId = UserUtil.getUserIdOrThrow();
    final Optional<ForumSubscription> subOpt = forumSubscriptionRepository.findByForumSubscriptionId_ForumIdAndForumSubscriptionId_UserId(forumId, userId);
    if (subOpt.isPresent()) {
      return;
    }
    ForumSubscription sub = new ForumSubscription();
    ForumSubscriptionId id = new ForumSubscriptionId(forumId, userId);
    sub.setForumSubscriptionId(id);
    forumSubscriptionRepository.save(sub);
  }

  public void forumUnsubscribe(Integer forumId) {
    final int userId = UserUtil.getUserIdOrThrow();
    final Optional<ForumSubscription> subOpt = forumSubscriptionRepository.findByForumSubscriptionId_ForumIdAndForumSubscriptionId_UserId(forumId, userId);
    subOpt.ifPresent(forumSubscriptionRepository::delete);
  }

  private void notifyAdminForPosts(ForumPost forumPost) {
    boolean isApproved = forumPost.getApproved();
    StringBuilder emailContent = new StringBuilder();

    if (!isApproved) {
      User user = forumPost.getUser();

      emailContent.append("FORUM TITLE: %s<br/>".formatted(forumPost.getForumTitle()))
          .append("CATEGORY: %s<br/><br/>".formatted(forumPost.getPostType().getPostType()))
          .append("FROM: %s %s &lt;%s&gt; - %s<br/><br/>".formatted(
              user.getFirstName(),
              user.getLastName(),
              user.getEmail(),
              user.getUserCompany().getCompanyName())
          )
          .append(forumPost.getForumContent()).append("<br/><br/>")
          .append("<a href=\"%s/admin/to_do/\">Go to Admin Page</a><br/><br/><br/><br/>".formatted(appProperties.getDomainUrl()))
          .append("<a href=\"%s/%s/%s\">View this forum message</a><br/><br/><br/>".formatted(
              appProperties.getDomainUrl(),
              forumPost.getPostType().getMaindir(),
              forumPost.getAlias()))
          .append("Go to <a href=\"%s/admin/approve_posts.cfm\"> Admin > Approve Posts </a>".formatted(appProperties.getDomainUrl()));

      String subject = "New Forum Post";

      EmailDetails emailDetails = EmailDetails.builder()
          .fromEmail(appProperties.getMailing().getSiteEmail())
          .replyToEmail(appProperties.getMailing().getReplyToEmail())
          .failTo(appProperties.getMailing().getFailToEmail())
          .subject(subject)
          .message(emailContent.toString())
          .toEmails(List.of(appProperties.getMailing().getSiteEmail()))
          .description(subject)
          .build();

      emailService.sendEmail(emailDetails);
    }

  }

  private void notifyAdminForReply(ForumReply forumReply) {

    boolean isApproved = forumReply.getApproved();
    StringBuilder emailContent = new StringBuilder();

    if (!isApproved) {
      User user = forumReply.getUser();

      emailContent.append("<strong>NEW REPLY</strong><br/><br/>")
          .append("<strong>Posted by:</strong>: %s %s - %s<br/><br/>".formatted(
              user.getFirstName(),
              user.getLastName(),
              user.getUserCompany().getCompanyName())
          )
          .append("<strong>In reply to:</strong> %s<br/><br/>".formatted(forumReply.getForumPost().getForumTitle()))
          .append(forumReply.getContent()).append("<br/><br/>")
          .append("<a href=\"%s/admin/to_do/\">Go to Admin Page</a><br/><br/><br/><br/>".formatted(appProperties.getDomainUrl()))
          .append("<a href=\"%s/%s/%s\">View this forum message</a><br/><br/><br/>".formatted(
              appProperties.getDomainUrl(),
              forumReply.getForumPost().getPostType().getMaindir(),
              forumReply.getForumPost().getAlias()))
          .append("Go to <a href=\"%s/admin/approve_posts.cfm\"> Admin > Approve Posts </a>".formatted(appProperties.getDomainUrl()));

      String subject = "New Reply";

      EmailDetails emailDetails = EmailDetails.builder()
          .fromEmail(appProperties.getMailing().getSiteEmail())
          .replyToEmail(appProperties.getMailing().getReplyToEmail())
          .failTo(appProperties.getMailing().getFailToEmail())
          .subject(subject)
          .message(emailContent.toString())
          .toEmails(List.of(appProperties.getMailing().getSiteEmail()))
          .description(subject)
          .build();

      emailService.sendEmail(emailDetails);
    }

  }
}