package com.bulkloads.web.forum.service.dto;

import java.time.Instant;
import java.time.LocalDate;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

@Data
@Builder(toBuilder = true)
@AllArgsConstructor
public class ReplyResponse {

  Integer replyId;
  String content;
  String contentHtml;
  Integer parentReplyId;
  String thumbUrl;
  String avatarThumbUrl;
  LocalDate signUpDate;
  Instant dateAdded;
  Integer likes;
  Integer dislikes;
  Integer approved;
  Integer userId;
  String firstName;
  String lastName;
  String companyName;
  String email;
  String city;
  String state;
  Integer userCommentCount;
  Integer userLikes;
  Integer userDislikes;
  @SuppressWarnings("checkstyle:MemberName")
  @JsonProperty("i_like")
  Integer iLike;
  @SuppressWarnings("checkstyle:MemberName")
  @JsonProperty("i_dislike")
  Integer idislike;
  List<String> links;
  List<ForumFileResponse> files;
}
