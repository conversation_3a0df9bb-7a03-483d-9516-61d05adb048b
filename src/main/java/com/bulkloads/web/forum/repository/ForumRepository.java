package com.bulkloads.web.forum.repository;

import java.util.List;
import com.bulkloads.web.forum.domain.entity.ForumPost;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

@Repository
public interface ForumRepository extends JpaRepository<ForumPost, Integer>, ForumQueryRepository {
  

  List<ForumPost> findByAlias(
      @Param("alias") String alias
  );

  // Query to count the number of posts with a specific alias
  @Query(value = "SELECT COUNT(1) FROM forum_post WHERE alias = :alias", nativeQuery = true)
  int countByAlias(@Param("alias") String alias);

  @Modifying
  @Transactional
  @Query(value = """
          UPDATE forum_post
          SET views = views + 1
          WHERE forum_id = :forumId
          """, nativeQuery = true)
  void updateViews(@Param("forumId") Integer forumId);


  @Modifying
  @Transactional
  @Query("UPDATE ForumPost f SET f.isFeatured = false WHERE f.isFeatured = true AND f.forumId <> :forumId")
  void unsetFeaturedPostsExcept(@Param("forumId") Integer forumId);

}

