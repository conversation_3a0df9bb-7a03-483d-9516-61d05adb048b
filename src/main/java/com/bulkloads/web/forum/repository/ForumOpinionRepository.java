package com.bulkloads.web.forum.repository;

import com.bulkloads.web.forum.domain.entity.ForumOpinion;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

@Repository
public interface ForumOpinionRepository extends JpaRepository<ForumOpinion, Integer> {

  @Modifying
  @Transactional
  @Query(value = """
        DELETE FROM forum_opinions
        WHERE forum_id = :forumId
          AND user_id = :userId
          AND (
                (:replyId IS NULL AND reply_id IS NULL)
                OR reply_id = :replyId
              )
        """, nativeQuery = true)
  void deleteOpinion(@Param("forumId") Integer forumId,
                     @Param("replyId") Integer replyId,
                     @Param("userId") Integer userId);

}
