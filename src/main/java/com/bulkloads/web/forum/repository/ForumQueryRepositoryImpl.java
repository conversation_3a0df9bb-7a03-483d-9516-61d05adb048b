package com.bulkloads.web.forum.repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.bulkloads.common.jpa.nativejpa.JpaNativeQueryService;
import com.bulkloads.config.AppProperties;
import com.bulkloads.web.aws.service.AmazonS3Service;
import com.bulkloads.web.forum.repository.template.GetForumFilesQueryTemplate;
import com.bulkloads.web.forum.repository.template.GetForumQueryTemplate;
import com.bulkloads.web.forum.repository.template.GetQueryPostDetailsTemplate;
import com.bulkloads.web.forum.repository.template.GetQueryRepliesDetailTemplate;
import com.bulkloads.web.forum.service.dto.ForumDetailResponse;
import com.bulkloads.web.forum.service.dto.ForumFileResponse;
import com.bulkloads.web.forum.service.dto.ForumResponse;
import com.bulkloads.web.forum.service.dto.ForumTotalResponse;
import com.bulkloads.web.forum.service.dto.ReplyResponse;
import com.bulkloads.web.forum.service.dto.transformer.ForumResponseTransformer;
import com.bulkloads.web.forum.service.dto.transformer.ForumTotalResponseTransformer;
import com.bulkloads.web.forum.service.dto.transformer.QueryForumFilesResponseTransformer;
import com.bulkloads.web.forum.service.dto.transformer.QueryPostDetailsResponseTransformer;
import com.bulkloads.web.forum.service.dto.transformer.QueryRepliesDetailResponseTransformer;
import org.springframework.stereotype.Repository;
import lombok.RequiredArgsConstructor;

@Repository
@RequiredArgsConstructor
public class ForumQueryRepositoryImpl implements ForumQueryRepository {

  private final JpaNativeQueryService jpaNativeQueryService;
  private final ForumResponseTransformer forumResponseTransformer;
  private final ForumTotalResponseTransformer getForumTotalResponseTransformer;
  private final QueryPostDetailsResponseTransformer queryPostDetailsResponseTransformer;
  private final QueryRepliesDetailResponseTransformer queryRepliesDetailResponseTransformer;
  private final QueryForumFilesResponseTransformer queryForumFilesResponseTransformer;
  private final AppProperties appProperties;
  private final AmazonS3Service amazonS3Service;

  public List<ForumResponse> getForum(Integer userId,
                                      boolean isSiteAdmin,
                                      boolean premiumOnly,
                                      String postTypeIds,
                                      String term,
                                      Double latitude,
                                      Double longitude,
                                      Double radius,
                                      Integer skip,
                                      Integer limit) {

    Map<String, Object> params = new HashMap<>();
    params.put("u_id", userId);
    params.put("is_site_admin", isSiteAdmin);
    params.put("premium_only", premiumOnly);
    params.put("post_type_ids", postTypeIds);
    params.put("term", term);
    params.put("latitude", latitude);
    params.put("longitude", longitude);
    params.put("radius", radius);
    params.put("skip", skip);
    params.put("limit", limit);

    params.put("domain_url", appProperties.getDomainUrl());
    params.put("profiles_dir", amazonS3Service.getProfilesDirUrl());
    params.put("classifieds_dir", amazonS3Service.getClassifiedsDirUrl());

    return jpaNativeQueryService.query(GetForumQueryTemplate.GET_FORUM_QUERY_TEMPLATE, params, forumResponseTransformer);

  }

  public ForumTotalResponse getForumTotals(Integer userId,
                                           boolean isSiteAdmin,
                                           boolean includePremium,
                                           String postTypeIds,
                                           String term
  ) {
    Map<String, Object> params = new HashMap<>();

    params.put("count", true);
    params.put("u_id", userId);
    params.put("is_site_admin", isSiteAdmin);
    params.put("include_premium", includePremium);
    params.put("post_type_ids", postTypeIds);
    params.put("term", term);

    return jpaNativeQueryService.queryForObject(GetForumQueryTemplate.GET_FORUM_QUERY_TEMPLATE, params, getForumTotalResponseTransformer);
  }

  public ForumDetailResponse queryPostsDetail(Integer forumId,
                                              Integer userId,
                                              boolean isSiteAdmin) {

    Map<String, Object> params = new HashMap<>();
    params.put("u_id", userId);
    params.put("is_site_admin", isSiteAdmin);
    params.put("forum_id", forumId);
    params.put("domain_url", appProperties.getDomainUrl());
    params.put("profiles_dir", amazonS3Service.getProfilesDirUrl());
    params.put("classifieds_dir", amazonS3Service.getClassifiedsDirUrl());

    return jpaNativeQueryService.queryForObject(
        GetQueryPostDetailsTemplate.GET_QUERY_POST_DETAILS_TEMPLATE,
        params,
        queryPostDetailsResponseTransformer);
  }

  public List<ReplyResponse> queryRepliesDetail(Integer forumId,
                                                Integer userId,
                                                boolean isSiteAdmin,
                                                boolean includePremium,
                                                Integer replyId) {
    Map<String, Object> params = new HashMap<>();
    params.put("u_id", userId);
    params.put("is_site_admin", isSiteAdmin);
    params.put("include_premium", includePremium);
    params.put("forum_id", forumId);
    params.put("reply_id", replyId);
    params.put("profiles_dir", amazonS3Service.getProfilesDirUrl());
    params.put("classifieds_dir", amazonS3Service.getClassifiedsDirUrl());

    return jpaNativeQueryService.query(
        GetQueryRepliesDetailTemplate.GET_QUERY_REPLIES_DETAIL_TEMPLATE,
        params,
        queryRepliesDetailResponseTransformer);
  }

  public List<ForumFileResponse> queryForumFiles(Integer forumId, Integer replyId) {
    Map<String, Object> params = new HashMap<>();
    params.put("forum_id", forumId);
    params.put("reply_id", replyId);
    params.put("classifieds_dir", amazonS3Service.getClassifiedsDirUrl());

    return jpaNativeQueryService.query(GetForumFilesQueryTemplate.GET_FORUM_FILES_QUERY_TEMPLATE, params, queryForumFilesResponseTransformer);
  }

}

