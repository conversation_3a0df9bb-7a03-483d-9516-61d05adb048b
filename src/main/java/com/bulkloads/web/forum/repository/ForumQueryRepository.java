package com.bulkloads.web.forum.repository;

import java.util.List;
import com.bulkloads.web.forum.service.dto.ForumDetailResponse;
import com.bulkloads.web.forum.service.dto.ForumFileResponse;
import com.bulkloads.web.forum.service.dto.ForumResponse;
import com.bulkloads.web.forum.service.dto.ForumTotalResponse;
import com.bulkloads.web.forum.service.dto.ReplyResponse;

public interface ForumQueryRepository {

  List<ForumResponse> getForum(
      Integer userId,
      boolean isSiteAdmin,
      boolean includePremium,
      String postTypeIds,
      String term,
      Double latitude,
      Double longitude,
      Double radius,
      Integer skip,
      Integer limit
  );

  ForumTotalResponse getForumTotals(
      Integer userId,
      boolean isSiteAdmin,
      boolean includePremium,
      String postTypeIds,
      String term);

  ForumDetailResponse queryPostsDetail(
      Integer forumId,
      Integer userId,
      boolean isSiteAdmin
  );

  List<ReplyResponse> queryRepliesDetail(
      Integer forumId,
      Integer userId,
      boolean isSiteAdmin,
      boolean includePremium,
      Integer replyId
  );

  List<ForumFileResponse> queryForumFiles(
      Integer forumId,
      Integer replyId
  );
}

