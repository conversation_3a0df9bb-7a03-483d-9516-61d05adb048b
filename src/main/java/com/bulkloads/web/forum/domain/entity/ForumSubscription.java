package com.bulkloads.web.forum.domain.entity;

import java.time.Instant;
import jakarta.persistence.Column;
import jakarta.persistence.EmbeddedId;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@Entity
@Getter
@Setter
@Table(name = "forum_subscription")
public class ForumSubscription {

  @EmbeddedId
  private ForumSubscriptionId forumSubscriptionId;

  @Column(name = "date_added")
  private Instant dateAdded = Instant.now();

}
