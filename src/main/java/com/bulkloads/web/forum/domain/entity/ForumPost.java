package com.bulkloads.web.forum.domain.entity;

import java.time.Instant;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import com.bulkloads.web.user.domain.entity.User;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;

@Entity
@Getter
@Setter
@Table(name = "forum_post")
public class ForumPost {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "forum_id")
  private Integer forumId;

  @Column(name = "site_id")
  private Integer siteId = 1;

  @NotEmpty(message = "You must enter a forum_title")
  @Size(max = 200, message = "Up to 200 chars")
  @Column(name = "forum_title")
  private String forumTitle = "";

  @NotEmpty(message = "You must enter a message")
  @Column(name = "forum_content")
  private String forumContent = "";

  @ManyToOne(cascade = {CascadeType.PERSIST, CascadeType.MERGE, CascadeType.DETACH, CascadeType.REFRESH}, fetch = FetchType.LAZY)
  @JoinColumn(name = "user_id")
  private User user;

  @Column(name = "date_added")
  private Instant dateAdded = Instant.now();

  @NotEmpty(message = "Alias is required")
  @Column(name = "alias")
  private String alias;

  @Column(name = "visibility_site_ids")
  private Integer visibilitySiteIds = 0;

  @Column(name = "thumb_path")
  private String thumbPath = "";

  @Column(name = "active")
  private Integer active = 1;

  @Column(name = "approved")
  private Boolean approved = false;

  @Column(name = "approved_by_user_id")
  private Integer approvedByUserId;

  @Column(name = "views")
  private Integer views = 0;

  @Column(name = "last_reply")
  private Instant lastReply;

  @Size(max = 255, message = "Up to 255 chars")
  @Column(name = "forum_link")
  private String forumLink = "";

  @Size(max = 45, message = "Up to 45 chars")
  @Column(name = "youtube_video_id")
  private String youtubeVideoId = "";

  @Column(name = "isFeatured")
  private Boolean isFeatured = false;

  @Column(name = "show_date")
  private LocalDate showDate;

  @Column(name = "premium_only")
  private Boolean premiumOnly = false;

  @Column(name = "contact_name")
  private String contactName = "";

  @Column(name = "contact_phone")
  private String contactPhone = "";

  @Column(name = "contact_email")
  private String contactEmail = "";

  @Column(name = "current_city")
  private String currentCity = "";

  @Column(name = "current_state")
  private String currentState = "";

  @Column(name = "current_zip")
  private String currentZip = "";

  @Column(name = "current_country")
  private String currentCountry = "";

  @Column(name = "current_latitude")
  private Double currentLatitude;

  @Column(name = "current_longitude")
  private Double currentLongitude;

  @Column(name = "orderby_show_date")
  private Instant orderbyShowDate = Instant.now();

  @Column(name = "orderby_last_reply")
  private Instant orderbyLastReply = Instant.now();

  @NotNull(message = "post type missing")
  @ManyToOne
  @JoinColumn(name = "post_type_id")
  private ForumPostType postType;

  @OneToMany(cascade = {CascadeType.ALL},
      orphanRemoval = true, fetch = FetchType.LAZY)
  @JoinColumn(name = "forum_id")
  private List<ForumReply> forumReply = new ArrayList<>();

  @OneToMany(cascade = {CascadeType.PERSIST, CascadeType.MERGE, CascadeType.DETACH, CascadeType.REFRESH},
      orphanRemoval = true, fetch = FetchType.LAZY)
  @JoinColumn(name = "forum_id")
  private List<ForumFile> files = new ArrayList<>();

}
