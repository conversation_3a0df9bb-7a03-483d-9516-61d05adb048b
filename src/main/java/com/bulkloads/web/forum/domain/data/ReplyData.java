package com.bulkloads.web.forum.domain.data;

import java.util.List;
import java.util.Optional;
import com.bulkloads.web.forum.domain.entity.ForumPost;
import com.bulkloads.web.forum.domain.entity.ForumReply;
import lombok.Data;

@Data
public class ReplyData {

  private ForumPost forumPost;
  private String content;
  private Optional<ForumReply> parentReply;
  @SuppressWarnings("checkstyle:MemberName")
  private Optional<Boolean> iSubscribe;
  private Optional<List<ForumFileData>> files;

}
