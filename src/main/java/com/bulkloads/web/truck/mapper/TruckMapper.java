package com.bulkloads.web.truck.mapper;

import com.bulkloads.web.contracts.mapper.CommonMapper;
import com.bulkloads.web.truck.domain.data.TruckData;
import com.bulkloads.web.truck.domain.entity.Truck;
import com.bulkloads.web.truck.service.dto.TruckRequest;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;

@Mapper(componentModel = "spring",
    nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
    nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
    unmappedTargetPolicy = org.mapstruct.ReportingPolicy.IGNORE,
    uses = CommonMapper.class)
public abstract class TruckMapper {

  public abstract TruckData requestToData(final TruckRequest request);

  @Mapping(source = "originLatitude", target = "originLat")
  @Mapping(source = "originLongitude", target = "originLong")
  @Mapping(source = "destinationLatitude", target = "destinationLat")
  @Mapping(source = "destinationLongitude", target = "destinationLong")
  public abstract void dataToEntity(final TruckData data, @MappingTarget final Truck truck);

  public abstract TruckData entityToData(final Truck truck);
}