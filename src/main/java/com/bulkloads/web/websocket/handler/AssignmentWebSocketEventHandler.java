package com.bulkloads.web.websocket.handler;

import static com.bulkloads.common.validation.ValidationUtils.isEmpty;
import static com.bulkloads.config.AppConstants.WebSocket.ACTION;
import static com.bulkloads.config.AppConstants.WebSocket.Action.DELETED;
import static com.bulkloads.config.AppConstants.WebSocket.Action.UPDATED;
import static com.bulkloads.config.AppConstants.WebSocket.Channel.LOAD_ASSIGNMENTS;
import static com.bulkloads.config.AppConstants.WebSocket.Channel.LOAD_BOOKINGS;
import static com.bulkloads.config.AppConstants.WebSocket.DATA;
import static com.bulkloads.config.AppConstants.WebSocket.USER_COMPANY_ID;
import static com.bulkloads.web.load.service.LoadService.LOAD_ASSIGNMENT_IDS;
import static com.bulkloads.web.load.service.LoadService.LOAD_ID;
import static java.util.Objects.nonNull;
import static java.util.Optional.ofNullable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.bulkloads.web.assignment.domain.entity.Assignment;
import com.bulkloads.web.assignment.event.AssignmentBookingEvent;
import com.bulkloads.web.assignment.event.AssignmentCancelledEvent;
import com.bulkloads.web.assignment.repository.AssignmentRepository;
import com.bulkloads.web.assignment.repository.BookingRepository;
import com.bulkloads.web.assignment.service.dto.AssignmentBookingListResponse;
import com.bulkloads.web.assignment.service.dto.AssignmentSearchRequest;
import com.bulkloads.web.assignment.service.dto.BookingSearchRequest;
import com.bulkloads.web.infra.websocket.WebSocketService;
import com.bulkloads.web.infra.websocket.dto.WebSocketPublishDto;
import com.bulkloads.web.load.domain.entity.Load;
import com.bulkloads.web.load.event.LoadUpdatedEvent;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;
import jakarta.persistence.EntityManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
@RequiredArgsConstructor
public class AssignmentWebSocketEventHandler {

  private final WebSocketService webSocketService;
  private final AssignmentRepository assignmentRepository;
  private final BookingRepository bookingRepository;
  private final EntityManager entityManager;

  @TransactionalEventListener(classes = {
      LoadUpdatedEvent.class
  }, phase = TransactionPhase.AFTER_COMMIT)
  public void handleLoadWebsocket(final LoadUpdatedEvent event) {
    final List<Integer> bookingIds = event.getBookingIds();

    if (!bookingIds.isEmpty()) {
      doHandleWebsocket(bookingIds);
    }

    final List<Integer> deletedBookingIds = event.getDeletedBookingIds();
    if (!deletedBookingIds.isEmpty()) {
      doHandleWebsocket(deletedBookingIds);
    }

    final List<Integer> assignmentIds = event.getAssignmentIds();
    if (!assignmentIds.isEmpty()) {
      doHandleWebsocket(assignmentIds);
    }

    final List<Integer> deletedAssignmentIds = event.getDeletedAssignmentIds();
    if (!deletedAssignmentIds.isEmpty()) {
      doHandleWebsocket(deletedAssignmentIds);
    }
  }

  @TransactionalEventListener(classes = {
      AssignmentBookingEvent.class
  }, phase = TransactionPhase.AFTER_COMMIT)
  public void handleWebsocket(final AssignmentBookingEvent event) {

    // entityManager.flush();

    List<Integer> ids = event.getLoadAssignmentIds();

    if (event instanceof AssignmentCancelledEvent cancelledEvent) {
      ids = new ArrayList<>();
      ids.add(cancelledEvent.getLoadAssignmentIds().get(0));
      ids.add(cancelledEvent.getBlankAssignmentId());
      ofNullable(cancelledEvent.getParentAssignmentId()).ifPresent(ids::add);
    }
    doHandleWebsocket(ids);
  }

  private void sendToAssignments(Assignment assignment, Map<String, Object> message) {
    WebSocketPublishDto dto = WebSocketPublishDto.builder()
        .channel(LOAD_ASSIGNMENTS)
        .message(message)
        .meta(Map.of(USER_COMPANY_ID, assignment.getUserCompany().getUserCompanyId()))
        .build();
    webSocketService.sendToWebSocket(dto);
  }

  private void sendToBookings(Assignment assignment, Map<String, Object> message) {
    WebSocketPublishDto dto = WebSocketPublishDto.builder()
        .channel(LOAD_BOOKINGS)
        .message(message)
        .meta(Map.of(USER_COMPANY_ID, assignment.getToUserCompany().getUserCompanyId()))
        .build();
    webSocketService.sendToWebSocket(dto);
  }

  private void doHandleWebsocket(final List<Integer> loadAssignmentIds) {

    assignmentRepository.findAllById(loadAssignmentIds).forEach(assignment -> {

      final Map<String, Object> message = new HashMap<>();

      if (nonNull(assignment.getUserCompany())) {

        message.put(LOAD_ID, ofNullable(assignment.getLoad()).map(Load::getLoadId).orElse(null));
        message.put(LOAD_ASSIGNMENT_IDS, assignment.getLoadAssignmentId());

        if (Boolean.TRUE.equals(assignment.getDeleted())) {
          message.put(ACTION, DELETED);

          sendToAssignments(assignment, message);
        } else {
          List<AssignmentBookingListResponse> assignments = searchAssignments(
              assignment.getLoadAssignmentId(),
              assignment.getUserCompany().getUserCompanyId());

          message.put(ACTION, UPDATED);
          message.put(DATA, assignments);
          sendToAssignments(assignment, message);
        }

      }

      if (nonNull(assignment.getToUserCompany())) {
        message.put(LOAD_ID, ofNullable(assignment.getToLoad()).map(Load::getLoadId).orElse(null));
        message.put(LOAD_ASSIGNMENT_IDS, assignment.getLoadAssignmentId());

        if (Boolean.TRUE.equals(assignment.getDeleted())) {
          message.put(ACTION, DELETED);
          sendToBookings(assignment, message);

        } else {
          List<AssignmentBookingListResponse> bookings = searchBookings(assignment.getLoadAssignmentId(), assignment.getToUserCompany().getUserCompanyId());
          if (!isEmpty(bookings)) {
            message.put(ACTION, UPDATED);
            message.put(DATA, bookings);

            sendToBookings(assignment, message);
          }
        }

      }
    });
  }

  private List<AssignmentBookingListResponse> searchAssignments(final int assignmentId, final int userCompanyId) {
    final AssignmentSearchRequest searchRequest = AssignmentSearchRequest.builder().loadAssignmentIds(List.of(assignmentId)).build();

    return assignmentRepository
        .getAssignments(false, null, userCompanyId, searchRequest, null, null, null);
  }

  private List<AssignmentBookingListResponse> searchBookings(final int assignmentId, final int toUserCompanyId) {
    final BookingSearchRequest searchRequest = BookingSearchRequest.builder().loadAssignmentIds(List.of(assignmentId)).build();

    return bookingRepository
        .getBookings(false, null, toUserCompanyId, null, searchRequest, null, null, null);
  }

}
