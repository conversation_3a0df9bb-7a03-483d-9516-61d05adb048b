package com.bulkloads.web.facility.repository;

import java.util.List;
import java.util.Optional;
import com.bulkloads.web.facility.domain.entity.Facility;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

@Repository
public interface FacilityRepository extends JpaRepository<Facility, Integer> {

  @Modifying
  @Transactional
  @Query(value = """
      UPDATE facilities SET
        view_count = view_count + 1
      WHERE facility_id = :facilityId
      """, nativeQuery = true)
  void incrementViewCount(@Param("facilityId") Integer facilityId);

  @Query(value = """
      SELECT f.facility_id as facilityId,
        f.name,
        f.location,
        f.city,
        f.state,
        f.address,
        f.country,
        f.latitude,
        f.longitude,
        f.avg_wait_time as avgWaitTime,
        f.wait_time_count as waitTimeCount,
        f.approved as approved,
        MIN(fwt.wait_duration_minutes) as minWaitTime,
        MAX(fwt.wait_duration_minutes) as maxWaitTime,
        MIN(CASE
          WHEN DATE(fwt.added_date) = CURRENT_DATE
          THEN fwt.wait_duration_minutes
          END) as minTodayWaitTime,
        MAX(CASE
          WHEN DATE(fwt.added_date) = CURRENT_DATE
          THEN fwt.wait_duration_minutes
          END) as maxTodayWaitTime,
        CASE
          WHEN :latitude IS NOT NULL AND :longitude IS NOT NULL THEN
            ROUND(
              ST_DISTANCE_SPHERE(
                POINT(:longitude, :latitude),
                POINT(f.longitude, f.latitude)
              ) / 1609.34, 1
            )
        END as distance
      FROM facilities f
      LEFT JOIN facility_wait_times fwt ON f.facility_id = fwt.facility_id
      WHERE f.deleted = false
        AND (:hasFacilityIds = false OR f.facility_id IN (:facilityIds))
        AND (
            (:approved IS NOT NULL AND f.approved = :approved)
            OR
            (:approved IS NULL AND f.added_by_user_id = :userId)
            OR
            (:approved IS NULL AND f.approved = true)
        )
        AND f.latitude IS NOT NULL
        AND f.longitude IS NOT NULL
        AND (:latitude IS NULL OR  f.latitude BETWEEN :latitude - 3 AND :latitude + 3)
        AND (:longitude IS NULL OR f.longitude BETWEEN :longitude - 3 AND :longitude + 3)
        AND (:latitude IS NULL OR :longitude IS NULL OR :radius IS NULL OR
            ST_DISTANCE_SPHERE(
              POINT(:longitude, :latitude),
              POINT(f.longitude, f.latitude)
            ) / 1609.34 <= :radius)
      GROUP BY f.facility_id
      ORDER BY distance, f.facility_id
      LIMIT :skip, :limit
      """, nativeQuery = true)
  List<FacilityListProjection> findFacilities(
      @Param("latitude") Double latitude,
      @Param("longitude") Double longitude,
      @Param("radius") Double radius,
      @Param("approved") Boolean approved,
      @Param("userId") Integer userId,
      @Param("hasFacilityIds") boolean hasFacilityIds,
      @Param("facilityIds") List<Integer> facilityIds,
      @Param("skip") int skip,
      @Param("limit") int limit);

  @Query(value = """
      SELECT
          f.facility_id as facilityId,
          f.name,
          f.location,
          f.city,
          f.state,
          f.address,
          f.zip,
          f.country,
          f.latitude,
          f.longitude,
          f.website,
          f.phone_1 as phone1,
          f.email,
          f.total_reviews as totalReviews,
          f.avg_rating as avgRating,
          f.view_count as viewCount,
          f.public_restrooms as publicRestrooms,
          f.overnight_parking as overnightParking,
          f.driver_lounge as driverLounge,
          f.onsite_scale as onsiteScale,
          f.appointment_required as appointmentRequired,
          f.washout_required as washoutRequired,
          f.hours_of_operation as hoursOfOperation,
          f.approved as approved,
          f.equipment_ids as equipmentIds,
          f.equipment_names as equipmentNames,
          f.updated_date as updatedDate,
      
          COUNT(fwt.facility_wait_time_id) as waitTimeCount,
          MIN(fwt.wait_duration_minutes) as minWaitTime,
          MAX(fwt.wait_duration_minutes) as maxWaitTime,
          MIN(CASE
              WHEN DATE(fwt.added_date) = CURRENT_DATE
              THEN fwt.wait_duration_minutes
              END) as minTodayWaitTime,
          MAX(CASE
              WHEN DATE(fwt.added_date) = CURRENT_DATE
              THEN fwt.wait_duration_minutes
              END) as maxTodayWaitTime
      FROM facilities f
        LEFT JOIN facility_wait_times fwt ON f.facility_id = fwt.facility_id
      WHERE f.facility_id = :facilityId
      AND f.deleted = false
      GROUP BY f.facility_id
      """, nativeQuery = true)
  Optional<FacilityProjection> findFacilityById(@Param("facilityId") int facilityId);

  Optional<Facility> findByFacilityIdAndDeletedFalse(final int facilityId);

  @Query(value = """
      SELECT
        ff.facility_id as facilityId,
        f.file_id as fileId,
        f.file_url as fileUrl,
        f.thumb_url as thumbUrl
      FROM facility_files ff
      JOIN files f ON f.file_id = ff.file_id
      WHERE ff.facility_id = :facilityId
        AND ff.approved = true
      ORDER BY f.file_id DESC
      LIMIT 5
      """, nativeQuery = true)
  List<FacilityFileProjection> findTop5FilesByFacilityId(@Param("facilityId") int facilityId);

  @Query(value = """
      SELECT
        ff.facility_id as facilityId,
        f.file_id as fileId,
        f.file_url as fileUrl,
        f.thumb_url as thumbUrl
      FROM facility_files ff
      JOIN files f ON f.file_id = ff.file_id
      WHERE ff.approved = false
      ORDER BY f.file_id DESC
      """, nativeQuery = true)
  List<FacilityFileProjection> findUnapprovedFiles();

  @Modifying
  @Transactional
  @Query(value = """
      UPDATE facilities f
      SET f.total_reviews = (
          SELECT COUNT(*) FROM facility_reviews fr
          WHERE fr.facility_id = :facilityId AND fr.approved = true
      ),
      f.avg_rating = (
          SELECT IFNULL(AVG(fr.rating), 0) FROM facility_reviews fr
          WHERE fr.facility_id = :facilityId AND fr.approved = true
      )
      WHERE f.facility_id = :facilityId
      """, nativeQuery = true)
  void updateFacilityReviewStats(@Param("facilityId") Integer facilityId);

  @Modifying
  @Transactional
  @Query(value = """
      UPDATE facilities f SET
        wait_time_count = (
          SELECT COUNT(*)
          FROM facility_wait_times
          WHERE facility_id = f.facility_id
        ),
        avg_wait_time = (
          SELECT AVG(wait_duration_minutes)
          FROM facility_wait_times
          WHERE facility_id = f.facility_id
        )
      WHERE facility_id = :facilityId
      """, nativeQuery = true)
  void updateWaitTimeCountAndAvg(@Param("facilityId") Integer facilityId);

}
