package com.bulkloads.web.facility.repository;

import java.util.List;
import com.bulkloads.web.facility.domain.entity.FacilitySearch;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface FacilitySearchRepository extends JpaRepository<FacilitySearch, Integer> {

  FacilitySearch findByUserIdAndLocation(int userId, final String location);

  List<FacilitySearch> findTop10ByUserIdOrderBySearchDateDesc(final int userId);
}