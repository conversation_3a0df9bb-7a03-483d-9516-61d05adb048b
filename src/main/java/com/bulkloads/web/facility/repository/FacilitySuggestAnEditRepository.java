package com.bulkloads.web.facility.repository;

import java.util.List;
import java.util.Optional;
import com.bulkloads.web.facility.domain.entity.FacilitySuggestAnEdit;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface FacilitySuggestAnEditRepository extends JpaRepository<FacilitySuggestAnEdit, Integer> {

  List<FacilitySuggestAnEdit> findByDeletedFalse();

  Optional<FacilitySuggestAnEdit> findByFacilitySuggestAnEditIdAndFacilityIdAndDeletedFalse(
      int facilitySuggestAnEditId,
      int facilityId
  );
}