package com.bulkloads.web.facility.api;

import static com.bulkloads.config.AppConstants.UserRole.ROLE_SITE_ADMIN;
import static com.bulkloads.config.AppConstants.UserRole.ROLE_USER;
import java.util.List;
import com.bulkloads.common.api.ApiResponse;
import com.bulkloads.web.facility.service.FacilitySuggestAnEditService;
import com.bulkloads.web.facility.service.dto.FacilitySuggestAnEditRequest;
import com.bulkloads.web.facility.service.dto.FacilitySuggestAnEditResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Positive;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequestMapping(path = "/rest/facilities")
@Tag(name = "Facilities")
@CrossOrigin(origins = "*", maxAge = 3600)
@RequiredArgsConstructor
@Validated
public class FacilitySuggestAnEditController {

  private final FacilitySuggestAnEditService facilitySuggestAnEditService;

  @Operation(summary = "Get facility edit suggestions")
  @GetMapping("/suggest_an_edit")
  @PreAuthorize("hasRole('" + ROLE_SITE_ADMIN + "')")
  public List<FacilitySuggestAnEditResponse> getFacilityEditSuggestions() {
    return facilitySuggestAnEditService.findAll();
  }

  @Operation(summary = "Suggest an edit for a facility")
  @PostMapping("/{facility_id}/suggest_an_edit")
  @PreAuthorize("hasRole('" + ROLE_USER + "')")
  public ApiResponse<Void, Integer> suggestFacilityEdit(
      @PathVariable("facility_id") @Positive final int facilityId,
      @Valid @RequestBody final FacilitySuggestAnEditRequest request) {
    int id = facilitySuggestAnEditService.create(facilityId, request);
    return ApiResponse.<Void, Integer>builder()
        .key(id)
        .message("Facility edit suggestion created")
        .build();
  }

  @Operation(summary = "Delete a facility edit suggestion")
  @DeleteMapping("/{facility_id}/suggest_an_edit/{suggest_edit_id}")
  @PreAuthorize("hasRole('" + ROLE_SITE_ADMIN + "')")
  public ApiResponse<Void, Void> deleteFacilityEditSuggestion(
      @PathVariable("facility_id") @Positive final int facilityId,
      @PathVariable("suggest_edit_id") @Positive final int suggestEditId) {
    facilitySuggestAnEditService.delete(facilityId, suggestEditId);
    return ApiResponse.<Void, Void>builder()
        .message("Facility edit suggestion deleted")
        .build();
  }
}