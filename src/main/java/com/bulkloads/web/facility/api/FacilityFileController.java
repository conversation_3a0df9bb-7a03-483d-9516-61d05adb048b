package com.bulkloads.web.facility.api;

import static com.bulkloads.config.AppConstants.UserRole.ROLE_SITE_ADMIN;
import static com.bulkloads.config.AppConstants.UserRole.ROLE_USER;
import java.util.List;
import com.bulkloads.common.api.ApiResponse;
import com.bulkloads.web.facility.service.FacilityFileService;
import com.bulkloads.web.facility.service.dto.FacilityFileRequest;
import com.bulkloads.web.facility.service.dto.FacilityFileResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Positive;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequestMapping(path = "/rest/facilities")
@Tag(name = "Facilities")
@CrossOrigin(origins = "*", maxAge = 3600)
@RequiredArgsConstructor
@Validated
@PreAuthorize("hasRole('" + ROLE_USER + "')")
public class FacilityFileController {

  private final FacilityFileService facilityFileService;

  @Operation(summary = "Add Facility File")
  @PostMapping("/{facility_id}/files")
  public ApiResponse<Void, Void> createFacilityFile(
      @PathVariable("facility_id") final int facilityId,
      @Valid @RequestBody final FacilityFileRequest dto) {
    facilityFileService.create(facilityId, dto);

    return ApiResponse.<Void, Void>builder()
        .message("Facility File created")
        .build();
  }

  @Operation(summary = "Get Facility Files")
  @GetMapping("/{facility_id}/files")
  public List<FacilityFileResponse> getFacilityFiles(@PathVariable("facility_id") final int facilityId) {
    return facilityFileService.findByFacilityId(facilityId);
  }

  @Operation(summary = "Delete Facility File")
  @DeleteMapping("/{facility_id}/files/{file_id}")
  @PreAuthorize("hasRole('" + ROLE_SITE_ADMIN + "')")
  public ApiResponse<Void, Void> deleteFacilityFile(
      @PathVariable("facility_id") @Positive final int facilityId,
      @PathVariable("file_id") @Positive final int fileId) {
    facilityFileService.delete(facilityId, fileId);

    return ApiResponse.<Void, Void>builder()
        .message("Facility File deleted")
        .build();
  }

  @Operation(summary = "Get Unapproved Facility Files")
  @GetMapping("/files/unapproved")
  @PreAuthorize("hasRole('" + ROLE_SITE_ADMIN + "')")
  public List<FacilityFileResponse> getUnapprovedFiles() {
    return facilityFileService.findUnapprovedFiles();
  }

  @Operation(summary = "Approve Facility File")
  @PutMapping("/{facility_id}/files/{file_id}")
  @PreAuthorize("hasRole('" + ROLE_SITE_ADMIN + "')")
  public ApiResponse<Void, Void> approveFacilityFile(
      @PathVariable("facility_id") @Positive final int facilityId,
      @PathVariable("file_id") @Positive final int fileId) {
    facilityFileService.approve(facilityId, fileId);
    return ApiResponse.<Void, Void>builder()
        .message("Facility File updated")
        .build();
  }
}