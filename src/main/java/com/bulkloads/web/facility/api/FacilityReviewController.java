package com.bulkloads.web.facility.api;

import static com.bulkloads.config.AppConstants.UserRole.ROLE_SITE_ADMIN;
import static com.bulkloads.config.AppConstants.UserRole.ROLE_USER;
import com.bulkloads.common.api.ApiResponse;
import com.bulkloads.web.facility.service.FacilityReviewService;
import com.bulkloads.web.facility.service.dto.FacilityResponse;
import com.bulkloads.web.facility.service.dto.FacilityReviewRequest;
import com.bulkloads.web.facility.service.dto.FacilityReviewResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Positive;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequestMapping(path = "/rest/facilities/{facility_id}")
@Tag(name = "Facilities")
@CrossOrigin(origins = "*", maxAge = 3600)
@RequiredArgsConstructor
@Validated
@PreAuthorize("hasRole('" + ROLE_USER + "')")
public class FacilityReviewController {

  private final FacilityReviewService facilityReviewService;

  @Operation(summary = "Create Facility Review")
  @PostMapping("/my_review")
  public ApiResponse<FacilityReviewResponse, Integer> createFacilityReview(
      @PathVariable("facility_id") final int facilityId,
      @Valid @RequestBody final FacilityReviewRequest dto) {
    final int id = facilityReviewService.create(facilityId, dto);

    return ApiResponse.<FacilityReviewResponse, Integer>builder()
        .key(id)
        .message("Facility Review created")
        .build();
  }

  @Operation(summary = "Update Facility Review")
  @PutMapping("/my_review")
  public ApiResponse<FacilityResponse, Integer> updateFacilityReview(
      @PathVariable("facility_id") @Positive final int facilityId,
      @Valid @RequestBody final FacilityReviewRequest dto) {
    facilityReviewService.update(facilityId, dto);
    return ApiResponse.<FacilityResponse, Integer>builder()
       .message("Facility Review updated")
        .build();
  }

  @Operation(summary = "Delete Facility Review")
  @DeleteMapping("/my_review")
  public ApiResponse<FacilityResponse, Integer> deleteFacilityReview(
      @PathVariable("facility_id") @Positive final int facilityId) {
    facilityReviewService.delete(facilityId);
    return ApiResponse.<FacilityResponse, Integer>builder()
        .message("Facility Review deleted")
        .build();
  }


  @PreAuthorize("hasRole('" + ROLE_SITE_ADMIN + "')")
  @Operation(summary = "Admin: Update/Approve Facility Review")
  @PutMapping("/reviews/{facility_review_id}")
  public ApiResponse<Void, Void> updateFacilityReviewAdmin(
      @PathVariable("facility_id") @Positive final int facilityId,
      @PathVariable("facility_review_id") @Positive final int facilityReviewId,
      @Valid @RequestBody final FacilityReviewRequest request) {
    facilityReviewService.updateByAdmin(facilityId, facilityReviewId, request);
    return ApiResponse.<Void, Void>builder()
        .message("Facility Review updated")
        .build();
  }

  @PreAuthorize("hasRole('" + ROLE_SITE_ADMIN + "')")
  @Operation(summary = "Admin: Delete Facility Review")
  @DeleteMapping("/reviews/{facility_review_id}")
  public ApiResponse<Void, Void> deleteFacilityReviewAdmin(
      @PathVariable("facility_id") @Positive final int facilityId,
      @PathVariable("facility_review_id") @Positive final int facilityReviewId) {
    facilityReviewService.deleteByAdmin(facilityId, facilityReviewId);
    return ApiResponse.<Void, Void>builder()
        .message("Facility Review deleted")
        .build();
  }
}
