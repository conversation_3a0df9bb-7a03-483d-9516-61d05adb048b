package com.bulkloads.web.facility.api;

import static com.bulkloads.config.AppConstants.UserRole.ROLE_SITE_ADMIN;
import static com.bulkloads.config.AppConstants.UserRole.ROLE_USER;
import com.bulkloads.common.api.ApiResponse;
import com.bulkloads.web.facility.service.FacilityService;
import com.bulkloads.web.facility.service.dto.FacilityRequest;
import com.bulkloads.web.facility.service.dto.FacilityResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Positive;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequestMapping(path = "/rest/facilities")
@Tag(name = "Facilities")
@CrossOrigin(origins = "*", maxAge = 3600)
@RequiredArgsConstructor
@Validated
public class FacilityController {

  private final FacilityService facilityService;

  @PreAuthorize("hasRole('" + ROLE_USER + "')")
  @Operation(summary = "Create Facility")
  @PostMapping
  public ApiResponse<FacilityResponse, Integer> createFacility(
      @Valid @RequestBody final FacilityRequest dto) {
    final int facilityId = facilityService.create(dto);
    return ApiResponse.<FacilityResponse, Integer>builder()
        .key(facilityId)
        .message("Facility created")
        .build();
  }

  @PreAuthorize("hasRole('" + ROLE_SITE_ADMIN + "')")
  @Operation(summary = "Admin: Update/Approve facility")
  @PutMapping("/{facility_id}")
  public ApiResponse<FacilityResponse, Integer> updateFacility(
      @PathVariable("facility_id") @Positive final int facilityId,
      @Valid @RequestBody final FacilityRequest dto) {
    facilityService.update(facilityId, dto);
    return ApiResponse.<FacilityResponse, Integer>builder()
        .key(facilityId)
        .message("Facility updated")
        .build();
  }

  @PreAuthorize("hasRole('" + ROLE_SITE_ADMIN + "')")
  @Operation(summary = "Admin: Delete Facility")
  @DeleteMapping("/{facility_id}")
  public ApiResponse<FacilityResponse, Integer> deleteFacility(
      @PathVariable("facility_id") @Positive final int facilityId) {
    facilityService.deleteById(facilityId);
    return ApiResponse.<FacilityResponse, Integer>builder()
        .key(facilityId)
        .message("Facility deleted")
        .build();
  }

}
