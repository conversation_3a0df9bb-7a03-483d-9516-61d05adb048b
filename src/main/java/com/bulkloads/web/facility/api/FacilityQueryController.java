package com.bulkloads.web.facility.api;

import static com.bulkloads.config.AppConstants.UserRole.ROLE_SITE_ADMIN;
import static com.bulkloads.config.AppConstants.UserRole.ROLE_USER;
import java.util.List;
import com.bulkloads.web.facility.service.FacilityService;
import com.bulkloads.web.facility.service.dto.FacilityListResponse;
import com.bulkloads.web.facility.service.dto.FacilityResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.PositiveOrZero;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequestMapping(path = "/rest/facilities")
@Tag(name = "Facilities")
@CrossOrigin(origins = "*", maxAge = 3600)
@RequiredArgsConstructor
@PreAuthorize("hasRole('" + ROLE_USER + "')")
public class FacilityQueryController {

  private final FacilityService facilityService;

  @Operation(summary = "Get facilities")
  @GetMapping
  public List<FacilityListResponse> findFacilities(
      @Parameter(description = "The location.")
      @RequestParam(value = "location", required = false) final String location,
      @Parameter(description = "Latitude for radius search")
      @RequestParam(value = "lat", required = false) final Double latitude,
      @Parameter(description = "Longitude for radius search")
      @RequestParam(value = "long", required = false) final Double longitude,
      @Parameter(description = "Radius in miles for location search")
      @RequestParam(value = "radius", required = false) Double radius,
      @Parameter(description = "Comma-separated list of facility IDs")
      @RequestParam(value = "facility_ids", required = false) List<Integer> facilityIds,
      @Parameter(description = "the number of records to skip, defaults to 0")
      @PositiveOrZero(message = "Must be 0 or positive")
      @RequestParam(value = "skip", defaultValue = "0") int skip,
      @Parameter(description = "the number of records to return, defaults to 100")
      @PositiveOrZero(message = "Must be 0 or positive")
      @RequestParam(value = "limit", defaultValue = "100") int limit) {
    return facilityService.findFacilities(location, latitude, longitude, radius, facilityIds, skip, limit);
  }

  @Operation(summary = "Get facility Details")
  @GetMapping("/{facility_id}")
  public FacilityResponse findByFacilityId(@PathVariable("facility_id") Integer facilityId) {
    return facilityService.findByFacilityId(facilityId);
  }

  @Operation(summary = "Get facility searches by user")
  @GetMapping("/searches")
  public List<String> findFacilitySearches() {
    return facilityService.findFacilitySearches();
  }

  @Operation(summary = "Get unapproved facilities")
  @GetMapping("/unapproved")
  @PreAuthorize("hasRole('" + ROLE_SITE_ADMIN + "')")
  public List<FacilityListResponse> getUnapprovedFacilities() {
    return facilityService.findUnapprovedFacilities();
  }
}
