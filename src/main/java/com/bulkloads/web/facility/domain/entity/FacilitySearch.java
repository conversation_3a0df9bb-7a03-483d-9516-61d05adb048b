package com.bulkloads.web.facility.domain.entity;

import java.time.Instant;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "facility_searches")
@Getter
@Setter
public class FacilitySearch {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "facility_search_id")
  private Integer facilitySearchId;

  @Column(name = "user_id")
  private Integer userId;

  @Column(name = "location")
  private String location;

  @Column(name = "search_date")
  private Instant searchDate;

}