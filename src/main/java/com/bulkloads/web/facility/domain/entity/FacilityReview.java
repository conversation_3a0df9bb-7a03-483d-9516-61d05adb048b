package com.bulkloads.web.facility.domain.entity;

import java.time.Instant;
import com.bulkloads.web.user.domain.entity.User;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "facility_reviews")
@Getter
@Setter
public class FacilityReview {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "facility_review_id")
  private Integer facilityReviewId;

  @NotNull
  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "user_id", nullable = false)
  private User user;

  @NotNull
  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "facility_id", nullable = false)
  private Facility facility;

  @NotNull(message = "Review cannot be null")
  @Column(name = "review", nullable = false)
  private String review;

  @NotNull(message = "Rating cannot be null")
  @Column(name = "rating", nullable = false)
  private Integer rating;

  @Column(name = "review_date")
  private Instant reviewDate;

  @Column(name = "approved", nullable = false)
  private Boolean approved = false;

}