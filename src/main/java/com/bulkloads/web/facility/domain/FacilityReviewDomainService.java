package com.bulkloads.web.facility.domain;

import java.time.Instant;
import com.bulkloads.common.BaseDomainService;
import com.bulkloads.common.validation.Result;
import com.bulkloads.common.validation.ValidationMethod;
import com.bulkloads.web.facility.domain.data.FacilityReviewData;
import com.bulkloads.web.facility.domain.entity.FacilityReview;
import com.bulkloads.web.facility.mapper.FacilityReviewMapper;
import com.bulkloads.web.user.domain.entity.User;
import com.bulkloads.web.user.service.UserService;
import org.springframework.stereotype.Component;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
@RequiredArgsConstructor
public class FacilityReviewDomainService extends BaseDomainService<FacilityReview> {

  private final FacilityReviewMapper facilityReviewMapper;
  private final UserService userService;

  public Result<FacilityReview> create(FacilityReviewData data) {
    final FacilityReview facilityReview = new FacilityReview();

    return super.validate(facilityReview, null, data, ValidationMethod.CREATE);
  }

  public Result<FacilityReview> update(FacilityReview facilityReview, FacilityReviewData data) {
    return validate(facilityReview, null, data, ValidationMethod.UPDATE);
  }

  @Override
  public void validateDataAndMapToEntity(Result<FacilityReview> result, FacilityReview entity,
                                         FacilityReview existing, Object facilityReviewData, ValidationMethod method) {
    FacilityReviewData data = (FacilityReviewData) facilityReviewData;

    final User user = userService.getLoggedInUser();
    final Instant now = Instant.now();
    entity.setUser(user);
    entity.setReviewDate(now);

  }

  public void mapToEntityAuto(Object data, FacilityReview entity) {
    facilityReviewMapper.dataToEntity((FacilityReviewData) data, entity);
  }

  @Override
  public void validateEntity(Result<FacilityReview> result, FacilityReview entity) {

  }


}
