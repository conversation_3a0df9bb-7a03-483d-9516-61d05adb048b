package com.bulkloads.web.facility.service.dto;

import java.time.Instant;
import java.util.List;
import com.bulkloads.common.jackson.CsvSerializer;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;

@Data
public class FacilityResponse {

  private Integer facilityId;
  private String name;
  private String location;
  private String city;
  private String state;
  private String address;
  private String zip;
  private String country;
  private Double longitude;
  private Double latitude;
  private String website;
  private String phone1;
  private String email;
  private Integer totalReviews;
  private Double avgRating;
  private Integer viewCount;
  private Boolean publicRestrooms;
  private Boolean overnightParking;
  private Boolean driverLounge;
  private Boolean onsiteScale;
  private Boolean appointmentRequired;
  private Boolean washoutRequired;
  private Boolean approved;
  private String hoursOfOperation;
  @JsonSerialize(using = CsvSerializer.class)
  private List<String> equipmentIds;
  @JsonSerialize(using = CsvSerializer.class)
  private List<String> equipmentNames;

  private Integer waitTimeCount;
  private Integer minWaitTime;
  private Integer maxWaitTime;

  private Integer minTodayWaitTime;
  private Integer maxTodayWaitTime;
  
  private Instant updatedDate;

}

