package com.bulkloads.web.facility.service;

import java.util.List;
import com.bulkloads.exception.BulkloadsException;
import com.bulkloads.exception.ValidationException;
import com.bulkloads.web.facility.domain.entity.Facility;
import com.bulkloads.web.facility.domain.entity.FacilityFile;
import com.bulkloads.web.facility.domain.entity.FacilityFileId;
import com.bulkloads.web.facility.repository.FacilityFileRepository;
import com.bulkloads.web.facility.repository.FacilityRepository;
import com.bulkloads.web.facility.service.dto.FacilityFileRequest;
import com.bulkloads.web.facility.service.dto.FacilityFileResponse;
import com.bulkloads.web.file.domain.entity.File;
import com.bulkloads.web.file.repository.FileRepository;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;

@Service
@Validated
@RequiredArgsConstructor
public class FacilityFileService {

  private final FacilityRepository facilityRepository;
  private final FileRepository fileRepository;
  private final FacilityFileRepository facilityFileRepository;

  @Transactional
  public int create(final int facilityId, @Valid final FacilityFileRequest request) {
    Facility facility = facilityRepository.findById(facilityId)
        .orElseThrow(() -> new BulkloadsException("Facility not found"));

    File file = fileRepository.findById(request.getFileId())
        .orElseThrow(() -> new BulkloadsException("File not found"));

    // Create FacilityFile entity directly to control approval status
    FacilityFileId facilityFileId = new FacilityFileId(facilityId, request.getFileId());
    FacilityFile facilityFile = new FacilityFile();
    facilityFile.setId(facilityFileId);
    facilityFile.setFacility(facility);
    facilityFile.setFile(file);
    facilityFile.setApproved(false); // New files start as unapproved
    
    facilityFileRepository.save(facilityFile);

    return request.getFileId();
  }

  @Transactional(readOnly = true)
  public List<FacilityFileResponse> findByFacilityId(final int facilityId) {
    return facilityRepository.findTop5FilesByFacilityId(facilityId)
        .stream()
        .map(p -> new FacilityFileResponse(
            facilityId, 
            p.getFileId(),
            p.getFileUrl(),
            p.getThumbUrl()))
        .toList();
  }

  @Transactional
  public void delete(final int facilityId, final int fileId) {
    Facility facility = facilityRepository.findByFacilityIdAndDeletedFalse(facilityId)
        .orElseThrow(() -> new ValidationException("facility_id", "Could not find facility with id %s".formatted(facilityId)));

    File file = fileRepository.findById(fileId)
        .orElseThrow(() -> new ValidationException("file_id", "Could not find file with id %s".formatted(fileId)));

    if (!facility.getFiles().remove(file)) {
      throw new ValidationException("file_id",
          "File with id %s is not associated with facility %s".formatted(fileId, facilityId));
    }

    facilityRepository.save(facility);
  }

  @Transactional(readOnly = true)
  public List<FacilityFileResponse> findUnapprovedFiles() {
    return facilityRepository.findUnapprovedFiles()
        .stream()
        .map(f -> new FacilityFileResponse(
            f.getFacilityId(),
            f.getFileId(),
            f.getFileUrl(),
            f.getThumbUrl()))
        .toList();
  }

  @Transactional
  public void approve(final int facilityId, final int fileId) {
    facilityRepository.findByFacilityIdAndDeletedFalse(facilityId)
        .orElseThrow(() -> new ValidationException("facility_id", 
            "Could not find facility with id %s".formatted(facilityId)));

    fileRepository.findById(fileId)
        .orElseThrow(() -> new ValidationException("file_id", 
            "Could not find file with id %s".formatted(fileId)));

    FacilityFileId facilityFileId = new FacilityFileId(facilityId, fileId);
    FacilityFile facilityFile = facilityFileRepository.findById(facilityFileId)
        .orElseThrow(() -> new ValidationException("file_id",
            "File with id %s is not associated with facility %s".formatted(fileId, facilityId)));

    facilityFile.setApproved(true);
    facilityFileRepository.save(facilityFile);
  }
}
