package com.bulkloads.web.facility.service.dto;

import java.util.List;
import java.util.Optional;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FacilityRequest {

  private Optional<String> name;
  private Optional<String> location;
  private Optional<String> address;
  private Optional<Double> longitude;
  private Optional<Double> latitude;
  private Optional<String> hoursOfOperation;
  private Optional<String> phone1;
  private Optional<String> email;
  private Optional<String> website;
  private Optional<Boolean> publicRestrooms;
  private Optional<Boolean> overnightParking;
  private Optional<Boolean> driverLounge;
  private Optional<Boolean> onsiteScale;
  private Optional<Boolean> appointmentRequired;
  private Optional<Boolean> washoutRequired;
  private Optional<String> equipmentIds;
  private Optional<Boolean> approved;
  private List<Integer> fileIds;

}
