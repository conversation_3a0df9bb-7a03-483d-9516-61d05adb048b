package com.bulkloads.web.phonenumber.repository;

import static com.bulkloads.web.phonenumber.repository.template.GetPhoneNumberQueryTemplate.GET_PHONE_NUMBER_QUERY_TEMPLATE;
import java.util.HashMap;
import java.util.Map;
import com.bulkloads.common.jpa.nativejpa.JpaNativeQueryService;
import com.bulkloads.web.phonenumber.service.dto.PhoneNumberResponse;
import com.bulkloads.web.phonenumber.service.dto.transformer.PhoneNumberResponseTransformer;
import org.springframework.stereotype.Repository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Repository
@RequiredArgsConstructor
public class PhoneNumberQueryRepository {

  private static final int CACHE_VALID_DAYS = 180;

  private final JpaNativeQueryService jpaNativeQueryService;
  private final PhoneNumberResponseTransformer phoneNumberResponseTransformer;

  public PhoneNumberResponse getPhoneNumber(String phoneNumber) {
    Map<String, Object> params = new HashMap<>();

    params.put("phoneNumber", phoneNumber);
    params.put("cacheValidDays", CACHE_VALID_DAYS);

    return jpaNativeQueryService.queryForObject(
        GET_PHONE_NUMBER_QUERY_TEMPLATE,
        params,
        phoneNumberResponseTransformer
    );
  }
}
