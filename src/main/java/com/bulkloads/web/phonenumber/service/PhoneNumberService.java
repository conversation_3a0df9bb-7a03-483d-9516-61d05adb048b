package com.bulkloads.web.phonenumber.service;

import java.time.Duration;
import java.time.Instant;
import com.bulkloads.web.phonenumber.domain.CachedPhoneNumber;
import com.bulkloads.web.phonenumber.repository.PhoneNumberRepository;
import com.bulkloads.web.phonenumber.service.exception.InvalidNumberException;
import com.google.i18n.phonenumbers.NumberParseException;
import com.google.i18n.phonenumbers.PhoneNumberUtil;
import com.google.i18n.phonenumbers.Phonenumber;
import com.twilio.rest.lookups.v1.PhoneNumber;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RequiredArgsConstructor
public class PhoneNumberService {

  final PhoneNumberRepository phoneNumberRepository;
  final TwilioService twilioService;

  public PhoneNumberDetails validate(String phoneNumber) throws InvalidNumberException {

    PhoneNumberDetails numberDetails = validateLocally(phoneNumber);

    final long number = Long.parseLong(numberDetails.getPhoneNumber());
    final Instant date = Instant.now().minus(Duration.ofDays(180));
    CachedPhoneNumber searchNumber = phoneNumberRepository.getCachedPhoneNumberAndDateAddedIsAfter(number, date);

    if (searchNumber != null) {
      numberDetails.setStdFormat(searchNumber.getStdFormat());
      numberDetails.setCarrierName(searchNumber.getCarrierName());
      numberDetails.setCarrierType(searchNumber.getCarrierType());
      return numberDetails;
    }

    validateAndCache(numberDetails);
    return numberDetails;
  }

  public PhoneNumberDetails validateLocally(String phoneNumber) throws InvalidNumberException {

    final PhoneNumberUtil util = PhoneNumberUtil.getInstance();
    final PhoneNumberDetails numberDetails = new PhoneNumberDetails();

    try {
      final Phonenumber.PhoneNumber parsedNumber = util.parse(phoneNumber, "US");

      if (!util.isValidNumber(parsedNumber)) {
        throw new InvalidNumberException("Invalid Number");
      }
      numberDetails.setPhoneNumber(String.valueOf(parsedNumber.getNationalNumber()));
      return numberDetails;

    } catch (NumberParseException e) {
      throw new InvalidNumberException(e.getMessage());
    }
  }

  public void validateAndCache(PhoneNumberDetails phone) {
    PhoneNumber validatedNumber = twilioService.validate(phone.getPhoneNumber());

    CachedPhoneNumber numberToCache = new CachedPhoneNumber();
    numberToCache.setPhoneNumber(Long.valueOf(phone.getPhoneNumber()));
    numberToCache.setDateAdded(Instant.now());

    if (validatedNumber != null) {

      String carrierName = (String) validatedNumber.getCarrier().get("name");
      String carrierType = (String) validatedNumber.getCarrier().get("type");

      numberToCache.setStdFormat(validatedNumber.getPhoneNumber().toString());
      numberToCache.setCarrierName(carrierName != null ? carrierName : "");
      numberToCache.setCarrierType(carrierType != null ? carrierType : "");
      numberToCache.setErrorCode("");
      numberToCache.setErrorMessage("");

      phone.setCarrierName(carrierName);
      phone.setCarrierType(carrierType);
      phone.setStdFormat(validatedNumber.getPhoneNumber().toString());

    } else {
      numberToCache.setErrorCode("20404");
      numberToCache.setErrorMessage("Not Found");

      phone.setCarrierName("");
      phone.setCarrierType("not found");
    }

    phoneNumberRepository.save(numberToCache);

  }

}
