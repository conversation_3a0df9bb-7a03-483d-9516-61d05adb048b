package com.bulkloads.web.phonenumber.service;

import java.util.List;
import com.bulkloads.config.AppProperties;
import com.twilio.exception.ApiException;
import com.twilio.http.TwilioRestClient;
import com.twilio.rest.lookups.v1.PhoneNumber;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class TwilioService {

  private final TwilioRestClient twilioClient;

  public TwilioService(AppProperties appProperties) {
    final AppProperties.Twilio twilio = appProperties.getTwilio();
    final String sid = twilio.getSid();
    final String token = twilio.getToken();
    twilioClient = new TwilioRestClient.Builder(sid, token).build();
  }

  public PhoneNumber validate(String number) {
    try {
      log.trace("Validating number with twilio: {}", number);
      return PhoneNumber.fetcher(new com.twilio.type.PhoneNumber(number))
          .setType(List.of("carrier"))
          .fetch(twilioClient);

    } catch (ApiException e) {
      log.warn("Could not validate phone number: {}, error: {}", number, e.getMessage());
      return null;
    }
  }
}