package com.bulkloads.web.quickbooks.service;

import static com.bulkloads.common.validation.ValidationUtils.existsAndIsNotEmpty;
import static com.bulkloads.web.quickbooks.api.dto.ChargeType.surcharge;
import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import com.bulkloads.common.UserUtil;
import com.bulkloads.config.AppProperties;
import com.bulkloads.exception.ValidationException;
import com.bulkloads.web.addressbook.abcompany.domain.entity.AbCompany;
import com.bulkloads.web.addressbook.abcompany.repository.AbCompanyRepository;
import com.bulkloads.web.infra.messaging.consumer.MessageQueueSender;
import com.bulkloads.web.infra.quickbooks.dto.BillResponse;
import com.bulkloads.web.infra.quickbooks.dto.CompanyInfoDto;
import com.bulkloads.web.infra.quickbooks.dto.CreateBillDto;
import com.bulkloads.web.infra.quickbooks.dto.CreateInvoiceDto;
import com.bulkloads.web.infra.quickbooks.dto.CustomerDto;
import com.bulkloads.web.infra.quickbooks.dto.InvoiceItemDto;
import com.bulkloads.web.infra.quickbooks.dto.InvoiceResponse;
import com.bulkloads.web.infra.quickbooks.dto.ItemDto;
import com.bulkloads.web.infra.quickbooks.dto.ReviseInvoiceDto;
import com.bulkloads.web.infra.quickbooks.dto.VendorDto;
import com.bulkloads.web.infra.quickbooks.dto.VoidInvoiceDto;
import com.bulkloads.web.infra.quickbooks.quickbooks.QbFacade;
import com.bulkloads.web.loadinvoice.domain.entity.LoadInvoice;
import com.bulkloads.web.loadinvoice.domain.entity.LoadInvoiceItem;
import com.bulkloads.web.loadinvoice.repository.LoadInvoiceRepository;
import com.bulkloads.web.quickbooks.api.dto.ChargeType;
import com.bulkloads.web.quickbooks.api.dto.CreateQbBillRequest;
import com.bulkloads.web.quickbooks.api.dto.CreateQbInvoiceRequest;
import com.bulkloads.web.quickbooks.api.dto.QbAbCompanyResponse;
import com.bulkloads.web.quickbooks.api.dto.QbItemSurchargeTypeResponse;
import com.bulkloads.web.quickbooks.api.dto.QbLoadAssignmentSurchargeLinkRequest;
import com.bulkloads.web.quickbooks.domain.entity.QbAbCompany;
import com.bulkloads.web.quickbooks.domain.entity.QbBill;
import com.bulkloads.web.quickbooks.domain.entity.QbInvoice;
import com.bulkloads.web.quickbooks.domain.entity.QbLoadAssignmentSurchargeType;
import com.bulkloads.web.quickbooks.mapper.QbAbCompanyMapper;
import com.bulkloads.web.quickbooks.mapper.QbInvoiceBillMapper;
import com.bulkloads.web.quickbooks.mapper.QbItemMapper;
import com.bulkloads.web.quickbooks.repository.QbAbCompanyProjection;
import com.bulkloads.web.quickbooks.repository.QbAbCompanyRepository;
import com.bulkloads.web.quickbooks.repository.QbBillRepository;
import com.bulkloads.web.quickbooks.repository.QbInvoiceRepository;
import com.bulkloads.web.quickbooks.repository.QbItemSurchargeTypeProjection;
import com.bulkloads.web.quickbooks.repository.QbLoadAssignmentSurchargeTypeRepository;
import com.bulkloads.web.usercompany.repository.UserCompanyRepository;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class QbService {

  private final AbCompanyRepository abCompanyRepository;
  private final UserCompanyRepository userCompanyRepository;
  private final QbLoadAssignmentSurchargeTypeRepository chargeQbLinkRepository;
  private final QbAbCompanyRepository qbAbCompanyRepository;
  private final QbInvoiceRepository qbInvoiceRepository;
  private final QbBillRepository qbBillRepository;
  private final LoadInvoiceRepository loadInvoiceRepository;
  private final MessageQueueSender messageQueueSender;

  private final QbAbCompanyMapper qbAbCompanyMapper;
  private final QbItemMapper qbItemMapper;
  private final QbInvoiceBillMapper qbInvoiceMapper;

  private final QbFacade qbFacade;
  private final AppProperties appProperties;

  public CompanyInfoDto findCompanyInfo() {
    return qbFacade.findCompanyInfo();
  }

  public List<CustomerDto> findAllCustomers() {
    return qbFacade.findAllCustomers();
  }

  public List<VendorDto> findAllVendors() {
    return qbFacade.findAllVendors();
  }

  public List<ItemDto> findAllItems() {
    return qbFacade.findAllItems();
  }

  @Transactional
  public String createInvoice(final CreateQbInvoiceRequest request) {
    final CreateInvoiceDto createInvoiceDto = buildCreateInvoiceDto(request);
    final String qbInvoiceId = qbFacade.createInvoice(createInvoiceDto);
    final QbInvoice qbInvoice = new QbInvoice();
    qbInvoice.setLoadInvoiceId(request.getLoadInvoiceId());
    qbInvoice.setQbInvoiceId(qbInvoiceId);
    qbInvoiceRepository.save(qbInvoice);
    return qbInvoiceId;
  }

  @Transactional
  public String createBill(final CreateQbBillRequest request) {
    final CreateBillDto createBillDto = buildCreateBillDto(request);
    final String qbInvoiceId = qbFacade.createBill(createBillDto);
    final QbBill qbBill = new QbBill();
    qbBill.setLoadInvoiceId(request.getLoadInvoiceId());
    qbBill.setQbBillId(qbInvoiceId);
    qbBillRepository.save(qbBill);
    return qbInvoiceId;
  }

  public void createInvoiceAsync(final CreateQbInvoiceRequest request) {
    final CreateInvoiceDto createInvoiceDto = buildCreateInvoiceDto(request);
    messageQueueSender.send(appProperties.getQuickBooks().getQueueName(), createInvoiceDto);
  }

  @Transactional
  public void voidInvoice(final int loadInvoiceId) {
    final QbInvoice qbInvoice = qbInvoiceRepository.getReferenceById(loadInvoiceId);
    qbFacade.voidInvoice(qbInvoice.getQbInvoiceId());
    qbInvoiceRepository.deleteById(loadInvoiceId);
  }

  public void voidInvoiceAsync(final int loadInvoiceId) {
    final VoidInvoiceDto voidInvoiceDto = VoidInvoiceDto.builder()
        .loadInvoiceId(loadInvoiceId)
        .userId(UserUtil.getUserIdOrThrow())
        .build();
    messageQueueSender.send(appProperties.getQuickBooks().getQueueName(), voidInvoiceDto);
  }

  @Transactional
  public void reviseInvoice(final int revisedFromLoadInvoiceId, final CreateQbInvoiceRequest request) {
    final CreateInvoiceDto createInvoiceDto = buildCreateInvoiceDto(request);
    final QbInvoice qbInvoice = qbInvoiceRepository.getReferenceById(revisedFromLoadInvoiceId);
    qbFacade.voidInvoice(qbInvoice.getQbInvoiceId());
    final String qbInvoiceId = qbFacade.createInvoice(createInvoiceDto);
    qbInvoice.setQbInvoiceId(qbInvoiceId);
    qbInvoiceRepository.save(qbInvoice);
  }

  public void reviseInvoiceAsync(final int revisedFromLoadInvoiceId, final CreateQbInvoiceRequest request) {
    final CreateInvoiceDto createInvoiceDto = buildCreateInvoiceDto(request);
    final ReviseInvoiceDto reviseInvoiceDto = ReviseInvoiceDto.builder()
        .revisedFromLoadInvoiceId(revisedFromLoadInvoiceId)
        .createInvoiceDto(createInvoiceDto)
        .build();
    messageQueueSender.send(appProperties.getQuickBooks().getQueueName(), reviseInvoiceDto);
  }

  @Transactional
  public void linkQbCustomer(final int abCompanyId, final String qbCustomerId) {
    final AbCompany abCompany = getAbCompanyId(abCompanyId);
    final QbAbCompany link = getOrCreateAccountingLink(abCompany);
    link.setQbCustomerId(qbCustomerId);
  }

  @Transactional
  public void unlinkQbCustomer(final int abCompanyId, final String qbCustomerId) {
    final AbCompany abCompany = getAbCompanyId(abCompanyId);
    final QbAbCompany link = abCompany.getAccountingLink();
    if (nonNull(link) && qbCustomerId.equals(link.getQbCustomerId())) {
      link.setQbCustomerId(null);
      if (isNull(link.getQbVendorId())) {
        abCompany.setAccountingLink(null);
      }
    }
  }

  @Transactional
  public void linkQbVendor(final int abCompanyId, final String qbVendorId) {
    final AbCompany abCompany = getAbCompanyId(abCompanyId);
    final QbAbCompany link = getOrCreateAccountingLink(abCompany);
    link.setQbVendorId(qbVendorId);
  }

  @Transactional
  public void unlinkQbVendor(final int abCompanyId, final String qbVendorId) {
    final AbCompany abCompany = getAbCompanyId(abCompanyId);
    final QbAbCompany link = abCompany.getAccountingLink();
    if (nonNull(link) && qbVendorId.equals(link.getQbVendorId())) {
      link.setQbVendorId(null);
      if (isNull(link.getQbCustomerId())) {
        abCompany.setAccountingLink(null);
      }
    }
  }

  @Transactional
  public void linkQbItem(final String qbItemId, final QbLoadAssignmentSurchargeLinkRequest request) {
    final ChargeType chargeType = request.getChargeType();
    final QbLoadAssignmentSurchargeType link = getOrCreateChargeLink(request);
    link.setQbItemId(qbItemId);
    final Optional<Integer> surchargeTypeId = request.getSurchargeTypeId();
    if (chargeType == surcharge && existsAndIsNotEmpty(surchargeTypeId)) {
      link.setSurchargeTypeId(surchargeTypeId.orElseThrow());
    }
  }

  @Transactional
  public void unlinkQbItem(final String qbItemId, final QbLoadAssignmentSurchargeLinkRequest request) {
    final Optional<QbLoadAssignmentSurchargeType> link = findChargeLink(request, UserUtil.getUserCompanyIdOrThrow());
    if (link.isPresent() && link.get().getQbItemId().equals(qbItemId)) {
      chargeQbLinkRepository.delete(link.get());
    }
  }

  public List<InvoiceResponse> findNonPostedInvoices() {
    final int userCompanyId = UserUtil.getUserCompanyIdOrThrow();
    final List<LoadInvoice> invoices = loadInvoiceRepository.findAllNonQuickbooksPostedInvoices(userCompanyId);
    final Map<Integer, QbItemSurchargeTypeProjection> qbItemSurchargeTypeBySurchargeTypeId = collectQbItemSurchargeTypeBySurchargeTypeId(userCompanyId);
    final Map<Integer, QbAbCompany> qbMappingsByAbCompanyId = collectQbMappingsByAbCompanyId(userCompanyId);
    return qbInvoiceMapper.mapToInvoices(invoices, qbItemSurchargeTypeBySurchargeTypeId, qbMappingsByAbCompanyId);
  }

  public List<BillResponse> findNonPostedBills() {
    final int userCompanyId = UserUtil.getUserCompanyIdOrThrow();
    final List<LoadInvoice> invoices = loadInvoiceRepository.findAllNonQuickbooksPostedBills(userCompanyId);
    final Map<Integer, QbItemSurchargeTypeProjection> qbItemSurchargeTypeBySurchargeTypeId = collectQbItemSurchargeTypeBySurchargeTypeId(userCompanyId);
    final Map<Integer, QbAbCompany> qbMappingsByAbCompanyId = collectQbMappingsByAbCompanyId(userCompanyId);
    return qbInvoiceMapper.mapToBills(invoices, qbItemSurchargeTypeBySurchargeTypeId, qbMappingsByAbCompanyId);
  }

  private CreateBillDto buildCreateBillDto(final CreateQbBillRequest request) {
    final int loadInvoiceId = request.getLoadInvoiceId();
    final LoadInvoice loadInvoice = loadInvoiceRepository.getReferenceById(loadInvoiceId);
    final int userId = UserUtil.getUserIdOrThrow();
    final BigDecimal invoiceTotal = loadInvoice.getInvoiceTotal();
    final List<LoadInvoiceItem> loadInvoiceItems = loadInvoice.getLoadInvoiceItems();
    final String qbVendorId = getQbVendorId(loadInvoice);
    final Map<Integer, String> qbSurchargeTypeBySurchargeTypeId = getQbSurchargeTypeBySurchargeTypeId();
    final List<InvoiceItemDto> invoiceItemDtos = loadInvoiceItems.stream()
        .map(loadInvoiceItem -> toInvoiceItemDto(loadInvoiceItem, qbSurchargeTypeBySurchargeTypeId))
        .toList();
    return CreateBillDto.builder()
        .userId(userId)
        .loadInvoiceId(loadInvoiceId)
        .vendorId(qbVendorId)
        .totalCostAmount(invoiceTotal)
        .invoiceItems(invoiceItemDtos)
        .build();
  }

  private CreateInvoiceDto buildCreateInvoiceDto(final CreateQbInvoiceRequest request) {
    final int loadInvoiceId = request.getLoadInvoiceId();
    final LoadInvoice loadInvoice = loadInvoiceRepository.getReferenceById(loadInvoiceId);
    final int userId = UserUtil.getUserIdOrThrow();
    final BigDecimal invoiceTotal = loadInvoice.getInvoiceTotal();
    final List<LoadInvoiceItem> loadInvoiceItems = loadInvoice.getLoadInvoiceItems();
    final String qbCustomerId = getQbCustomerId(loadInvoice);
    final Map<Integer, String> qbSurchargeTypeBySurchargeTypeId = getQbSurchargeTypeBySurchargeTypeId();
    final List<InvoiceItemDto> invoiceItemDtos = loadInvoiceItems.stream()
        .map(loadInvoiceItem -> toInvoiceItemDto(loadInvoiceItem, qbSurchargeTypeBySurchargeTypeId))
        .toList();
    return CreateInvoiceDto.builder()
        .userId(userId)
        .loadInvoiceId(loadInvoiceId)
        .customerId(qbCustomerId)
        .totalCostAmount(invoiceTotal)
        .invoiceItems(invoiceItemDtos)
        .build();
  }

  public List<QbAbCompanyResponse> getQbAbCompanyMappings() {
    final int userCompanyId = UserUtil.getUserCompanyIdOrThrow();
    final List<QbAbCompanyProjection> qbAbCompanies = qbAbCompanyRepository.findShipperAndBrokerVendorMappings(userCompanyId);
    return qbAbCompanyMapper.map(qbAbCompanies);
  }

  public List<QbItemSurchargeTypeResponse> getQbItemMappings() {
    final int userCompanyId = UserUtil.getUserCompanyIdOrThrow();
    final List<QbItemSurchargeTypeProjection> mappings = chargeQbLinkRepository.findMappings(userCompanyId);
    return qbItemMapper.map(mappings);
  }

  private Map<Integer, String> getQbSurchargeTypeBySurchargeTypeId() {
    final int userCompanyId = UserUtil.getUserCompanyIdOrThrow();
    return chargeQbLinkRepository
        .findAllByUserCompanyUserCompanyId(userCompanyId).stream()
        .collect(Collectors.toMap(QbLoadAssignmentSurchargeType::getSurchargeTypeId, QbLoadAssignmentSurchargeType::getQbItemId));
  }

  private InvoiceItemDto toInvoiceItemDto(final LoadInvoiceItem lii, final Map<Integer, String> qbLoadAssignmentSurchargeTypeBySurchargeTypeId) {
    final Integer loadAssignmentSurchargeTypeId = lii.getLoadAssignmentSurchargeTypeId();
    final String qbItemId = qbLoadAssignmentSurchargeTypeBySurchargeTypeId.get(loadAssignmentSurchargeTypeId);
    final BigDecimal amount = lii.getItemAmount();
    final BigDecimal qty = lii.getItemQuantity();
    final BigDecimal unitPrice = lii.getItemAmount().divide(qty, RoundingMode.HALF_EVEN);
    return InvoiceItemDto.builder()
        .qbItemId(qbItemId)
        .unitPrice(unitPrice)
        .qty(qty)
        .amount(amount)
        .build();
  }

  private String getQbCustomerId(final LoadInvoice loadInvoice) {
    final Integer abCompanyId = loadInvoice.getBillToAbCompanyId();
    if (isNull(abCompanyId)) {
      throw new ValidationException("bill_to_ab_company_id", "Internal invoices are not supported");
    }
    final QbAbCompany qbAbCompany = qbAbCompanyRepository.findByAbCompanyAbCompanyId(abCompanyId)
        .orElseThrow(() -> new ValidationException("ab_company_id", "The company is not linked to a QuickBooks customer"));
    return qbAbCompany.getQbCustomerId();
  }

  private String getQbVendorId(final LoadInvoice loadInvoice) {
    final Integer billToAbCompanyId = loadInvoice.getBillToAbCompanyId();

    if (isNull(billToAbCompanyId)) {
      throw new ValidationException("bill_to_ab_company_id", "Internal invoices are not supported");
    }

    final Integer abCompanyId = loadInvoice.getAbCompanyId();

    if (isNull(abCompanyId)) {
      throw new ValidationException("ab_company_id", "Carrier [%s %s] with abCompanyId is not in the address book");
    }

    //TODO think what to do for invoices without ab_company_id

    final QbAbCompany qbAbCompany = qbAbCompanyRepository.findByAbCompanyAbCompanyId(abCompanyId)
        .orElseThrow(() -> new ValidationException("ab_company_id", "The company is not linked to a QuickBooks vendor"));
    return qbAbCompany.getQbVendorId();
  }

  private QbLoadAssignmentSurchargeType getOrCreateChargeLink(final QbLoadAssignmentSurchargeLinkRequest request) {
    final int userCompanyId = UserUtil.getUserCompanyIdOrThrow();
    final ChargeType chargeType = request.getChargeType();

    return findChargeLink(request, userCompanyId)
        .orElseGet(() -> {
          final QbLoadAssignmentSurchargeType link = new QbLoadAssignmentSurchargeType();
          link.setUserCompany(userCompanyRepository.getReferenceById(userCompanyId));
          link.setChargeType(chargeType.name());
          return chargeQbLinkRepository.save(link);
        });
  }

  private QbAbCompany getOrCreateAccountingLink(final AbCompany abCompany) {
    QbAbCompany accountingLink = abCompany.getAccountingLink();
    if (isNull(accountingLink)) {
      accountingLink = new QbAbCompany();
      accountingLink.setAbCompanyId(abCompany.getAbCompanyId());
      accountingLink.setAbCompany(abCompany);
      abCompany.setAccountingLink(accountingLink);
    }
    return accountingLink;
  }

  private Optional<QbLoadAssignmentSurchargeType> findChargeLink(final QbLoadAssignmentSurchargeLinkRequest request, final int userCompanyId) {
    final Optional<Integer> surchargeTypeId = request.getSurchargeTypeId();
    final ChargeType chargeType = request.getChargeType();
    final String chargeTypeName = chargeType.name();

    if (chargeType == surcharge && existsAndIsNotEmpty(surchargeTypeId)) {
      return chargeQbLinkRepository.findByUserCompanyUserCompanyIdAndSurchargeTypeId(userCompanyId, surchargeTypeId.orElseThrow());
    } else {
      return chargeQbLinkRepository.findByUserCompanyUserCompanyIdAndChargeType(userCompanyId, chargeTypeName);
    }
  }

  private AbCompany getAbCompanyId(final int abCompanyId) {
    final int userCompanyId = UserUtil.getUserCompanyIdOrThrow();
    return abCompanyRepository.findByAbCompanyIdAndUserCompanyUserCompanyId(abCompanyId, userCompanyId)
        .orElseThrow(() -> new ValidationException("ab_company_id", "You are not allowed to update this company"));
  }

  private Map<Integer, QbAbCompany> collectQbMappingsByAbCompanyId(final int userCompanyId) {
    return qbAbCompanyRepository.findAllByAbCompanyUserCompanyUserCompanyId(userCompanyId).stream()
        .collect(Collectors.toMap(QbAbCompany::getAbCompanyId, qbAbCompany -> qbAbCompany));
  }

  private Map<Integer, QbItemSurchargeTypeProjection> collectQbItemSurchargeTypeBySurchargeTypeId(final int userCompanyId) {
    return chargeQbLinkRepository.findMappings(userCompanyId).stream()
        .collect(Collectors.toMap(QbItemSurchargeTypeProjection::getSurchargeTypeId, qbItemSurchargeTypeProjection -> qbItemSurchargeTypeProjection));
  }
}
