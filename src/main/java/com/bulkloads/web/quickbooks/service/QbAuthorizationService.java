package com.bulkloads.web.quickbooks.service;

import static org.springframework.util.StringUtils.hasLength;
import com.bulkloads.common.UserUtil;
import com.bulkloads.web.usercompany.domain.entity.UserCompany;
import com.bulkloads.web.usercompany.domain.entity.UserCompanySettings;
import com.bulkloads.web.usercompany.service.UserCompanyService;
import org.apache.logging.log4j.util.Strings;
import org.springframework.security.oauth2.client.OAuth2AuthorizedClientService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@Transactional
@RequiredArgsConstructor
public class QbAuthorizationService {

  private final UserCompanyService userCompanyService;
  private final OAuth2AuthorizedClientService authorizedClientService;

  public void deleteAuthorization() {
    final int userId = UserUtil.getUserIdOrThrow();
    final int userCompanyId = UserUtil.getUserCompanyIdOrThrow();
    final UserCompany userCompany = userCompanyService.getUserCompany(userCompanyId);
    final UserCompanySettings userCompanySettings = userCompany.getUserCompanySettings();
    final String accountingProviderId = userCompanySettings.getAccountingProviderId();
    if (hasLength(accountingProviderId)) {
      authorizedClientService.removeAuthorizedClient(accountingProviderId, String.valueOf(userId));
      log.info("Removed authorization for user {} and provider {}", userId, accountingProviderId);
    }
    userCompanySettings.setAccountingProviderId(Strings.EMPTY);
  }
}
