package com.bulkloads.web.quickbooks.api.openapi;

import com.bulkloads.web.quickbooks.api.dto.CreateQbBillRequest;
import com.bulkloads.web.quickbooks.api.dto.CreateQbInvoiceRequest;
import com.bulkloads.web.quickbooks.api.dto.QbLoadAssignmentSurchargeLinkRequest;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;

@Tag(name = "QuickBooks")
public interface QbApiDoc {

  @Operation(summary = "Create Quickbooks Invoice")
  String createInvoice(
      @Parameter(name = "request", description = "The request body", required = true) final CreateQbInvoiceRequest request);

  @Operation(summary = "Create Quickbooks Bill")
  String createBill(
      @Parameter(name = "request", description = "The request body", required = true) final CreateQbBillRequest request);

  @Operation(summary = "Link Quickbooks Customer to AbCompany")
  void linkCustomerToAbCompany(
      @Parameter(name = "customer_id", description = "The id of the QuickBooks customer", required = true) final String qbCustomerId,
      @Parameter(name = "ab_company_id", description = "The id of the AbCompany", required = true) final int abCompanyId);

  @Operation(summary = "Unlink Quickbooks Customer from AbCompany")
  void unlinkCustomerFromAbCompany(
      @Parameter(name = "customer_id", description = "The id of the QuickBooks customer", required = true) final String qbCustomerId,
      @Parameter(name = "ab_company_id", description = "The id of the AbCompany", required = true) final int abCompanyId);

  @Operation(summary = "Link Quickbooks Vendor to AbCompany")
  void linkVendorToAbCompany(
      @Parameter(name = "vendor_id", description = "The id of the QuickBooks vendor", required = true) final String qbVendorId,
      @Parameter(name = "ab_company_id", description = "The id of the AbCompany", required = true) final int abCompanyId);

  @Operation(summary = "Unlink Quickbooks Vendor from AbCompany")
  void unlinkVendorFromAbCompany(
      @Parameter(name = "vendor_id", description = "The id of the QuickBooks vendor", required = true) final String qbVendorId,
      @Parameter(name = "ab_company_id", description = "The id of the AbCompany", required = true) final int abCompanyId);

  @Operation(summary = "Link Quickbooks Item to ChargeType")
  void linkItemToChargeType(
      @Parameter(name = "item_id", description = "The id of the QuickBooks item", required = true) final String qbItemId,
      @Parameter(name = "request", description = "The request body", required = true) final QbLoadAssignmentSurchargeLinkRequest request);

  @Operation(summary = "Unlink Quickbooks Item from ChargeType")
  void unlinkItemFromChargeType(
      @Parameter(name = "item_id", description = "The id of the QuickBooks item", required = true) final String qbItemId,
      @Parameter(name = "request", description = "The request body", required = true) final QbLoadAssignmentSurchargeLinkRequest request);
}
