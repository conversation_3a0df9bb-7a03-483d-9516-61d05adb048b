package com.bulkloads.web.quickbooks.api.dto;

import java.util.Optional;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Value;
import lombok.extern.jackson.Jacksonized;

@Value
@Builder
@Jacksonized
public class QbLoadAssignmentSurchargeLinkRequest {

  @Schema(name = "charge_type", requiredMode = Schema.RequiredMode.REQUIRED)
  ChargeType chargeType;
  @Schema(name = "surcharge_type_id", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<Integer> surchargeTypeId;
}

