package com.bulkloads.web.quickbooks.api;

import static com.bulkloads.config.AppConstants.UserRole.ROLE_USER;

import com.bulkloads.web.quickbooks.api.dto.CreateQbBillRequest;
import com.bulkloads.web.quickbooks.api.dto.CreateQbInvoiceRequest;
import com.bulkloads.web.quickbooks.api.dto.QbLoadAssignmentSurchargeLinkRequest;
import com.bulkloads.web.quickbooks.api.openapi.QbApiDoc;
import com.bulkloads.web.quickbooks.service.QbService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;

@RestController
@RequestMapping("/rest/quickbooks")
@RequiredArgsConstructor
@PreAuthorize("hasRole('" + ROLE_USER + "')")
public class QbController implements QbApiDoc {

  private final QbService service;

  @PostMapping("/invoices")
  public String createInvoice(@Valid @RequestBody CreateQbInvoiceRequest request) {
    return service.createInvoice(request);
  }

  @PostMapping("/bills")
  public String createBill(@Valid @RequestBody CreateQbBillRequest request) {
    return service.createBill(request);
  }

  @PutMapping("customers/{customer_id}/link/{ab_company_id}")
  public void linkCustomerToAbCompany(@PathVariable("customer_id") final String qbCustomerId,
                                      @PathVariable("ab_company_id") final int abCompanyId) {
    service.linkQbCustomer(abCompanyId, qbCustomerId);
  }

  @DeleteMapping("customers/{customer_id}/unlink/{ab_company_id}")
  public void unlinkCustomerFromAbCompany(@PathVariable("customer_id") final String qbCustomerId,
                                        @PathVariable("ab_company_id") final int abCompanyId) {
    service.unlinkQbCustomer(abCompanyId, qbCustomerId);
  }

  @PutMapping("vendors/{vendor_id}/link/{ab_company_id}")
  public void linkVendorToAbCompany(@PathVariable("vendor_id") final String qbVendorId,
                                    @PathVariable("ab_company_id") final int abCompanyId) {
    service.linkQbVendor(abCompanyId, qbVendorId);
  }

  @DeleteMapping("vendors/{vendor_id}/unlink/{ab_company_id}")
  public void unlinkVendorFromAbCompany(@PathVariable("vendor_id") final String qbVendorId,
                                      @PathVariable("ab_company_id") final int abCompanyId) {
    service.unlinkQbVendor(abCompanyId, qbVendorId);
  }

  @PutMapping("items/{item_id}/link")
  public void linkItemToChargeType(@PathVariable("item_id") final String qbItemId,
                                   @Valid @RequestBody final QbLoadAssignmentSurchargeLinkRequest request) {
    service.linkQbItem(qbItemId, request);
  }

  @DeleteMapping("items/{item_id}/unlink")
  public void unlinkItemFromChargeType(@PathVariable("item_id") final String qbItemId,
                                       @Valid @RequestBody final QbLoadAssignmentSurchargeLinkRequest request) {
    service.unlinkQbItem(qbItemId, request);
  }
}
