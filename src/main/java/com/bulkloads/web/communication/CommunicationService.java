package com.bulkloads.web.communication;

import static com.bulkloads.common.validation.ValidationUtils.existsAndIsNotEmpty;
import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import com.bulkloads.config.AppProperties;
import com.bulkloads.web.addressbook.abuser.domain.entity.AbUser;
import com.bulkloads.web.addressbook.abuser.repository.AbUserRepository;
import com.bulkloads.web.communication.domain.CommunicationDetails;
import com.bulkloads.web.communication.domain.CommunicationIds;
import com.bulkloads.web.infra.email.EmailService;
import com.bulkloads.web.infra.email.domain.EmailDetails;
import com.bulkloads.web.infra.sms.SmsService;
import com.bulkloads.web.infra.sms.domain.SmsDetails;
import com.bulkloads.web.notification.service.NotificationService;
import com.bulkloads.web.notification.service.dto.NotificationRequest;
import com.bulkloads.web.user.domain.entity.User;
import com.bulkloads.web.user.service.UserService;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class CommunicationService {

  private final AbUserRepository abUserRepository;
  private final UserService userService;
  private final EmailService emailService;
  private final NotificationService notificationService;
  private final SmsService smsService;
  private final AppProperties appProperties;


  public Optional<CommunicationIds> send(final CommunicationDetails details) {
    if (details.getToAbUserId().isPresent()) {
      return sendToAbUser(details);
    } else if (details.getToUserId().isPresent()) {
      return sendToUser(details);
    }
    return Optional.empty();
  }

  public Optional<CommunicationIds> sendToUser(final CommunicationDetails details) {
    final User fromUser = userService.findById(details.getFromUserId());
    final User toUser = details.getToUserId().map(userService::findById).orElse(null);

    if (isNull(toUser)) {
      return Optional.empty();
    }

    final String siteEmail = appProperties.getMailing().getSiteEmail();
    String toEmail = toUser.getEmail();
    final String senderEmail = buildSenderEmailAddress(fromUser, siteEmail);
    final String replyToEmailAddress = buildReplyToEmailAddress(details, fromUser);
    final List<String> ccEmails = new ArrayList<>();

    if (details.isSendEmailCopyToSender()) {
      ccEmails.add(replyToEmailAddress);
    }

    if (details.isAccounting()) {
      final String accountingEmail = toUser.getAccountingEmail();
      if (!accountingEmail.isBlank() && !accountingEmail.equals(toEmail)) {
        toEmail = accountingEmail;
        if (toUser.getAccountingEmailCcMe()) {
          ccEmails.add(toUser.getEmail());
        }
      }
      details.getEmailCc().ifPresent(ccEmails::add);
    }
    final String ccString = getCcString(ccEmails);

    final CommunicationIds.CommunicationIdsBuilder builder = CommunicationIds.builder();

    sendEmail(details, toEmail, senderEmail, replyToEmailAddress, ccString, toUser)
        .ifPresent(builder::emailQueueId);
    sendNotification(details).ifPresent(builder::notificationId);
    sendSms(details, toUser.getCellPhone(), toUser).ifPresent(builder::smsId);

    return Optional.of(builder.build());
  }

  public Optional<CommunicationIds> sendToAbUser(final CommunicationDetails details) {

    final User fromUser = userService.findById(details.getFromUserId());
    final List<AbUser> abUsers;

    //TODO
    if (details.getToAbUserId().isPresent()) {
      final Integer toAbUserId = details.getToAbUserId().get();
      abUsers =
          abUserRepository.findAllByAbUserIdInAndUserCompanyUserCompanyIdAndDeletedIsFalse(List.of(toAbUserId), fromUser.getUserCompany().getUserCompanyId());
      if (abUsers.isEmpty()) {
        return Optional.empty();
      }
    } else {
      return Optional.empty();
    }

    final AbUser abUser = abUsers.get(0);

    final String siteEmail = appProperties.getMailing().getSiteEmail();
    final String senderEmail = buildSenderEmailAddress(fromUser, siteEmail);
    final String replyToEmailAddress = buildReplyToEmailAddress(details, fromUser);
    final List<String> ccEmails = new ArrayList<>();

    final CommunicationIds.CommunicationIdsBuilder builder = CommunicationIds.builder();

    final User toUser = abUser.getBlUser();
    if (nonNull(toUser)) {

      String toEmail = abUser.getEmail();

      if (details.isAccounting()) {
        final String accountingEmail = toUser.getAccountingEmail();
        if (!accountingEmail.isBlank() && !accountingEmail.equals(toEmail)) {
          toEmail = accountingEmail;
          if (toUser.getAccountingEmailCcMe()) {
            ccEmails.add(toUser.getEmail());
          }
        }
      }
      details.getEmailCc().ifPresent(ccEmails::add);
      final String ccString = getCcString(ccEmails);

      sendEmail(details, toEmail, senderEmail, replyToEmailAddress, ccString, toUser).ifPresent(builder::emailQueueId);
      sendNotification(details).ifPresent(builder::notificationId);
    } else {
      sendEmail(details, abUser.getEmail(), senderEmail, replyToEmailAddress, null, null).ifPresent(builder::emailQueueId);
    }
    sendSms(details, abUser.getPhone1(), toUser).ifPresent(builder::smsId);

    return Optional.of(builder.build());
  }

  private Optional<Integer> sendEmail(final CommunicationDetails details, final String toEmail, final String senderEmail, final String replyToEmailAddress,
                         final String ccEmail, final User toUser) {
    if (!toEmail.isBlank() && (details.getEmailTitle().isPresent() && details.getEmailMessage().isPresent())) {
      final EmailDetails.EmailDetailsBuilder emailDetailsBuilder = EmailDetails.builder()
          .fromEmail(senderEmail)
          .replyToEmail(replyToEmailAddress)
          .toEmails(List.of(toEmail))
          .failTo(appProperties.getMailing().getFailToEmail())
          .cc(ccEmail)
          .bcc(details.getEmailBcc())
          .subject(details.getEmailTitle().get())
          .message(details.getEmailMessage().get())
          .category(details.getEmailCategoryId())
          .toUserId(Optional.ofNullable(toUser).map(User::getUserId).orElse(null))
          .senderUserId(details.getFromUserId())
          .attachments(details.getAttachments())
          .description(details.getEmailTitle().get());

      if (details.isSendEmailCopyToSender()) {
        emailDetailsBuilder.cc(replyToEmailAddress);
      }

      return emailService.sendEmail(emailDetailsBuilder.build());
    }
    return Optional.empty();
  }

  private Optional<Integer> sendNotification(final CommunicationDetails details) {
    if (details.getPushTitle().isPresent() && details.getPushMessage().isPresent() && details.getToUserId().isPresent()) {
      final NotificationRequest notificationRequest = new NotificationRequest();
      notificationRequest.setBody(details.getPushMessage().get());
      notificationRequest.setTitle(details.getPushTitle().get());
      notificationRequest.setEmailCategoryId(details.getEmailCategoryId());
      notificationRequest.setUserId(details.getToUserId().get());
      if (!details.getPushData().isEmpty()) {
        notificationRequest.setPushData(details.getPushData());
      }
      return Optional.of(notificationService.create(notificationRequest).getNotificationId());
    }
    return Optional.empty();
  }

  private Optional<Integer> sendSms(final CommunicationDetails details, final String userNumber, final User toUser) {
    if (existsAndIsNotEmpty(details.getSmsMessage()) && !userNumber.isBlank()) {
      final SmsDetails.SmsDetailsBuilder builder = SmsDetails.builder()
          .fromUserId(details.getFromUserId())
          .toUserNumber(userNumber)
          .message(details.getSmsMessage().get());
      if (nonNull(toUser)) {
        builder.toUserId(toUser.getUserId());
      }
      return smsService.sendSms(builder.build());
    }
    return Optional.empty();
  }

  private String buildReplyToEmailAddress(final CommunicationDetails details, final User fromUser) {
    return details.getReplyToEmail().isPresent() ? details.getReplyToEmail().get() :
        "%s %s <%s>".formatted(fromUser.getFirstName(), fromUser.getLastName(), fromUser.getEmail());
  }

  private String buildSenderEmailAddress(final User fromUser, final String siteEmail) {
    return "=?utf-8?Q? %s %s _=F0=9F=9A=9B?=<%s>".formatted(fromUser.getFirstName(), fromUser.getLastName(), siteEmail);
  }

  private String getCcString(final List<String> ccEmails) {
    return ccEmails.stream().filter(email -> !email.isBlank()).reduce((email1, email2) -> email1 + "," + email2).orElse(null);
  }
}
