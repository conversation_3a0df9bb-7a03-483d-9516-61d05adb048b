package com.bulkloads.web.communication.domain;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import com.bulkloads.web.infra.email.domain.Attachment;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class CommunicationDetails {

  Integer fromUserId;

  Integer toUserId;
  Integer toAbUserId;

  String replyToEmail;
  String emailCc;
  String emailBcc;
  Integer emailCategoryId;
  String emailTitle;
  String emailMessage;
  String pushTitle;
  String pushMessage;
  @Builder.Default
  Map<String, Object> pushData = Map.of();
  String smsMessage;
  boolean sendEmailCopyToSender;
  @Builder.Default
  List<Attachment> attachments = List.of();
  boolean isAccounting;

  public Optional<Integer> getToUserId() {
    return Optional.ofNullable(toUserId);
  }

  public Optional<Integer> getToAbUserId() {
    return Optional.ofNullable(toAbUserId);
  }

  public Optional<String> getReplyToEmail() {
    return Optional.ofNullable(replyToEmail);
  }

  public Optional<String> getEmailCc() {
    return Optional.ofNullable(emailCc);
  }

  public Optional<String> getEmailTitle() {
    return Optional.ofNullable(emailTitle);
  }

  public Optional<String> getEmailMessage() {
    return Optional.ofNullable(emailMessage);
  }

  public Optional<String> getPushTitle() {
    return Optional.ofNullable(pushTitle);
  }

  public Optional<String> getPushMessage() {
    return Optional.ofNullable(pushMessage);
  }

  public Optional<String> getSmsMessage() {
    return Optional.ofNullable(smsMessage);
  }
}
