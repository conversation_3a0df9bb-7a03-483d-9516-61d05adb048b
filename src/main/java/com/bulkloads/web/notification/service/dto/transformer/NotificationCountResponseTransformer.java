package com.bulkloads.web.notification.service.dto.transformer;

import com.bulkloads.common.jpa.nativejpa.QueryParts;
import com.bulkloads.web.notification.service.dto.NotificationCountResponse;
import org.hibernate.query.TupleTransformer;
import org.springframework.stereotype.Component;
import lombok.RequiredArgsConstructor;


@Component
@RequiredArgsConstructor
public class NotificationCountResponseTransformer implements TupleTransformer<NotificationCountResponse> {

  @Override
  public NotificationCountResponse transformTuple(Object[] columns, String[] aliases) {
    QueryParts parts = new QueryParts(columns, aliases);
    NotificationCountResponse response = new NotificationCountResponse();
    response.setCount(parts.asInteger("count"));
    return response;

  }
}
