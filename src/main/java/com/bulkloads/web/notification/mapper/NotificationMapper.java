package com.bulkloads.web.notification.mapper;

import com.bulkloads.exception.BulkloadsException;
import com.bulkloads.web.contracts.mapper.CommonMapper;
import com.bulkloads.web.infra.email.entity.EmailCategory;
import com.bulkloads.web.infra.email.repository.EmailCategoryRepository;
import com.bulkloads.web.notification.domain.data.NotificationData;
import com.bulkloads.web.notification.domain.entity.Notification;
import com.bulkloads.web.notification.service.dto.NotificationRequest;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Named;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.springframework.beans.factory.annotation.Autowired;

@Mapper(componentModel = "spring",
    nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
    nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
    unmappedTargetPolicy = org.mapstruct.ReportingPolicy.IGNORE,
    uses = CommonMapper.class)
public abstract class NotificationMapper {

  @Autowired
  EmailCategoryRepository emailCategoryRepository;

  @Mapping(source = "emailCategoryId", target = "emailCategory", qualifiedByName = "mapEmailCategoryIdToEmailCategory")
  public abstract NotificationData requestToData(final NotificationRequest req);


  @Mapping(source = "emailCategory", target = "emailCategory", qualifiedByName = "mapEmailCategory")
  public abstract void dataToEntity(final NotificationData data,
                                    @MappingTarget final Notification notification);


  @Named("mapEmailCategoryIdToEmailCategory")
  public EmailCategory mapEmailCategoryIdToEmailCategory(Integer emailCategoryId) {
    return emailCategoryRepository.findById(emailCategoryId)
        .orElseThrow(() -> new BulkloadsException("Could not find emailCategoryId: " + emailCategoryId));
  }


  @Named("mapEmailCategory")
  public String mapEmailCategory(EmailCategory emailCategory) {
    return emailCategory.getEmailCategory();
  }


}
