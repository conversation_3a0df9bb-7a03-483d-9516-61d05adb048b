package com.bulkloads.web.notification.domain;

import static com.bulkloads.common.Converters.mapToJsonString;
import static org.apache.commons.lang3.StringUtils.left;
import java.time.Instant;
import java.util.List;
import java.util.Map;
import com.bulkloads.common.BaseDomainService;
import com.bulkloads.common.validation.Result;
import com.bulkloads.common.validation.ValidationMethod;
import com.bulkloads.web.notification.domain.data.NotificationData;
import com.bulkloads.web.notification.domain.entity.Notification;
import com.bulkloads.web.notification.mapper.NotificationMapper;
import com.bulkloads.web.user.domain.entity.User;
import com.bulkloads.web.user.domain.entity.UserDevice;
import com.bulkloads.web.user.repository.UserDeviceRepository;
import com.bulkloads.web.user.service.UserService;
import org.springframework.stereotype.Component;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
@RequiredArgsConstructor
public class NotificationDomainService extends BaseDomainService<Notification> {

  private final NotificationMapper notificationMapper;
  private final UserDeviceRepository userDeviceRepository;
  private final UserService userService;

  public Result<Notification> create(final NotificationData data) {
    final Notification notification = new Notification();
    return super.validate(notification, null, data, ValidationMethod.CREATE);
  }

  @Override
  public void validateDataAndMapToEntity(final Result<Notification> result,
                                         final Notification entity,
                                         final Notification existing,
                                         final Object data,
                                         final ValidationMethod method) {
    final NotificationData notificationData = (NotificationData) data;

    final List<UserDevice> byUserUserId = userDeviceRepository.findByUserUserIdAndPushEnabledIsTrue(notificationData.getUserId());
    final User user = userService.findById(notificationData.getUserId());
    final String deviceIds = String.join(",", byUserUserId.stream().map(UserDevice::getDeviceId).toList());
    final String deviceTokens = String.join(",", byUserUserId.stream().map(UserDevice::getNotificationToken).toList());

    entity.setData(mapToJsonString(notificationData.getPushData()));
    entity.setUser(user);
    entity.setDateAdded(Instant.now());
    entity.setEmailCategory(notificationData.getEmailCategory().getEmailCategory());
    entity.setEmailCategoryId(notificationData.getEmailCategory().getEmailCategoryId());
    entity.setDeviceCount(byUserUserId.size());
    entity.setDeviceIds(left(deviceIds, 1024));
    entity.setNotificationTokens(left(deviceTokens, 2500));
    entity.setSiteId(1);
    entity.setNotificationJson(buildNotificationJson(notificationData)
    );
  }

  private Map<String, Object> buildNotificationJson(final NotificationData data) {
    return Map.of("data", data.getPushData(),
                  "notification", Map.of("body", data.getBody(),
                                         "title", data.getTitle()));
  }

  @Override
  public void mapToEntityAuto(final Object data, final Notification entity) {
    notificationMapper.dataToEntity((NotificationData) data, entity);
  }

  @Override
  public void validateEntity(final Result<Notification> result, final Notification entity) {
  }

}
