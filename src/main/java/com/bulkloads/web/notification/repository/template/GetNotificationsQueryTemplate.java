package com.bulkloads.web.notification.repository.template;

public class GetNotificationsQueryTemplate {

  public static final String GET_NOTIFICATIONS_QUERY_TEMPLATE = """
      select
          notification_id,
          user_id,
          title,
          body,
          data,
          email_category_id,
          email_category_id as notification_type_id,
          email_category,
          email_category as notification_type,
          device_ids,
          notification_tokens,
          date_added,
          is_read,
          date_read,
          is_emailed,
          date_emailed,
          email_queue_id,
          ifnull((
              select group_concat(nd.fcm_message_id)
              from notification_devices nd
              where nd.notification_id = notifications.notification_id
              and nd.date_added > curdate() - interval 1 month
              <% if (paramExistsAdd("device_id")) { %>
                  and device_id = :device_id
              <% } %>
          ), '') as fcm_message_ids
      from notifications
      where
        <% params.put("u_id", u_id) %>
        user_id = :u_id
        and date_added > curdate() - interval 1 month

        <% if (paramExistsAdd("notification_ids")) { %>
            and notification_id in (:notification_ids)
        <% } %>

        <% if (paramExistsAdd("email_category_id")) { %>
            and email_category_id = :email_category_id
        <% } %>

        <% if (paramExistsAdd("notification_type_id")) { %>
            and email_category_id = :notification_type_id
        <% } %>

        <% if (paramExistsAdd("is_read")) { %>
            and is_read = :is_read
        <% } %>

        order by notification_id desc

        <% if (paramExistsAdd("limit")) { %>
            LIMIT
            <% if (paramExistsAdd("skip")) { %>
            :skip,
            <% } %>
            :limit
        <% } %>
      """;


  public static final String GET_NOTIFICATION_COUNT_QUERY_TEMPLATE = """
      select
        count(*) as count from notifications
      where
        <% params.put("u_id", u_id) %>
        user_id = :u_id
      and
        date_added > curdate() - interval 1 month

      <% if (paramExistsAdd("email_category_id")) { %>
          and email_category_id = :email_category_id
      <% } %>

      <% if (paramExistsAdd("notification_type_id")) { %>
          and email_category_id = :notification_type_id
      <% } %>
      
      <% if (paramExistsAdd("is_read")) { %>
          and is_read = :is_read
      <% } %>
      """;
}
