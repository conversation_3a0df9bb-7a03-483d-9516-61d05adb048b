package com.bulkloads.web.notification.repository;

import static com.bulkloads.web.notification.repository.template.GetNotificationsQueryTemplate.GET_NOTIFICATIONS_QUERY_TEMPLATE;
import static com.bulkloads.web.notification.repository.template.GetNotificationsQueryTemplate.GET_NOTIFICATION_COUNT_QUERY_TEMPLATE;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.bulkloads.common.jpa.nativejpa.JpaNativeQueryService;
import com.bulkloads.web.notification.service.dto.NotificationCountResponse;
import com.bulkloads.web.notification.service.dto.NotificationResponse;
import com.bulkloads.web.notification.service.dto.transformer.NotificationCountResponseTransformer;
import com.bulkloads.web.notification.service.dto.transformer.NotificationResponseTransformer;
import org.springframework.stereotype.Repository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;


@Slf4j
@Repository
@RequiredArgsConstructor
class NotificationQueryRepositoryImpl implements NotificationQueryRepository {

  final JpaNativeQueryService jpaNativeQueryService;
  final NotificationResponseTransformer notificationResponseTransformer;
  final NotificationCountResponseTransformer notificationCountResponseTransformer;

  public NotificationResponse getNotification(
      final int userId,
      final int notificationId) {

    Map<String, Object> params = new HashMap<>();
    params.put("u_id", userId);
    params.put("notification_ids", List.of(notificationId));

    return jpaNativeQueryService.queryForObject(
        GET_NOTIFICATIONS_QUERY_TEMPLATE,
        params,
        notificationResponseTransformer);
  }


  public List<NotificationResponse> getNotifications(
      final int userId,
      final List<Integer> notificationIds,
      final Integer emailCategoryId,
      final Integer notificationTypeId,
      final String deviceId,
      final Integer isRead,
      final int skip,
      final int limit) {

    Map<String, Object> params = new HashMap<>();
    params.put("u_id", userId);
    params.put("notification_ids", notificationIds);
    params.put("email_category_id", emailCategoryId);
    params.put("notification_type_id", notificationTypeId);
    params.put("device_id", deviceId);
    params.put("is_read", isRead);
    params.put("skip", skip);
    params.put("limit", limit);

    return jpaNativeQueryService.query(
        GET_NOTIFICATIONS_QUERY_TEMPLATE,
        params,
        notificationResponseTransformer);

  }

  @Override
  public NotificationCountResponse getNotificationCount(
      final int userId,
      final Boolean isRead,
      Integer emailCategoryId,
      Integer notificationTypeId) {

    Map<String, Object> params = new HashMap<>();
    params.put("u_id", userId);
    params.put("is_read", isRead);
    params.put("email_category_id", emailCategoryId);
    params.put("notification_type_id", notificationTypeId);

    return jpaNativeQueryService.queryForObject(
        GET_NOTIFICATION_COUNT_QUERY_TEMPLATE,
        params,
        notificationCountResponseTransformer);

  }
}
