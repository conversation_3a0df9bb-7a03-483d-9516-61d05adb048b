package com.bulkloads.web.file.event;

import java.util.List;
import com.bulkloads.web.file.domain.entity.File;
import com.bulkloads.web.file.domain.entity.FileField;
import lombok.Getter;

/**
 * Event that is published when file fields are created or updated.
 * This event can be listened to by other components to react to field changes.
 */
@Getter
public class FileFieldsUpdatedEvent {
  
  private final File file;
  private final List<FileField> fields;
  
  public FileFieldsUpdatedEvent(File file, List<FileField> fields) {
    this.file = file;
    this.fields = fields;
  }
}