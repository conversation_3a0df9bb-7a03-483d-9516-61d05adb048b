package com.bulkloads.web.file.util;

import static com.bulkloads.common.StringUtil.extractFileName;
import static com.bulkloads.common.StringUtil.getUuid;
import static com.bulkloads.config.AppConstants.Paths.TEMP;
import static com.bulkloads.web.aws.service.AmazonS3Service.sanitizeForS3;
import java.io.BufferedInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.UncheckedIOException;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;
import java.util.Objects;
import com.bulkloads.config.AppConstants;
import com.bulkloads.exception.ValidationException;
import org.apache.commons.io.FilenameUtils;
import org.springframework.web.multipart.MultipartFile;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@UtilityClass
public class FileUtils {
  private static final Object DOWNLOAD_LOCK = new Object();

  public static final long MAX_FILE_SIZE = 30 * 1024 * 1024; // 30MB

  public static Path resolveResource(String folderName) {
    try {
      URL resourceUrl = AppConstants.class.getClassLoader().getResource(folderName);
      if (resourceUrl == null) {
        throw new IllegalStateException("Resource folder not found: " + folderName);
      }
      return Path.of(resourceUrl.toURI());
    } catch (Exception e) {
      throw new IllegalStateException("Failed to resolve resource folder: " + folderName, e);
    }
  }

  public static void testDirectoryExists(Path dir) {
    if (!Files.exists(dir)
        || (Files.exists(dir) && !Files.isDirectory(dir))) {
      throw new IllegalStateException("Path exists but is not a directory: " + dir);
    }
  }

  public static void ensureDirectoryExists(Path dir) {

    if (Files.exists(dir)) {
      if (!Files.isDirectory(dir)) {
        throw new IllegalStateException("Path exists but is not a directory: " + dir);
      }
    } else {
      try {
        Files.createDirectories(dir);
      } catch (IOException e) {
        throw new IllegalStateException("Failed to create temporary directory: " + dir, e);
      }
    }
  }

  public static Path saveMultipartFileToDisk(MultipartFile multipartFile) throws IOException {

    if (multipartFile.getSize() > MAX_FILE_SIZE) {
      throw new ValidationException("file", "File size exceeds the maximum allowed size of 30MB");
    }
    String filename = multipartFile.getOriginalFilename();

    if (filename != null && filename.length() > 100) {
      filename = filename.substring(filename.length() - 100);
    }

    if (filename == null) {
      throw new ValidationException("file", "File name is required");
    }

    filename = getUuid() + "-" + sanitizeForS3(filename);

    Path destinationPath = TEMP.resolve(filename);

    try (InputStream inputStream = multipartFile.getInputStream()) {
      Files.copy(inputStream, destinationPath, StandardCopyOption.REPLACE_EXISTING);
    }
    return destinationPath;
  }

  public static boolean isAudioFile(Path filePath) {
    try {
      String mimeType = Files.probeContentType(filePath);
      return mimeType != null && mimeType.startsWith("audio/");
    } catch (IOException e) {
      return false;
    }
  }

  // TODO: the thumbnail image cause a problem, file is not getting deleted from local filesystem
  public static void deleteFileIfExists(Path filePath) {
    if (filePath == null) {
      return;
    }
    if (Files.exists(filePath)) {
      try {
        log.trace("Deleting file: {}", filePath);
        Files.delete(filePath);
      } catch (IOException e) {
        log.error("Failed to delete file: {}", filePath, e);
      }
    }
  }

  public static boolean pathsAreEqual(Path first, Path second) {
    return first.normalize().equals(second.normalize());
  }

  public static Path downloadFile(String fileUril) {
    return downloadFile(fileUril, TEMP);
  }

  public static Path downloadFile(String fileUrl, Path outputDirectory) {
    Objects.requireNonNull(fileUrl, "File URL cannot be null");
    Objects.requireNonNull(outputDirectory, "Output directory cannot be null");

    if (!Files.isDirectory(outputDirectory)) {
      throw new IllegalArgumentException(outputDirectory + " is not not a directory");
    }

    Path outputPath = outputDirectory.resolve(extractFileName(fileUrl));
    log.debug("Downloading file from URL: {} into: {}", fileUrl, outputPath);

    try (InputStream in = new URL(fileUrl).openStream()) {
      synchronized (DOWNLOAD_LOCK) {
        outputPath = getUniqueFilePath(outputPath, outputDirectory);
        // Use buffered streams with a reasonable buffer size
        try (BufferedInputStream bis = new BufferedInputStream(in, 8192);
             OutputStream out = Files.newOutputStream(outputPath)) {
          byte[] buffer = new byte[8192];
          int bytesRead;
          while ((bytesRead = bis.read(buffer)) != -1) {
            out.write(buffer, 0, bytesRead);
          }
        }
      }
      log.debug("Downloaded file saved to: {}", outputPath);
      return outputPath;
    } catch (IOException e) {
      throw new UncheckedIOException("Failed to download file: " + fileUrl, e);
    }
  }

  public static synchronized Path getUniqueFilePath(Path outputPath, Path targetDir) {

    if (Files.exists(outputPath)) {
      String baseName = FilenameUtils.getBaseName(outputPath.getFileName().toString());
      String extension = FilenameUtils.getExtension(outputPath.getFileName().toString());
      String newFilename = baseName;

      for (int i = 1; Files.exists(targetDir.resolve(newFilename + "." + extension)); i++) {
        newFilename = baseName + "-" + i;
      }

      final Path path = targetDir.resolve(newFilename + "." + extension);
      log.debug("File already exists: {} renamed to : {}", outputPath, path);
      return path;
    }
    return outputPath;
  }

  public static String megaBytes(Path path) throws IOException {
    long outputSizeBytes = Files.size(path);
    double outputSizeMB = outputSizeBytes / (1024.0 * 1024.0);
    return String.format("%.2f", outputSizeMB);
  }
}