package com.bulkloads.web.file.service;

import static com.bulkloads.common.validation.ValidationUtils.isEmpty;
import static com.bulkloads.common.validation.ValidationUtils.isMissingOrIsEmpty;
import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;
import java.net.URI;
import java.time.Instant;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.bulkloads.config.AppProperties;
import com.bulkloads.exception.BulkloadsException;
import com.bulkloads.exception.ValidationException;
import com.bulkloads.web.assignment.domain.entity.Assignment;
import com.bulkloads.web.assignment.repository.AssignmentRepository;
import com.bulkloads.web.file.domain.entity.File;
import com.bulkloads.web.file.domain.entity.FileField;
import com.bulkloads.web.file.domain.entity.FileType;
import com.bulkloads.web.file.event.FileFieldsUpdatedEvent;
import com.bulkloads.web.file.mapper.FileFieldMapper;
import com.bulkloads.web.file.mapper.FileMapper;
import com.bulkloads.web.file.repository.FileOcrProjection;
import com.bulkloads.web.file.repository.FileRepository;
import com.bulkloads.web.file.service.dto.FileFieldRequest;
import com.bulkloads.web.file.service.dto.FileFieldResponse;
import com.bulkloads.web.file.service.dto.OcrResponse;
import com.bulkloads.web.file.service.dto.TicketItemResponse;
import com.bulkloads.web.file.service.dto.UnapprovedFileResponse;
import com.bulkloads.web.user.domain.entity.User;
import com.bulkloads.web.user.service.UserService;
import org.apache.http.client.utils.URIBuilder;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@Transactional
@RequiredArgsConstructor
public class FileOcrService {

  private static final String UUID = "ad350820-433c-4932-a135-f7f9929ecf81";

  public static final String OCR_FIELD_TICKET_NUMBER = "ticket_number";
  public static final String OCR_FIELD_TICKET_DATE = "ticket_date";
  public static final String OCR_FIELD_GROSS_WEIGHT = "gross";
  public static final String OCR_FIELD_TARE_WEIGHT = "tare";
  public static final String OCR_FIELD_VOLUME = "volume";

  private final AppProperties appProperties;
  private final WebClient webClient;
  private final AssignmentRepository assignmentRepository;
  private final FileRepository fileRepository;
  private final FileMapper fileMapper;
  private final FileFieldMapper fileFieldMapper;
  private final UserService userService;
  private final ApplicationEventPublisher applicationEventPublisher;

  @Async
  public void ocrFileAsync(int fileId) {
    try {
      ocrFile(fileId);
    } catch (Exception e) {
      log.error("Error running OCR for fileId: {}", fileId, e);
      throw new RuntimeException(e);
    }
  }

  public List<UnapprovedFileResponse> findUnapprovedOcrFilesWithDetails() {
    List<FileOcrProjection> results = fileRepository.findUnapprovedOcrFilesWithDetails();
    return fileMapper.projectionsToResponses(results);
  }

  public List<TicketItemResponse> getFileFields(int fileId) {

    List<TicketItemResponse> allFields = new ArrayList<>();

    final File file = fileRepository.findById(fileId).orElseThrow(() -> new BulkloadsException("File not found"));
    if (isNull(file.getFileType())) {
      return allFields;
    }

    final FileType fileType = file.getFileType();
    File originFile = null;
    if (isEmpty(fileType.getOcrModelId())) {
      return allFields;
    }

    if (fileType.getFileTypeId() == 2) {
      // check if fileId exists in load_assignments.loading_ticket_file_id
      List<Assignment> assignments = assignmentRepository.findAllByUnloadingTicketFileId(fileId);
      if (!isEmpty(assignments) && nonNull(assignments.get(0).getLoadingTicketFileId())) {
        originFile = fileRepository.findById(assignments.get(0).getLoadingTicketFileId()).orElseThrow();
      }
    }

    List<FileField> fields = fileRepository.getFileFields(file);
    List<FileField> originFields = originFile != null ? fileRepository.getFileFields(originFile) : null;

    // Special fields
    TicketItemResponse ticketNumber = null;
    TicketItemResponse grossWeight = null;
    TicketItemResponse tareWeight = null;
    TicketItemResponse volume = null;
    TicketItemResponse ticketDate = null;
    List<TicketItemResponse> grades = new ArrayList<>();
    List<TicketItemResponse> gradesWithoutValues = new ArrayList<>();

    // loop fields
    for (FileField field : fields) {
      final FileFieldResponse fileFieldResponse = fileFieldMapper.entityToResponse(field);
      String originValue = "";

      // Find matching origin field
      if (nonNull(originFields) && nonNull(field.getFieldName())) {
        // For special fields (ticket number, tare weight, date), match by field name
        for (FileField originField : originFields) {
          if (nonNull(originField.getFieldName())
              && originField.getFieldName().equalsIgnoreCase(field.getFieldName())) {
            originValue = originField.getFieldValue();
            break;
          }
        }
      }

      TicketItemResponse ticketItem = TicketItemResponse.builder()
          .fileField(fileFieldResponse)
          .originValue(originValue)
          .build();

      if (field.getFieldName().equalsIgnoreCase(OCR_FIELD_TICKET_NUMBER)) {
        ticketNumber = ticketItem;
      } else if (field.getFieldName().equalsIgnoreCase(OCR_FIELD_TICKET_DATE)) {
        ticketDate = ticketItem;
      } else if (field.getFieldName().equalsIgnoreCase(OCR_FIELD_GROSS_WEIGHT)) {
        grossWeight = ticketItem;
      } else if (field.getFieldName().equalsIgnoreCase(OCR_FIELD_TARE_WEIGHT)) {
        tareWeight = ticketItem;
      } else if (field.getFieldName().equalsIgnoreCase(OCR_FIELD_VOLUME)) {
        volume = ticketItem;
      } else { //if (field.getGradeId() != null) {
        if (!isEmpty(field.getFieldValue())) {
          grades.add(ticketItem);
        } else {
          gradesWithoutValues.add(ticketItem);
        }
      }
    }

    // Add fields in the required order
    if (ticketNumber != null) {
      allFields.add(ticketNumber);
    }
    if (ticketDate != null) {
      allFields.add(ticketDate);
    }
    if (grossWeight != null) {
      allFields.add(grossWeight);
    }
    if (tareWeight != null) {
      allFields.add(tareWeight);
    }
    if (volume != null) {
      allFields.add(volume);
    }

    // Add grades with values, sorted alphabetically
    grades.sort((a, b) -> {
      String nameA = a.getFileField().getFieldName();
      String nameB = b.getFileField().getFieldName();
      return nameA.compareToIgnoreCase(nameB);
    });
    allFields.addAll(grades);

    // Add grades without values, sorted alphabetically
    gradesWithoutValues.sort((a, b) -> {
      String nameA = a.getFileField().getFieldName();
      String nameB = b.getFileField().getFieldName();
      return nameA.compareToIgnoreCase(nameB);
    });
    allFields.addAll(gradesWithoutValues);

    return allFields;
  }

  public void updateFileFields(int fileId, List<@Valid FileFieldRequest> request) {
    final File file = fileRepository.findById(fileId)
        .orElseThrow(() -> new BulkloadsException("File not found"));

    if (isEmpty(request)) {
      return;
    }

    // Validate request fields
    Map<String, String> errors = new HashMap<>();

    for (FileFieldRequest fieldRequest : request) {
      if (isMissingOrIsEmpty(fieldRequest.getFieldName())) {
        errors.put("fields", "Field name is required");
      }
    }

    if (!errors.isEmpty()) {
      throw new ValidationException(errors);
    }

    // Get current user ID for auditing
    final User user = userService.getLoggedInUser();
    Integer userId = user.getUserId();
    Instant now = Instant.now();

    // Get existing fields for this file
    List<FileField> existingFields = fileRepository.getFileFields(file);

    for (FileFieldRequest fieldRequest : request) {
      if (isMissingOrIsEmpty(fieldRequest.getFieldName())) {
        continue;
      }

      // Find the existing field to update
      FileField fieldToUpdate = null;

      for (FileField field : existingFields) {
        if (nonNull(field.getFieldName())
            && field.getFieldName().equalsIgnoreCase(fieldRequest.getFieldName().orElse(""))) {
          fieldToUpdate = field;
          break;
        }
      }

      if (nonNull(fieldToUpdate)) {
        // Update the field
        fieldToUpdate.setFieldValue(fieldRequest.getFieldValue().orElse(""));
        fieldToUpdate.setEditByUserId(userId);
        fieldToUpdate.setEditDate(now);
      }
    }

    // Mark the file as OCR approved
    file.setOcrApproved(true);
    file.setOcrApprovedDate(now);
    file.setApprovedByUserId(userId);

    // Save all changes
    fileRepository.save(file);

    // Publish event to notify other components about the updated fields
    applicationEventPublisher.publishEvent(new FileFieldsUpdatedEvent(file, existingFields));
  }

  private void ocrFile(int fileId) {
    String targetUrl = appProperties.getDomainUrl() + "/rest/files/" + fileId + "/ocr";
    try {
      URI uri = new URIBuilder(targetUrl)
          .addParameter("uuid", UUID)
          .build();

      OcrResponse response = webClient.post()
          .uri(uri)
          .header("Content-Type", "application/json")
          .bodyValue(Map.of())
          .retrieve()
          .bodyToMono(OcrResponse.class)
          .block();

      if (isEmpty(response)) {
        log.error("Error calling OCR service: {} for file_id: {}", targetUrl, fileId);
        throw new BulkloadsException("Error calling OCR service");

      }
      log.debug("Response from OCR service: [{}]", response.getMessage());

    } catch (WebClientResponseException e) {
      log.error("Error calling OCR service: {} for file_id: {}", targetUrl, fileId, e);
      throw new BulkloadsException(e);
    } catch (Exception e) {
      log.error("Unexpected error calling OCR service: {} for file_id: {}", targetUrl, fileId, e);
      throw new BulkloadsException(e);
    }
  }
}