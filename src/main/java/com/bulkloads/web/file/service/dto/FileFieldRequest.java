package com.bulkloads.web.file.service.dto;

import java.util.Optional;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class FileFieldRequest {
  @Schema(name = "field_name", description = "Field name to update", requiredMode = Schema.RequiredMode.REQUIRED)
  private Optional<String> fieldName;
  
  @Schema(name = "field_value", description = "New value for the field", requiredMode = Schema.RequiredMode.REQUIRED)
  private Optional<String> fieldValue;
}