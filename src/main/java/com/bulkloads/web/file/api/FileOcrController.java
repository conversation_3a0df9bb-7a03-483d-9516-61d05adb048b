package com.bulkloads.web.file.api;

import static com.bulkloads.config.AppConstants.UserRole.ROLE_SITE_ADMIN;
import java.util.List;
import com.bulkloads.common.api.ApiResponse;
import com.bulkloads.web.file.service.FileOcrService;
import com.bulkloads.web.file.service.dto.FileFieldRequest;
import com.bulkloads.web.file.service.dto.TicketItemResponse;
import com.bulkloads.web.file.service.dto.UnapprovedFileResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Positive;
import lombok.RequiredArgsConstructor;

@RestController
@RequestMapping(path = "/rest/files/ocr")
@Tag(name = "File")
@RequiredArgsConstructor
@Validated
public class FileOcrController {

  private final FileOcrService fileOcrService;

  @GetMapping("/unapproved")
  @PreAuthorize("hasRole('" + ROLE_SITE_ADMIN + "')")
  @Operation(summary = "Get unapproved OCR files")
  public List<UnapprovedFileResponse> getFileUnapproved() {
    return fileOcrService.findUnapprovedOcrFilesWithDetails();
  }

  @GetMapping("/{file_id}/fields")
  @PreAuthorize("hasRole('" + ROLE_SITE_ADMIN + "')")
  @Operation(summary = "Get OCR fields for a file")
  public List<TicketItemResponse> getFileFields(
      @PathVariable("file_id") 
      @Positive(message = "File id should be positive") final int fileId) {
    return fileOcrService.getFileFields(fileId);
  }
  
  @PutMapping("/{file_id}/fields")
  @PreAuthorize("hasRole('" + ROLE_SITE_ADMIN + "')")
  @Operation(summary = "Update OCR fields for a file")
  public ApiResponse<Void, Integer> updateFileFields(
      @PathVariable("file_id") 
      @Positive(message = "File id should be positive") final int fileId, 
      @Valid @RequestBody List<@Valid FileFieldRequest> request) {
    fileOcrService.updateFileFields(fileId, request);
    
    return ApiResponse.<Void, Integer>builder()
        .key(fileId)
        .message("Ocr values updated")
        .build();
  }
}