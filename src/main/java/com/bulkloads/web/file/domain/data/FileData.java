package com.bulkloads.web.file.domain.data;

import java.util.Optional;
import com.bulkloads.web.file.domain.entity.FileType;
import lombok.Data;

@Data
public class FileData {

  Optional<FileType> fileType;
  Optional<String> fileUrl;
  Optional<String> thumbUrl;
  Optional<String> mimeType;
  Optional<Boolean> isImage;
  Optional<Boolean> isAudio;
  Optional<Long> size;
  Optional<String> extension;
  Optional<String> filename;
  Optional<Integer> numberOfPages = Optional.of(1);
  Optional<String> caption;
  Optional<Boolean> ocrProcessed;
}
