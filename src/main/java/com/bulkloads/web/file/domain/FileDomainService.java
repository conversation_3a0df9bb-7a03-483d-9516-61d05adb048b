package com.bulkloads.web.file.domain;

import static com.bulkloads.common.validation.ValidationUtils.existsAndIsNotEmpty;
import java.time.Instant;
import com.bulkloads.common.BaseDomainService;
import com.bulkloads.common.UserUtil;
import com.bulkloads.common.validation.Result;
import com.bulkloads.common.validation.ValidationMethod;
import com.bulkloads.web.file.domain.data.FileData;
import com.bulkloads.web.file.domain.entity.File;
import com.bulkloads.web.file.domain.entity.UserFile;
import com.bulkloads.web.file.mapper.FileMapper;
import com.bulkloads.web.user.service.UserService;
import org.springframework.stereotype.Component;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
@RequiredArgsConstructor
public class FileDomainService extends BaseDomainService<File> {

  private final FileMapper fileMapper;
  private final UserService userService;

  public Result<File> create(final FileData fileData) {
    return validate(new File(), null, fileData, ValidationMethod.CREATE);
  }

  @Override
  public void validateDataAndMapToEntity(final Result<File> result,
                                         final File entity,
                                         final File existing,
                                         final Object data,
                                         final ValidationMethod method) {

    FileData fileData = (FileData) data;

    UserUtil.getUserId().ifPresent(entity::setAddedByUserId);
    UserUtil.getAbUserId().ifPresent(entity::setAddedByAbUserId);

    if (existsAndIsNotEmpty(fileData.getFileType())) {
      entity.setOcrModelId(fileData.getFileType().get().getOcrModelId());
      if (!entity.getOcrModelId().isEmpty() && fileData.getNumberOfPages().get() > 1) {
        result.addError("number_of_pages", "Upload a single image/page");
      }
    }


    UserFile userFile = new UserFile();
    userFile.setFile(entity);
    userFile.setUser(userService.getLoggedInUser());
    userFile.setDateAdded(Instant.now());
    entity.getUserFiles().add(userFile);
  }

  @Override
  public void mapToEntityAuto(final Object data, final File entity) {
    fileMapper.dataToEntity((FileData) data, entity);

    entity.setDateAdded(Instant.now());
  }

  @Override
  public void validateEntity(final Result<File> result, final File entity) {

    // any custom validation?
  }

}
