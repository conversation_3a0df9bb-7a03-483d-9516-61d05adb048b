package com.bulkloads.web.file.mapper;

import static com.bulkloads.common.validation.ValidationUtils.existsAndIsNotEmpty;
import java.util.List;
import java.util.Optional;
import com.bulkloads.exception.BulkloadsException;
import com.bulkloads.web.contracts.mapper.CommonMapper;
import com.bulkloads.web.file.domain.data.FileData;
import com.bulkloads.web.file.domain.entity.File;
import com.bulkloads.web.file.domain.entity.FileType;
import com.bulkloads.web.file.repository.FileOcrProjection;
import com.bulkloads.web.file.repository.FileTypeRepository;
import com.bulkloads.web.file.service.dto.FileRequest;
import com.bulkloads.web.file.service.dto.FileResponse;
import com.bulkloads.web.file.service.dto.UnapprovedFileResponse;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.springframework.beans.factory.annotation.Autowired;

@Mapper(componentModel = "spring",
    nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
    nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
    unmappedTargetPolicy = org.mapstruct.ReportingPolicy.IGNORE,
    uses = CommonMapper.class)
public abstract class FileMapper {

  @Autowired
  private FileTypeRepository fileTypeRepository;

  @Mapping(target = "fileType", source = "fileTypeId")
  public abstract FileData fileRequestToData(final FileRequest request);

  public abstract void dataToEntity(final FileData data, @MappingTarget final File entity);


  @Mapping(target = "fileTypeId", expression = "java(file.getFileType() != null ? file.getFileType().getFileTypeId() : null)")
  @Mapping(target = "fileType", expression = "java(file.getFileType() != null ? file.getFileType().getFileType() : \"\")")
  public abstract FileResponse entityToResponse(final File file);

  @Mapping(target = "fileTypeId", expression = "java(com.bulkloads.common.validation.ValidationUtils.existsAndIsNotEmpty(data.getFileType()) ? "
      + "data.getFileType().get().getFileTypeId() : null)")
  @Mapping(target = "fileType", expression = "java(com.bulkloads.common.validation.ValidationUtils.existsAndIsNotEmpty(data.getFileType()) ? "
      + "data.getFileType().get().getFileType() : \"\")")
  public abstract FileResponse dataToResponse(final FileData data);

  public abstract UnapprovedFileResponse projectionToResponse(FileOcrProjection projection);

  public abstract List<UnapprovedFileResponse> projectionsToResponses(List<FileOcrProjection> projections);

  public Optional<FileType> mapOptionalFileTypeIdToOptionalFileType(final Optional<Integer> fileTypeId) {
    if (existsAndIsNotEmpty(fileTypeId)) {
      return Optional.ofNullable(fileTypeRepository.findById(fileTypeId.get())
          .orElseThrow(() -> new BulkloadsException("The file_type_id was not found. Enter a valid file type.")));
    }
    return null;
  }
}
