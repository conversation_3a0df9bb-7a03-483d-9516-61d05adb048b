package com.bulkloads.web.file.repository;

import java.util.List;
import com.bulkloads.web.file.domain.entity.FileType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface FileTypeRepository extends JpaRepository<FileType, Integer> {

  @Query("""
        SELECT ft FROM FileType ft
        WHERE (:fileTypeId IS NULL OR ft.fileTypeId = :fileTypeId)
        AND ft.fileTypeUserCompanyId = :fileTypeUserCompanyId
        AND ft.fileTypeUserId = :fileTypeUserId
        ORDER BY ft.fileTypeId
        """)
  List<FileType> findFileTypes(@Param("fileTypeUserCompanyId") int fileTypeUserCompanyId,
                               @Param("fileTypeUserId") int fileTypeUserId,
                               @Param("fileTypeId") Integer fileTypeId);

}
