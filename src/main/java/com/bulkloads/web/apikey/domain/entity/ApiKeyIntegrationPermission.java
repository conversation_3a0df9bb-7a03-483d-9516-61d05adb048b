package com.bulkloads.web.apikey.domain.entity;

import com.bulkloads.web.usercompany.domain.entity.UserCompany;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.IdClass;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "api_key_integration_permissions")
@IdClass(ApiKeyIntegrationPermissionId.class)
@Getter
@Setter
public class ApiKeyIntegrationPermission {

  @Id
  @ManyToOne
  @JoinColumn(name = "api_key_id")
  private ApiKey apiKey;

  @Id
  @ManyToOne
  @JoinColumn(name = "user_company_id")
  private UserCompany userCompany;
}