package com.bulkloads.web.apikey.domain.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(name = "api_endpoints")
public class ApiEndpoint {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "api_endpoint_id")
  private Integer id;

  @Size(max = 50)
  @NotNull
  @Column(name = "category")
  private String category;

  @Size(max = 10)
  @NotNull
  @Column(name = "method")
  private String method;

  @Size(max = 150)
  @NotNull
  @Column(name = "path")
  private String path;

  @Size(max = 500)
  @Column(name = "description")
  private String description;

}