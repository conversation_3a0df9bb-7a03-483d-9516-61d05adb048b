package com.bulkloads.web.equipment.service;

import java.util.List;
import com.bulkloads.web.equipment.repository.EquipmentQueryRepository;
import com.bulkloads.web.equipment.service.dto.EquipmentListResponse;
import com.bulkloads.web.user.repository.UserRepository;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RequiredArgsConstructor
public class EquipmentQueryService {

  private final EquipmentQueryRepository equipmentQueryRepository;
  private final UserRepository userRepository;

  public List<EquipmentListResponse> getEquipments(String term, String order) {
    return equipmentQueryRepository.getEquipments(term, order);
  }

}
