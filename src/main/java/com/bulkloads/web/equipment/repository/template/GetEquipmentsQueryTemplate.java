package com.bulkloads.web.equipment.repository.template;

public class GetEquipmentsQueryTemplate {

  public static final String GET_EQUIPMENTS_QUERY_TEMPLATE = """
          SELECT
            equipment_id,
            equipment_name,
            freq
          FROM equipment
          WHERE site_id = 1
           <% if (equipmentIds != null) { %>
               <% params.put("equipmentIds", equipmentIds) %>
               and a.equipment_id in (:equipmentIds)
           <% } %>

           <% if (term != null) { %>
               <% var wildTerm = "%"+term+"%" %>
               <% params.put("wildTerm", wildTerm) %>
               <% params.put("term", term) %>
               AND (
                  equipment_name like :wildTerm
                  OR
                  replace(replace(replace(equipment_name,' ','-'),'/','-'),',','-') in (:term)
              )
           <% } %>
           <% if (order == null || order.equalsIgnoreCase("freq")) { %>
               ORDER BY freq desc
          <% } else { %>
              ORDER BY equipment_id
          <% } %>
      """;
}
