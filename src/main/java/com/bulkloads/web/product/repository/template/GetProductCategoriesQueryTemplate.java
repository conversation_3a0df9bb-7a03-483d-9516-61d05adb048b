package com.bulkloads.web.product.repository.template;

import org.intellij.lang.annotations.Language;

public class GetProductCategoriesQueryTemplate {

  @Language("SQL")
  public static final String GET_PRODUCT_CATEGORIES_QUERY_TEMPLATE = """
          SELECT
              rate_product_category_id,
              rate_product_category,
              rate_product_category_equipment
          from rate_product_categories
          order by rate_product_category
      """;

}
