package com.bulkloads.web.product.api;

import static com.bulkloads.config.AppConstants.UserRole.ROLE_USER;
import java.util.List;
import com.bulkloads.web.product.service.ProductQueryService;
import com.bulkloads.web.product.service.dto.ProductCategoryListResponse;
import com.bulkloads.web.product.service.dto.ProductExternalListResponse;
import com.bulkloads.web.product.service.dto.ProductListResponse;
import org.springframework.http.MediaType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequestMapping(path = "/rest/products", produces = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "Products")
@CrossOrigin(origins = "*", maxAge = 3600)
@RequiredArgsConstructor
@PreAuthorize("hasRole('" + ROLE_USER + "')")
public class ProductQueryController {

  private final ProductQueryService productQueryService;

  @GetMapping
  public List<ProductListResponse> getProducts(
      @RequestParam(value = "term", required = false) String term
  ) {
    return productQueryService.getProducts(term);
  }

  @GetMapping("/external")
  public List<ProductExternalListResponse> getProductExternals() {
    return productQueryService.getProductExternals();
  }

  @GetMapping("/categories")
  public List<ProductCategoryListResponse> getProductCategories() {
    return productQueryService.getProductCategories();
  }

}
