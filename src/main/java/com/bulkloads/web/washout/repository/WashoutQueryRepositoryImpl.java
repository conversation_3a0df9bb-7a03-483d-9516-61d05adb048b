package com.bulkloads.web.washout.repository;

import static com.bulkloads.web.washout.repository.template.GetWashoutsQueryTemplate.GET_WASHOUTS_QUERY_TEMPLATE;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.bulkloads.common.jpa.nativejpa.JpaNativeQueryService;
import com.bulkloads.web.washout.service.dto.WashoutListResponse;
import com.bulkloads.web.washout.service.dto.WashoutResponse;
import com.bulkloads.web.washout.service.dto.transformers.WashoutListResponseTransformer;
import com.bulkloads.web.washout.service.dto.transformers.WashoutResponseTransformer;
import org.springframework.stereotype.Repository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Repository
@RequiredArgsConstructor
class WashoutQueryRepositoryImpl implements WashoutQueryRepository {

  private final JpaNativeQueryService jpaNativeQueryService;
  private final WashoutResponseTransformer washoutResponseTransformer;
  private final WashoutListResponseTransformer washoutListResponseTransformer;

  public List<WashoutListResponse> getWashouts(
      String term,
      Double latitude,
      Double longitude,
      Double radius,
      String state,
      Integer skip,
      Integer limit
  ) {
    Map<String, Object> params = new HashMap<>();
    params.put("term", term);
    params.put("latitude", latitude);
    params.put("longitude", longitude);
    params.put("state", state);
    params.put("radius", radius);
    params.put("skip", skip);
    params.put("limit", limit);

    return jpaNativeQueryService.query(
        GET_WASHOUTS_QUERY_TEMPLATE,
        params,
        washoutListResponseTransformer);

  }

  public WashoutResponse getWashout(Integer washoutId) {
    Map<String, Object> params = new HashMap<>();

    params.put("washoutId", washoutId);

    return jpaNativeQueryService.queryForObject(
        GET_WASHOUTS_QUERY_TEMPLATE,
        params,
        washoutResponseTransformer
    );
  }
}
