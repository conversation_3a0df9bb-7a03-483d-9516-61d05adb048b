package com.bulkloads.web.washout.repository;

import java.util.List;
import com.bulkloads.web.washout.service.dto.WashoutListResponse;
import com.bulkloads.web.washout.service.dto.WashoutResponse;

interface WashoutQueryRepository {

  List<WashoutListResponse> getWashouts(
      String term,
      Double latitude,
      Double longitude,
      Double radius,
      String state,
      Integer skip,
      Integer limit
  );

  WashoutResponse getWashout(Integer washoutId);

}
