package com.bulkloads.web.washout.repository.template;

import org.intellij.lang.annotations.Language;

public class GetWashoutsQueryTemplate {

  @Language("SQL")
  public static final String GET_WASHOUTS_QUERY_TEMPLATE = """
      SELECT
          w.washout_id,
          w.washout_name,
          w.origin_city,
          w.origin_state,
          w.phone_1,
          w.phone_2,
          w.directions,
          w.cost,
          w.address,
          w.hot_water,
          w.date_verified,
          w.day_contact,
          w.after_hours_contact,
          w.hours_of_operation,
          w.longitude,
          w.latitude,
          w.posted_date,
          w.notes,
          w.missing_info,
          w.public_notes,
          w.premium_end_date,
          w.allow_livestock,
          w.allow_tanker,
          w.edit_date,
          w.edit_by_user_id,
          w.take_credit_cards,
          w.location_verified_date,

          w.origin_city as city,
          w.origin_state as abbreviation,
          states.state as `state`

      <% if (paramExistsAdd("latitude") && paramExistsAdd("longitude")) { %>

          , st_distance_sphere(
              point(:longitude, :latitude),
              point(w.longitude, w.latitude)) / 1609.34 as distance
      <% } else { %>
          , 0 as distance
      <% } %>

      FROM washouts w
          LEFT JOIN states ON w.origin_state = states.abbreviation

      WHERE w.latitude IS NOT NULL
          AND w.longitude IS NOT NULL

      <% if (paramExistsAdd("washoutId")) { %>
          AND w.washout_id = :washoutId
      <% } %>

      <% if (paramExistsAdd("state")) { %>
          AND w.origin_state = :state
      <% } %>

      <% if (paramExistsAdd("term")) { %>
          <% params.put("termParam", "%" + term + "%") %>
          AND w.washout_name like :termParam
      <% } %>

       <% if (paramExistsAdd("latitude") && paramExistsAdd("longitude") && paramExistsAdd("radius")) { %>
          AND st_distance_sphere(
              point(:longitude, :latitude),
              point(w.longitude, w.latitude))/1609.34
              <= :radius
          ORDER BY st_distance_sphere(
              point(:longitude, :latitude),
              point(w.longitude, w.latitude))/1609.34
      <% } else { %>
          ORDER BY states.state, w.origin_city, w.washout_name
      <% } %>

      <% if (paramExistsAdd("limit")) { %>
          LIMIT
          <% if (paramExistsAdd("skip")) { %>
          :skip,
          <% } %>
          :limit
      <% } %>
      """;
}
