package com.bulkloads.web.washout.service;

import java.util.List;
import java.util.Optional;
import com.bulkloads.common.UserUtil;
import com.bulkloads.exception.ValidationException;
import com.bulkloads.web.washout.domain.WashoutDomainService;
import com.bulkloads.web.washout.domain.data.WashoutData;
import com.bulkloads.web.washout.domain.entity.Washout;
import com.bulkloads.web.washout.mapper.WashoutMapper;
import com.bulkloads.web.washout.repository.WashoutCommentRepository;
import com.bulkloads.web.washout.repository.WashoutRepository;
import com.bulkloads.web.washout.service.dto.WashoutCommentResponse;
import com.bulkloads.web.washout.service.dto.WashoutListResponse;
import com.bulkloads.web.washout.service.dto.WashoutRequest;
import com.bulkloads.web.washout.service.dto.WashoutResponse;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RequiredArgsConstructor
public class WashoutService {

  private final WashoutDomainService washoutDomainService;
  private final WashoutRepository washoutRepository;
  private final WashoutCommentRepository washoutCommentRepository;
  private final WashoutMapper washoutMapper;


  public WashoutResponse createWashout(WashoutRequest request) {
    WashoutData data = washoutMapper.requestToData(request);
    Washout washout = washoutDomainService.create(data).orElseThrow();
    washout = washoutRepository.save(washout);
    return getWashout(washout.getWashoutId());
  }

  public WashoutResponse updateWashout(int washoutId, WashoutRequest request) {
    final Washout washoutToUpdate = findWashoutById(washoutId);
    WashoutData data = washoutMapper.requestToData(request);
    Washout washout = washoutDomainService.update(washoutToUpdate, data).orElseThrow();
    washout = washoutRepository.save(washout);
    return getWashout(washout.getWashoutId());
  }

  public void removeWashout(final int washoutId) {
    final Washout washoutToRemove = findWashoutById(washoutId);
    washoutRepository.delete(washoutToRemove);
  }


  public WashoutResponse getWashout(final Integer washoutId) {

    final WashoutResponse washoutResponse = washoutRepository.getWashout(washoutId);

    if (washoutResponse == null) {
      return null;
    }

    final Optional<Integer> userId = UserUtil.getUserId();
    final boolean isSiteAdmin = UserUtil.isSiteAdmin();

    final List<WashoutCommentResponse> washoutComments = washoutCommentRepository.getWashoutComments(
        washoutId,
        null,
        null,
        null,
        userId.orElse(null),
        isSiteAdmin);

    washoutResponse.setComments(washoutComments);
    return washoutResponse;
  }

  public List<WashoutListResponse> getWashouts(String term, Double latitude, Double longitude, Double radius,
      String state, Integer skip, Integer limit) {
    return washoutRepository.getWashouts(term, latitude, longitude, radius, state, skip, limit);
  }

  private Washout findWashoutById(int washoutId) {
    return washoutRepository.findById(washoutId).orElseThrow(
        () -> new ValidationException("washout_id", "Could not find id %s".formatted(washoutId)));
  }
}
