package com.bulkloads.web.washout.service.dto.validators;

import java.util.Arrays;
import java.util.List;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

public class WashoutCommentTypeValidator implements ConstraintValidator<WashoutCommentTypeValid, String> {

  private static final List<String> ALLOWED_TYPES = Arrays.asList("comment", "suggestion");

  @Override
  public boolean isValid(String value, ConstraintValidatorContext context) {
    return value != null && ALLOWED_TYPES.contains(value.toLowerCase());
  }
}