package com.bulkloads.web.washout.service.dto.transformers;

import com.bulkloads.common.jpa.nativejpa.QueryParts;
import com.bulkloads.web.washout.service.dto.WashoutCommentResponse;
import org.hibernate.query.TupleTransformer;
import org.springframework.stereotype.Component;
import lombok.RequiredArgsConstructor;

@Component
@RequiredArgsConstructor
public class WashoutCommentResponseTransformer implements TupleTransformer<WashoutCommentResponse> {

  @Override
  public WashoutCommentResponse transformTuple(Object[] columns, String[] aliases) {
    QueryParts parts = new QueryParts(columns, aliases);

    WashoutCommentResponse response = new WashoutCommentResponse();
    response.setWashoutCommentId(parts.asInteger("washout_comment_id"));
    response.setWashoutComment(parts.asString("washout_comment"));
    response.setWashoutCommentDate(parts.asLocalDate("washout_comment_date"));
    response.setWashoutCommentType(parts.asString("washout_comment_type"));
    response.setWashoutCommentApproved(parts.asBoolean("washout_comment_approved"));
    response.setAvatarSmall(parts.asString("avatar_small"));
    response.setFirstName(parts.asString("first_name"));
    response.setLastName(parts.asString("last_name"));
    return response;
  }

}
