package com.bulkloads.web.washout.service.dto;

import java.time.Instant;
import java.time.LocalDate;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class WashoutListResponse {

  private Integer washoutId;
  private String address;
  private String afterHoursContact;
  private String cost;
  private LocalDate dateVerified;
  private String dayContact;
  private String directions;
  private Double distance;
  private String hotWater;
  private String hoursOfOperation;
  private Double latitude;
  private Double longitude;
  private String city;
  private String abbreviation;

  @JsonProperty("phone_1")
  private String phone1;

  @JsonProperty("phone_2")
  private String phone2;

  private LocalDate postedDate;
  private String publicNotes;
  private String state;
  private String washoutName;
  private String allowLivestock;
  private String allowTanker;
  private String takeCreditCards;
  private Instant editDate;
  private Integer editByUserId;
  private LocalDate locationVerifiedDate;
}
