package com.bulkloads.web.washout.domain;

import static com.bulkloads.common.validation.ValidationUtils.exists;
import static com.bulkloads.common.validation.ValidationUtils.isEmpty;
import static com.bulkloads.common.validation.ValidationUtils.isMissingOrIsEmpty;
import java.util.Optional;
import java.util.Set;
import com.bulkloads.common.BaseDomainService;
import com.bulkloads.common.validation.Result;
import com.bulkloads.common.validation.ValidationMethod;
import com.bulkloads.exception.ValidationException;
import com.bulkloads.web.city.domain.entity.City;
import com.bulkloads.web.city.repository.CityRepository;
import com.bulkloads.web.user.domain.entity.User;
import com.bulkloads.web.user.service.UserService;
import com.bulkloads.web.washout.domain.data.WashoutData;
import com.bulkloads.web.washout.domain.entity.Washout;
import com.bulkloads.web.washout.mapper.WashoutMapper;
import com.bulkloads.web.washout.repository.WashoutRepository;
import org.springframework.stereotype.Component;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
@RequiredArgsConstructor
public class WashoutDomainService extends BaseDomainService<Washout> {

  private final WashoutRepository washoutRepository;
  private final CityRepository cityRepository;
  private final WashoutMapper washoutMapper;
  private final UserService userService;

  public Result<Washout> create(WashoutData dto) {
    return validate(new Washout(), null, dto, ValidationMethod.CREATE);
  }

  public Result<Washout> update(Washout washout, WashoutData dto) {
    final Washout existing = washoutRepository.findById(washout.getWashoutId()).orElseThrow(
        () -> new ValidationException("washout_id", "Could not find id %s".formatted(washout.getWashoutId())));
    return validate(washout, existing, dto, ValidationMethod.UPDATE);
  }

  @Override
  public void mapToEntityAuto(Object washoutData, Washout entity) {
    washoutMapper.dataToEntity((WashoutData) washoutData, entity);
  }

  @Override
  public void validateDataAndMapToEntity(
      Result<Washout> result,
      Washout entity,
      Washout existing,
      Object dataObj,
      ValidationMethod method) {

    WashoutData data = (WashoutData) dataObj;

    validateDtoLocation(result, data, entity);

    if (exists(data.getAllowLivestock())) {
      if (data.getAllowLivestock().isEmpty()) {
        data.setAllowLivestock(Optional.of(""));
      } else {
        if (!isValidPseudoBoolean(data.getAllowLivestock().get())) {
          result.addError("allow_live_stock", "Should be Y, N or empty");
        }
      }
    }

    if (exists(data.getAllowTanker())) {
      if (data.getAllowTanker().isEmpty()) {
        data.setAllowTanker(Optional.of(""));
      } else {
        if (!isValidPseudoBoolean(data.getAllowTanker().get())) {
          result.addError("allow_tanker", "Should be Y, N or empty");
        }
      }
    }

    if (exists(data.getHotWater())) {
      if (data.getHotWater().isEmpty()) {
        data.setHotWater(Optional.of(""));
      } else {
        if (!isValidPseudoBoolean(data.getHotWater().get())) {
          result.addError("hot_water", "Should be Y, N or empty");
        }
      }
    }

    if (exists(data.getTakeCreditCards())) {
      if (data.getTakeCreditCards().isEmpty()) {
        data.setTakeCreditCards(Optional.of(""));
      } else {
        if (!isValidPseudoBoolean(data.getTakeCreditCards().get())) {
          result.addError("take_credit_cards", "Should be Y, N or empty");
        }
      }
    }

    if (method == ValidationMethod.UPDATE) {
      final User user = userService.getLoggedInUser();
      entity.setEditedBy(user);
    }
  }


  private void validateDtoLocation(Result<Washout> result, WashoutData data, Washout entity) {

    if (!isEmpty(data.getLocation())) {

      City city = cityRepository.getCityZip(data.getLocation());

      if (city == null) {
        result.addError("location", "Invalid location");
        return;
      }

      entity.setOriginCity(city.getCity());
      entity.setOriginState(city.getState());

      if (isMissingOrIsEmpty(data.getLatitude()) || isMissingOrIsEmpty(data.getLongitude())) {
        data.setLatitude(Optional.of(city.getLatitude()));
        data.setLongitude(Optional.of(city.getLongitude()));
      }
    }
  }


  @Override
  public void validateEntity(Result<Washout> result, Washout entity) {
  }

  private static boolean isValidPseudoBoolean(String value) {
    return Set.of("Y", "N", "").contains(value);
  }

}
