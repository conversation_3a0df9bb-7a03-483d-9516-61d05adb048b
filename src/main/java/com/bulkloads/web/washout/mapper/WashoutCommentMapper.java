package com.bulkloads.web.washout.mapper;

import com.bulkloads.web.contracts.mapper.CommonMapper;
import com.bulkloads.web.washout.domain.data.WashoutCommentData;
import com.bulkloads.web.washout.domain.entity.WashoutComment;
import com.bulkloads.web.washout.service.dto.WashoutCommentRequest;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;

@Mapper(componentModel = "spring",
    nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
    nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
    unmappedTargetPolicy = org.mapstruct.ReportingPolicy.IGNORE,
    uses = CommonMapper.class)
public abstract class WashoutCommentMapper {

  public abstract void dataToEntity(final WashoutCommentData data, @MappingTarget final WashoutComment washoutComment);

  public abstract WashoutCommentData requestToData(final WashoutCommentRequest request);
}
