package com.bulkloads.web.washout.mapper;

import com.bulkloads.web.contracts.mapper.CommonMapper;
import com.bulkloads.web.washout.domain.data.WashoutData;
import com.bulkloads.web.washout.domain.entity.Washout;
import com.bulkloads.web.washout.service.dto.WashoutRequest;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;

@Mapper(componentModel = "spring",
    nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
    nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
    unmappedTargetPolicy = org.mapstruct.ReportingPolicy.IGNORE,
    uses = CommonMapper.class)
public abstract class WashoutMapper {

  public abstract void dataToEntity(final WashoutData washoutData, @MappingTarget final Washout washout);

  public abstract WashoutData requestToData(final WashoutRequest washoutRequest);
}
