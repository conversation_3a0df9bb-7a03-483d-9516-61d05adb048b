package com.bulkloads.web.utility.service;

import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import com.bulkloads.web.utility.service.dto.ServerTimeResponse;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class UtilityService {

  @Getter
  @Value("${spring.profiles.active}")
  private String activeProfile;

  public boolean isTestMode() {
    return activeProfile.contains("test") || activeProfile.contains("dev");
  }

  public ServerTimeResponse getServerTime() {
    ZonedDateTime utcTime = ZonedDateTime.now(ZoneOffset.UTC);
    DateTimeFormatter formatter = DateTimeFormatter.ISO_DATE_TIME;

    return ServerTimeResponse.builder()
        .time(utcTime.format(formatter))
        .build();
  }
}
