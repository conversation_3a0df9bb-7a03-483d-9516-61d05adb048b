package com.bulkloads.web.link.api;

import com.bulkloads.web.link.service.LinkService;
import com.bulkloads.web.link.service.dto.LinkRequest;
import com.bulkloads.web.link.service.dto.LinkResponse;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;

@RestController
@RequestMapping("/rest/links")
@Tag(name = "Links")
@RequiredArgsConstructor
public class LinkController {

  private final LinkService linkService;

  @Operation(summary = "Get metadata for a dynamic link")
  @GetMapping("/{short_code}")
  public ResponseEntity<String> getMetadata(@PathVariable("short_code") String shortCode) {
    return linkService.getMetadata(shortCode)
        .map(ResponseEntity::ok)
        .orElse(ResponseEntity.notFound().build());
  }

  @Operation(summary = "Create a dynamic link")
  @PostMapping
  public LinkResponse createShortLink(@Valid @RequestBody LinkRequest request) {
    return linkService.create(request);
  }
}
