package com.bulkloads.web.common.jpa.converter;


import com.bulkloads.web.companyequipment.domain.entity.EquipmentStatus;
import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;

@Converter(autoApply = true)
public class EquipmentStatusConverter implements AttributeConverter<EquipmentStatus, String> {

  @Override
  public String convertToDatabaseColumn(EquipmentStatus attribute) {
    if (attribute == null) {
      return null;
    }
    return attribute.toString();
  }

  @Override
  public EquipmentStatus convertToEntityAttribute(String dbData) {
    if (dbData == null) {
      return null;
    }
    return EquipmentStatus.fromString(dbData);
  }
}