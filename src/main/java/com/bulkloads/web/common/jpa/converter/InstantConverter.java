package com.bulkloads.web.common.jpa.converter;

import static java.util.Objects.isNull;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;

@Converter(autoApply = true)
public class InstantConverter implements AttributeConverter<Instant, LocalDateTime> {

  @Override
  public LocalDateTime convertToDatabaseColumn(final Instant attribute) {
    if (isNull(attribute)) {
      return null;
    }
    final ZoneOffset offset = OffsetDateTime.now().getOffset();
    return attribute.atOffset(offset).toLocalDateTime();
  }

  @Override
  public Instant convertToEntityAttribute(final LocalDateTime dbData) {
    if (isNull(dbData)) {
      return null;
    }
    final ZoneOffset offset = OffsetDateTime.now().getOffset();
    return dbData.toInstant(offset);
  }
}