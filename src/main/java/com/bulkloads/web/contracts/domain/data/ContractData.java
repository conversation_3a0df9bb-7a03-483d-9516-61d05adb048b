package com.bulkloads.web.contracts.domain.data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Optional;
import com.bulkloads.web.addressbook.abcompany.domain.entity.AbCompany;
import com.bulkloads.web.commodity.domain.entity.Commodity;
import com.bulkloads.web.rate.domain.entity.RateType;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class ContractData {

  private Optional<String> externalContractId;
  private Optional<String> buySell;
  private Optional<String> contractNumber;
  private Optional<BigDecimal> rate;
  private Optional<RateType> rateType;
  private Optional<BigDecimal> freightRate;
  private Optional<RateType> freightRateType;
  private Optional<BigDecimal> freightRatePerMile;
  private Optional<Integer> numberOfLoads;
  private Optional<BigDecimal> quantity;
  private Optional<BigDecimal> weight;
  private Optional<Integer> remainingNumberOfLoads;
  private Optional<BigDecimal> remainingQuantity;
  private Optional<BigDecimal> remainingWeight;
  private Optional<Commodity> commodity;
  private Optional<LocalDate> shipFrom;
  private Optional<LocalDate> shipTo;
  private Optional<String> contactInfo;
  private Optional<String> notes;
  private Optional<AbCompany> pickupAbCompany;
  private Optional<AbCompany> dropAbCompany;

  private boolean validateRemainingNumberOfLoads = true;
}
