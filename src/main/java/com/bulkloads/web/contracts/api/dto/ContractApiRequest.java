package com.bulkloads.web.contracts.api.dto;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Optional;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Value;
import lombok.extern.jackson.Jacksonized;

@Value
@Builder
@Jacksonized
public class ContractApiRequest {

  @Schema(name = "external_contract_id", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<String> externalContractId;

  @Schema(name = "buy_sell", description = "buy|sell", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<String> buySell;

  @Schema(name = "contract_number", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<String> contractNumber;

  @Schema(name = "rate", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<BigDecimal> rate;

  @Schema(name = "rate_type", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<String> rateType;

  @Schema(name = "freight_rate", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<BigDecimal> freightRate;

  @Schema(name = "freight_rate_type", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<String> freightRateType;

  @Schema(name = "freight_rate_per_mile", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<BigDecimal> freightRatePerMile;

  @Schema(name = "number_of_loads", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<Integer> numberOfLoads;

  @Schema(name = "quantity", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<BigDecimal> quantity;

  @Schema(name = "commodityId", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<Integer> commodityId;

  @Schema(name = "commodity", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<String> commodity;

  @Schema(name = "ship_from", description = "shipping starting date", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<LocalDate> shipFrom;

  @Schema(name = "ship_to", description = "shipping ending date", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<LocalDate> shipTo;

  @Schema(name = "contact_info", description = "Trader's name/phone etc", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<String> contactInfo;

  @Schema(name = "notes", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<String> notes;

  @Schema(name = "pickup_ab_company_id", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<Integer> pickupAbCompanyId;

  @Schema(name = "drop_ab_company_id", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Optional<Integer> dropAbCompanyId;
}
