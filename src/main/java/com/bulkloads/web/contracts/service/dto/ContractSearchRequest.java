package com.bulkloads.web.contracts.service.dto;

import static java.util.Objects.isNull;
import java.time.Instant;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.web.bind.annotation.BindParam;
import io.swagger.v3.oas.annotations.Parameter;
import jakarta.validation.constraints.Pattern;
import lombok.Builder;
import lombok.Value;

@Value
@Builder
@ParameterObject
public class ContractSearchRequest {

  @Parameter(name = "user_ids", description = "List of user_ids to get contracts for")
  @BindParam("user_ids")
  String userIds;

  @Parameter(name = "contract_ids", description = "Comma separated list of Contract IDs")
  @BindParam("contract_ids")
  String contractIds;

  @Pattern(regexp = "(?i)buy|sell", message = "Buy or Sell")
  @Parameter(name = "buy_sell", description = "Get Buy or Sell contracts ('Buy', 'Sell')")
  @BindParam("buy_sell")
  String buySell;

  @Pattern(regexp = "(?i)open|closed", message = "Open or Closed")
  @Parameter(name = "contract_status", description = "open|closed")
  @BindParam("contract_status")
  String contractStatus;

  @Parameter(name = "last_modified_date",
      description = "Records that were edited after this date. UTC datetime in ISO8601 format e.g. 2022-05-03T18:31:38.480Z")
  @BindParam("last_modified_date")
  Instant lastModifiedDate;

  @Parameter(name = "include_deleted", description = "When syncing records with last_modified_date you may want to pass include_deleted=1"
      + " to get past records that were deleted after the last sync")
  @BindParam("include_deleted")
  Integer includeDeleted;

  public Integer getIncludeDeleted() {
    return isNull(includeDeleted) ? 0 : includeDeleted;
  }
}
