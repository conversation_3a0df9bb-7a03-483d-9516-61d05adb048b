package com.bulkloads.web.routing.domain.entity;

import java.math.BigDecimal;
import java.time.Instant;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Lob;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(name = "routes")
public class Route {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "routeID")
  private Integer id;

  @Size(max = 80, message = "Must be up to 80 chars")
  @NotNull
  @Column(name = "startAddress")
  private String startAddress = "";

  @Size(max = 60, message = "Must be up to 60 chars")
  @NotNull
  @Column(name = "startCity",  nullable = false)
  private String startCity = "";

  @Size(max = 2, message = "Must be up to 2 chars")
  @NotNull
  @Column(name = "startState",  nullable = false)
  private String startState = "";

  @Size(max = 60, message = "Must be up to 60 chars")
  @NotNull
  @Column(name = "startZip",  nullable = false)
  private String startZip = "";

  @Size(max = 2, message = "Must be up to 2 chars")
  @NotNull
  @Column(name = "startCountry",  nullable = false)
  private String startCountry = "";

  @Size(max = 80, message = "Must be up to 80 chars")
  @NotNull
  @Column(name = "endAddress",  nullable = false)
  private String endAddress = "";

  @Size(max = 60, message = "Must be up to 60 chars")
  @NotNull
  @Column(name = "endCity",  nullable = false)
  private String endCity = "";

  @Size(max = 2, message = "Must be up to 2 chars")
  @NotNull
  @Column(name = "endState",  nullable = false)
  private String endState = "";

  @Size(max = 60, message = "Must be up to 60 chars")
  @NotNull
  @Column(name = "endZip",  nullable = false)
  private String endZip = "";

  @Size(max = 2, message = "Must be up to 2 chars")
  @NotNull
  @Column(name = "endCountry",  nullable = false)
  private String endCountry = "";

  @Column(name = "startLat")
  private Double startLat;

  @Column(name = "startLng")
  private Double startLng;

  @Column(name = "endLat")
  private Double endLat;

  @Column(name = "endLng")
  private Double endLng;

  @NotNull
  @Lob
  @Column(name = "response",  nullable = false)
  private String response = "";

  @Column(name = "bearing")
  private Double bearing;

  @Size(max = 3, message = "Must be up to 3 chars")
  @Column(name = "bearing_direction",  length = 3)
  private String bearingDirection = "";

  @NotNull
  @Lob
  @Column(name = "errorMessage",  nullable = false)
  private String errorMessage = "";

  @Column(name = "miles",  precision = 10)
  private BigDecimal miles;

  @Column(name = "duration")
  private Long duration;

  @Size(max = 60, message = "Must be up to 60 chars")
  @NotNull
  @Column(name = "durationText",  nullable = false)
  private String durationText = "";

  @Column(name = "dateCreated")
  private Instant dateCreated;

  @Column(name = "stops")
  private Integer stops;

  @Lob
  @Column(name = "provider")
  private String provider = "google";

}