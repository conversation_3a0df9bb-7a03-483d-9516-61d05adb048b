package com.bulkloads.web.routing.provider;

import java.util.concurrent.ThreadLocalRandom;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

@Component
public class RoutingProviderFactory {

  private final RoutingProviderClient googleMapsClient;
  private final RoutingProviderClient mapboxClient;

  public RoutingProviderFactory(
      @Qualifier("googleMapsProviderClient") final RoutingProviderClient googleMapsClient,
      @Qualifier("mapboxProviderClient") final RoutingProviderClient mapboxClient) {
    this.googleMapsClient = googleMapsClient;
    this.mapboxClient = mapboxClient;
  }

  public RoutingProviderClient getClient() {
    return ThreadLocalRandom.current().nextInt(1000) > 333 ? googleMapsClient : mapboxClient;
  }
}
