package com.bulkloads.web.routing.service;

import java.util.Optional;
import com.bulkloads.web.routing.domain.entity.Route;
import com.bulkloads.web.routing.domain.vo.Location;
import com.bulkloads.web.routing.mapper.RouteMapper;
import com.bulkloads.web.routing.provider.RoutingProviderClient;
import com.bulkloads.web.routing.provider.RoutingProviderFactory;
import com.bulkloads.web.routing.repository.RouteRepository;
import com.bulkloads.web.routing.service.dto.RouteDto;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RequiredArgsConstructor
public class RouteService {

  private final RoutingProviderFactory routingProviderFactory;
  private final RouteRepository routeRepository;
  private final RouteMapper routeMapper;

  public Optional<RouteDto> findRoute(final Location origin, final Location destination) {
    return findRouteFromDb(origin, destination)
        .or(() -> findRouteFromRemoteApi(origin, destination)
            .map(this::save));
  }

  public Optional<RouteDto> findRouteFromRemoteApi(final Location origin, final Location destination) {
    final RoutingProviderClient client = routingProviderFactory.getClient();
    return client.getRoute(origin, destination);
  }

  public Optional<RouteDto> findRouteFromDb(final Location origin, final Location destination) {
    return routeRepository.findCached(origin, destination)
        .map(routeMapper::entityToRouteDto);
  }

  private RouteDto save(final RouteDto dto) {
    Route route = routeMapper.routeDtoToEntity(dto);
    route = routeRepository.save(route);
    return routeMapper.entityToRouteDto(route);
  }
}
