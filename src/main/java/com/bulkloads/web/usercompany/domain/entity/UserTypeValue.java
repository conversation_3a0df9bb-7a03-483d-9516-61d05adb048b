package com.bulkloads.web.usercompany.domain.entity;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum UserTypeValue {

  CARRIER(20, "Carrier"),
  <PERSON><PERSON><PERSON>(30, "Broker"),
  <PERSON><PERSON><PERSON><PERSON>(40, "Shipper"),
  <PERSON><PERSON><PERSON>(50, "Other"),
  PICKUP_DROP_FACILITY(60, "Pickup / Drop Facility"),
  ADMIN(100, "Admin");

  private final int userTypeId;

  private final String userType;

  public static UserTypeValue of(int userTypeId) {
    for (UserTypeValue value : UserTypeValue.values()) {
      if (value.getUserTypeId() == userTypeId) {
        return value;
      }
    }
    throw new IllegalArgumentException();
  }

}
