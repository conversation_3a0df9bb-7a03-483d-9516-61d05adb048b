package com.bulkloads.web.usercompany.service;

import com.bulkloads.exception.ValidationException;
import com.bulkloads.web.usercompany.domain.entity.UserCompany;
import com.bulkloads.web.usercompany.repository.UserCompanyRepository;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RequiredArgsConstructor
public class UserCompanyService {

  private final UserCompanyRepository userCompanyRepository;

  public UserCompany getUserCompany(final int userCompanyId) {
    return userCompanyRepository
        .findById(userCompanyId)
        .orElseThrow(() -> new ValidationException("user_company_id", "Could not find company"));
  }

}
