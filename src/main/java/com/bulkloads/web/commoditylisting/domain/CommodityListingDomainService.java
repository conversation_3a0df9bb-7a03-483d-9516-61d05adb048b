package com.bulkloads.web.commoditylisting.domain;

import static com.bulkloads.common.validation.ValidationMethod.CREATE;
import static com.bulkloads.common.validation.ValidationMethod.UPDATE;
import static com.bulkloads.common.validation.ValidationUtils.isEmpty;
import static com.bulkloads.web.commoditylisting.service.CommodityListingService.COMMODITY_LISTING_ID;
import java.time.Instant;
import java.time.LocalDate;
import com.bulkloads.common.BaseDomainService;
import com.bulkloads.common.UserUtil;
import com.bulkloads.common.validation.Result;
import com.bulkloads.common.validation.ValidationMethod;
import com.bulkloads.exception.ValidationException;
import com.bulkloads.web.city.domain.entity.City;
import com.bulkloads.web.city.repository.CityRepository;
import com.bulkloads.web.commoditylisting.domain.data.CommodityListingData;
import com.bulkloads.web.commoditylisting.domain.entity.CommodityListing;
import com.bulkloads.web.commoditylisting.mapper.CommodityListingMapper;
import com.bulkloads.web.commoditylisting.repository.CommodityListingRepository;
import com.bulkloads.web.user.domain.entity.User;
import com.bulkloads.web.user.service.UserService;
import com.bulkloads.web.usercompany.domain.entity.UserCompany;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class CommodityListingDomainService extends BaseDomainService<CommodityListing> {

  private final CommodityListingRepository commodityListingRepository;
  private final CityRepository cityRepository;
  private final UserService userService;
  private final CommodityListingMapper commodityListingMapper;

  public Result<CommodityListing> create(final CommodityListingData data) {
    final CommodityListing commodityListing = new CommodityListing();

    return super.validate(commodityListing, null, data, CREATE);
  }

  public Result<CommodityListing> update(final CommodityListing commodityListing, final CommodityListingData data) {
    final Integer commodityListingId = commodityListing.getCommodityListingId();
    final CommodityListing existing = commodityListingRepository.findById(commodityListingId)
        .orElseThrow(() -> new ValidationException(COMMODITY_LISTING_ID, "Commodity Listing not found id %s".formatted(commodityListingId)));
    return super.validate(commodityListing, existing, data, UPDATE);
  }

  @Override
  public void validateDataAndMapToEntity(final Result<CommodityListing> result,
                                         final CommodityListing entity,
                                         final CommodityListing existing, Object data,
                                         final ValidationMethod method) {
    final CommodityListingData commodityListingData = (CommodityListingData) data;

    final int userId = UserUtil.getUserIdOrThrow();
    final User user = userService.findById(userId);
    final UserCompany userCompany = user.getUserCompany();

    if (method == CREATE) {
      entity.setUser(user);
      entity.setUserCompany(userCompany);
      final Instant now = Instant.now();
      entity.setDatePosted(LocalDate.now());
      entity.setDateAdded(now);
    }
    entity.setContactCompany(userCompany.getCompanyName());

    if (isEmpty(commodityListingData.getLocation())) {
      result.addError("location", "Location is required");
    } else {
      final City city = cityRepository.getCityZip(commodityListingData.getLocation().get());

      if (city == null) {
        result.addError("location", "The location entered cannot be found in our City database. Please retype the city/state to continue.");
      } else {
        entity.setCurrentCity(city.getCity());
        entity.setCurrentState(city.getState());
        entity.setCurrentZip(city.getZip());
        entity.setCurrentCountry(city.getCountry());
        entity.setCurrentLatitude(city.getLatitude());
        entity.setCurrentLongitude(city.getLongitude());
      }
    }
  }

  @Override
  public void mapToEntityAuto(final Object data,
                              final CommodityListing entity) {
    commodityListingMapper.dataToEntity((CommodityListingData) data, entity);
  }

  @Override
  public void validateEntity(final Result<CommodityListing> result,
                             final CommodityListing entity) {

  }
}
