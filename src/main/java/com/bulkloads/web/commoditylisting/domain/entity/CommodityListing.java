package com.bulkloads.web.commoditylisting.domain.entity;

import java.time.Instant;
import java.time.LocalDate;
import com.bulkloads.web.user.domain.entity.User;
import com.bulkloads.web.usercompany.domain.entity.UserCompany;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(name = "commodity_listing")
public class CommodityListing {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "commodity_listing_id")
  private Integer commodityListingId;

  @NotNull
  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "user_id")
  private User user;

  @NotNull
  @ManyToOne(fetch = FetchType.LAZY, optional = false)
  @JoinColumn(name = "user_company_id")
  private UserCompany userCompany;

  @NotEmpty(message = "You must select the Commodity Type")
  @Size(max = 200, message = "Commodity type should be less than 200 characters")
  @Column(name = "commodity_type")
  private String commodityType;

  @Size(max = 3, message = "Price type should be less than 3 characters")
  @Column(name = "price_type")
  private String priceType;

  @Size(max = 25, message = "Price should be less than 25 characters")
  @Column(name = "price")
  private String price;

  @Size(max = 255, message = "Origin should be less than 255 characters")
  @Column(name = "origin")
  private String origin;

  @Size(max = 50, message = "Contact company should be less than 50 characters")
  @Column(name = "contact_company")
  private String contactCompany;

  @NotEmpty(message = "You must enter your Name")
  @Size(max = 50, message = "Contact name should be less than 50 characters")
  @Column(name = "contact_name")
  private String contactName;

  @NotEmpty(message = "You must enter your Phone Number")
  @Size(max = 25, message = "Contact phone should be less than 25 characters")
  @Column(name = "contact_phone")
  private String contactPhone;

  @Size(max = 50, message = "Contact email should be less than 50 characters")
  @Column(name = "contact_email")
  private String contactEmail;

  @Size(max = 1000, message = "Notes should be less than 1000 characters")
  @Column(name = "notes")
  private String notes;

  @Column (name = "date_posted")
  private LocalDate datePosted;

  @Column (name = "date_added")
  private Instant dateAdded;

  @NotEmpty(message = "The city is required")
  @Size(max = 60, message = "Current city should be less than 60 characters")
  @Column(name = "current_city")
  private String currentCity;

  @NotEmpty(message = "The state is required")
  @Size(max = 2, message = "Current state should be less than 2 characters")
  @Column(name = "current_state")
  private String currentState;

  @Size(max = 10, message = "Current zip should be less than 10 characters")
  @Column(name = "current_zip")
  private String currentZip;

  @Size(max = 5, message = "Current country should be less than 5 characters")
  @Column(name = "current_country")
  private String currentCountry;

  @Column(name = "current_latitude")
  private double currentLatitude;

  @Column(name = "current_longitude")
  private double currentLongitude;

}
