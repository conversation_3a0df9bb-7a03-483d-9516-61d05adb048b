package com.bulkloads.web.commoditylisting.service.dto.transformer;

import com.bulkloads.common.jpa.nativejpa.QueryParts;
import com.bulkloads.web.commoditylisting.service.dto.CommodityListingListResponse;
import org.hibernate.query.TupleTransformer;
import org.springframework.stereotype.Component;
import lombok.RequiredArgsConstructor;

@Component
@RequiredArgsConstructor
public class CommodityListingListResponseTransformer implements TupleTransformer<CommodityListingListResponse> {

  @Override
  public CommodityListingListResponse transformTuple(Object[] columns, String[] aliases) {
    QueryParts parts = new QueryParts(columns, aliases);

    CommodityListingListResponse response = new CommodityListingListResponse();
    response.setCommodityListingId(parts.asInteger("commodity_listing_id"));
    response.setCommodityType(parts.asString("commodity_type"));
    response.setPrice(parts.asString("price"));
    response.setPriceType(parts.asString("price_type"));
    response.setOrigin(parts.asString("origin"));
    response.setContactCompany(parts.asString("contact_company"));
    response.setContactName(parts.asString("contact_name"));
    response.setContactPhone(parts.asString("contact_phone"));
    response.setContactEmail(parts.asString("contact_email"));
    response.setNotes(parts.asString("notes"));
    response.setDatePosted(parts.asLocalDate("date_posted"));
    return response;
  }
}
