package com.bulkloads.web.commoditylisting.service.dto.transformer;

import com.bulkloads.common.jpa.nativejpa.QueryParts;
import com.bulkloads.web.commoditylisting.service.dto.CommodityListingResponse;
import org.hibernate.query.TupleTransformer;
import org.springframework.stereotype.Component;
import lombok.RequiredArgsConstructor;

@Component
@RequiredArgsConstructor
public class CommodityListingResponseTransformer implements TupleTransformer<CommodityListingResponse> {

  @Override
  public CommodityListingResponse transformTuple(Object[] columns, String[] aliases) {
    QueryParts parts = new QueryParts(columns, aliases);
    return toResponse(parts);
  }

  static CommodityListingResponse toResponse(QueryParts parts) {
    return CommodityListingResponse.builder()
        .commodityListingId(parts.asInteger("commodity_listing_id"))
        .commodityType(parts.asString("commodity_type"))
        .price(parts.asString("price"))
        .priceType(parts.asString("price_type"))
        .origin(parts.asString("origin"))
        .contactCompany(parts.asString("contact_company"))
        .contactName(parts.asString("contact_name"))
        .contactPhone(parts.asString("contact_phone"))
        .contactEmail(parts.asString("contact_email"))
        .notes(parts.asString("notes"))
        .datePosted(parts.asLocalDate("date_posted"))
        .currentCity(parts.asString("current_city"))
        .currentState(parts.asString("current_state"))
        .currentZip(parts.asString("current_zip"))
        .currentCountry(parts.asString("current_country"))
        .currentLatitude(parts.asDouble("current_latitude"))
        .currentLongitude(parts.asDouble("current_longitude"))
        .location(parts.asLocation("current_city", "current_state", "current_zip", "current_country"))
        .build();

  }
}

