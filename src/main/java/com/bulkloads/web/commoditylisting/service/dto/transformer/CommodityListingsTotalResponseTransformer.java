package com.bulkloads.web.commoditylisting.service.dto.transformer;

import com.bulkloads.common.jpa.nativejpa.QueryParts;
import com.bulkloads.web.commoditylisting.service.dto.CommodityListingsTotalResponse;
import org.hibernate.query.TupleTransformer;
import org.springframework.stereotype.Component;
import lombok.RequiredArgsConstructor;

@Component
@RequiredArgsConstructor
public class CommodityListingsTotalResponseTransformer implements TupleTransformer<CommodityListingsTotalResponse> {

  @Override
  public CommodityListingsTotalResponse transformTuple(Object[] columns, String[] aliases) {
    CommodityListingsTotalResponse response = new CommodityListingsTotalResponse();
    QueryParts parts = new QueryParts(columns, aliases);
    response.setTotal(parts.asInteger("total"));
    return response;
  }

}
