package com.bulkloads.web.activity.domain.entity;

import java.time.Instant;
import com.bulkloads.web.user.domain.entity.User;
import com.bulkloads.web.usercompany.domain.entity.UserCompany;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "activities")
@Getter
@Setter
public class Activity {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "activity_id")
  private Integer activityId;

  @Column(name = "added_date")
  private Instant addedDate = Instant.now();

  @Column(name = "activity")
  private String activity = "";

  @Column(name = "action")
  private String action = "";

  @Column(name = "data")
  private String data = "";

  @Column(name = "load_id")
  private Integer loadId;

  @ManyToOne(cascade = {CascadeType.PERSIST, CascadeType.MERGE, CascadeType.DETACH,
      CascadeType.REFRESH}, fetch = FetchType.LAZY)
  @JoinColumn(name = "user_id")
  private User user;

  @ManyToOne(cascade = {CascadeType.PERSIST, CascadeType.MERGE, CascadeType.DETACH,
      CascadeType.REFRESH}, fetch = FetchType.LAZY)
  @JoinColumn(name = "user_company_id")
  private UserCompany userCompany;

  @ManyToOne(cascade = {CascadeType.PERSIST, CascadeType.MERGE, CascadeType.DETACH,
      CascadeType.REFRESH}, fetch = FetchType.LAZY)
  @JoinColumn(name = "activity_type_id")
  private ActivityType activityType;

}
