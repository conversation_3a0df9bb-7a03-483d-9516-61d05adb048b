package com.bulkloads.web.activity.domain.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "activity_types")
@Getter
@Setter
public class ActivityType {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "activity_type_id")
  private Integer activityTypeId;

  @Column(name = "activity_type")
  private String activityType = "";

  @Column(name = "activity_metadata")
  private String activityMetadata = "";

  @RequiredArgsConstructor
  public enum ActivityTypes {
    LOADS_ACTIVITY_ID(1),
    LOAD_ASSIGNMENTS_ACTIVITY_ID(2),
    LOAD_BOOKINGS_ACTIVITY_ID(3),
    OFFERS_SENT_ACTIVITY_ID(4),
    OFFERS_RECEIVED_ACTIVITY_ID(5),
    LOAD_INVOICES_SENT_ACTIVITY_ID(6),
    LOAD_INVOICES_RECEIVED_ACTIVITY_ID(7),
    CONTRACTS_ACTIVITY_ID(8);

    final int activityTypeId;

    public int get() {
      return activityTypeId;
    }
  }

}