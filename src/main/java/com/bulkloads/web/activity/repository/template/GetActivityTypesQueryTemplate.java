package com.bulkloads.web.activity.repository.template;

import org.intellij.lang.annotations.Language;

public class GetActivityTypesQueryTemplate {

  @Language("SQL")
  public static final String GET_ACTIVITY_TYPES_QUERY_TEMPLATE = """
          SELECT
              activity_type_id,
              activity_type,
              activity_metadata
          FROM activity_types
           <% if (paramExistsAdd("activityTypeId")) { %>
              WHERE activity_type_id = :activityTypeId
          <% } %>
          ORDER BY activity_type_id
      """;

}
