package com.bulkloads.web.activity.repository.template;

import org.intellij.lang.annotations.Language;

public class GetActivitiesQueryTemplate {

  @Language("SQL")
  public static final String GET_ACTIVITIES_QUERY_TEMPLATE = """
          select
              a.activity_id,
              a.user_id,
              u.first_name,
              u.last_name,
              a.added_date,
              a.activity,
              a.action,
              a.activity_type_id,
              atypes.activity_type,
              `data`
          from activities a
              inner join activity_types as atypes using(activity_type_id)
              inner join user_info u using(user_id)
          where
               <% params.put("userCompanyId", userCompanyId) %>
               a.user_company_id = :userCompanyId

               <% if (paramExistsAdd("userIds")) { %>
                   and a.user_id in (:userIds)
               <% } %>

               <% if (paramExistsAdd("activityTypeId")) { %>
                   and a.activity_type_id = :activityTypeId
               <% } %>

               <% if (paramExistsAdd("pastDays")) { %>
                   and a.added_date >= now() - interval :pastDays day
               <% } %>

          order by a.activity_id desc

          <% if (paramExistsAdd("limit")) { %>
              LIMIT
              <% if (paramExistsAdd("skip")) { %>
               :skip,
              <% } %>
              :limit
          <% } %>
      """;

}
