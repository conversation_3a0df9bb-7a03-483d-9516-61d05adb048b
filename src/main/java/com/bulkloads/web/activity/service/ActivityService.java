package com.bulkloads.web.activity.service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.bulkloads.common.validation.Result;
import com.bulkloads.exception.ValidationException;
import com.bulkloads.web.activity.domain.ActivityDomainService;
import com.bulkloads.web.activity.domain.data.ActivityCreateData;
import com.bulkloads.web.activity.domain.entity.Activity;
import com.bulkloads.web.activity.mapper.ActivityMapper;
import com.bulkloads.web.activity.repository.ActivityRepository;
import com.bulkloads.web.activity.service.dto.ActivityRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RequiredArgsConstructor
public class ActivityService {

  private final ActivityDomainService activityDomainService;
  private final ActivityRepository activityRepository;
  private final ActivityMapper activityMapper;

  @Transactional
  public List<Activity> createActivities(List<ActivityRequest> activityRequests) {
    final Map<String, String> errorMap = new HashMap<>();
    final List<Activity> activities = new ArrayList<>();
    for (ActivityRequest activityRequest : activityRequests) {
      final ActivityCreateData activityCreateData = activityMapper.requestToData(activityRequest, errorMap);
      if (!errorMap.isEmpty()) {
        throw new ValidationException(errorMap);
      }
      final Result<Activity> result = activityDomainService.create(activityCreateData);
      final Activity activity = result.orElseThrow();
      activities.add(activity);
    }

    return activityRepository.saveAll(activities);
  }

}
