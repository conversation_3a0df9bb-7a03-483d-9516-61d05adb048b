package com.bulkloads.web.activity.service;

import java.util.List;
import com.bulkloads.common.UserUtil;
import com.bulkloads.web.activity.repository.ActivityQueryRepository;
import com.bulkloads.web.activity.service.dto.ActivityListResponse;
import com.bulkloads.web.activity.service.dto.ActivityTypeListResponse;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RequiredArgsConstructor
public class ActivityQueryService {

  private final ActivityQueryRepository activityQueryRepository;

  public List<ActivityListResponse> getActivities(
      final String userIds,
      final Integer activityTypeId,
      final Integer pastDays,
      final Integer skip,
      final Integer limit) {
    final int userCompanyId = UserUtil.getUserCompanyIdOrThrow();
    return activityQueryRepository.getActivities(userCompanyId, userIds, activityTypeId, pastDays, skip, limit);
  }

  public List<ActivityTypeListResponse> getActivityTypes() {
    return activityQueryRepository.getActivityTypes(null);
  }
}
