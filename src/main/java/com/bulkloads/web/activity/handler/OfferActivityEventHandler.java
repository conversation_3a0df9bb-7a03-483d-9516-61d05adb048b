
package com.bulkloads.web.activity.handler;

import static com.bulkloads.config.AppConstants.ActivityType.CREATE_LOAD_OFFER;
import static com.bulkloads.web.activity.domain.entity.ActivityType.ActivityTypes.OFFERS_RECEIVED_ACTIVITY_ID;
import static com.bulkloads.web.activity.domain.entity.ActivityType.ActivityTypes.OFFERS_SENT_ACTIVITY_ID;
import static java.util.Objects.nonNull;
import java.util.List;
import java.util.Optional;
import com.bulkloads.web.activity.service.ActivityService;
import com.bulkloads.web.activity.service.dto.ActivityRequest;
import com.bulkloads.web.offer.domain.entity.Offer;
import com.bulkloads.web.offer.event.OfferCreatedEvent;
import com.bulkloads.web.offer.repository.OfferRepository;
import com.bulkloads.web.user.service.UserService;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
@RequiredArgsConstructor
public class OfferActivityEventHandler {

  private final ActivityService activityService;
  private final OfferRepository offerRepository;
  private final UserService userService;

  @TransactionalEventListener(classes = OfferCreatedEvent.class,
      phase = TransactionPhase.BEFORE_COMMIT)
  public void handleOfferCreatedActivity(final OfferCreatedEvent event) {
    final Integer offerId = event.getOfferId();
    final Offer offer = offerRepository.getReferenceById(offerId);
    final int loadId = offer.getLoad().getLoadId();

    // activity to sender
    final String activitySentContent = "Offer Sent: " + getOfferActivityLocation(offer);
    final ActivityRequest activityRequest =
        buildActivityRequest(userService.getLoggedInUser().getUserId(), offerId, loadId, activitySentContent, OFFERS_SENT_ACTIVITY_ID.get(), CREATE_LOAD_OFFER);
    activityService.createActivities(List.of(activityRequest));

    // activity to recipients
    final String activityReceivedContent = "Offer Received: " + getOfferActivityLocation(offer);
    offer.getRecipients().forEach(recipient -> {
      if (nonNull(recipient.getBlUser())) {
        final ActivityRequest recipientActivityRequest =
            buildActivityRequest(recipient.getBlUser().getUserId(), offerId, loadId, activityReceivedContent, OFFERS_RECEIVED_ACTIVITY_ID.get(),
                CREATE_LOAD_OFFER);
        activityService.createActivities(List.of(recipientActivityRequest));
      }
    });
  }

  private String getOfferActivityLocation(final Offer offer) {
    final String pickupCity = offer.getLoad().getPickupAbCompany().getCity();
    final String pickupState = offer.getLoad().getPickupAbCompany().getState();
    final String dropCity = offer.getLoad().getDropAbCompany().getCity();
    final String dropState = offer.getLoad().getDropAbCompany().getState();
    return "%s, %s to %s, %s".formatted(pickupCity, pickupState, dropCity, dropState);
  }

  private ActivityRequest buildActivityRequest(final int userId, final int offerId, final int loadId,
                                               final String activityContent, final int activityTypeId, final String activityType) {
    final ActivityRequest activityRequest = new ActivityRequest();
    activityRequest.setUserId(userId);
    activityRequest.setActivityTypeId(activityTypeId); //LOAD_OFFERS_ACTIVITY_ID.get());
    activityRequest.setAction(activityType);
    activityRequest.setActivity(activityContent);
    activityRequest.setLoadId(Optional.of(loadId));
    activityRequest.setData(
        """
            {"load_ids":"%s","offer_ids":"%s"}""".formatted(loadId, offerId));
    return activityRequest;
  }

}