package com.bulkloads.web.activity.handler;

import static com.bulkloads.config.AppConstants.ActivityType.ACTIVATE_LOAD;
import static com.bulkloads.config.AppConstants.ActivityType.CREATE_LOAD;
import static com.bulkloads.config.AppConstants.ActivityType.DEACTIVATE_LOAD;
import static com.bulkloads.config.AppConstants.ActivityType.UPDATE_LOAD;
import static com.bulkloads.config.AppConstants.LoadAction.LOAD_ACTIVATE;
import static com.bulkloads.config.AppConstants.LoadAction.LOAD_CREATE;
import static com.bulkloads.config.AppConstants.LoadAction.LOAD_DEACTIVATE;
import static com.bulkloads.config.AppConstants.LoadAction.LOAD_UPDATE;
import java.util.List;
import java.util.Optional;
import com.bulkloads.exception.BulkloadsException;
import com.bulkloads.web.activity.domain.entity.ActivityType;
import com.bulkloads.web.activity.service.ActivityService;
import com.bulkloads.web.activity.service.dto.ActivityRequest;
import com.bulkloads.web.load.domain.entity.Load;
import com.bulkloads.web.load.event.LoadActivatedEvent;
import com.bulkloads.web.load.event.LoadCreatedEvent;
import com.bulkloads.web.load.event.LoadDeactivatedEvent;
import com.bulkloads.web.load.event.LoadEvent;
import com.bulkloads.web.load.event.LoadUpdatedEvent;
import com.bulkloads.web.load.repository.LoadRepository;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
@RequiredArgsConstructor
public class LoadActivityEventHandler {

  private final ActivityService activityService;
  private final LoadRepository loadRepository;

  @TransactionalEventListener(classes = {
      LoadCreatedEvent.class,
      LoadActivatedEvent.class,
      LoadDeactivatedEvent.class,
      LoadUpdatedEvent.class
  }, phase = TransactionPhase.BEFORE_COMMIT)
  public void handleActivity(final LoadEvent event) {
    final ActivityRequest activityRequest = buildActivityRequest(event);
    activityService.createActivities(List.of(activityRequest));
  }

  private ActivityRequest buildActivityRequest(final LoadEvent event) {
    final String action = event.getAction();
    final int loadId = event.getLoadId();
    final Load load = loadRepository.getReferenceById(loadId);

    final String activity = buildActivityMsg(load, action);

    final ActivityRequest activityRequest = new ActivityRequest();
    activityRequest.setUserId(load.getUser().getUserId());
    activityRequest.setLoadId(Optional.of(loadId));
    activityRequest.setActivityTypeId(ActivityType.ActivityTypes.LOADS_ACTIVITY_ID.get());
    activityRequest.setActivity(activity);
    activityRequest.setAction(mapToActivityAction(action));
    activityRequest.setLoadId(Optional.of(loadId));
    activityRequest.setData(
        """
            {
              "load_ids": "%s"
            }""".formatted((loadId)));

    return activityRequest;
  }

  private String buildActivityMsg(final Load load, final String action) {
    final String originCity = load.getOriginCity();
    final String originState = load.getOriginState();
    final String destinationCity = load.getDestinationCity();
    final String destinationState = load.getDestinationState();

    String msg = "Load was activated: %s, %s to %s, %s";

    if (action.equals(LOAD_DEACTIVATE)) {
      msg = "Load was deactivated: %s, %s to %s, %s";
    }

    return msg.formatted(originCity, originState, destinationCity, destinationState);
  }

  private String mapToActivityAction(final String action) {
    return switch (action) {
      case LOAD_DEACTIVATE -> DEACTIVATE_LOAD;
      case LOAD_ACTIVATE -> ACTIVATE_LOAD;
      case LOAD_CREATE -> CREATE_LOAD;
      case LOAD_UPDATE -> UPDATE_LOAD;
      default -> throw new BulkloadsException("Action %s not supported".formatted(action));
    };
  }
}
