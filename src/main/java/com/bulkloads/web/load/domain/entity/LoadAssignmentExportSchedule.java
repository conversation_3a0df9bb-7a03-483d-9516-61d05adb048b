package com.bulkloads.web.load.domain.entity;

import java.time.LocalDate;
import java.time.LocalTime;
import com.bulkloads.web.user.domain.entity.BlUserSettings;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@Entity
@Getter
@Setter
@Table(name = "load_assignment_export_schedules")
public class LoadAssignmentExportSchedule {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "user_company_id")
  private Integer userCompanyId;

  @Column(name = "export_active")
  private Integer exportActive;

  @Column(name = "export_master")
  private Integer exportMaster;

  @Column(name = "export_receivables")
  private Integer exportReceivables;

  @Column(name = "export_payables")
  private Integer exportPayables;

  @Column(name = "first_run")
  private Integer firstRun;

  @Column(name = "date_only")
  private Integer dateOnly;

  @Column(name = "start_date")
  private LocalDate startDate;

  @Column(name = "start_time")
  private LocalTime startTime;

  @Column(name = "last_run_date")
  private LocalDate lastRunDate;

  @Column(name = "next_run_date")
  private LocalDate nextRunDate;

  @Column(name = "next_run_date_client_tz")
  private LocalDate nextRunDateClientTz;

  @Column(name = "frequency")
  private String frequency;


  @Column(name = "export_emails")
  private String exportEmails;

  @ManyToOne(cascade = {CascadeType.PERSIST, CascadeType.MERGE, CascadeType.DETACH, CascadeType.REFRESH}, fetch = FetchType.LAZY)
  @JoinColumn(name = "user_company_id", insertable = false, updatable = false)
  private BlUserSettings blUserSettings;
}
