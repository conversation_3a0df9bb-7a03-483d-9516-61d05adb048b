package com.bulkloads.web.load.domain.entity;

import java.time.Instant;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(name = "load_flags")
public class LoadFlag {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "flag_id")
  private Integer flagId;

  @NotNull
  @Column(name = "user_id")
  private Integer userId;

  @NotNull
  @Column(name = "site_id")
  private Integer siteId = 1;

  @NotNull
  @Column(name = "flagged_load_id")
  private Integer flaggedLoadId;

  @NotEmpty(message = "You must enter a reason for flagging this load")
  @Column(name = "reason")
  private String reason;

  @NotNull
  @Column(name = "date_added")
  private Instant dateAdded = Instant.now();

  @Column(name = "processed_by_user_id")
  private Integer processedByUserId;

  @Size(max = 45, message = "Up to 45 chars")
  @Column(name = "processed_action")
  private String processedAction = "";

  @Column(name = "processed_date")
  private Instant processedDate;

  @NotNull
  @Column(name = "processed")
  private Boolean processed = false;
}
