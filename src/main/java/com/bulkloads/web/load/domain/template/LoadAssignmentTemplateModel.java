package com.bulkloads.web.load.domain.template;

import static com.bulkloads.common.validation.ValidationUtils.isEmpty;
import static com.bulkloads.config.AppConstants.ReroutePickupDrop.DROP;
import static com.bulkloads.config.AppConstants.ReroutePickupDrop.PICKUP;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.LocalDate;
import java.util.List;
import lombok.Builder;
import lombok.Value;

@Value
@Builder(toBuilder = true)
public class LoadAssignmentTemplateModel {

  List<DynamicLink> links;
  String domainUrl;

  String accountingEmail;
  Instant assignedDate;
  String cellPhone;
  String companyLogoUrl;
  String companyName;
  Boolean isDriver;
  String dropAddress;
  Boolean dropApptRequired;
  String dropCompanyName;
  String dropCompanyNotes;
  String dropCompanyPhone;
  String dropDirections;
  String dropLocation;
  String dropNotes;
  String dropReceivingHours;
  String dropCity;
  String dropState;
  Instant editDate;
  String email;
  String equipmentNames;
  String firstName;
  Boolean isIntraCompany;
  String lastName;
  String loadAssignmentNumber;
  String loadConfirmationFooter;
  String personalMessage;
  String pickupAddress;
  String phone1;
  Boolean pickupApptRequired;
  String pickupCompanyName;
  String pickupCompanyNotes;
  String pickupCompanyPhone;
  String pickupDirections;
  String pickupLocation;
  String pickupNotes;
  String pickupReceivingHours;
  String pickupCity;
  String pickupState;
  Boolean rerouteApptRequired;
  String rerouteCompanyNotes;
  String rerouteCompanyName;
  String rerouteCompanyPhone;
  String rerouteAddress;
  Instant rerouteDate;
  String rerouteDirections;
  String rerouteLocation;
  String reroutePickupDrop;
  String rerouteReason;
  String rerouteReceivingHours;
  String rerouteCity;
  String rerouteState;
  Boolean isRerouted;
  LocalDate shipFrom;
  LocalDate shipTo;
  String toCompanyName;
  String toEmail;
  String toFirstName;
  String toLastName;
  String toPhone1;
  String rateTypeTextMedium;
  BigDecimal rate;
  Integer childLoadAssignmentId;
  Boolean toDeleted;
  String deletedMessage;
  String sharedWithHiredCompanyResponse;
  Integer loadId;
  Integer toLoadId;
  String truckUserCompanyEquipment;
  String trailerUserCompanyEquipment;

  List<AssignmentTemplateModel> assignments;

  public String getFullName() {
    return getFirstName() + " " + getLastName();
  }

  public String getRateMediumFormat() {
    if (rate == null) {
      return "";
    }
    BigDecimal rateRoundedThreeDecimals = getRate().setScale(3, RoundingMode.HALF_UP);
    int thirdDecimalDigit = rateRoundedThreeDecimals.remainder(BigDecimal.ONE).movePointRight(3).abs().intValue();
    if (thirdDecimalDigit != 0) {
      return "$" + rateRoundedThreeDecimals + getRateTypeTextMedium();
    } else {
      BigDecimal rateRoundedTwoDecimals = getRate().setScale(0, RoundingMode.HALF_UP);
      return "$" + rateRoundedTwoDecimals + getRateTypeTextMedium();
    }
  }

  public String getPickupDrop() {

    String pickup = "";
    if (getIsRerouted() && PICKUP.equals(getReroutePickupDrop())) {
      if (isEmpty(getRerouteCity())) {
        pickup = getRerouteCompanyName();
      } else {
        pickup = getRerouteCity() + ", " + getRerouteState();
      }
    } else {
      if (isEmpty(getPickupCity())) {
        pickup = getPickupCompanyName();
      } else {
        pickup = getPickupCity() + ", " + getPickupState();
      }
    }

    String drop = "";
    if (getIsRerouted() && DROP.equals(getReroutePickupDrop())) {
      if (isEmpty(getRerouteCity())) {
        drop = getRerouteCompanyName();
      } else {
        drop = getRerouteCity() + ", " + getRerouteState();
      }
    } else {
      if (isEmpty(getDropCity())) {
        drop = getDropCompanyName();
      } else {
        drop = getDropCity() + ", " + getDropState();
      }
    }

    return pickup + " to " + drop;
  }

}
