package com.bulkloads.web.load.domain;

import static com.bulkloads.common.validation.ValidationMethod.CREATE;
import static com.bulkloads.common.validation.ValidationMethod.UPDATE;
import static com.bulkloads.common.validation.ValidationUtils.exists;
import static com.bulkloads.common.validation.ValidationUtils.existsAndIsEmpty;
import static com.bulkloads.common.validation.ValidationUtils.existsAndIsFalse;
import static com.bulkloads.common.validation.ValidationUtils.existsAndIsNotEmpty;
import static com.bulkloads.common.validation.ValidationUtils.existsAndIsTrue;
import static com.bulkloads.common.validation.ValidationUtils.hasChange;
import static com.bulkloads.common.validation.ValidationUtils.isEmpty;
import static com.bulkloads.common.validation.ValidationUtils.isMissingOrIsEmpty;
import static com.bulkloads.config.AppConstants.DELIVERED_COMPLETED;
import static com.bulkloads.config.AppConstants.LoadAccess.CARRIER;
import static com.bulkloads.config.AppConstants.LoadAccess.PRIVATE;
import static com.bulkloads.config.AppConstants.LoadAccess.PUBLIC;
import static com.bulkloads.config.AppConstants.RateType.FLAT;
import static com.bulkloads.config.AppConstants.RateType.GALLON;
import static com.bulkloads.config.AppConstants.RateType.HOUR;
import static com.bulkloads.config.AppConstants.RateType.MILE;
import static com.bulkloads.config.AppConstants.RateType.TWO_K;
import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Stream;
import com.bulkloads.common.BaseDomainService;
import com.bulkloads.common.UserUtil;
import com.bulkloads.common.validation.Result;
import com.bulkloads.common.validation.ValidationMethod;
import com.bulkloads.web.addressbook.abcompany.domain.AbCompanyDomainService;
import com.bulkloads.web.addressbook.abcompany.domain.data.AbCompanyData;
import com.bulkloads.web.addressbook.abcompany.domain.entity.AbCompany;
import com.bulkloads.web.addressbook.abuser.domain.entity.AbUser;
import com.bulkloads.web.city.domain.entity.City;
import com.bulkloads.web.city.repository.CityRepository;
import com.bulkloads.web.equipment.domain.entity.Equipment;
import com.bulkloads.web.equipment.repository.EquipmentRepository;
import com.bulkloads.web.file.domain.entity.FileType;
import com.bulkloads.web.file.repository.FileTypeRepository;
import com.bulkloads.web.load.domain.data.LoadData;
import com.bulkloads.web.load.domain.entity.Load;
import com.bulkloads.web.load.mapper.LoadMapper;
import com.bulkloads.web.phonenumber.service.PhoneNumberDetails;
import com.bulkloads.web.phonenumber.service.PhoneNumberService;
import com.bulkloads.web.phonenumber.service.exception.InvalidNumberException;
import com.bulkloads.web.rate.domain.entity.RateSummary;
import com.bulkloads.web.rate.domain.entity.RateType;
import com.bulkloads.web.rate.repository.RateTypeRepository;
import com.bulkloads.web.rate.service.RateService;
import com.bulkloads.web.routing.domain.vo.Coordinates;
import com.bulkloads.web.routing.domain.vo.Location;
import com.bulkloads.web.routing.service.RouteService;
import com.bulkloads.web.routing.service.dto.RouteDto;
import com.bulkloads.web.user.domain.entity.User;
import com.bulkloads.web.user.service.UserService;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class LoadDomainService extends BaseDomainService<Load> {

  public static final String LOAD_ACCESS = "load_access";
  public static final String NUMBER_OF_LOADS = "number_of_loads";
  public static final String IS_MANAGED = "is_managed";
  public static final String PICKUP_AB_COMPANY_ID = "pickup_ab_company_id";
  public static final String DROP_AB_COMPANY_ID = "drop_ab_company_id";
  public static final String LO_RATE_TYPE = "lo_rate_type";
  public static final String LO_RATE = "lo_rate";
  public static final String LO_ESTIMATED_WEIGHT = "lo_estimated_weight";
  public static final String LO_ESTIMATED_VOLUME = "lo_estimated_volume";
  public static final String RATE_TYPE = "rate_type";
  public static final String USER_ID = "user_id";
  public static final String SHIP_FROM = "ship_from";
  public static final String UNLOAD_WEIGHT = "unload_weight";
  public static final String LOADED_WEIGHT = "loaded_weight";
  public static final String DEFAULT_BILL_WEIGHT_USE = "default_bill_weight_use";
  public static final String CERTIFIED_OWNER = "certified_owner";
  public static final String CONTRACT_ID = "contract_id";
  public static final String EQUIPMENT_IDS = "equipment_ids";
  public static final String HIRING_AB_COMPANY_ID = "hiring_ab_company_id";
  public static final String HIRING_AB_USER_ID = "hiring_ab_user_id";
  public static final String ORIGIN = "origin";
  public static final String DESTINATION = "destination";
  public static final String COMPANY_PHONE = "company_phone";
  public static final String CONTACT_NAME = "contact_name";
  public static final String CONTACT_NUMBER = "contact_number";
  public static final String SHIP_TO = "ship_to";
  public static final String IS_BROKER = "is_broker";

  private final FileTypeRepository fileTypeRepository;
  private final EquipmentRepository equipmentRepository;
  private final CityRepository cityRepository;
  private final UserService userService;
  private final PhoneNumberService phoneNumberService;
  private final RouteService routeService;
  private final RateService rateService;
  private final AbCompanyDomainService abCompanyDomainService;
  private final LoadMapper loadMapper;
  private final RateTypeRepository rateTypeRepository;

  public Result<Load> create(final LoadData data) {

    return super.validate(new Load(), null, data, CREATE);
  }

  public Result<Load> update(final Load entity, final LoadData data) {
    return super.validate(entity, null, data, UPDATE);
  }

  @Override
  public void validateDataAndMapToEntity(final Result<Load> result, final Load entity, final Load existing, final Object data, final ValidationMethod method) {

    final LoadData loadData = (LoadData) data;
    final int loggedInUserId = UserUtil.getUserIdOrThrow();
    final int loggedInUserCompanyId = UserUtil.getUserCompanyIdOrThrow();
    final User user = userService.findById(loggedInUserId);

    final Optional<String> appName = UserUtil.getAppName();

    if (CREATE == method) {
      if (existsAndIsFalse(loadData.getIsManaged()) && existsAndIsFalse(loadData.getIsBroker())
          && existsAndIsNotEmpty(loadData.getLoadAccess()) && Stream.of(CARRIER, PUBLIC).noneMatch(loadData.getLoadAccess().get()::equalsIgnoreCase)) {
        result.addError(LOAD_ACCESS, "Only Public and Carrier loads are allowed.");
      }

      if (existsAndIsTrue(loadData.getIsBroker())
          && existsAndIsNotEmpty(loadData.getAssignmentStatus()) && DELIVERED_COMPLETED.stream()
          .anyMatch(loadData.getAssignmentStatus().get()::equalsIgnoreCase)) {
        if (existsAndIsNotEmpty(loadData.getNumberOfLoads()) && loadData.getNumberOfLoads().get() != 1) {
          result.addError(NUMBER_OF_LOADS, "The number of loads must be 1 for completed loads");
        }

        if (existsAndIsTrue(loadData.getIsManaged())) {
          result.addError(IS_MANAGED, "When adding a 'completed' load, is_managed must be 0");
        }
      }

      entity.setUserCompany(user.getUserCompany());
      entity.setNumberOfAvailableLoads(loadData.getNumberOfLoads().get());
      entity.setNumberOfAssignedLoads(0);
      entity.setNumberOfAssignedDrivers(0);
      entity.setAddedByUserId(loggedInUserId);
      entity.setCreateDate(Instant.now());
    } else if (UPDATE == method) {

      //when is_managed changes from 0 to 1 and new blank assignments are created for the first time,
      // if any parent bookings have started being hauled (assignment_status > assigned), copy the info
      // from the parent record and assign the record internally to yourself. That is, a driver assignment w/
      // the to_user_id the same as the parent booking's to_user_id
      final boolean loadChangedToManaged = hasChange(entity.getIsManaged(), loadData.getIsManaged());

      if (existsAndIsFalse(loadData.getIsRootLoad()) && existsAndIsNotEmpty(appName)) {
        validateNonRootChanges(result, entity, loadData);
      }

      //don't allow is_managed to be changed from 1 to 0
      if (entity.getIsManaged() && existsAndIsFalse(loadData.getIsManaged())) {
        result.addError(IS_MANAGED, "A managed load cannot become non-managed");
      }

      entity.setEditDate(Instant.now());
      entity.setEditByUserId(loggedInUserId);
    }

    final LocalDate now = LocalDate.now();

    validateShipFrom(result, loadData, now);
    validateShipTo(result, loadData, now);
    setRequiredFileTypeIds(entity, loadData);
    validateNumberOfLoads(result, loadData);
    validateDefaultBillWeightUse(result, loadData);
    validateHiringAbCompany(result, entity, loadData, user);
    validatePickupAbCompany(result, entity, loadData, user, method);
    validateDropAbCompany(result, entity, loadData, user, method);
    validateCertifiedOwnerRequired(result, loadData);

    if (result.hasErrors()) {
      return;
    }

    validateOrigin(result, entity, loadData);
    validateDestination(result, entity, loadData);

    calculateCostPerMile(result, entity, loadData);

    validateEquipmentIds(result, entity, loadData);
    validateContactNumber(result, entity, loadData);
    validateContract(result, loadData);

    if (isMissingOrIsEmpty(loadData.getLoRateType())) {
      final Optional<RateType> rateTypeOpt = rateTypeRepository.findById(TWO_K);
      loadData.setLoRateType(rateTypeOpt);
    }

    //TODO
    if (existsAndIsTrue(loadData.getIsBroker())) {
      //validateLoadBooking
    }

    //flag to denote if info used on the assignment confirmations has changed, e.g. pickup/drop location
    if (UPDATE == method) {
      validatePickupAbCompanyChanged(result, entity, loadData);
      validateDropAbCompanyHasChange(result, entity, loadData);
    }

    validateUser(result, loadData, loggedInUserCompanyId, user);

    //update avg_rate_per_mile
    if (nonNull(entity.getDestLat()) && nonNull(entity.getDestLong())
        && nonNull(entity.getOriginLat()) && nonNull(entity.getOriginLong())
        && existsAndIsNotEmpty(loadData.getEquipmentIds())) {

      final Coordinates origin = Coordinates.builder().latitude(entity.getOriginLat()).longitude(entity.getOriginLong()).build();
      final Coordinates destination = Coordinates.builder().latitude(entity.getDestLat()).longitude(entity.getDestLong()).build();

      final RateSummary rateSummary = rateService.getLaneRate(origin, 200, destination, 200, entity.getEquipmentIds(), List.of());

      if (nonNull(rateSummary.getAvgRatePerMile()) && BigDecimal.valueOf(rateSummary.getAvgRatePerMile()).compareTo(BigDecimal.valueOf(4)) <= 0) {
        entity.setAvgRatePerMile(BigDecimal.valueOf(rateSummary.getAvgRatePerMile()));
      }
    }

    //update the loads_posted field + 1
    user.getBlUserSettings().setLoadsPosted(user.getBlUserSettings().getLoadsPosted() + 1);
  }

  @Override
  public void mapToEntityAuto(final Object data, final Load entity) {
    loadMapper.dataToEntity((LoadData) data, entity);
  }

  @Override
  public void validateEntity(final Result<Load> result, final Load entity) {
    validateLoadAccess(result, entity);
  }

  private void validateDropAbCompanyHasChange(final Result<Load> result, final Load entity, final LoadData loadData) {
    final Optional<AbCompany> dropAbCompany = loadData.getDropAbCompany();
    final Integer oldDropAbCompanyId = entity.getDropAbCompany().getAbCompanyId();
    if (entity.getNumberOfAssignedLoads() > 0) {
      if (exists(dropAbCompany) && !dropAbCompany.get().getAbCompanyId().equals(oldDropAbCompanyId)) {
        result.addError(DROP_AB_COMPANY_ID, "You cannot change the drop location because "
            + "some loads have already been assigned. Click the Reroute button to change the destination or cancel the assignments first.");
      }
    }
  }

  private void validatePickupAbCompanyChanged(final Result<Load> result, final Load entity, final LoadData loadData) {
    final Optional<AbCompany> pickupAbCompanyOpt = loadData.getPickupAbCompany();
    final Integer oldPickupAbCompanyId = entity.getPickupAbCompany().getAbCompanyId();
    //check if any assignments have been made
    if (entity.getNumberOfAssignedLoads() > 0) {
      if (exists(pickupAbCompanyOpt) && !pickupAbCompanyOpt.get().getAbCompanyId().equals(oldPickupAbCompanyId)) {
        result.addError(PICKUP_AB_COMPANY_ID, "You cannot change the pickup location because "
            + "some loads have already been assigned. Click the Reroute button to change the origin or cancel the assignments first.");
      }
    }
  }

  private void validateNumberOfLoads(final Result<Load> result, final LoadData loadData) {
    if (exists(loadData.getNumberOfLoads())) {
      if (isEmpty(loadData.getNumberOfLoads())) {
        result.addError(NUMBER_OF_LOADS, "Enter a number");
      } else if (loadData.getNumberOfLoads().get() < 1) {
        result.addError(NUMBER_OF_LOADS, "Enter at least 1 load");
      } else if (loadData.getNumberOfLoads().get() > 150) {
        result.addError(NUMBER_OF_LOADS, "Up to 150 loads allowed");
      } else {
        //tms validation
        if ((existsAndIsTrue(loadData.getIsManaged()) || existsAndIsTrue(loadData.getIsBroker()))
            && existsAndIsNotEmpty(loadData.getAssignments())) {
          if (loadData.getAssignments().get().size() != loadData.getNumberOfLoads().get()) {
            result.addError("assignments", "You must pass an assignments array with " + loadData.getNumberOfLoads().get() + " elements");
          }
        }
      }
    }
  }

  private void calculateCostPerMile(final Result<Load> result, final Load entity, final LoadData loadData) {

    validateLoEstWeight(loadData);
    validateLoEstVolume(loadData);

    if (existsAndIsTrue(loadData.getIsBroker())) {
      validateRateType(result, loadData);
    }

    if (existsAndIsNotEmpty(loadData.getPickupAbCompany())
        && existsAndIsNotEmpty(loadData.getDropAbCompany())) {

      final Location pickupLocation = buildLocation(loadData.getPickupAbCompany().get());
      final Location dropLocation = buildLocation(loadData.getDropAbCompany().get());
      final Optional<RouteDto> routeOpt = routeService.findRoute(pickupLocation, dropLocation);
      if (routeOpt.isPresent()) {
        final RouteDto route = routeOpt.get();
        entity.setPickupDropMiles(route.getMiles());
        //don't overwrite lo_est_miles in case it exists
        if (isMissingOrIsEmpty(loadData.getLoEstMiles())) {
          loadData.setLoEstMiles(Optional.of(route.getMiles()));
        }

        //don't overwrite lo_est_hours in case it exists
        if (isMissingOrIsEmpty(loadData.getLoEstHours())) {
          calulateLoEstHours(loadData, route);
        }
      } else {
        entity.setPickupDropMiles(null);
      }
    }

    if (nonNull(entity.getOriginLat()) && nonNull(entity.getDestLat())) {
      final Location originLocation = buildOriginLocation(entity);
      final Location destinationLocation = buildDestinationLocation(entity);

      final Optional<RouteDto> routeOpt = routeService.findRoute(originLocation, destinationLocation);
      if (routeOpt.isPresent()) {
        final RouteDto route = routeOpt.get();
        entity.setEstimatedMiles(route.getMiles());
        entity.setLoadBearing(route.getBearing());
        entity.setLoadBearingDirection(route.getBearingDirection());
        if (isMissingOrIsEmpty(loadData.getLoEstMiles())) {
          loadData.setLoEstMiles(Optional.of(route.getMiles()));
        }

        if (isMissingOrIsEmpty(loadData.getLoEstHours())) {
          calulateLoEstHours(loadData, route);
        }
        entity.setMilesChecked(true);
      } else {
        entity.setEstimatedMiles(null);
      }
    } else {
      //Either the origin or destination is missing which is allowed for private loads. Mark miles_checkeed=1 so we don't check again
      entity.setMilesChecked(true);
    }

    //Calculation: (lo_estimated_weight * lo_rate) / mileage = estimated rate per mile
    entity.setLoEstRatePerMile(null);

    calculateLoEstQuantity(result, entity, loadData);
  }

  private void validateRateType(final Result<Load> result, final LoadData loadData) {
    if (isMissingOrIsEmpty(loadData.getLoRateType())) {
      result.addError(LO_RATE_TYPE, "Select the rate type");
    }

    if (isMissingOrIsEmpty(loadData.getLoRate())
        || (existsAndIsEmpty(loadData.getLoRate())
        && (loadData.getLoRate().get().compareTo(BigDecimal.ZERO) < 0 || loadData.getLoRate().get().compareTo(BigDecimal.valueOf(31000d)) > 0))) {
      result.addError(LO_RATE, "Enter a valid rate");
    }
  }

  private void validateLoEstVolume(final LoadData loadData) {
    if (isMissingOrIsEmpty(loadData.getLoEstimatedVolume())) {
      loadData.setLoEstimatedVolume(Optional.of(BigDecimal.valueOf(5000d)));
    }
  }

  private void validateLoEstWeight(final LoadData loadData) {
    if (isMissingOrIsEmpty(loadData.getLoEstimatedWeight())) {
      loadData.setLoEstimatedWeight(Optional.of(BigDecimal.valueOf(52000d)));
    }
  }

  private void calculateLoEstQuantity(final Result<Load> result, final Load entity, final LoadData loadData) {
    if (existsAndIsNotEmpty(loadData.getLoRateType()) && existsAndIsNotEmpty(loadData.getLoRate())) {
      final RateType rateType = loadData.getLoRateType().get();
      final String rateTypeString = rateType.getRateType();
      if (rateType.getIsWeight()) {
        if (loadData.getLoEstimatedWeight().get().compareTo(BigDecimal.ZERO) < 0) {
          result.addError(LO_ESTIMATED_WEIGHT, "Enter a valid number");
        } else {
          entity.setLoEstQuantity(loadData.getLoEstimatedWeight().get().divide(new BigDecimal(rateTypeString), 2, RoundingMode.HALF_UP));
        }
      } else if (rateTypeString.equalsIgnoreCase(GALLON) || rateTypeString.equalsIgnoreCase("liter")) {
        if (loadData.getLoEstimatedVolume().get().compareTo(BigDecimal.ZERO) < 0) {
          result.addError(LO_ESTIMATED_VOLUME, "Enter a valid number");
        } else {
          entity.setLoEstQuantity(loadData.getLoEstimatedVolume().get());
        }
      } else if (rateTypeString.equalsIgnoreCase(MILE)) {
        if (nonNull(entity.getPickupDropMiles()) && entity.getPickupDropMiles().compareTo(BigDecimal.ZERO) > 0) {
          entity.setLoEstQuantity(entity.getPickupDropMiles());
        } else if (existsAndIsNotEmpty(loadData.getLoEstMiles()) && loadData.getLoEstMiles().get().compareTo(BigDecimal.ZERO) > 0) {
          entity.setLoEstQuantity(loadData.getLoEstMiles().get());
        }
      } else if (rateTypeString.equalsIgnoreCase(HOUR)) {
        if (existsAndIsNotEmpty(loadData.getLoEstHours()) && loadData.getLoEstHours().get().compareTo(BigDecimal.ZERO) > 0) {
          entity.setLoEstQuantity(loadData.getLoEstHours().get());
        }
      } else if (rateTypeString.equalsIgnoreCase(FLAT)) {
        entity.setLoEstQuantity(BigDecimal.ONE);
      } else {
        result.addError(RATE_TYPE, "Unknown rate type");
      }

      if (nonNull(entity.getLoEstQuantity())) {
        entity.setLoEstQuantity(entity.getLoEstQuantity().setScale(2, RoundingMode.CEILING));

        if (entity.getLoEstQuantity().compareTo(BigDecimal.ZERO) > 0) {
          entity.setLoEstPay(loadData.getLoRate().get().multiply(entity.getLoEstQuantity()).setScale(2, RoundingMode.CEILING));
          if (nonNull(entity.getPickupDropMiles()) && entity.getPickupDropMiles().compareTo(BigDecimal.ZERO) > 0) {
            entity.setLoEstRatePerMile(entity.getLoEstPay().divide(entity.getPickupDropMiles(), 2, RoundingMode.HALF_UP));
          } else if (existsAndIsNotEmpty(loadData.getLoEstMiles()) && loadData.getLoEstMiles().get().compareTo(BigDecimal.ZERO) > 0) {
            entity.setLoEstRatePerMile(entity.getLoEstPay().divide(loadData.getLoEstMiles().get(), 2, RoundingMode.HALF_UP));
          }
        }
      }
    }
  }

  private void calulateLoEstHours(final LoadData loadData, final RouteDto route) {
    if (nonNull(route.getDuration())) {
      loadData.setLoEstHours(Optional.of(BigDecimal.valueOf(route.getDuration()).divide(BigDecimal.valueOf(3600), 1, RoundingMode.HALF_UP)));
    } else {
      loadData.setLoEstHours(Optional.empty());
    }
  }

  private void validateUser(final Result<Load> result, final LoadData loadData, final int loggedInUserCompanyId, final User user) {
    if (existsAndIsNotEmpty(loadData.getUser()) && loadData.getUser().get().getUserCompany().getUserCompanyId() != loggedInUserCompanyId) {
      result.addError(USER_ID, "The user_id: %s was not found in your company".formatted(loadData.getUser().get().getUserId()));
    } else if (isMissingOrIsEmpty(loadData.getUser())) {
      loadData.setUser(Optional.of(user));
    }
  }

  private void validateShipFrom(final Result<Load> result, final LoadData loadData, final LocalDate now) {
    if (existsAndIsNotEmpty(loadData.getShipFrom())) {
      final LocalDate shipFrom = loadData.getShipFrom().get();
      if (shipFrom.isAfter(now.plusMonths(36))) {
        result.addError(SHIP_FROM, "The date is too far out into the future");
      }
    }
  }

  private void validateShipTo(final Result<Load> result, final LoadData loadData, final LocalDate now) {
    if (existsAndIsNotEmpty(loadData.getShipTo())) {
      final LocalDate shipTo = loadData.getShipTo().get();
      if (shipTo.isAfter(now.plusMonths(36))) {
        result.addError(SHIP_FROM, "The date is too far out into the future");
      }
    }
  }

  private void setRequiredFileTypeIds(final Load entity, final LoadData loadData) {
    if (existsAndIsNotEmpty(loadData.getRequiredFileTypeIds())) {
      final List<FileType> fileTypes = fileTypeRepository.findAllById(loadData.getRequiredFileTypeIds().orElse(new ArrayList<>()));
      entity.setRequiredFileTypes(fileTypes.stream().map(FileType::getFileType).toList());
      entity.setRequiredFileTypeIds(fileTypes.stream().map(FileType::getFileTypeId).toList());
    }
  }

  private void validateDefaultBillWeightUse(final Result<Load> result, final LoadData loadData) {
    //default_bill_weight_use validation. allow values only unload_weight or loaded_weight
    if (existsAndIsNotEmpty(loadData.getDefaultBillWeightUse())
        && Stream.of(UNLOAD_WEIGHT, LOADED_WEIGHT).noneMatch(loadData.getDefaultBillWeightUse().get()::equalsIgnoreCase)) {
      result.addError(DEFAULT_BILL_WEIGHT_USE, "Select to Bill By Origin or Destination Weight/Volume");
    }
  }

  private void validateCertifiedOwnerRequired(final Result<Load> result, final LoadData loadData) {
    //certified_owner_required is only sent in via the website, this is currently the only place certified ownership is required when positing a load
    //In the future if we require it everywhere, we can just remove the 'certified_owner_required' form field and check here
    if (existsAndIsTrue(loadData.getCertifiedOwnerRequired())
        && (!exists(loadData.getCertifiedOwner()) || existsAndIsFalse(loadData.getCertifiedOwner()))) {
      result.addError(CERTIFIED_OWNER, "Only loads that you certify ownership of may be added");
    }
  }

  private void validateContract(final Result<Load> result, final LoadData loadData) {
    final int loggedInUserCompanyId = UserUtil.getUserCompanyIdOrThrow();
    if (existsAndIsNotEmpty(loadData.getContract()) && loadData.getContract().get().getUserCompany().getUserCompanyId() != loggedInUserCompanyId) {
      result.addError(CONTRACT_ID, "You don't own this contract");
    }
  }

  private void validateEquipmentIds(final Result<Load> result, final Load entity, final LoadData loadData) {
    if (existsAndIsNotEmpty(loadData.getEquipmentIds())) {
      final Optional<List<String>> equipmentIdsOpt = loadData.getEquipmentIds();
      final List<Equipment> equipments = equipmentRepository.findAllById(equipmentIdsOpt.orElse(new ArrayList<>()));
      if (equipments.size() != equipmentIdsOpt.get().size()) {
        result.addError(EQUIPMENT_IDS, "Invalid equipment %s. Please enter a valid list of equipment_ids e.g. H,HHS".formatted(equipmentIdsOpt));
      } else {
        entity.setEquipmentNames(equipments.stream().map(Equipment::getEquipmentName).toList());
        entity.setEquipmentIds(equipments.stream().map(Equipment::getEquipmentId).toList());
        entity.setEquipments(equipments);
      }
    }
  }

  private void validateHiringAbCompany(final Result<Load> result, final Load entity, final LoadData loadData, final User user) {
    final int loggedInUserCompanyId = UserUtil.getUserCompanyIdOrThrow();
    final Integer defaultBillToCompanyId = user.getDefaultBillToCompanyId();

    //tms validation
    if (existsAndIsTrue(loadData.getIsBroker())) {
      if (isMissingOrIsEmpty(loadData.getHiringAbCompany())) {
        result.addError(HIRING_AB_COMPANY_ID, "Select the hiring company");
      } else {
        AbCompany hiringAbCompany = loadData.getHiringAbCompany().get();
        final Integer userCompanyId = hiringAbCompany.getUserCompany().getUserCompanyId();
        if (userCompanyId.equals(defaultBillToCompanyId)) {
          hiringAbCompany = abCompanyDomainService.replicate(user, hiringAbCompany);
          loadData.setHiringAbCompany(Optional.of(hiringAbCompany));
        } else if (userCompanyId != loggedInUserCompanyId) {
          result.addError(HIRING_AB_COMPANY_ID, "The company is not in your address book");
        }
      }
    }
  }

  private void validatePickupAbCompany(final Result<Load> result, final Load entity, final LoadData loadData, final User user, final ValidationMethod method) {
    final int loggedInUserCompanyId = UserUtil.getUserCompanyIdOrThrow();
    final Integer defaultBillToCompanyId = user.getDefaultBillToCompanyId();

    if (exists(loadData.getPickupAbCompany())) {
      final Optional<AbCompany> pickupAbCompanyOpt = loadData.getPickupAbCompany();
      if (pickupAbCompanyOpt.isEmpty()) {
        if (existsAndIsTrue(loadData.getIsBroker()) || existsAndIsTrue(loadData.getIsManaged())) {
          result.addError(PICKUP_AB_COMPANY_ID, "The pickup company is required");
        }
      } else {
        AbCompany pickupAbCompany = pickupAbCompanyOpt.get();
        final Integer userCompanyId = pickupAbCompany.getUserCompany().getUserCompanyId();
        if (userCompanyId.equals(defaultBillToCompanyId)) {
          pickupAbCompany = abCompanyDomainService.replicate(user, pickupAbCompany);
          loadData.setPickupAbCompany(Optional.of(pickupAbCompany));
        } else if (userCompanyId != loggedInUserCompanyId) {
          result.addError(PICKUP_AB_COMPANY_ID, "The company was not found in your address book");
        } else if (isMissingOrIsEmpty(loadData.getOrigin())) {
          loadData.setOrigin(Optional.of(pickupAbCompany.getLocation()));
        }
      }
    } else if ( (CREATE == method && isMissingOrIsEmpty(loadData.getOrigin())) || (UPDATE == method && isNull(entity.getOriginCity()))) {
      result.addError(ORIGIN, "The origin is required");
    }

    //If in create load and origin/dest passed but pick/drop not passed (e.g. BL site), create pick/drop companies from origin/dest
    if (existsAndIsTrue(loadData.getIsManaged()) || existsAndIsTrue(loadData.getIsBroker()) && CREATE == method) {
      if (isNull(loadData.getPickupAbCompany()) && existsAndIsNotEmpty(loadData.getOrigin())) {
        final AbCompanyData abCompanyData = new AbCompanyData();
        abCompanyData.setUserTypeIds(Optional.of(List.of(60)));
        abCompanyData.setCompanyName(loadData.getOrigin());
        abCompanyData.setLocation(loadData.getOrigin());
        abCompanyData.setLatitude(loadData.getOriginLatitude());
        abCompanyData.setLongitude(loadData.getOriginLongitude());
        abCompanyData.setCity(loadData.getOriginCity());
        abCompanyData.setState(loadData.getOriginState());
        abCompanyData.setZip(loadData.getOriginZipcode());
        abCompanyData.setCountry(loadData.getOriginCountry());
        abCompanyData.setValidateLocation(loadData.isValidateCities());
        final Result<AbCompany> abCompanyResult = abCompanyDomainService.createOrGetExisting(abCompanyData);
        if (abCompanyResult.hasErrors()) {
          result.getErrors().putAll(abCompanyResult.getErrors());
        } else {
          loadData.setPickupAbCompany(Optional.of(abCompanyResult.getEntity()));
        }
      }
    }
  }

  private void validateDropAbCompany(final Result<Load> result, final Load entity, final LoadData loadData, final User user, final ValidationMethod method) {
    final Integer defaultBillToCompanyId = user.getDefaultBillToCompanyId();
    final int loggedInUserCompanyId = UserUtil.getUserCompanyIdOrThrow();
    if (exists(loadData.getDropAbCompany())) {
      final Optional<AbCompany> dropAbCompanyOpt = loadData.getDropAbCompany();
      if (dropAbCompanyOpt.isEmpty()) {

        if (existsAndIsTrue(loadData.getIsBroker()) || existsAndIsTrue(loadData.getIsManaged())) {
          result.addError(DROP_AB_COMPANY_ID, "The destination company is required");
        }
      } else {
        AbCompany dropAbCompany = dropAbCompanyOpt.get();
        final Integer userCompanyId = dropAbCompany.getUserCompany().getUserCompanyId();
        if (userCompanyId.equals(defaultBillToCompanyId)) {
          dropAbCompany = abCompanyDomainService.replicate(user, dropAbCompany);
          loadData.setDropAbCompany(Optional.of(dropAbCompany));
        } else if (userCompanyId != loggedInUserCompanyId) {
          result.addError(DROP_AB_COMPANY_ID, "The company was not found in your address book");
        } else if (isMissingOrIsEmpty(loadData.getDestination())) {
          loadData.setDestination(Optional.of(dropAbCompany.getLocation()));
        }
      }
    } else if ( (CREATE == method && isMissingOrIsEmpty(loadData.getDestination())) || (UPDATE == method && isNull(entity.getDestinationCity()))) {
      result.addError(DESTINATION, "The destination is required");
    }

    //If in create load and origin/dest passed but pick/drop not passed (e.g. BL site), create pick/drop companies from origin/dest
    if (existsAndIsTrue(loadData.getIsManaged()) || existsAndIsTrue(loadData.getIsBroker()) && CREATE == method) {

      if (isNull(entity.getDropAbCompany())) {
        final AbCompanyData abCompanyData = new AbCompanyData();
        abCompanyData.setUserTypeIds(Optional.of(List.of(60)));
        final AbCompany abCompany = loadData.getDropAbCompany().get();
        abCompanyData.setCompanyName(Optional.of(abCompany.getCompanyName()));
        abCompanyData.setLocation(Optional.of(abCompany.getLocation()));
        abCompanyData.setLatitude(Optional.of(abCompany.getLatitude()));
        abCompanyData.setLongitude(Optional.of(abCompany.getLongitude()));
        abCompanyData.setCity(Optional.of(abCompany.getCity()));
        abCompanyData.setState(Optional.of(abCompany.getState()));
        abCompanyData.setZip(Optional.of(abCompany.getZip()));
        abCompanyData.setCountry(Optional.of(abCompany.getCountry()));
        abCompanyData.setValidateLocation(loadData.isValidateCities());
        final Result<AbCompany> abCompanyResult = abCompanyDomainService.createOrGetExisting(abCompanyData);
        if (abCompanyResult.hasErrors()) {
          result.getErrors().putAll(abCompanyResult.getErrors());
        } else {
          loadData.setDropAbCompany(Optional.of(abCompanyResult.getEntity()));
        }
      }
    }
  }

  private void validateDestination(final Result<Load> result, final Load entity, final LoadData loadData) {
    if (existsAndIsNotEmpty(loadData.getDestination())) {
      final City city = cityRepository.getCityZip(loadData.getDestination().get());
      if (isNull(city)) {
        entity.setMilesChecked(true);
        if (loadData.isValidateCities()) {
          result.addError(ORIGIN, "The origin entered cannot be found in our City database. Please retype the city/state to continue.");
        } else {
          //Make another attempt to find the city in Canada
          final City qorig = cityRepository.getCityZip(loadData.getDestination().get() + ", CA");
          if (isNull(qorig)) {
            entity.setDestinationCity(loadData.getOriginCity().get());
            entity.setDestinationState(loadData.getOriginState().get());
            entity.setDestinationZipcode(loadData.getOriginZipcode().get());
            entity.setDestinationCountry(loadData.getOriginCountry().get());
            entity.setDestLat(loadData.getOriginLatitude().get());
            entity.setDestLong(loadData.getOriginLongitude().get());
          } else {
            entity.setDestinationCity(qorig.getCity());
            entity.setDestinationState(qorig.getState());
            entity.setDestinationZipcode(qorig.getZip());
            entity.setDestinationCountry(qorig.getCountry());
            entity.setDestLat(qorig.getLatitude());
            entity.setDestLong(qorig.getLongitude());
          }
        }
      } else {
        entity.setDestinationCity(city.getCity());
        entity.setDestinationState(city.getState());
        entity.setDestinationZipcode(city.getZip());
        entity.setDestinationCountry(city.getCountry());
        entity.setDestLat(city.getLatitude());
        entity.setDestLong(city.getLongitude());
      }
    }
  }

  private void validateOrigin(final Result<Load> result, final Load entity, final LoadData loadData) {
    if (existsAndIsNotEmpty(loadData.getOrigin())) {
      final City city = cityRepository.getCityZip(loadData.getOrigin().get());
      if (isNull(city)) {
        if (loadData.isValidateCities()) {
          result.addError(ORIGIN, "The origin entered cannot be found in our City database. Please retype the city/state to continue.");
        } else {
          final City qorig = cityRepository.getCityZip(loadData.getOrigin().get() + ", CA");
          if (isNull(qorig)) {
            entity.setOriginCity(loadData.getOriginCity().get());
            entity.setOriginState(loadData.getOriginState().get());
            entity.setOriginZipcode(loadData.getOriginZipcode().get());
            entity.setOriginCountry(loadData.getOriginCountry().get());
            entity.setOriginLat(loadData.getOriginLatitude().get());
            entity.setOriginLong(loadData.getOriginLongitude().get());
          } else {
            entity.setOriginCity(qorig.getCity());
            entity.setOriginState(qorig.getState());
            entity.setOriginZipcode(qorig.getZip());
            entity.setOriginCountry(qorig.getCountry());
            entity.setOriginLat(qorig.getLatitude());
            entity.setOriginLong(qorig.getLongitude());
          }
        }
      } else {
        entity.setOriginCity(city.getCity());
        entity.setOriginState(city.getState());
        entity.setOriginZipcode(city.getZip());
        entity.setOriginCountry(city.getCountry());
        entity.setOriginLat(city.getLatitude());
        entity.setOriginLong(city.getLongitude());
      }
    }
  }

  private void validateContactNumber(final Result<Load> result, final Load entity, final LoadData loadData) {
    if (hasChange(entity.getContactNumber(), loadData.getContactNumber()) && existsAndIsNotEmpty(loadData.getContactNumber())) {
      final PhoneNumberDetails numberDetails;
      try {
        numberDetails = phoneNumberService.validate(loadData.getContactNumber().get());
        entity.setContactNumber(numberDetails.getPhoneNumber());
        entity.setContactNumberType(numberDetails.getCarrierType());
      } catch (InvalidNumberException e) {
        result.addError(CONTACT_NUMBER, "Enter a valid phone");
      }
    }
  }

  private void validateLoadAccess(final Result<Load> result, final Load entity) {
    if (exists(entity.getLoadAccess())) {
      if (!PRIVATE.equals(entity.getLoadAccess())) {
        if (isEmpty(entity.getOriginCity())) {
          result.addError(ORIGIN, "The origin is required");
        }
        if (isEmpty(entity.getDestinationCity())) {
          result.addError(DESTINATION, "The destination is required");
        }
        if (isEmpty(entity.getContactName())) {
          result.addError(CONTACT_NAME, "The contact name is required");
        }
        if (isEmpty(entity.getContactNumber())) {
          result.addError(CONTACT_NUMBER, "The contact phone is required");
        }

        if (isEmpty(entity.getShipFrom())) {
          result.addError(SHIP_FROM, "Enter a date");
        }

        if (isEmpty(entity.getShipTo())) {
          result.addError(SHIP_TO, "Enter a date");
        }

        if (isEmpty(entity.getEquipmentIds())) {
          result.addError(EQUIPMENT_IDS, "Select at least 1 Trailer Type");
        }

        //check number of available loads. if no more available loads, it cannot be changed to public
        if (entity.getNumberOfAvailableLoads() <= 0) {
          result.addError(LOAD_ACCESS, "The load cannot be made available to the public because all loads have been "
              + "assigned and there are no more available loads");
          result.addError(NUMBER_OF_LOADS, "The load cannot be made available to the public because all loads have "
              + "been assigned and there are no more available loads");
        }
      } else {
        result.getErrors().remove(ORIGIN);
        result.getErrors().remove(DESTINATION);
      }
    }
  }

  private void validateNonRootChanges(final Result<Load> result, final Load entity, final LoadData loadData) {
    final String errorMessage = "You are not allowed to make this change";

    final Optional<AbCompany> pickupAbCompany = loadData.getPickupAbCompany();
    if (existsAndIsNotEmpty(pickupAbCompany) && !pickupAbCompany.get().getAbCompanyId().equals(entity.getPickupAbCompany().getAbCompanyId())) {
      result.addError(PICKUP_AB_COMPANY_ID, errorMessage);
    }

    final Optional<AbCompany> dropAbCompany = loadData.getDropAbCompany();
    if (existsAndIsNotEmpty(dropAbCompany) && !dropAbCompany.get().getAbCompanyId().equals(entity.getDropAbCompany().getAbCompanyId())) {
      result.addError(PICKUP_AB_COMPANY_ID, errorMessage);
    }

    final Optional<LocalDate> shipFrom = loadData.getShipFrom();
    if (existsAndIsNotEmpty(shipFrom) && !shipFrom.get().equals(entity.getShipFrom())) {
      result.addError(SHIP_FROM, errorMessage);
    }

    final Optional<LocalDate> shipTo = loadData.getShipTo();
    if (existsAndIsNotEmpty(shipTo) && !shipTo.get().equals(entity.getShipTo())) {
      result.addError(SHIP_TO, errorMessage);
    }

    final Optional<AbCompany> hiringAbCompany = loadData.getHiringAbCompany();
    if (existsAndIsNotEmpty(hiringAbCompany) && !hiringAbCompany.get().getAbCompanyId().equals(entity.getHiringAbCompany().getAbCompanyId())) {
      result.addError(HIRING_AB_COMPANY_ID, errorMessage);
    }

    final Optional<AbUser> hiringAbUser = loadData.getHiringAbUser();
    if (existsAndIsNotEmpty(hiringAbUser) && !hiringAbUser.get().getAbUserId().equals(entity.getHiringAbUser().getAbUserId())) {
      result.addError(HIRING_AB_USER_ID, errorMessage);
    }

    final Optional<Integer> numberOfLoads = loadData.getNumberOfLoads();
    if (existsAndIsNotEmpty(numberOfLoads) && !numberOfLoads.get().equals(entity.getNumberOfLoads())) {
      result.addError(NUMBER_OF_LOADS, errorMessage);
    }

    final Optional<Boolean> isBroker = loadData.getIsBroker();
    if (existsAndIsNotEmpty(isBroker) && !isBroker.get().equals(entity.getIsBroker())) {
      result.addError(IS_BROKER, errorMessage);
    }

    final Optional<BigDecimal> loRate = loadData.getLoRate();
    if (existsAndIsNotEmpty(loRate) && loRate.get().compareTo(entity.getLoRate()) != 0) {
      result.addError(LO_RATE, errorMessage);
    }

    if (existsAndIsNotEmpty(loadData.getLoRateType()) && !loadData.getLoRateType().get().equals(entity.getLoRateType())) {
      result.addError(LO_RATE_TYPE, errorMessage);
    }
  }

  private Location buildOriginLocation(final Load entity) {
    final Double lon = entity.getOriginLong();
    final Double lat = entity.getOriginLat();
    final Coordinates coordinates = buildCoordinates(lon, lat);

    return Location.builder()
        .address("")
        .city(entity.getOriginCity())
        .state(entity.getOriginState())
        .zip(entity.getOriginZipcode())
        .country(entity.getOriginCountry())
        .coordinates(coordinates)
        .build();
  }

  private Location buildDestinationLocation(final Load entity) {
    final Double lon = entity.getDestLong();
    final Double lat = entity.getDestLat();
    final Coordinates coordinates = buildCoordinates(lon, lat);

    return Location.builder()
        .address("")
        .city(entity.getDestinationCity())
        .state(entity.getDestinationState())
        .zip(entity.getDestinationZipcode())
        .country(entity.getDestinationCountry())
        .coordinates(coordinates)
        .build();
  }

  private Location buildLocation(final AbCompany abCompany) {
    final Double lon = abCompany.getLongitude();
    final Double lat = abCompany.getLatitude();
    final Coordinates coordinates = buildCoordinates(lon, lat);

    return Location.builder()
        .address(abCompany.getAddress())
        .city(abCompany.getCity())
        .state(abCompany.getState())
        .zip(abCompany.getZip())
        .country(abCompany.getCountry())
        .coordinates(coordinates)
        .build();
  }

  private static Coordinates buildCoordinates(final Double longitude, final Double latitude) {
    return Coordinates.builder().longitude(longitude).latitude(latitude).build();
  }
}
