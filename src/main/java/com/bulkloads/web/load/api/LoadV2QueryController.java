package com.bulkloads.web.load.api;

import static com.bulkloads.common.mui.model.QueryParams.getFieldInfoFromClass;
import java.util.List;
import java.util.Map;
import com.bulkloads.common.mui.model.FieldInfo;
import com.bulkloads.common.mui.model.QueryParams;
import com.bulkloads.web.load.api.dto.MyLoadV2ListResponse;
import com.bulkloads.web.load.service.LoadService;
import com.bulkloads.web.load.service.dto.LoadTotalResponse;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.constraints.PositiveOrZero;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequestMapping(path = "/rest/v2/")
@Tag(name = "Loads")
@CrossOrigin(origins = "*", maxAge = 3600)
@RequiredArgsConstructor
@Validated
public class LoadV2QueryController {

  private static final Map<String, FieldInfo> MY_LOADS_LIST_FIELD_NAME_CACHE = getFieldInfoFromClass(MyLoadV2ListResponse.class);

  private final LoadService loadService;

  @GetMapping("/loads/my_loads")
  public List<MyLoadV2ListResponse> getMyLoads(
      HttpServletRequest request,
      @RequestParam(name = "active", required = false) Boolean active,
      @RequestParam(name = "user_ids", required = false, defaultValue = "") String userIds,

      @Parameter(description = "the number of records to skip, defaults to 0")
      @PositiveOrZero(message = "Must be 0 or positive")
      @RequestParam(value = "skip", defaultValue = "0") int skip,

      @Parameter(description = "the number of records to return, defaults to 100")
      @PositiveOrZero(message = "Must be 0 or positive")
      @RequestParam(value = "limit", defaultValue = "100") int limit) {

    return loadService.getMyLoadsV2(
        active,
        userIds,
        new QueryParams(request.getQueryString(), MY_LOADS_LIST_FIELD_NAME_CACHE),
        skip, limit);
  }

  @GetMapping("/loads/my_loads/totals")
  public LoadTotalResponse getMyLoadsTotals(
      HttpServletRequest request,
      @RequestParam(name = "active", required = false) Boolean active,
      @RequestParam(name = "user_ids", required = false, defaultValue = "") String userIds) {

    return loadService.getMyLoadsTotalsV2(
        active,
        userIds,
        new QueryParams(request.getQueryString(), MY_LOADS_LIST_FIELD_NAME_CACHE));
  }

}
