package com.bulkloads.web.load.v2.service.dto;

import java.time.Instant;
import java.time.LocalDate;
import lombok.Data;

@Data
public class LoadListResponse {

  private String firstName;
  private String lastName;
  private String numberOfLoads;
  private String numberOfAvailableLoads;
  private String dropCity;
  private String dropCompanyName;
  private String dropState;
  private Double estimatedMiles;
  private Integer loadId;
  private String loCommodity;
  private String loContractNumber;
  private Double loRate;
  private String rateTypeText;
  private Integer numberOfDeliveredLoads;
  private String parentHiringCompanyName;
  private String pickupCity;
  private String pickupCompanyName;
  private String pickupState;
  private Instant postDate;
  private Boolean postedToBulkLoads;
  private String rateProductCategory;
  private LocalDate shipFrom;
  private LocalDate shipTo;
  private String workOrderNumber;
}