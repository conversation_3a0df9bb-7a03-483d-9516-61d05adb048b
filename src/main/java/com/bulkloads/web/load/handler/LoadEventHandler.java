package com.bulkloads.web.load.handler;

import static com.bulkloads.config.AppConstants.DELIVERED_COMPLETED;
import static com.bulkloads.config.AppConstants.StatusPropagationDirection.ABOVE_REFERENCE_ASSIGNMENT_BOOKING;
import static com.bulkloads.config.AppConstants.StatusPropagationDirection.BELOW_REFERENCE_ASSIGNMENT_BOOKING;
import static com.bulkloads.config.AppConstants.StatusPropagationDirection.THE_REFERENCE_ASSIGNMENT_BOOKING;
import static java.util.Objects.nonNull;
import static java.util.Optional.ofNullable;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import com.bulkloads.config.AppProperties;
import com.bulkloads.web.assignment.domain.entity.Assignment;
import com.bulkloads.web.assignment.event.AssignmentBookingEvent;
import com.bulkloads.web.assignment.event.AssignmentBookingStatusChangedEvent;
import com.bulkloads.web.assignment.event.AssignmentCancelledEvent;
import com.bulkloads.web.assignment.event.AssignmentCreatedEvent;
import com.bulkloads.web.assignment.mapper.AssignmentMapper;
import com.bulkloads.web.assignment.repository.AssignmentRepository;
import com.bulkloads.web.assignment.service.AssignmentService;
import com.bulkloads.web.assignment.service.template.RerouteAssignmentTemplateBuilder;
import com.bulkloads.web.infra.email.EmailService;
import com.bulkloads.web.infra.email.domain.EmailDetails;
import com.bulkloads.web.load.domain.entity.Load;
import com.bulkloads.web.load.domain.template.DynamicLink;
import com.bulkloads.web.load.domain.template.LoadAssignmentTemplateModel;
import com.bulkloads.web.load.event.LoadReroutedEvent;
import com.bulkloads.web.load.event.LoadUpdatedEvent;
import com.bulkloads.web.load.repository.LoadRepository;
import com.bulkloads.web.load.service.LoadService;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
@RequiredArgsConstructor
public class LoadEventHandler {

  public static final String UNASSIGNED = "Unassigned";

  private final LoadService loadService;
  private final LoadRepository loadRepository;
  private final AssignmentRepository assignmentRepository;
  private final EmailService emailService;
  private final AssignmentMapper assignmentMapper;
  private final AssignmentService assignmentService;
  private final RerouteAssignmentTemplateBuilder rerouteAssignmentTemplateBuilder;
  private final AppProperties appProperties;

  @TransactionalEventListener(classes = {
      AssignmentCreatedEvent.class,
      AssignmentCancelledEvent.class
  }, phase = TransactionPhase.BEFORE_COMMIT)
  public void handleAssignmentCreatedCancelled(final AssignmentBookingEvent event) {
    final Assignment assignment = assignmentRepository.getReferenceById(event.getLoadAssignmentIds().get(0));

    log.info("LoadEventHandler: handleLoadUpdate: assignment: {}", assignment);
    refreshLoadCalculatedFields(assignment.getLoad());
    refreshLoadCalculatedFields(assignment.getToLoad());
  }

  @TransactionalEventListener(classes = {
      AssignmentBookingStatusChangedEvent.class
  }, phase = TransactionPhase.BEFORE_COMMIT)
  public void handleAssignmentStatusChanged(final AssignmentBookingStatusChangedEvent event) {
    final Assignment assignment = assignmentRepository.getReferenceById(event.getLoadAssignmentIds().get(0));
    log.info("LoadEventHandler: handleStatusChanged: assignment: {}", assignment);

    switch (event.getDirection()) {
      case ABOVE_REFERENCE_ASSIGNMENT_BOOKING -> refreshLoadCalculatedFields(assignment.getLoad());
      case THE_REFERENCE_ASSIGNMENT_BOOKING -> {
        refreshLoadCalculatedFields(assignment.getLoad());
        refreshLoadCalculatedFields(assignment.getToLoad());
      }
      case BELOW_REFERENCE_ASSIGNMENT_BOOKING -> refreshLoadCalculatedFields(assignment.getToLoad());
      default -> throw new IllegalArgumentException("Invalid direction: " + event.getDirection());
    }

  }

  @TransactionalEventListener(classes = AssignmentBookingStatusChangedEvent.class,
      phase = TransactionPhase.BEFORE_COMMIT)
  public void handleLoadActivation(final AssignmentBookingStatusChangedEvent event) {
    final Assignment assignment = assignmentRepository.getReferenceById(event.getLoadAssignmentIds().get(0));
    final String newStatus = event.getNewStatus();

    Optional.ofNullable(assignment.getLoad())
        .map(Load::getLoadId)
        .map(loadRepository::getReferenceById)
        .ifPresent(load -> processLoadActivation(load, newStatus));

    Optional.ofNullable(assignment.getToLoad())
        .map(Load::getLoadId)
        .map(loadRepository::getReferenceById)
        .ifPresent(load -> processLoadActivation(load, newStatus));
  }

  @EventListener(classes = LoadReroutedEvent.class)
  public void handleReroutedEvent(final LoadReroutedEvent event) {
    final List<Integer> assignmentIds = event.getReroutedAssignmentIds();

    build(assignmentIds, event.getAction())
        .forEach(emailService::sendEmail);
  }

  public List<EmailDetails> build(final List<Integer> assignmentIds, final String eventAction) {
    final List<Assignment> assignments = assignmentRepository.findAllById(assignmentIds);
    final List<DynamicLink> dynamicLinks = assignmentService.createDynamicLinks(assignments, eventAction);
    final String domainUrl = appProperties.getDomainUrl();

    return groupAssignmentsByRecipient(assignments).values().stream()
        .map(it -> assignmentMapper.assignmentsToFmModel(it, dynamicLinks, domainUrl))
        .map(this::buildEmailDetails)
        .toList();
  }

  private EmailDetails buildEmailDetails(final LoadAssignmentTemplateModel model) {
    final AppProperties.Mailing mailing = appProperties.getMailing();
    return EmailDetails.builder()
        .fromEmail(mailing.getFromEmail())
        .failTo(mailing.getFailToEmail())
        .replyToEmail(mailing.getReplyToEmail())
        .toEmails(List.of(model.getToEmail()))
        .message(rerouteAssignmentTemplateBuilder.getEmailContent(model))
        .subject(rerouteAssignmentTemplateBuilder.getEmailTitle(model, null))
        .build();
  }

  private Map<String, List<Assignment>> groupAssignmentsByRecipient(final List<Assignment> assignments) {
    return assignments.stream()
        .filter(assignment -> nonNull(assignment.getLoad()) && !UNASSIGNED.equals(assignment.getAssignmentStatus()))
        .collect(Collectors.groupingBy(this::getRecipientId, Collectors.toList()));
  }

  private String getRecipientId(final Assignment assignment) {
    return assignment.getToAbUser() != null
        ? "ab" + assignment.getToAbUser().getAbUserId()
        : "user" + ofNullable(assignment.getToUser()).map(user -> user.getUserId().toString()).orElse("");
  }

  private boolean shouldActivateLoad(final Load load, final String assignmentStatus) {
    return !DELIVERED_COMPLETED.contains(assignmentStatus)
        && !load.getDeleted() && !load.getActive();
  }

  private void processLoadActivation(final Load load, final String newStatus) {
    if (shouldActivateLoad(load, newStatus)) {
      loadService.activateLoad(load.getLoadId());
    }
  }

  private void refreshLoadCalculatedFields(Load load) {
    if (nonNull(load)) {
      loadService.refreshAssignmentCalculatedFields(load.getLoadId());
      load.registerDomainEvent(new LoadUpdatedEvent(load.getLoadId()));
    }
  }
}
