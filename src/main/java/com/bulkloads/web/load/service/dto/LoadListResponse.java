package com.bulkloads.web.load.service.dto;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import lombok.Data;

@Data
public class LoadListResponse {

  private Integer loadId;
  private String destinationCity;
  private String destinationZip;
  private String contactName;
  private String contactNumber;
  private String companyName;
  private String companyLogoUrl;
  private String companyLogoThumbUrl;
  private String userTypes;
  private Double avgRating;
  private Integer ratingCount;
  private Double avgDaysToPay;
  private Integer nonPaymentCount;
  private String originCountry;
  private String originState;
  private String originCity;
  private String originZip;
  private Double originLat;
  private Double originLong;
  private String destinationCountry;
  private String destinationState;
  private Double destinationLat;
  private Double destinationLong;
  private Double currentBouncemiles;
  private Double currentBouncemilesPcmiler;
  private Double currentBounceBearing;
  private String currentBounceBearingDirection;
  private Integer rateProductCategoryId;
  private String product;
  private Double originBouncemiles;
  private Double originBouncemilesPcmiler;
  private Double originBounceBearing;
  private String originBounceBearingDirection;
  private LocalDate shipFrom;
  private LocalDate shipTo;
  private Integer numberOfLoads;
  private String rate;
  private Double avgRatePerMile;
  private Boolean isHazmat;
  private String equipmentIds;
  private String equipmentNames;
  private Double estimatedMiles;
  private Double loadBearing;
  private String loadBearingDirection;
  private Instant postDate;
  private Integer userId;
  private Integer userCompanyId;
  private Boolean isFavoriteLoad;
  private Boolean isFavoriteCompany;
  private Boolean isFavoriteLane;
  private Boolean isOfferedLoad;
  private Integer offerId;
  private BigDecimal offerRate;
  private String offerRateType;
  private Double offerRatePerMile;
  private Instant offerDate;
  private String offerMessage;
  private String offerStatus;
  private Boolean allowAutoBooking;
  private String pickupCompanyName;
  private String pickupAddress;
  private String pickupLocation;
  private String pickupCompanyPhone;
  private Boolean pickupApptRequired;
  private String pickupReceivingHours;
  private String pickupDirections;
  private Double pickupLatitude;
  private Double pickupLongitude;
  private String pickupNotes;
  private String dropCompanyName;
  private String dropAddress;
  private String dropLocation;
  private String dropCompanyPhone;
  private Boolean dropApptRequired;
  private String dropReceivingHours;
  private String dropDirections;
  private Double dropLatitude;
  private Double dropLongitude;
  private String dropNotes;
  private Boolean washoutRequired;
  private Boolean isUserOnline;
  private LocalDate signUpDate;
  private Boolean active;
}