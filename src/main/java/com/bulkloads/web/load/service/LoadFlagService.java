package com.bulkloads.web.load.service;

import java.time.Instant;
import java.util.Collections;
import java.util.Optional;
import com.bulkloads.common.UserUtil;
import com.bulkloads.common.validation.Result;
import com.bulkloads.config.AppProperties;
import com.bulkloads.exception.ValidationException;
import com.bulkloads.web.infra.email.EmailService;
import com.bulkloads.web.infra.email.domain.EmailDetails;
import com.bulkloads.web.load.domain.entity.Load;
import com.bulkloads.web.load.domain.entity.LoadFlag;
import com.bulkloads.web.load.repository.LoadFlagRepository;
import com.bulkloads.web.load.repository.LoadRepository;
import com.bulkloads.web.load.service.dto.LoadFlagRequest;
import org.springframework.stereotype.Service;
import jakarta.validation.Validator;
import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class LoadFlagService {

  private final LoadFlagRepository loadFlagRepository;
  private final LoadRepository loadRepository;
  private final EmailService emailService;
  private final AppProperties appProperties;
  private final Validator validator;

  public void loadFlag(final int loadId, final LoadFlagRequest dto) {

    final Optional<Load> load = loadRepository.findById(loadId);
    if (load.isEmpty()) {
      throw new ValidationException("load_id", "Load not found");
    }

    final int userId = UserUtil.getUserIdOrThrow();

    final LoadFlag flag = new LoadFlag();
    final Result<LoadFlag> result = new Result<>(flag);
    flag.setUserId(userId);
    flag.setSiteId(1);
    flag.setFlaggedLoadId(loadId);
    flag.setReason(dto.getReason());
    flag.setDateAdded(Instant.now());

    result.validate(validator, flag);
    if (result.hasErrors()) {
      throw new ValidationException(result.getErrors());
    }

    loadFlagRepository.save(flag);

    sendLoadFlagEmail();
  }


  private void sendLoadFlagEmail() {

    final String subject = "A Load has been Flagged";
    final String emailMessage = "A Load has been flagged for removal.  Please review it in Admin.";
    final String description = "Admin Load Flagged Email";
    final String siteEmail = appProperties.getMailing().getSiteEmail();

    EmailDetails emailDetails = EmailDetails.builder()
        .toEmails(Collections.singletonList(siteEmail))
        .fromEmail(siteEmail)
        .subject(subject)
        .replyToEmail(siteEmail)
        .failTo(siteEmail)
        .message(emailMessage)
        .category(17)
        .siteId(1)
        .senderUserId(0)
        .description(description)
        .build();

    emailService.sendEmail(emailDetails);

  }

}
