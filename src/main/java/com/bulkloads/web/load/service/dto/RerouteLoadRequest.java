package com.bulkloads.web.load.service.dto;

import java.math.BigDecimal;
import java.util.List;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Positive;
import jakarta.validation.constraints.Size;
import lombok.Builder;
import lombok.Value;
import lombok.extern.jackson.Jacksonized;

@Value
@Builder(toBuilder = true)
@Jacksonized
public class RerouteLoadRequest {

  @Schema(description = "Reroute pickup drop.", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  String reroutePickupDrop;

  @Positive(message = "Reroute ab company id must be a positive number.")
  @Schema(description = "Reroute address book company id.", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Integer rerouteAbCompanyId;

  @Size(message = "The reason can be up to 1000 chars.")
  @Schema(description = "Reroute reason.", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  String rerouteReason;

  @Positive(message = "Reroute contract id must be a positive number.")
  @Schema(description = "Reroute contract id.", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Integer rerouteContractId;

  @Size(max = 45, message = "Reroute contract number should be less than 45 characters.")
  @Schema(description = "Reroute contract number.", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  String rerouteContractNumber;

  @Positive(message = "Lo rate must be a positive number.")
  @Schema(description = "Load rate.", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  BigDecimal loRate;

  @Schema(description = "Lo rate type.", requiredMode = Schema.RequiredMode.NOT_REQUIRED, defaultValue = "2000")
  String loRateType;

  @NotEmpty(message = "Select one or more loads to reroute.")
  @Schema(description = "Reroute load assignments.", requiredMode = Schema.RequiredMode.REQUIRED)
  List<@Valid RerouteLoadAssignment> assignments;
}
