package com.bulkloads.web.load.service;

import java.time.LocalDateTime;
import com.bulkloads.web.load.domain.entity.LoadView;
import com.bulkloads.web.load.repository.LoadViewRepository;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class LoadViewService {

  private final LoadViewRepository loadViewRepository;

  public void create(
      final int userId,
      final int loadId,
      final int ownerUserId,
      final int ownerUserCompanyId,
      final String originCity,
      final String originState,
      final String destinationCity,
      final String destinationState,
      final String equipmentIds
  ) {
    final LoadView loadView = new LoadView();
    loadView.setUserId(userId);
    loadView.setLoadId(loadId);
    loadView.setDateAdded(LocalDateTime.now());
    loadView.setSiteId(1);
    loadView.setOwnerUserId(ownerUserId);
    loadView.setOwnerUserCompanyId(ownerUserCompanyId);
    loadView.setOriginCity(originCity);
    loadView.setOriginState(originState);
    loadView.setDestinationCity(destinationCity);
    loadView.setDestinationState(destinationState);
    loadView.setEquipment(equipmentIds);

    loadViewRepository.save(loadView);
  }

}
