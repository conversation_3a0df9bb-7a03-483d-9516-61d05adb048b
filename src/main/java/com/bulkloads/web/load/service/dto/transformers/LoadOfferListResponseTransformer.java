package com.bulkloads.web.load.service.dto.transformers;

import static com.bulkloads.web.load.service.dto.transformers.LoadListResponseTransformer.toResponse;
import com.bulkloads.common.jpa.nativejpa.QueryParts;
import com.bulkloads.web.load.service.dto.LoadOfferListResponse;
import org.hibernate.query.TupleTransformer;
import org.springframework.stereotype.Component;
import lombok.RequiredArgsConstructor;

@Component
@RequiredArgsConstructor
public class LoadOfferListResponseTransformer implements TupleTransformer<LoadOfferListResponse> {

  @Override
  public LoadOfferListResponse transformTuple(Object[] columns, String[] aliases) {
    QueryParts parts = new QueryParts(columns, aliases);
    LoadOfferListResponse response = (LoadOfferListResponse)toResponse(columns, aliases);

    response.setOfferRecipientId(parts.asInteger("offer_recipient_id"));
    response.setNumberOfLoadsAccepted(parts.asInteger("number_of_loads_accepted"));

    return response;
  }

}

