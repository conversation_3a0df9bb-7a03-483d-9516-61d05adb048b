package com.bulkloads.web.load.repository.template;

public class GetMyLoadsQueryTemplate {

  public static final String GET_MY_LOADS_QUERY_TEMPLATE = """
      select
        l.load_id,
        l.number_of_loads,
        l.number_of_assigned_loads,
        l.number_of_available_loads,
        l.number_of_delivered_loads,
        l.pickup_drop_miles,
        l.lo_commodity,
        l.lo_contract_number,
        l.lo_rate,
        rt.rate_type_text as lo_rate_type_label,
        hiring_c.company_name as parent_hiring_company_name,
        pickup_c.city as pickup_city,
        pickup_c.company_name as pickup_company_name,
        pickup_c.state as pickup_state,
        pickup_c.latitude as pickup_latitude,
        pickup_c.longitude as pickup_longitude,
        drop_c.city as drop_city,
        drop_c.company_name as drop_company_name,
        drop_c.state as drop_state,
        drop_c.latitude as drop_latitude,
        drop_c.longitude as drop_longitude,
        (l.load_access != 'private') as posted_to_bulk_loads,
        l.ship_from,
        l.ship_to,
        l.post_date,
        trim(concat(u.first_name, ' ', u.last_name)) as added_by,
        rate_product_category as rate_product_category
      from loads l
          inner join user_info u on l.user_id = u.user_id
          left join ab_companies pickup_c on l.pickup_ab_company_id = pickup_c.ab_company_id
          left join ab_companies drop_c on l.drop_ab_company_id = drop_c.ab_company_id
          left join ab_companies hiring_c on l.hiring_ab_company_id = hiring_c.ab_company_id
          left join rate_types rt on l.lo_rate_type = rt.rate_type
      where
        <% params.put("c_id", c_id) %>
        l.user_company_id = :c_id
  
      and l.is_managed = 1
      and l.deleted = 0
      
      <% if (paramExistsAdd("active")) { %>
        AND l.active = :active
      <% } %>

      <% if (paramExistsAdd("user_ids")) { %>
        AND l.user_id in (:user_ids)
      <% } %>
      
      <% if (paramExists("having_clause")) {
        print(" having " + having_clause)
      } %>
      
      <% if (paramExists("order_by_clause")) { %>
        <% print(" order by " + order_by_clause + ", l.load_id desc") %>
      <% } else { %>
        order by l.load_id desc
      <% } %>
      
      <% if (paramExistsAdd("limit")) { %>
        limit
        <% if (paramExistsAdd("skip")) { %>
         :skip,
        <% } %>
        :limit
      <% } %>
      """;
}
