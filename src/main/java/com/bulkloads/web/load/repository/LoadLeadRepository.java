package com.bulkloads.web.load.repository;

import java.util.List;
import com.bulkloads.web.load.domain.entity.LoadLead;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

@Repository
public interface LoadLeadRepository extends JpaRepository<LoadLead, Integer> {

  @Transactional
  @Modifying
  @Query(value = """
        INSERT INTO load_leads (load_id, user_id, truck_id, lead_source, date_added)
        SELECT l.load_id, :userId, :truckId, 'T', CURRENT_TIMESTAMP
        FROM loads l
        WHERE l.load_id IN (:loadIds)
      """, nativeQuery = true)
  void insertNewLeads(@Param("loadIds") List<Integer> loadIds,
                      @Param("userId") Integer userId,
                      @Param("truckId") Integer truckId);

  @Query("SELECT ll.loadId FROM LoadLead ll WHERE ll.truckId = :truckId")
  List<Integer> findLoadIdsByTruckId(@Param("truckId") Integer truckId);

  @Modifying
  @Query("DELETE FROM LoadLead ll WHERE ll.loadId IN :loadIds AND ll.truckId = :truckId")
  void deleteLeadsByLoadIdsAndTruckId(@Param("loadIds") List<Integer> loadIds, @Param("truckId") Integer truckId);

  @Modifying
  @Transactional
  @Query("""
      DELETE FROM LoadLead l
      WHERE l.truckId = :truckId
      """)
  void deleteLeadsByTruckId(@Param("truckId") Integer truckId);

  @Modifying
  @Transactional
  @Query("""
       UPDATE Load l
       SET l.leadCount = l.leadCount - 1
       WHERE l.loadId IN :loadIds
      """)
  void decrementLeadCount(@Param("loadIds") List<Integer> loadIds);

}
