package com.bulkloads.web.load.repository;

import static com.bulkloads.web.load.repository.template.GetLoadsQueryTemplate.GET_LOADS_QUERY_TEMPLATE;
import static com.bulkloads.web.load.repository.template.GetMyLoadsQueryTemplate.GET_MY_LOADS_QUERY_TEMPLATE;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.bulkloads.common.jpa.nativejpa.JpaNativeQueryService;
import com.bulkloads.common.mui.model.QueryParams;
import com.bulkloads.web.load.api.dto.MyLoadV2ListResponse;
import com.bulkloads.web.load.service.dto.LoadListResponse;
import com.bulkloads.web.load.service.dto.LoadOfferListResponse;
import com.bulkloads.web.load.service.dto.LoadResponse;
import com.bulkloads.web.load.service.dto.LoadSearchRequest;
import com.bulkloads.web.load.service.dto.LoadTotalResponse;
import com.bulkloads.web.load.service.dto.transformers.LoadListResponseTransformer;
import com.bulkloads.web.load.service.dto.transformers.LoadOfferListResponseTransformer;
import com.bulkloads.web.load.service.dto.transformers.LoadResponseTransformer;
import com.bulkloads.web.load.service.dto.transformers.LoadTotalResponseTransformer;
import com.bulkloads.web.load.service.dto.transformers.MyLoadResponseTransformer;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.stereotype.Repository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Repository
@RequiredArgsConstructor
public class LoadQueryRepositoryImpl implements LoadQueryRepository {

  private final JpaNativeQueryService jpaNativeQueryService;
  private final LoadResponseTransformer loadResponseTransformer;
  private final MyLoadResponseTransformer myLoadResponseTransformer;
  private final LoadListResponseTransformer loadListResponseTransformer;
  private final LoadOfferListResponseTransformer loadOfferListResponseTransformer;
  private final LoadTotalResponseTransformer loadTotalResponseTransformer;
  private final ObjectMapper objectMapper;

  @Override
  public List<LoadListResponse> findLoads(
      final Integer uId,
      final Integer abUserId,
      final Integer cId,
      final Boolean isPro,
      final LoadSearchRequest loadSearchRequest,
      final String orderBy,
      final Integer skip,
      final Integer limit) {

    Map<String, Object> params = new HashMap<>();

    params.put("u_id", uId);
    params.put("ab_user_id", abUserId);
    params.put("c_id", cId);
    params.put("is_pro", isPro);

    Map<String, Object> searchParams = objectMapper.convertValue(loadSearchRequest, Map.class);
    params.putAll(searchParams);

    params.put("order_by", orderBy);
    params.put("skip", skip);
    params.put("limit", limit);

    return jpaNativeQueryService.query(
        GET_LOADS_QUERY_TEMPLATE,
        params,
        loadListResponseTransformer);
  }

  @Override
  public LoadTotalResponse findLoadTotals(
      final Integer uId,
      final Integer abUserId,
      final Integer cId,
      final Boolean isPro,
      final LoadSearchRequest loadSearchRequest) {

    Map<String, Object> params = new HashMap<>();
    params.put("count", true);
    params.put("u_id", uId);
    params.put("ab_user_id", abUserId);
    params.put("c_id", cId);
    params.put("is_pro", isPro);

    Map<String, Object> searchParams = objectMapper.convertValue(loadSearchRequest, Map.class);
    params.putAll(searchParams);

    return jpaNativeQueryService.queryForObject(
        GET_LOADS_QUERY_TEMPLATE,
        params,
        loadTotalResponseTransformer);
  }

  @Override
  public LoadResponse findLoadById(
      final Integer uId,
      final Integer abUserId,
      final Integer cId,
      final Boolean isPro,
      final Integer loadId) {

    Map<String, Object> params = new HashMap<>();
    params.put("u_id", uId);
    params.put("ab_user_id", abUserId);
    params.put("c_id", cId);
    params.put("is_pro", isPro);
    params.put("load_id", loadId);

    return jpaNativeQueryService.queryForObject(
        GET_LOADS_QUERY_TEMPLATE,
        params,
        loadResponseTransformer);
  }

  @Override
  public List<MyLoadV2ListResponse> findMyLoadsV2(
      final int cId,
      final Boolean active,
      final List<Integer> userIds,
      final QueryParams queryParams,
      int skip,
      int limit) {

    Map<String, Object> params = queryParams.buildParamsMap();

    params.put("c_id", cId);
    params.put("active", active);
    params.put("user_ids", userIds);
    params.put("skip", skip);
    params.put("limit", limit);

    return jpaNativeQueryService.query(
        GET_MY_LOADS_QUERY_TEMPLATE,
        params,
        myLoadResponseTransformer);
  }

  @Override
  public LoadTotalResponse findMyLoadsTotalsV2(
      final int cId,
      final Boolean active,
      final List<Integer> userIds,
      QueryParams queryParams) {

    Map<String, Object> params = queryParams.buildParamsMap();
    params.put("c_id", cId);
    params.put("active", active);
    params.put("user_ids", userIds);

    String totalsWrapperQuery = String.format(
        """
            select
              count(*) as count,
              ifnull(sum(number_of_loads), 0) as sum_of_loads
            from (
              %s
            ) as totals;
            """, GET_MY_LOADS_QUERY_TEMPLATE);

    return jpaNativeQueryService.queryForObject(
        totalsWrapperQuery,
        params,
        loadTotalResponseTransformer);
  }

  @Override
  public LoadOfferListResponse findLoadOfferById(
      final int uId,
      final int offerRecipientId) {
    Map<String, Object> params = new HashMap<>();
    params.put("u_id", uId);
    params.put("offer_recipient_id", offerRecipientId);
    params.put("only_offered_loads", "true");

    return jpaNativeQueryService.queryForObject(
        GET_LOADS_QUERY_TEMPLATE,
        params,
        loadOfferListResponseTransformer);
  }

}
