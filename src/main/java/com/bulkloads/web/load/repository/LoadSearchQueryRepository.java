package com.bulkloads.web.load.repository;

import java.util.List;
import com.bulkloads.web.load.service.dto.LoadSearchResponse;

public interface LoadSearchQueryRepository {

  List<LoadSearchResponse> getLoadSearches(
                                           int userId,
                                           Integer siteId,
                                           Integer searchType,
                                           int limit);
}
