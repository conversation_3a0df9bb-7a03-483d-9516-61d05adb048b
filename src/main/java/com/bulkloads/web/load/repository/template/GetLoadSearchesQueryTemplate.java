package com.bulkloads.web.load.repository.template;

public class GetLoadSearchesQueryTemplate {

  public static final String GET_LOAD_SEARCHES_QUERY_TEMPLATE = """
      SELECT
          user_company_ids,
          origin_country,
          origin_state,
          origin_city,
          origin_zip,
          origin_lat,
          origin_long,
          distance as origin_radius,
          destination_country,
          destination_state,
          destination_city,
          destination_zip,
          destination_lat,
          destination_long,
          destination_distance as destination_radius,
          equipment as equipment_ids,
          favorite_loads as only_favorite_loads,
          offered_loads as only_offered_loads,
          favorite_lanes as only_favorite_lanes,
          favorite_companies as only_favorite_companies,
          max(load_search_id) as max_id
      from load_search
      <% params.put("userId", userId) %>
      where user_id = :userId
      <% params.put("siteId", siteId) %>
      and site_id = :siteId
      <% if (paramExistsAdd("searchType")) { %>
          and search_type = :searchType
      <% } %>
      
      group by
             user_company_ids,
          origin_country,
          origin_state,
          origin_city,
          origin_zip,
          origin_lat,
          origin_long,
          distance,
          destination_country,
          destination_state,
          destination_city,
          destination_zip,
          destination_lat,
          destination_long,
          destination_distance,
          equipment,
          favorite_loads,
          offered_loads,
          favorite_lanes,
          favorite_companies
      order by max_id desc
      <% if (paramExistsAdd("limit")) { %>
         LIMIT :limit
          <% } %>
      """;
}
