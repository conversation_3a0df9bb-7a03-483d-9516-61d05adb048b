package com.bulkloads.web.load.repository;


import static com.bulkloads.web.load.repository.template.GetLoadSearchesQueryTemplate.GET_LOAD_SEARCHES_QUERY_TEMPLATE;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.bulkloads.common.jpa.nativejpa.JpaNativeQueryService;
import com.bulkloads.web.load.service.dto.LoadSearchResponse;
import com.bulkloads.web.load.service.dto.transformers.LoadSearchResponseTransformer;
import org.springframework.stereotype.Repository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Repository
@RequiredArgsConstructor
public class LoadSearchQueryRepositoryImpl implements LoadSearchQueryRepository {

  private final JpaNativeQueryService jpaNativeQueryService;
  private final LoadSearchResponseTransformer loadSearchResponseTransformer;


  @Override
  public List<LoadSearchResponse> getLoadSearches(
                                                  int userId,
                                                  Integer siteId,
                                                  Integer searchType,
                                                  int limit
                                                  ) {
    Map<String, Object> queryParams = new HashMap<>();
    queryParams.put("userId", userId);
    queryParams.put("siteId", siteId);
    queryParams.put("searchType", searchType);
    queryParams.put("limit", limit);

    return jpaNativeQueryService.query(GET_LOAD_SEARCHES_QUERY_TEMPLATE, queryParams, loadSearchResponseTransformer);

  }
}
