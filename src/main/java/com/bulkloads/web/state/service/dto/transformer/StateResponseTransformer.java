package com.bulkloads.web.state.service.dto.transformer;

import com.bulkloads.common.jpa.nativejpa.QueryParts;
import com.bulkloads.web.state.service.dto.StateResponse;
import org.hibernate.query.TupleTransformer;
import org.springframework.stereotype.Component;
import lombok.RequiredArgsConstructor;


@Component
@RequiredArgsConstructor
public class StateResponseTransformer implements TupleTransformer<StateResponse> {

  @Override
  public StateResponse transformTuple(Object[] columns, String[] aliases) {
    QueryParts parts = new QueryParts(columns, aliases);
    StateResponse response = new StateResponse();
    response.setState(parts.asString("state"));
    response.setAbbreviation(parts.asString("abbreviation"));
    response.setCountry(parts.asString("country"));
    response.setRegion(parts.asString("region"));
    return response;

  }
}
