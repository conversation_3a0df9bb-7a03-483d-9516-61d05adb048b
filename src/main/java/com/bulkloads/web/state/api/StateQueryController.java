package com.bulkloads.web.state.api;

import java.util.List;
import com.bulkloads.web.state.service.StateService;
import com.bulkloads.web.state.service.dto.StateResponse;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

// sort the endpoint Tags alphabetically

@Slf4j
@RestController
@RequestMapping(path = "/rest/states", produces = MediaType.APPLICATION_JSON_VALUE)
@Tag(name = "States")
@CrossOrigin(origins = "*", maxAge = 3600)
@RequiredArgsConstructor
public class StateQueryController {

  private final StateService stateService;

  @Operation(summary = "Get states")
  @GetMapping
  public List<StateResponse> getStates(
      @Parameter(description = "The search term.")
      @RequestParam(value = "term", required = false) String term
  ) {
    return stateService.getStates(
        term
    );
  }

}
