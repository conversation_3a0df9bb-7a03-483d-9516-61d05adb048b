package com.bulkloads.web.state.repository.template;

public class GetStateQueryTemplate {

  public static final String GET_STATES_QUERY_TEMPLATE = """
          SELECT state, abbreviation, country, type as region
          from states
          inner join regions on states.region = regions.id
          WHERE state_id NOT IN (71,72,73,74,75)

          <% if (paramExists("term")) {
              var wildTerms = term.split("\\s+")
              for (int i = 0; i < wildTerms.length; i++) {
                  var part = "wildTerms_"+i
                  params.put(part, "%"+wildTerms[i]+"%")
            %>
                  AND state LIKE :<% print(part) %>
            <% } %>
          <% } %>
          order by state
      """;

}
