package com.bulkloads.web.offer.domain.entity;

import java.math.BigDecimal;
import java.time.Instant;
import com.bulkloads.web.addressbook.abuser.domain.entity.AbUser;
import com.bulkloads.web.load.domain.entity.Load;
import com.bulkloads.web.rate.domain.entity.RateType;
import com.bulkloads.web.user.domain.entity.User;
import org.hibernate.annotations.DynamicUpdate;
import org.hibernate.annotations.OnDelete;
import org.hibernate.annotations.OnDeleteAction;
import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.Lob;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Entity
@DynamicUpdate
@Table(name = "load_offers")
public class LoadOffer {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "load_offer_id")
  private Integer loadOfferId;

  @NotNull
  @ManyToOne(fetch = FetchType.LAZY, optional = false)
  @JoinColumn(name = "load_id")
  private Load load;

  @Lob
  @Column(name = "message")
  private String message;

  @ManyToOne(fetch = FetchType.LAZY)
  @OnDelete(action = OnDeleteAction.CASCADE)
  @JoinColumn(name = "user_id")
  private User user;

  @ManyToOne(fetch = FetchType.LAZY)
  @OnDelete(action = OnDeleteAction.CASCADE)
  @JoinColumn(name = "ab_user_id")
  private AbUser abUser;

  @Column(name = "offer_rate")
  private BigDecimal offerRate;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "offer_rate_type")
  private RateType offerRateType;

  @Column(name = "offer_rate_per_mile")
  private BigDecimal offerRatePerMile;

  @Convert(disableConversion = true)
  @Column(name = "offer_date")
  private Instant offerDate;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "offer_id")
  private Offer offer;

  @NotNull
  @Lob
  @Column(name = "offer_status")
  private String offerStatus = "Sent";

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "offer_recipient_id")
  private OfferRecipient offerRecipient;

  @NotNull
  @Column(name = "allow_auto_booking")
  private Boolean allowAutoBooking;
}