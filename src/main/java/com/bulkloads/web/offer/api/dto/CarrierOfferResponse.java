package com.bulkloads.web.offer.api.dto;

import lombok.Builder;
import lombok.Value;

@Value
@Builder
public class CarrierOfferResponse {
  private Integer offerRecipientId;
  private Integer numberOfLoadsAccepted;
  private Integer loadId;
  private String originCountry;
  private String originState;
  private String originCity;
  private String originZip;
  private String originLat;
  private String originLong;
  private String destinationCountry;
  private String destinationState;
  private Double currentBouncemiles;
  private Double currentBouncemilesPcmiler;
  private Double currentBounceBearing;
  private String currentBounceBearingDirection;
  private Double originBouncemiles;
  private Double originBouncemilesPcmiler;
  private Double originBounceBearing;
  private String originBounceBearingDirection;
  private String shipFrom;
  private String shipTo;
  private Integer numberOfLoads;
  private String rate;
  private Double avgRatePerMile;
  private Integer isHazmat;
  private String equipmentIds;
  private String equipmentNames;
  private Integer estimatedMiles;
  private Double loadBearing;
  private String loadBearingDirection;
  private String postDate;
  private Integer userId;
  private Integer userCompanyId;
  private Integer isFavoriteLoad;
  private Integer isOfferedLoad;
  private Integer offerId;
  private Double offerRate;
  private String offerRateType;
  private Double offerRatePerMile;
  private String offerDate;
  private String offerMessage;
  private String offerStatus;
  private Integer allowAutoBooking;
  private Integer numberOfAvailableLoads;
  // ... other fields as per your response structure
}