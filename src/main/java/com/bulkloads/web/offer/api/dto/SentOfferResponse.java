package com.bulkloads.web.offer.api.dto;

import java.math.BigDecimal;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Value;

@Value
@Builder
public class SentOfferResponse {
  @JsonProperty("offer_id")
  Integer offerId;

  String message;

  @JsonProperty("offer_recipient_id")
  Integer offerRecipientId;

  @JsonProperty("load_id")
  Integer loadId;

  @JsonProperty("user_id")
  Integer userId;

  @JsonProperty("ab_user_id")
  Integer abUserId;

  @JsonProperty("ab_company_id")
  Integer abCompanyId;

  @JsonProperty("offer_rate")
  BigDecimal offerRate;

  @JsonProperty("offer_rate_type")
  String offerRateType;

  @JsonProperty("offer_rate_per_mile")
  BigDecimal offerRatePerMile;

  @JsonProperty("offer_date")
  String offerDate;

  @JsonProperty("allow_auto_booking")
  Integer allowAutoBooking;

  @JsonProperty("offer_status")
  String offerStatus;

  @JsonProperty("first_name")
  String firstName;

  @JsonProperty("last_name")
  String lastName;

  String email;

  @JsonProperty("phone_1")
  String phone1;

  @JsonProperty("company_name")
  String companyName;

  @JsonProperty("company_logo_url")
  String companyLogoUrl;

  @JsonProperty("company_logo_thumb_url")
  String companyLogoThumbUrl;

  @JsonProperty("number_of_loads_accepted")
  Integer numberOfLoadsAccepted;

  @JsonProperty("accepted_date")
  String acceptedDate;

  @JsonProperty("dismissed_date")
  String dismissedDate;

  @JsonProperty("booked_date")
  String bookedDate;

  @JsonProperty("rejected_date")
  String rejectedDate;
}