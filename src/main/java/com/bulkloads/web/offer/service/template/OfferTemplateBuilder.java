package com.bulkloads.web.offer.service.template;

import java.time.format.DateTimeFormatter;
import java.util.Map;
import com.bulkloads.config.AppConstants;
import com.bulkloads.web.infra.template.TemplateService;
import com.bulkloads.web.offer.domain.template.OfferTemplateModel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class OfferTemplateBuilder {

  private static final DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("MMM d");

  @Autowired
  protected TemplateService templateService;


  public String getEmailTitle(final OfferTemplateModel model) {
    return "Load Offer " + model.getPickupCity() + ", " + model.getPickupState() + " to " + model.getDropCity() + ", " + model.getDropState() + " "
           + model.getRateMessage();
  }

  public String getEmailContent(final OfferTemplateModel model) {
    return templateService.processFromTemplateFile(AppConstants.Templates.OFFER_CREATED, model);
  }

  public String getSmsContent(final OfferTemplateModel model) {
    final StringBuilder smsContent = new StringBuilder();
// Phone:  Email: <EMAIL>
    smsContent.append("[DO NOT REPLY] From: ")
        .append(model.getFromFirstName()).append(" ")
        .append(model.getFromLastName()).append(" - ")
        .append(model.getFromCompanyName()).append(" ")
        .append("Phone: ")
        .append(model.getFromPhone()).append(" ")
        .append("Email: ")
        .append(model.getFromEmail());

    if (model.getMessage() != null && !model.getMessage().isEmpty()) {
      smsContent.append(" ").append(model.getMessage());
    }

    smsContent.append(" ")
        .append(model.getPickupCity()).append(", ")
        .append(model.getPickupState()).append(" to ")
        .append(model.getDropCity()).append(", ")
        .append(model.getDropState()).append(" ")
        .append(model.getPickupCompanyName()).append(" - ")
        .append(model.getDropCompanyName()).append(" ")
        .append(model.getPickupDropMiles()).append(" Miles ")
        .append(model.getNumberOfAvailableLoads()).append(" Loads Available ");

    if (model.getShipFrom() != null) {
      smsContent.append("Loading ").append(
          model.getShipFrom().format(dateFormatter));
      if (model.getShipTo() != null) {
        smsContent.append(" to ").append(model.getShipTo().format(dateFormatter));
      }
    }
    if (model.getLoCommodity() != null && !model.getLoCommodity().isEmpty()) {
      smsContent.append(" ").append(model.getLoCommodity());
    }
    smsContent.append(" ").append(model.getRateMessage())
        .append(" Call ").append(model.getFromFirstName())
        .append(" at ");
    if (model.getFromCellPhone() != null && !model.getFromCellPhone().isEmpty()) {
      smsContent.append(model.getFromCellPhone());
    } else {
      smsContent.append(model.getFromPhone());
    }
    smsContent.append(" to book. View details for this offer: ")
        .append(model.getLink().getDynamicLink());

    return smsContent.toString();
  }

  public String getNotificationTitle(final OfferTemplateModel model) {
    return "New Load Offer";
  }

  public String getNotificationContent(final OfferTemplateModel model) {
    return model.getFromFirstName() + " " + model.getFromLastName() + " sent a Load Offer: " + model.getPickupCity() + ", " + model.getPickupState() + " to "
           + model.getDropCity() + ", " + model.getDropState() + " " + model.getRateMessage();
  }

  public Map<String, Object> getNotificationData(final OfferTemplateModel model) {
    return Map.of(
        "notification_type", "Load Offers",
        "offer_status", "Sent",
        "load_id", model.getLoadId(),
        "offer_id", model.getOfferId(),
        "offer_recipient_id", model.getOfferRecipientId()
    );
  }

}
