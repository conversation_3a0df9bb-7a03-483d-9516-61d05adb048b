package com.bulkloads.web.offer.repository.projection;

import java.math.BigDecimal;
import java.time.LocalDateTime;

public interface SentOfferProjection {
  Integer getOfferId();

  String getMessage();

  Integer getOfferRecipientId();

  Integer getLoadId();

  Integer getUserId();

  Integer getAbUserId();

  Integer getAbCompanyId();

  BigDecimal getOfferRate();

  String getOfferRateType();

  BigDecimal getOfferRatePerMile();

  LocalDateTime getOfferDate();

  Byte getAllowAutoBooking();

  String getOfferStatus();

  String getFirstName();

  String getLastName();

  String getEmail();

  String getPhone1();

  String getCompanyName();

  String getCompanyLogoUrl();

  String getCompanyLogoThumbUrl();

  Integer getNumberOfLoadsAccepted();

  LocalDateTime getAcceptedDate();

  LocalDateTime getDismissedDate();

  LocalDateTime getBookedDate();

  LocalDateTime getRejectedDate();
}