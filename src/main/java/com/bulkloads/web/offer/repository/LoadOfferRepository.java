package com.bulkloads.web.offer.repository;

import java.math.BigDecimal;
import java.util.List;
import com.bulkloads.web.offer.domain.entity.LoadOffer;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

@Repository
public interface LoadOfferRepository extends JpaRepository<LoadOffer, Integer> {

  @Query("""
      SELECT lo FROM LoadOffer lo
        JOIN OfferRecipient orec on lo.offerRecipient.offerRecipientId = orec.offerRecipientId
      WHERE lo.load.loadId = :loadId
        AND ((:numberOfAvailableLoads > 0 AND lo.offerStatus IN ('Sent', 'Dismissed')
        AND :numberOfAvailableLoads - COALESCE(orec.alreadyAcceptedLoads, 0) <= 0)
          OR (:numberOfAvailableLoads <= 0 AND lo.offerStatus IN ('Sent', 'Dismissed', 'Accepted')))
      ORDER BY lo.user.userId, lo.loadOfferId
      """)
  List<LoadOffer> findUnavailableLoadOffers(@Param("loadId") final int loadId,
                                            @Param("numberOfAvailableLoads") final int numberOfAvailableLoads);

  @Query("""
      SELECT lo
      FROM LoadOffer lo
        JOIN OfferRecipient orec on lo.offerRecipient.offerRecipientId = orec.offerRecipientId
      WHERE lo.load.loadId = :loadId
      AND lo.offerStatus IN ('Sent', 'Dismissed')
      AND (
          :abUserId IS NOT NULL AND lo.abUser.abUserId = :abUserId
          OR
          :userId IS NOT NULL AND lo.user.userId = :userId
      )
      """)
  List<LoadOffer> findRecipientLoadOffers(@Param("loadId") final Integer loadId,
                                          @Param("abUserId") final Integer abUserId,
                                          @Param("userId") final Integer userId);

  List<LoadOffer> findByOfferRecipient_OfferRecipientIdAndOfferStatusIn(Integer offerRecipientId, List<String> statuses);

  @Transactional
  @Modifying
  @Query("""
      UPDATE LoadOffer lo 
      SET lo.offerStatus = 'Deleted'
      WHERE lo.loadOfferId IN :loadOfferIds
      """)
  int updateLoadOffersToDeleted(@Param("loadOfferIds") final List<Integer> loadOfferIds);

  @Query("""
      SELECT lo
      FROM LoadOffer lo
        JOIN OfferRecipient orec on lo.offerRecipient.offerRecipientId = orec.offerRecipientId
        JOIN Offer o on orec.offer.offerId = o.offerId
        JOIN User u on o.user.userId = u.userId
      WHERE orec.offerRecipientId = :offerRecipientId
      AND u.userCompany.userCompanyId = :userCompanyId
      AND orec.offerStatus = 'Accepted'
      """)
  LoadOffer findAcceptedLoadOffer(@Param("offerRecipientId") final Integer offerRecipientId,
                                  @Param("userCompanyId") final Integer userCompanyId);

  @Transactional
  @Modifying
  @Query("""
      UPDATE LoadOffer lo
      SET lo.offerStatus = 'Booked'
      WHERE lo.loadOfferId = :loadOfferId
      """)
  void updateLoadOffersToBooked(@Param("loadOfferId") final int loadOfferId);

  @Query("""
      SELECT lo
      FROM LoadOffer lo
      WHERE lo.load.loadId = :loadId
        AND lo.offerStatus IN ('sent', 'dismissed')
        AND lo.allowAutoBooking = :allowAutoBooking
        AND (:offerRate IS NULL and lo.offerRate IS NULL OR lo.offerRate = :offerRate)
        AND lo.offerRateType.rateType = :offerRateType
        AND ((:abUserId IS NOT NULL AND lo.abUser.abUserId = :abUserId)
            OR (:blUserId IS NOT NULL AND lo.user.userId = :blUserId))
      """)
  List<LoadOffer> findExistingOffers(
      @Param("loadId") Integer loadId,
      @Param("allowAutoBooking") Boolean allowAutoBooking,
      @Param("offerRate") BigDecimal offerRate,
      @Param("offerRateType") String offerRateType,
      @Param("abUserId") Integer abUserId,
      @Param("blUserId") Integer blUserId
  );

  @Query(value = """
      SELECT ifnull(SUM(ore.number_of_loads_accepted), 0) AS total_accepted_loads
      FROM offer_recipients ore
      INNER JOIN load_offers lo ON ore.offer_recipient_id = lo.offer_recipient_id
      WHERE lo.load_id = :loadId
        AND lo.offer_status = 'Accepted'
        AND (
              (:abUserId IS NOT NULL AND lo.ab_user_id = :abUserId)
              OR (:blUserId IS NOT NULL AND lo.user_id = :blUserId)
        )
      """, nativeQuery = true)
  Integer getAcceptedLoadsSum(@Param("loadId") Integer loadId,
                              @Param("abUserId") Integer abUserId,
                              @Param("blUserId") Integer blUserId);

  @Query("""
          SELECT lo
          FROM LoadOffer lo
          WHERE lo.load.loadId = :loadId
            AND lo.offerStatus IN ('Sent', 'Dismissed')
            AND (
              (:abUserId IS NOT NULL AND lo.abUser.abUserId = :abUserId)
              OR (:blUserId IS NOT NULL AND lo.user.userId = :blUserId)
            )
      """)
  List<LoadOffer> getSentAndDismissedLoadOffers(@Param("loadId") Integer loadId,
                                                @Param("abUserId") Integer abUserId,
                                                @Param("blUserId") Integer blUserId);

  @Query("""
      SELECT lo
      FROM LoadOffer lo
      WHERE lo.load.loadId = :loadId
        AND lo.offerStatus IN ('Sent', 'Dismissed')
        AND ((:abUserId IS NOT NULL AND lo.abUser.abUserId = :abUserId)
            OR (:userId IS NOT NULL AND lo.user.userId = :userId))
      """)
  List<LoadOffer> findLoadOffersForDeletion(
      @Param("loadId") Integer loadId,
      @Param("abUserId") Integer abUserId,
      @Param("userId") Integer userId
  );

  @Query("""
      SELECT lo
      FROM LoadOffer lo
      WHERE lo.offerRecipient.offerRecipientId = :offerRecipientId
        AND lo.offerStatus IN ('Sent', 'Dismissed')
      """)
  List<LoadOffer> findLoadOffersByRecipientId(@Param("offerRecipientId") Integer offerRecipientId);

  @Query("""
      SELECT lo
      FROM LoadOffer lo
      JOIN lo.offerRecipient orec
      JOIN lo.offer o
      JOIN o.user u
      WHERE orec.offerStatus IN ('Sent', 'Accepted', 'Dismissed')
      AND u.userCompany.userCompanyId = :userCompanyId
      AND (:loadId IS NULL OR lo.load.loadId = :loadId)
      AND (:offerRecipientId IS NULL OR orec.offerRecipientId = :offerRecipientId)
      ORDER BY lo.user.userId, lo.loadOfferId
      """)
  List<LoadOffer> findOffersForDeletion(
      @Param("userCompanyId") Integer userCompanyId,
      @Param("loadId") Integer loadId,
      @Param("offerRecipientId") Integer offerRecipientId
  );

  @Query("""
      SELECT lo FROM LoadOffer lo
      JOIN FETCH lo.offerRecipient orec
      JOIN FETCH lo.load l
      JOIN FETCH lo.offer o
      WHERE o.offerId = :offerId
      AND orec.offerStatus IN :statuses
      AND (
          (orec.blUser.userId = :userId) OR
          (orec.abUser IS NOT NULL AND orec.abUser.abUserId IN 
              (SELECT abu.abUserId FROM AbUser abu WHERE abu.user.userId = :userId))
      )
      ORDER BY lo.loadOfferId
      """)
  List<LoadOffer> findAvailableOffersByOfferId(
      @Param("offerId") Integer offerId,
      @Param("userId") Integer userId,
      @Param("statuses") List<String> statuses
  );

}
