package com.bulkloads.web.user.mapper;

import com.bulkloads.web.contracts.mapper.CommonMapper;
import com.bulkloads.web.user.api.dto.UserHosResponse;
import com.bulkloads.web.user.domain.entity.UserHosHistory;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;

@Mapper(componentModel = "spring",
    nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
    nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
    unmappedTargetPolicy = org.mapstruct.ReportingPolicy.IGNORE,
    uses = CommonMapper.class)
public abstract class UserMapper {

  public abstract UserHosResponse map(final UserHosHistory userHosHistory);
}
