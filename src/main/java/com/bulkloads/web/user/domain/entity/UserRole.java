package com.bulkloads.web.user.domain.entity;

import java.util.ArrayList;
import java.util.List;
import com.bulkloads.common.jpa.CsvListSize;
import com.bulkloads.web.common.jpa.converter.CsvIntegerListConverter;
import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.JoinTable;
import jakarta.persistence.ManyToMany;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "user_roles")
@Getter
@Setter
public class UserRole {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "user_role_id")
  private Integer userRoleId;

  @Column(name = "user_role")
  private String userRole = "";

  @CsvListSize(max = 45)
  @Convert(converter = CsvIntegerListConverter.class)
  @Column(name = "user_type_ids")
  private List<Integer> userTypeIds = new ArrayList<>();

  @Column(name = "description")
  private String description = "";

  @ManyToMany(fetch = FetchType.LAZY)
  @JoinTable(name = "user_role_permissions",
      joinColumns = @JoinColumn(name = "user_role_id"),
      inverseJoinColumns = @JoinColumn(name = "user_permission_id"))
  private List<UserPermission> permissions;
}
