package com.bulkloads.web.user.model;

import java.util.List;
import com.bulkloads.web.user.domain.entity.UserPermission;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface UserPermissionRepository extends JpaRepository<UserPermission, Long> {

  @Query(value = """
      select up.* from user_info ui
      join user_roles_ref urr on ui.user_id = urr.user_id
      join user_roles ur on ur.user_role_id = urr.user_role_id
      join user_role_permissions urp on ur.user_role_id = urp.user_role_id
      join user_permissions up on urp.user_permission_id = up.user_permission_id
      where ui.user_id = :userId
      """, nativeQuery = true)
  List<UserPermission> findAllByUserId(final int userId);
}
