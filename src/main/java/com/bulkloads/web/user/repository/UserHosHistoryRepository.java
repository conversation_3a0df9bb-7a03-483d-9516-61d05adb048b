package com.bulkloads.web.user.repository;

import java.util.List;
import com.bulkloads.web.user.domain.entity.UserHosHistory;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface UserHosHistoryRepository extends JpaRepository<UserHosHistory, Integer> {

  @Query("""
      select hh from UserHosHistory hh
      where hh.id in (
          select max(h.id) from UserHosHistory h
          where h.userId in :userIds
          group by h.userId
      )
      order by hh.createdOn desc
      """)
  List<UserHosHistory> findLatestHosByUserIds(@Param("userIds") final List<Integer> userIds);
}
