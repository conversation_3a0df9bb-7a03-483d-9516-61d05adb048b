package com.bulkloads.web.user.repository;

import com.bulkloads.common.jpa.nativejpa.QueryParts;
import com.bulkloads.web.user.api.dto.UserAutoResponse;
import org.hibernate.query.TupleTransformer;
import org.springframework.stereotype.Component;
import lombok.RequiredArgsConstructor;

@Component
@RequiredArgsConstructor
public class UserAutoResponseTransformer implements TupleTransformer<UserAutoResponse> {

  @Override
  public UserAutoResponse transformTuple(Object[] columns, String[] aliases) {
    QueryParts parts = new QueryParts(columns, aliases);

    return new UserAutoResponse(
        parts.asInteger("user_id"),
        parts.asString("value"),
        parts.asString("first_name"),
        parts.asString("last_name"),
        parts.asString("company_name"),
        parts.asDouble("score")
    );
  }
}