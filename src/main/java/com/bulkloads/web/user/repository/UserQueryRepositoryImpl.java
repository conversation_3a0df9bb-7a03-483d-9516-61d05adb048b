package com.bulkloads.web.user.repository;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import com.bulkloads.common.jpa.nativejpa.JpaNativeQueryService;
import org.springframework.stereotype.Repository;
import lombok.RequiredArgsConstructor;

@Repository
@RequiredArgsConstructor
public class UserQueryRepositoryImpl implements UserQueryRepository {

  private final JpaNativeQueryService jpaNativeQueryService;

  public Optional<Integer> fetchByUserIdAndByEmailOrPhone(Integer userId, String email, String phoneNumber) {

    Map<String, Object> params = new HashMap<>();
    params.put("userId", userId);
    params.put("email", email);
    params.put("phoneNumber", phoneNumber);

    String sql = """
        <% params.put("userId", userId) %>
        <% params.put("email", email) %>
        <% params.put("phoneNumber", phoneNumber) %>
        
        select u.user_id
        from user_info u
          inner join bl_user_settings s using(user_id)
        where u.user_id = :userId
        and s.deletion_date is NULL
        and (
          ( email = :email and email_verified = 1 )
          OR
          ( REGEXP_REPLACE(cell_phone, '[^0-9]+', '') = :phoneNumber and cell_phone_verified = 1 )
        )
        """;
    return Optional.ofNullable(jpaNativeQueryService.queryForObject(sql, params, (columns, aliases) -> (int) columns[0]));

  }
}
