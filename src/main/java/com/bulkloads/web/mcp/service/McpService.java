package com.bulkloads.web.mcp.service;

import static java.time.Instant.now;
import java.time.Instant;
import com.bulkloads.exception.BulkloadsException;
import com.bulkloads.exception.UnauthorizedActionException;
import com.bulkloads.web.mcp.ApiMcpClient;
import com.bulkloads.web.mcp.domain.entity.UserCompanyMcpIntegration;
import com.bulkloads.web.mcp.dto.TokenResponse;
import com.bulkloads.web.mcp.ext.CancelMonitoringOutput;
import com.bulkloads.web.mcp.ext.CarrierDto;
import com.bulkloads.web.mcp.ext.RequestMonitoringOutput;
import com.bulkloads.web.mcp.mapper.McpMapper;
import com.bulkloads.web.mcp.repository.UserCompanyMcpIntegrationRepository;
import com.bulkloads.web.mcp.service.dto.McpAbCompanyResponse;
import com.bulkloads.web.usercompany.service.UserCompanyService;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RequiredArgsConstructor
public class McpService {

  final UserCompanyMcpIntegrationRepository userCompanyMcpIntegrationRepository;
  final UserCompanyService userCompanyService;
  final ApiMcpClient apiMcpClient;
  final McpMapper mapper;

  private UserCompanyMcpIntegration getAccessToken(int userCompanyId) {

    UserCompanyMcpIntegration mcpInt = userCompanyMcpIntegrationRepository
        .findById(userCompanyId)
        .orElseThrow(UnauthorizedActionException::new);

    if (mcpInt.isValid()) {
      return mcpInt;
    }

    try {
      Instant now = now();
      TokenResponse tokenResponse = apiMcpClient.getToken(mcpInt.getMcpUsername(), mcpInt.getMcpPassword());
      mcpInt.setAccessToken(tokenResponse.getToken());
      mcpInt.setExpires(now.plusSeconds(tokenResponse.getExpiresIn()));
      mcpInt.setIssued(now);
      userCompanyMcpIntegrationRepository.save(mcpInt);
      return mcpInt;
    } catch (Exception e) {
      throw new BulkloadsException("Could not get MCP access token (userCompanyId=" + userCompanyId + ")", e);
    }
  }

  public void requestMonitoring(int userCompanyId, String censusNum, String mcNum) {
    UserCompanyMcpIntegration mcpInt = getAccessToken(userCompanyId);
    RequestMonitoringOutput res = apiMcpClient.requestMonitoring(mcpInt.getAccessToken(), censusNum, mcNum);
    log.info("Monitoring requested for userCompanyId={} censusNum={}, mcNum={} monitoringID={}",
        userCompanyId, censusNum, mcNum, res.getMonitoringID());
  }

  public void cancelMonitoring(int userCompanyId, String censusNum, String mcNum) {
    UserCompanyMcpIntegration mcpInt = getAccessToken(userCompanyId);
    CancelMonitoringOutput res = apiMcpClient.cancelMonitoring(mcpInt.getAccessToken(), censusNum, mcNum);
    log.info("Monitoring canceled for userCompanyId={}, censusNum={}, mcNum={}, monitoringID={}",
        userCompanyId, censusNum, mcNum, res != null ? res.getMonitoringID() : "null");
  }

  public McpAbCompanyResponse getCarrierInfo(int userCompanyId, String censusNum, String mcNum) {
    UserCompanyMcpIntegration mcpInt = getAccessToken(userCompanyId);
    CarrierDto carrierData = apiMcpClient.getCarrierData(mcpInt.getAccessToken(), censusNum, mcNum);
    return mapper.carrierDtoToMcpLookupResponse(carrierData);
  }

}
