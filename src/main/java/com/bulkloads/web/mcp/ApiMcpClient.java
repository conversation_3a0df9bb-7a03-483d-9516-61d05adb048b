package com.bulkloads.web.mcp;

import java.time.Instant;
import java.util.List;
import com.bulkloads.web.mcp.dto.TokenResponse;
import com.bulkloads.web.mcp.ext.CancelMonitoringOutput;
import com.bulkloads.web.mcp.ext.CarrierChangesResultDto;
import com.bulkloads.web.mcp.ext.CarrierDto;
import com.bulkloads.web.mcp.ext.CompletedPacketDto;
import com.bulkloads.web.mcp.ext.RequestMonitoringOutput;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.MediaType;
import org.springframework.http.codec.json.Jackson2JsonDecoder;
import org.springframework.http.codec.json.Jackson2JsonEncoder;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.ExchangeFilterFunction;
import org.springframework.web.reactive.function.client.ExchangeStrategies;
import org.springframework.web.reactive.function.client.WebClient;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Slf4j
@Component
public class ApiMcpClient {

  private static final String MCP_DOMAIN = "https://api.mycarrierpackets.com";
  private static final String MCP_TOKEN_URL = "/token";
  private static final String MCP_BASE_URL = "/api/v1/carrier";
  private static final String MCP_COMPLETED_PACKETS_URL = MCP_BASE_URL + "/completedpackets";
  private static final String MCP_CARRIER_CHANGES_URL = MCP_BASE_URL + "/carrierschanges";
  private static final String MCP_GET_CARRIER_DATA_URL = MCP_BASE_URL + "/getcarrierdata";

  private static final String MCP_GET_MONITORED_CARRIERS_URL = MCP_BASE_URL + "/monitoredcarriers";
  private static final String MCP_REQUEST_MONITORING_URL = MCP_BASE_URL + "/requestmonitoring";
  private static final String MCP_CANCEL_MONITORING_URL = MCP_BASE_URL + "/cancelmonitoring";

  private final WebClient webClient;

  public ApiMcpClient(WebClient.Builder webClientBuilder) {

    ObjectMapper customObjectMapper = new ObjectMapper();

    ExchangeStrategies exchangeStrategies = ExchangeStrategies
        .builder()
        .codecs(configurer -> {
          configurer.defaultCodecs().jackson2JsonEncoder(new Jackson2JsonEncoder(customObjectMapper));
          configurer.defaultCodecs().jackson2JsonDecoder(new Jackson2JsonDecoder(customObjectMapper));
        })
        .build();

    this.webClient = webClientBuilder
        .baseUrl(MCP_DOMAIN)
        .filter(ApiMcpClient.logRequest())
        .exchangeStrategies(exchangeStrategies)
        .codecs(clientCodecConfigurer -> clientCodecConfigurer.defaultCodecs().maxInMemorySize(32 * 1024 * 1024))
        .build();
  }

  public TokenResponse getToken(String username, String password) {

    return this.webClient
        .post()
        .uri(MCP_TOKEN_URL)
        .contentType(MediaType.APPLICATION_FORM_URLENCODED)
        .body(BodyInserters
            .fromFormData("grant_type", "password")
            .with("username", username)
            .with("password", password))
        .retrieve()
        .bodyToMono(TokenResponse.class)
        .block();
  }

  public List<CompletedPacketDto> getCompletedPackets(
      String accessToken,
      Instant fromDate,
      Instant toDate) {

    ParameterizedTypeReference<List<CompletedPacketDto>> responseType = new ParameterizedTypeReference<>() {
    };

    return this.webClient
        .post()
        .uri(MCP_COMPLETED_PACKETS_URL, uriBuilder -> uriBuilder
            .queryParam("fromDate", fromDate)
            .queryParam("toDate", toDate)
            .build())
        .contentType(MediaType.APPLICATION_FORM_URLENCODED)
        .header("Authorization", "Bearer " + accessToken)
        .retrieve()
        .bodyToMono(responseType)
        .block();
  }

  public CarrierChangesResultDto getCarrierChanges(
      String accessToken,
      Instant fromDate,
      Instant toDate) {

    ParameterizedTypeReference<CarrierChangesResultDto> responseType = new ParameterizedTypeReference<>() {
    };

    return this.webClient
        .post()
        .uri(MCP_CARRIER_CHANGES_URL, uriBuilder -> uriBuilder
            .queryParam("fromDate", fromDate)
            .queryParam("toDate", toDate)
            .build())
        .contentType(MediaType.APPLICATION_FORM_URLENCODED)
        .header("Authorization", "Bearer " + accessToken)
        .retrieve()
        .bodyToMono(responseType)
        .block();
  }

  public CarrierDto getCarrierData(String accessToken, String dotNumber, String docketNumber) {

    ParameterizedTypeReference<CarrierDto> responseType = new ParameterizedTypeReference<>() {
    };

    return this.webClient
        .post()
        .uri(MCP_GET_CARRIER_DATA_URL, uriBuilder -> uriBuilder
            .queryParam("DOTNumber", dotNumber)
            .queryParam("Docket", docketNumber)
            .build())
        .contentType(MediaType.APPLICATION_FORM_URLENCODED)
        .header("Authorization", "Bearer " + accessToken)
        .retrieve()
        .bodyToMono(responseType)
        .block();
  }

  public RequestMonitoringOutput requestMonitoring(String accessToken, String dotNumber, String docketNumber) {

    ParameterizedTypeReference<RequestMonitoringOutput> responseType = new ParameterizedTypeReference<>() {
    };

    return this.webClient
        .post()
        .uri(MCP_REQUEST_MONITORING_URL, uriBuilder -> uriBuilder
            .queryParam("DOTNumber", dotNumber)
            .queryParam("Docket", docketNumber)
            .build())
        .contentType(MediaType.APPLICATION_FORM_URLENCODED)
        .header("Authorization", "Bearer " + accessToken)
        .retrieve()
        .onStatus(status -> status.value() == 400, clientResponse -> {
          return clientResponse.bodyToMono(String.class)
              .doOnNext(body -> System.out.println("Error 400: " + body))
              .then(clientResponse.createException());
        })
        .bodyToMono(responseType)
        .block();
  }

  public CancelMonitoringOutput cancelMonitoring(String accessToken, String dotNumber, String docketNumber) {

    ParameterizedTypeReference<CancelMonitoringOutput> responseType = new ParameterizedTypeReference<>() {
    };

    return this.webClient
        .post()
        .uri(MCP_CANCEL_MONITORING_URL, uriBuilder -> uriBuilder
            .queryParam("DOTNumber", dotNumber)
            .queryParam("Docket", docketNumber)
            .build())
        .contentType(MediaType.APPLICATION_FORM_URLENCODED)
        .header("Authorization", "Bearer " + accessToken)
        .retrieve()
        .bodyToMono(responseType)
        .onErrorResume(e -> {
          // The carrier is already canceled, log the error and continue
          log.error("MCP Cancel monitoring error occurred: ", e);
          return Mono.empty();
        })
        .block();
  }

  static ExchangeFilterFunction logRequest() {
    return ExchangeFilterFunction.ofRequestProcessor(clientRequest -> {
      log.debug("Request: {}  {}", clientRequest.method(), clientRequest.url());
      return Mono.just(clientRequest);
    });
  }

}