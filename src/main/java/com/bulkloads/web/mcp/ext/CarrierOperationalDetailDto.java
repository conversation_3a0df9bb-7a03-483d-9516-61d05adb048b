package com.bulkloads.web.mcp.ext;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

/**
 * CarrierOperationalDetailDto
 */
@JsonPropertyOrder({CarrierOperationalDetailDto.JSON_PROPERTY_FLEET_SIZE, CarrierOperationalDetailDto.JSON_PROPERTY_TOTAL_POWER_UNITS,
    CarrierOperationalDetailDto.JSON_PROPERTY_NUMBER_OF_VEHICLES, CarrierOperationalDetailDto.JSON_PROPERTY_REEFER_EQUIPMENT,
    CarrierOperationalDetailDto.JSON_PROPERTY_VAN_EQUIPMENT, CarrierOperationalDetailDto.JSON_PROPERTY_FLATBED_STEP_DECK_EQUIPMENT,
    CarrierOperationalDetailDto.JSO<PERSON>_PROPERTY_OWNED_TRACTORS, CarrierOperationalDetailDto.JSON_PROPERTY_OWNED_TRUCKS,
    CarrierOperationalDetailDto.JSON_PROPERTY_OWNED_TRAILERS, CarrierOperationalDetailDto.JSON_PROPERTY_TERM_LEASED_TRACTORS,
    CarrierOperationalDetailDto.JSON_PROPERTY_TERM_LEASED_TRUCKS, CarrierOperationalDetailDto.JSON_PROPERTY_TERM_LEASED_TRAILERS,
    CarrierOperationalDetailDto.JSON_PROPERTY_TRIP_LEASED_TRACTORS, CarrierOperationalDetailDto.JSON_PROPERTY_TRIP_LEASED_TRUCKS,
    CarrierOperationalDetailDto.JSON_PROPERTY_TRIP_LEASED_TRAILERS, CarrierOperationalDetailDto.JSON_PROPERTY_INTERSTATE_AND_INTRASTATE_DRIVERS,
    CarrierOperationalDetailDto.JSON_PROPERTY_CD_L_EMPLOYED_DRIVERS, CarrierOperationalDetailDto.JSON_PROPERTY_MONTHLY_AVERAGE_LEASED_DRIVERS,
    CarrierOperationalDetailDto.JSON_PROPERTY_INTERSTATE_DRIVERS_TOTAL, CarrierOperationalDetailDto.JSON_PROPERTY_INTERSTATE_DRIVERS_G_T100_MILES,
    CarrierOperationalDetailDto.JSON_PROPERTY_INTERSTATE_DRIVERS_L_T100_MILES, CarrierOperationalDetailDto.JSON_PROPERTY_INTRASTATE_DRIVERS_TOTAL,
    CarrierOperationalDetailDto.JSON_PROPERTY_INTRASTATE_DRIVERS_G_T100_MILES, CarrierOperationalDetailDto.JSON_PROPERTY_INTRASTATE_DRIVERS_L_T100_MILES,
    CarrierOperationalDetailDto.JSON_PROPERTY_POWER_ONLY, CarrierOperationalDetailDto.JSON_PROPERTY_SATELLITE_EQUIPMENT,
    CarrierOperationalDetailDto.JSON_PROPERTY_TEAM_DRIVERS, CarrierOperationalDetailDto.JSON_PROPERTY_DROP_TRAILER,
    CarrierOperationalDetailDto.JSON_PROPERTY_EL_D_COMPLIANT, CarrierOperationalDetailDto.JSON_PROPERTY_EL_D_COMPLIANT_BY,
    CarrierOperationalDetailDto.JSON_PROPERTY_EL_D_IDENTIFIER, CarrierOperationalDetailDto.JSON_PROPERTY_NUMBER_OF_TRACTORS,
    CarrierOperationalDetailDto.JSON_PROPERTY_NUMBER_OF_VANS, CarrierOperationalDetailDto.JSON_PROPERTY_NUMBER_OF_REEFERS,
    CarrierOperationalDetailDto.JSON_PROPERTY_NUMBER_OF_FLATS, CarrierOperationalDetailDto.JSON_PROPERTY_NUMBER_OF_STEP_DECKS,
    CarrierOperationalDetailDto.JSON_PROPERTY_NUMBER_OF_TANKS})

public class CarrierOperationalDetailDto {

  public static final String JSON_PROPERTY_FLEET_SIZE = "FleetSize";
  public static final String JSON_PROPERTY_TOTAL_POWER_UNITS = "TotalPowerUnits";
  public static final String JSON_PROPERTY_NUMBER_OF_VEHICLES = "NumberOfVehicles";
  public static final String JSON_PROPERTY_REEFER_EQUIPMENT = "ReeferEquipment";
  public static final String JSON_PROPERTY_VAN_EQUIPMENT = "VanEquipment";
  public static final String JSON_PROPERTY_FLATBED_STEP_DECK_EQUIPMENT = "FlatbedStepDeckEquipment";
  public static final String JSON_PROPERTY_OWNED_TRACTORS = "OwnedTractors";
  public static final String JSON_PROPERTY_OWNED_TRUCKS = "OwnedTrucks";
  public static final String JSON_PROPERTY_OWNED_TRAILERS = "OwnedTrailers";
  public static final String JSON_PROPERTY_TERM_LEASED_TRACTORS = "TermLeasedTractors";
  public static final String JSON_PROPERTY_TERM_LEASED_TRUCKS = "TermLeasedTrucks";
  public static final String JSON_PROPERTY_TERM_LEASED_TRAILERS = "TermLeasedTrailers";
  public static final String JSON_PROPERTY_TRIP_LEASED_TRACTORS = "TripLeasedTractors";
  public static final String JSON_PROPERTY_TRIP_LEASED_TRUCKS = "TripLeasedTrucks";
  public static final String JSON_PROPERTY_TRIP_LEASED_TRAILERS = "TripLeasedTrailers";
  public static final String JSON_PROPERTY_INTERSTATE_AND_INTRASTATE_DRIVERS = "InterstateAndIntrastateDrivers";
  public static final String JSON_PROPERTY_CD_L_EMPLOYED_DRIVERS = "CDLEmployedDrivers";
  public static final String JSON_PROPERTY_MONTHLY_AVERAGE_LEASED_DRIVERS = "MonthlyAverageLeasedDrivers";
  public static final String JSON_PROPERTY_INTERSTATE_DRIVERS_TOTAL = "InterstateDriversTotal";
  public static final String JSON_PROPERTY_INTERSTATE_DRIVERS_G_T100_MILES = "InterstateDriversGT100Miles";
  public static final String JSON_PROPERTY_INTERSTATE_DRIVERS_L_T100_MILES = "InterstateDriversLT100Miles";
  public static final String JSON_PROPERTY_INTRASTATE_DRIVERS_TOTAL = "IntrastateDriversTotal";
  public static final String JSON_PROPERTY_INTRASTATE_DRIVERS_G_T100_MILES = "IntrastateDriversGT100Miles";
  public static final String JSON_PROPERTY_INTRASTATE_DRIVERS_L_T100_MILES = "IntrastateDriversLT100Miles";
  public static final String JSON_PROPERTY_POWER_ONLY = "PowerOnly";
  public static final String JSON_PROPERTY_SATELLITE_EQUIPMENT = "SatelliteEquipment";
  public static final String JSON_PROPERTY_TEAM_DRIVERS = "TeamDrivers";
  public static final String JSON_PROPERTY_DROP_TRAILER = "DropTrailer";
  public static final String JSON_PROPERTY_EL_D_COMPLIANT = "ELDCompliant";
  public static final String JSON_PROPERTY_EL_D_COMPLIANT_BY = "ELDCompliantBy";
  public static final String JSON_PROPERTY_EL_D_IDENTIFIER = "ELDIdentifier";
  public static final String JSON_PROPERTY_NUMBER_OF_TRACTORS = "NumberOfTractors";
  public static final String JSON_PROPERTY_NUMBER_OF_VANS = "NumberOfVans";
  public static final String JSON_PROPERTY_NUMBER_OF_REEFERS = "NumberOfReefers";
  public static final String JSON_PROPERTY_NUMBER_OF_FLATS = "NumberOfFlats";
  public static final String JSON_PROPERTY_NUMBER_OF_STEP_DECKS = "NumberOfStepDecks";
  public static final String JSON_PROPERTY_NUMBER_OF_TANKS = "NumberOfTanks";
  private Integer fleetSize;
  private Integer totalPowerUnits;
  private Integer numberOfVehicles;
  private Boolean reeferEquipment;
  private Boolean vanEquipment;
  private Boolean flatbedStepDeckEquipment;
  private Integer ownedTractors;
  private Integer ownedTrucks;
  private Integer ownedTrailers;
  private Integer termLeasedTractors;
  private Integer termLeasedTrucks;
  private Integer termLeasedTrailers;
  private Integer tripLeasedTractors;
  private Integer tripLeasedTrucks;
  private Integer tripLeasedTrailers;
  private Integer interstateAndIntrastateDrivers;
  private Integer cdLEmployedDrivers;
  private Integer monthlyAverageLeasedDrivers;
  private Integer interstateDriversTotal;
  private Integer interstateDriversGT100Miles;
  private Integer interstateDriversLT100Miles;
  private Integer intrastateDriversTotal;
  private Integer intrastateDriversGT100Miles;
  private Integer intrastateDriversLT100Miles;
  private Boolean powerOnly;
  private Boolean satelliteEquipment;
  private Boolean teamDrivers;
  private Boolean dropTrailer;
  private Boolean elDCompliant;
  private String elDCompliantBy;
  private String elDIdentifier;
  private Integer numberOfTractors;
  private Integer numberOfVans;
  private Integer numberOfReefers;
  private Integer numberOfFlats;
  private Integer numberOfStepDecks;
  private Integer numberOfTanks;

  public CarrierOperationalDetailDto() {
  }

  public CarrierOperationalDetailDto fleetSize(Integer fleetSize) {

    this.fleetSize = fleetSize;
    return this;
  }

  /**
   * Get fleetSize
   *
   * @return fleetSize
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_FLEET_SIZE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Integer getFleetSize() {
    return fleetSize;
  }


  @JsonProperty(JSON_PROPERTY_FLEET_SIZE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setFleetSize(Integer fleetSize) {
    this.fleetSize = fleetSize;
  }


  public CarrierOperationalDetailDto totalPowerUnits(Integer totalPowerUnits) {

    this.totalPowerUnits = totalPowerUnits;
    return this;
  }

  /**
   * Get totalPowerUnits
   *
   * @return totalPowerUnits
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_TOTAL_POWER_UNITS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Integer getTotalPowerUnits() {
    return totalPowerUnits;
  }


  @JsonProperty(JSON_PROPERTY_TOTAL_POWER_UNITS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setTotalPowerUnits(Integer totalPowerUnits) {
    this.totalPowerUnits = totalPowerUnits;
  }


  public CarrierOperationalDetailDto numberOfVehicles(Integer numberOfVehicles) {

    this.numberOfVehicles = numberOfVehicles;
    return this;
  }

  /**
   * Get numberOfVehicles
   *
   * @return numberOfVehicles
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_NUMBER_OF_VEHICLES)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Integer getNumberOfVehicles() {
    return numberOfVehicles;
  }


  @JsonProperty(JSON_PROPERTY_NUMBER_OF_VEHICLES)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setNumberOfVehicles(Integer numberOfVehicles) {
    this.numberOfVehicles = numberOfVehicles;
  }


  public CarrierOperationalDetailDto reeferEquipment(Boolean reeferEquipment) {

    this.reeferEquipment = reeferEquipment;
    return this;
  }

  /**
   * Get reeferEquipment
   *
   * @return reeferEquipment
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_REEFER_EQUIPMENT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getReeferEquipment() {
    return reeferEquipment;
  }


  @JsonProperty(JSON_PROPERTY_REEFER_EQUIPMENT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setReeferEquipment(Boolean reeferEquipment) {
    this.reeferEquipment = reeferEquipment;
  }


  public CarrierOperationalDetailDto vanEquipment(Boolean vanEquipment) {

    this.vanEquipment = vanEquipment;
    return this;
  }

  /**
   * Get vanEquipment
   *
   * @return vanEquipment
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_VAN_EQUIPMENT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getVanEquipment() {
    return vanEquipment;
  }


  @JsonProperty(JSON_PROPERTY_VAN_EQUIPMENT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setVanEquipment(Boolean vanEquipment) {
    this.vanEquipment = vanEquipment;
  }


  public CarrierOperationalDetailDto flatbedStepDeckEquipment(Boolean flatbedStepDeckEquipment) {

    this.flatbedStepDeckEquipment = flatbedStepDeckEquipment;
    return this;
  }

  /**
   * Get flatbedStepDeckEquipment
   *
   * @return flatbedStepDeckEquipment
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_FLATBED_STEP_DECK_EQUIPMENT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getFlatbedStepDeckEquipment() {
    return flatbedStepDeckEquipment;
  }


  @JsonProperty(JSON_PROPERTY_FLATBED_STEP_DECK_EQUIPMENT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setFlatbedStepDeckEquipment(Boolean flatbedStepDeckEquipment) {
    this.flatbedStepDeckEquipment = flatbedStepDeckEquipment;
  }


  public CarrierOperationalDetailDto ownedTractors(Integer ownedTractors) {

    this.ownedTractors = ownedTractors;
    return this;
  }

  /**
   * Get ownedTractors
   *
   * @return ownedTractors
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_OWNED_TRACTORS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Integer getOwnedTractors() {
    return ownedTractors;
  }


  @JsonProperty(JSON_PROPERTY_OWNED_TRACTORS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setOwnedTractors(Integer ownedTractors) {
    this.ownedTractors = ownedTractors;
  }


  public CarrierOperationalDetailDto ownedTrucks(Integer ownedTrucks) {

    this.ownedTrucks = ownedTrucks;
    return this;
  }

  /**
   * Get ownedTrucks
   *
   * @return ownedTrucks
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_OWNED_TRUCKS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Integer getOwnedTrucks() {
    return ownedTrucks;
  }


  @JsonProperty(JSON_PROPERTY_OWNED_TRUCKS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setOwnedTrucks(Integer ownedTrucks) {
    this.ownedTrucks = ownedTrucks;
  }


  public CarrierOperationalDetailDto ownedTrailers(Integer ownedTrailers) {

    this.ownedTrailers = ownedTrailers;
    return this;
  }

  /**
   * Get ownedTrailers
   *
   * @return ownedTrailers
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_OWNED_TRAILERS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Integer getOwnedTrailers() {
    return ownedTrailers;
  }


  @JsonProperty(JSON_PROPERTY_OWNED_TRAILERS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setOwnedTrailers(Integer ownedTrailers) {
    this.ownedTrailers = ownedTrailers;
  }


  public CarrierOperationalDetailDto termLeasedTractors(Integer termLeasedTractors) {

    this.termLeasedTractors = termLeasedTractors;
    return this;
  }

  /**
   * Get termLeasedTractors
   *
   * @return termLeasedTractors
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_TERM_LEASED_TRACTORS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Integer getTermLeasedTractors() {
    return termLeasedTractors;
  }


  @JsonProperty(JSON_PROPERTY_TERM_LEASED_TRACTORS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setTermLeasedTractors(Integer termLeasedTractors) {
    this.termLeasedTractors = termLeasedTractors;
  }


  public CarrierOperationalDetailDto termLeasedTrucks(Integer termLeasedTrucks) {

    this.termLeasedTrucks = termLeasedTrucks;
    return this;
  }

  /**
   * Get termLeasedTrucks
   *
   * @return termLeasedTrucks
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_TERM_LEASED_TRUCKS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Integer getTermLeasedTrucks() {
    return termLeasedTrucks;
  }


  @JsonProperty(JSON_PROPERTY_TERM_LEASED_TRUCKS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setTermLeasedTrucks(Integer termLeasedTrucks) {
    this.termLeasedTrucks = termLeasedTrucks;
  }


  public CarrierOperationalDetailDto termLeasedTrailers(Integer termLeasedTrailers) {

    this.termLeasedTrailers = termLeasedTrailers;
    return this;
  }

  /**
   * Get termLeasedTrailers
   *
   * @return termLeasedTrailers
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_TERM_LEASED_TRAILERS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Integer getTermLeasedTrailers() {
    return termLeasedTrailers;
  }


  @JsonProperty(JSON_PROPERTY_TERM_LEASED_TRAILERS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setTermLeasedTrailers(Integer termLeasedTrailers) {
    this.termLeasedTrailers = termLeasedTrailers;
  }


  public CarrierOperationalDetailDto tripLeasedTractors(Integer tripLeasedTractors) {

    this.tripLeasedTractors = tripLeasedTractors;
    return this;
  }

  /**
   * Get tripLeasedTractors
   *
   * @return tripLeasedTractors
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_TRIP_LEASED_TRACTORS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Integer getTripLeasedTractors() {
    return tripLeasedTractors;
  }


  @JsonProperty(JSON_PROPERTY_TRIP_LEASED_TRACTORS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setTripLeasedTractors(Integer tripLeasedTractors) {
    this.tripLeasedTractors = tripLeasedTractors;
  }


  public CarrierOperationalDetailDto tripLeasedTrucks(Integer tripLeasedTrucks) {

    this.tripLeasedTrucks = tripLeasedTrucks;
    return this;
  }

  /**
   * Get tripLeasedTrucks
   *
   * @return tripLeasedTrucks
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_TRIP_LEASED_TRUCKS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Integer getTripLeasedTrucks() {
    return tripLeasedTrucks;
  }


  @JsonProperty(JSON_PROPERTY_TRIP_LEASED_TRUCKS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setTripLeasedTrucks(Integer tripLeasedTrucks) {
    this.tripLeasedTrucks = tripLeasedTrucks;
  }


  public CarrierOperationalDetailDto tripLeasedTrailers(Integer tripLeasedTrailers) {

    this.tripLeasedTrailers = tripLeasedTrailers;
    return this;
  }

  /**
   * Get tripLeasedTrailers
   *
   * @return tripLeasedTrailers
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_TRIP_LEASED_TRAILERS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Integer getTripLeasedTrailers() {
    return tripLeasedTrailers;
  }


  @JsonProperty(JSON_PROPERTY_TRIP_LEASED_TRAILERS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setTripLeasedTrailers(Integer tripLeasedTrailers) {
    this.tripLeasedTrailers = tripLeasedTrailers;
  }


  public CarrierOperationalDetailDto interstateAndIntrastateDrivers(Integer interstateAndIntrastateDrivers) {

    this.interstateAndIntrastateDrivers = interstateAndIntrastateDrivers;
    return this;
  }

  /**
   * Get interstateAndIntrastateDrivers
   *
   * @return interstateAndIntrastateDrivers
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_INTERSTATE_AND_INTRASTATE_DRIVERS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Integer getInterstateAndIntrastateDrivers() {
    return interstateAndIntrastateDrivers;
  }


  @JsonProperty(JSON_PROPERTY_INTERSTATE_AND_INTRASTATE_DRIVERS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setInterstateAndIntrastateDrivers(Integer interstateAndIntrastateDrivers) {
    this.interstateAndIntrastateDrivers = interstateAndIntrastateDrivers;
  }


  public CarrierOperationalDetailDto cdLEmployedDrivers(Integer cdLEmployedDrivers) {

    this.cdLEmployedDrivers = cdLEmployedDrivers;
    return this;
  }

  /**
   * Get cdLEmployedDrivers
   *
   * @return cdLEmployedDrivers
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CD_L_EMPLOYED_DRIVERS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Integer getCdLEmployedDrivers() {
    return cdLEmployedDrivers;
  }


  @JsonProperty(JSON_PROPERTY_CD_L_EMPLOYED_DRIVERS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCdLEmployedDrivers(Integer cdLEmployedDrivers) {
    this.cdLEmployedDrivers = cdLEmployedDrivers;
  }


  public CarrierOperationalDetailDto monthlyAverageLeasedDrivers(Integer monthlyAverageLeasedDrivers) {

    this.monthlyAverageLeasedDrivers = monthlyAverageLeasedDrivers;
    return this;
  }

  /**
   * Get monthlyAverageLeasedDrivers
   *
   * @return monthlyAverageLeasedDrivers
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_MONTHLY_AVERAGE_LEASED_DRIVERS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Integer getMonthlyAverageLeasedDrivers() {
    return monthlyAverageLeasedDrivers;
  }


  @JsonProperty(JSON_PROPERTY_MONTHLY_AVERAGE_LEASED_DRIVERS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setMonthlyAverageLeasedDrivers(Integer monthlyAverageLeasedDrivers) {
    this.monthlyAverageLeasedDrivers = monthlyAverageLeasedDrivers;
  }


  public CarrierOperationalDetailDto interstateDriversTotal(Integer interstateDriversTotal) {

    this.interstateDriversTotal = interstateDriversTotal;
    return this;
  }

  /**
   * Get interstateDriversTotal
   *
   * @return interstateDriversTotal
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_INTERSTATE_DRIVERS_TOTAL)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Integer getInterstateDriversTotal() {
    return interstateDriversTotal;
  }


  @JsonProperty(JSON_PROPERTY_INTERSTATE_DRIVERS_TOTAL)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setInterstateDriversTotal(Integer interstateDriversTotal) {
    this.interstateDriversTotal = interstateDriversTotal;
  }


  public CarrierOperationalDetailDto interstateDriversGT100Miles(Integer interstateDriversGT100Miles) {

    this.interstateDriversGT100Miles = interstateDriversGT100Miles;
    return this;
  }

  /**
   * Get interstateDriversGT100Miles
   *
   * @return interstateDriversGT100Miles
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_INTERSTATE_DRIVERS_G_T100_MILES)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Integer getInterstateDriversGT100Miles() {
    return interstateDriversGT100Miles;
  }


  @JsonProperty(JSON_PROPERTY_INTERSTATE_DRIVERS_G_T100_MILES)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setInterstateDriversGT100Miles(Integer interstateDriversGT100Miles) {
    this.interstateDriversGT100Miles = interstateDriversGT100Miles;
  }


  public CarrierOperationalDetailDto interstateDriversLT100Miles(Integer interstateDriversLT100Miles) {

    this.interstateDriversLT100Miles = interstateDriversLT100Miles;
    return this;
  }

  /**
   * Get interstateDriversLT100Miles
   *
   * @return interstateDriversLT100Miles
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_INTERSTATE_DRIVERS_L_T100_MILES)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Integer getInterstateDriversLT100Miles() {
    return interstateDriversLT100Miles;
  }


  @JsonProperty(JSON_PROPERTY_INTERSTATE_DRIVERS_L_T100_MILES)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setInterstateDriversLT100Miles(Integer interstateDriversLT100Miles) {
    this.interstateDriversLT100Miles = interstateDriversLT100Miles;
  }


  public CarrierOperationalDetailDto intrastateDriversTotal(Integer intrastateDriversTotal) {

    this.intrastateDriversTotal = intrastateDriversTotal;
    return this;
  }

  /**
   * Get intrastateDriversTotal
   *
   * @return intrastateDriversTotal
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_INTRASTATE_DRIVERS_TOTAL)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Integer getIntrastateDriversTotal() {
    return intrastateDriversTotal;
  }


  @JsonProperty(JSON_PROPERTY_INTRASTATE_DRIVERS_TOTAL)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setIntrastateDriversTotal(Integer intrastateDriversTotal) {
    this.intrastateDriversTotal = intrastateDriversTotal;
  }


  public CarrierOperationalDetailDto intrastateDriversGT100Miles(Integer intrastateDriversGT100Miles) {

    this.intrastateDriversGT100Miles = intrastateDriversGT100Miles;
    return this;
  }

  /**
   * Get intrastateDriversGT100Miles
   *
   * @return intrastateDriversGT100Miles
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_INTRASTATE_DRIVERS_G_T100_MILES)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Integer getIntrastateDriversGT100Miles() {
    return intrastateDriversGT100Miles;
  }


  @JsonProperty(JSON_PROPERTY_INTRASTATE_DRIVERS_G_T100_MILES)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setIntrastateDriversGT100Miles(Integer intrastateDriversGT100Miles) {
    this.intrastateDriversGT100Miles = intrastateDriversGT100Miles;
  }


  public CarrierOperationalDetailDto intrastateDriversLT100Miles(Integer intrastateDriversLT100Miles) {

    this.intrastateDriversLT100Miles = intrastateDriversLT100Miles;
    return this;
  }

  /**
   * Get intrastateDriversLT100Miles
   *
   * @return intrastateDriversLT100Miles
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_INTRASTATE_DRIVERS_L_T100_MILES)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Integer getIntrastateDriversLT100Miles() {
    return intrastateDriversLT100Miles;
  }


  @JsonProperty(JSON_PROPERTY_INTRASTATE_DRIVERS_L_T100_MILES)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setIntrastateDriversLT100Miles(Integer intrastateDriversLT100Miles) {
    this.intrastateDriversLT100Miles = intrastateDriversLT100Miles;
  }


  public CarrierOperationalDetailDto powerOnly(Boolean powerOnly) {

    this.powerOnly = powerOnly;
    return this;
  }

  /**
   * Get powerOnly
   *
   * @return powerOnly
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_POWER_ONLY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getPowerOnly() {
    return powerOnly;
  }


  @JsonProperty(JSON_PROPERTY_POWER_ONLY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setPowerOnly(Boolean powerOnly) {
    this.powerOnly = powerOnly;
  }


  public CarrierOperationalDetailDto satelliteEquipment(Boolean satelliteEquipment) {

    this.satelliteEquipment = satelliteEquipment;
    return this;
  }

  /**
   * Get satelliteEquipment
   *
   * @return satelliteEquipment
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_SATELLITE_EQUIPMENT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getSatelliteEquipment() {
    return satelliteEquipment;
  }


  @JsonProperty(JSON_PROPERTY_SATELLITE_EQUIPMENT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setSatelliteEquipment(Boolean satelliteEquipment) {
    this.satelliteEquipment = satelliteEquipment;
  }


  public CarrierOperationalDetailDto teamDrivers(Boolean teamDrivers) {

    this.teamDrivers = teamDrivers;
    return this;
  }

  /**
   * Get teamDrivers
   *
   * @return teamDrivers
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_TEAM_DRIVERS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getTeamDrivers() {
    return teamDrivers;
  }


  @JsonProperty(JSON_PROPERTY_TEAM_DRIVERS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setTeamDrivers(Boolean teamDrivers) {
    this.teamDrivers = teamDrivers;
  }


  public CarrierOperationalDetailDto dropTrailer(Boolean dropTrailer) {

    this.dropTrailer = dropTrailer;
    return this;
  }

  /**
   * Get dropTrailer
   *
   * @return dropTrailer
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_DROP_TRAILER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getDropTrailer() {
    return dropTrailer;
  }


  @JsonProperty(JSON_PROPERTY_DROP_TRAILER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setDropTrailer(Boolean dropTrailer) {
    this.dropTrailer = dropTrailer;
  }


  public CarrierOperationalDetailDto elDCompliant(Boolean elDCompliant) {

    this.elDCompliant = elDCompliant;
    return this;
  }

  /**
   * Get elDCompliant
   *
   * @return elDCompliant
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_EL_D_COMPLIANT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getElDCompliant() {
    return elDCompliant;
  }


  @JsonProperty(JSON_PROPERTY_EL_D_COMPLIANT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setElDCompliant(Boolean elDCompliant) {
    this.elDCompliant = elDCompliant;
  }


  public CarrierOperationalDetailDto elDCompliantBy(String elDCompliantBy) {

    this.elDCompliantBy = elDCompliantBy;
    return this;
  }

  /**
   * Get elDCompliantBy
   *
   * @return elDCompliantBy
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_EL_D_COMPLIANT_BY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getElDCompliantBy() {
    return elDCompliantBy;
  }


  @JsonProperty(JSON_PROPERTY_EL_D_COMPLIANT_BY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setElDCompliantBy(String elDCompliantBy) {
    this.elDCompliantBy = elDCompliantBy;
  }


  public CarrierOperationalDetailDto elDIdentifier(String elDIdentifier) {

    this.elDIdentifier = elDIdentifier;
    return this;
  }

  /**
   * Get elDIdentifier
   *
   * @return elDIdentifier
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_EL_D_IDENTIFIER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getElDIdentifier() {
    return elDIdentifier;
  }


  @JsonProperty(JSON_PROPERTY_EL_D_IDENTIFIER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setElDIdentifier(String elDIdentifier) {
    this.elDIdentifier = elDIdentifier;
  }


  public CarrierOperationalDetailDto numberOfTractors(Integer numberOfTractors) {

    this.numberOfTractors = numberOfTractors;
    return this;
  }

  /**
   * Get numberOfTractors
   *
   * @return numberOfTractors
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_NUMBER_OF_TRACTORS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Integer getNumberOfTractors() {
    return numberOfTractors;
  }


  @JsonProperty(JSON_PROPERTY_NUMBER_OF_TRACTORS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setNumberOfTractors(Integer numberOfTractors) {
    this.numberOfTractors = numberOfTractors;
  }


  public CarrierOperationalDetailDto numberOfVans(Integer numberOfVans) {

    this.numberOfVans = numberOfVans;
    return this;
  }

  /**
   * Get numberOfVans
   *
   * @return numberOfVans
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_NUMBER_OF_VANS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Integer getNumberOfVans() {
    return numberOfVans;
  }


  @JsonProperty(JSON_PROPERTY_NUMBER_OF_VANS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setNumberOfVans(Integer numberOfVans) {
    this.numberOfVans = numberOfVans;
  }


  public CarrierOperationalDetailDto numberOfReefers(Integer numberOfReefers) {

    this.numberOfReefers = numberOfReefers;
    return this;
  }

  /**
   * Get numberOfReefers
   *
   * @return numberOfReefers
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_NUMBER_OF_REEFERS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Integer getNumberOfReefers() {
    return numberOfReefers;
  }


  @JsonProperty(JSON_PROPERTY_NUMBER_OF_REEFERS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setNumberOfReefers(Integer numberOfReefers) {
    this.numberOfReefers = numberOfReefers;
  }


  public CarrierOperationalDetailDto numberOfFlats(Integer numberOfFlats) {

    this.numberOfFlats = numberOfFlats;
    return this;
  }

  /**
   * Get numberOfFlats
   *
   * @return numberOfFlats
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_NUMBER_OF_FLATS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Integer getNumberOfFlats() {
    return numberOfFlats;
  }


  @JsonProperty(JSON_PROPERTY_NUMBER_OF_FLATS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setNumberOfFlats(Integer numberOfFlats) {
    this.numberOfFlats = numberOfFlats;
  }


  public CarrierOperationalDetailDto numberOfStepDecks(Integer numberOfStepDecks) {

    this.numberOfStepDecks = numberOfStepDecks;
    return this;
  }

  /**
   * Get numberOfStepDecks
   *
   * @return numberOfStepDecks
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_NUMBER_OF_STEP_DECKS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Integer getNumberOfStepDecks() {
    return numberOfStepDecks;
  }


  @JsonProperty(JSON_PROPERTY_NUMBER_OF_STEP_DECKS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setNumberOfStepDecks(Integer numberOfStepDecks) {
    this.numberOfStepDecks = numberOfStepDecks;
  }


  public CarrierOperationalDetailDto numberOfTanks(Integer numberOfTanks) {

    this.numberOfTanks = numberOfTanks;
    return this;
  }

  /**
   * Get numberOfTanks
   *
   * @return numberOfTanks
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_NUMBER_OF_TANKS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Integer getNumberOfTanks() {
    return numberOfTanks;
  }


  @JsonProperty(JSON_PROPERTY_NUMBER_OF_TANKS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setNumberOfTanks(Integer numberOfTanks) {
    this.numberOfTanks = numberOfTanks;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    CarrierOperationalDetailDto carrierOperationalDetailDto = (CarrierOperationalDetailDto) o;
    return Objects.equals(this.fleetSize, carrierOperationalDetailDto.fleetSize) && Objects.equals(this.totalPowerUnits,
        carrierOperationalDetailDto.totalPowerUnits) && Objects.equals(this.numberOfVehicles, carrierOperationalDetailDto.numberOfVehicles) && Objects.equals(
        this.reeferEquipment, carrierOperationalDetailDto.reeferEquipment) && Objects.equals(this.vanEquipment, carrierOperationalDetailDto.vanEquipment)
           && Objects.equals(this.flatbedStepDeckEquipment, carrierOperationalDetailDto.flatbedStepDeckEquipment) && Objects.equals(this.ownedTractors,
        carrierOperationalDetailDto.ownedTractors) && Objects.equals(this.ownedTrucks, carrierOperationalDetailDto.ownedTrucks) && Objects.equals(
        this.ownedTrailers, carrierOperationalDetailDto.ownedTrailers) && Objects.equals(this.termLeasedTractors,
        carrierOperationalDetailDto.termLeasedTractors) && Objects.equals(this.termLeasedTrucks, carrierOperationalDetailDto.termLeasedTrucks)
           && Objects.equals(this.termLeasedTrailers, carrierOperationalDetailDto.termLeasedTrailers) && Objects.equals(this.tripLeasedTractors,
        carrierOperationalDetailDto.tripLeasedTractors) && Objects.equals(this.tripLeasedTrucks, carrierOperationalDetailDto.tripLeasedTrucks)
           && Objects.equals(this.tripLeasedTrailers, carrierOperationalDetailDto.tripLeasedTrailers) && Objects.equals(this.interstateAndIntrastateDrivers,
        carrierOperationalDetailDto.interstateAndIntrastateDrivers) && Objects.equals(this.cdLEmployedDrivers, carrierOperationalDetailDto.cdLEmployedDrivers)
           && Objects.equals(this.monthlyAverageLeasedDrivers, carrierOperationalDetailDto.monthlyAverageLeasedDrivers) && Objects.equals(
        this.interstateDriversTotal, carrierOperationalDetailDto.interstateDriversTotal) && Objects.equals(this.interstateDriversGT100Miles,
        carrierOperationalDetailDto.interstateDriversGT100Miles) && Objects.equals(this.interstateDriversLT100Miles,
        carrierOperationalDetailDto.interstateDriversLT100Miles) && Objects.equals(this.intrastateDriversTotal,
        carrierOperationalDetailDto.intrastateDriversTotal) && Objects.equals(this.intrastateDriversGT100Miles,
        carrierOperationalDetailDto.intrastateDriversGT100Miles) && Objects.equals(this.intrastateDriversLT100Miles,
        carrierOperationalDetailDto.intrastateDriversLT100Miles) && Objects.equals(this.powerOnly, carrierOperationalDetailDto.powerOnly) && Objects.equals(
        this.satelliteEquipment, carrierOperationalDetailDto.satelliteEquipment) && Objects.equals(this.teamDrivers, carrierOperationalDetailDto.teamDrivers)
           && Objects.equals(this.dropTrailer, carrierOperationalDetailDto.dropTrailer) && Objects.equals(this.elDCompliant,
        carrierOperationalDetailDto.elDCompliant) && Objects.equals(this.elDCompliantBy, carrierOperationalDetailDto.elDCompliantBy) && Objects.equals(
        this.elDIdentifier, carrierOperationalDetailDto.elDIdentifier) && Objects.equals(this.numberOfTractors, carrierOperationalDetailDto.numberOfTractors)
           && Objects.equals(this.numberOfVans, carrierOperationalDetailDto.numberOfVans) && Objects.equals(this.numberOfReefers,
        carrierOperationalDetailDto.numberOfReefers) && Objects.equals(this.numberOfFlats, carrierOperationalDetailDto.numberOfFlats) && Objects.equals(
        this.numberOfStepDecks, carrierOperationalDetailDto.numberOfStepDecks) && Objects.equals(this.numberOfTanks, carrierOperationalDetailDto.numberOfTanks);
  }

  @Override
  public int hashCode() {
    return Objects.hash(fleetSize, totalPowerUnits, numberOfVehicles, reeferEquipment, vanEquipment, flatbedStepDeckEquipment, ownedTractors, ownedTrucks,
        ownedTrailers, termLeasedTractors, termLeasedTrucks, termLeasedTrailers, tripLeasedTractors, tripLeasedTrucks, tripLeasedTrailers,
        interstateAndIntrastateDrivers, cdLEmployedDrivers, monthlyAverageLeasedDrivers, interstateDriversTotal, interstateDriversGT100Miles,
        interstateDriversLT100Miles, intrastateDriversTotal, intrastateDriversGT100Miles, intrastateDriversLT100Miles, powerOnly, satelliteEquipment,
        teamDrivers, dropTrailer, elDCompliant, elDCompliantBy, elDIdentifier, numberOfTractors, numberOfVans, numberOfReefers, numberOfFlats,
        numberOfStepDecks, numberOfTanks);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class CarrierOperationalDetailDto {\n");
    sb.append("    fleetSize: ").append(toIndentedString(fleetSize)).append("\n");
    sb.append("    totalPowerUnits: ").append(toIndentedString(totalPowerUnits)).append("\n");
    sb.append("    numberOfVehicles: ").append(toIndentedString(numberOfVehicles)).append("\n");
    sb.append("    reeferEquipment: ").append(toIndentedString(reeferEquipment)).append("\n");
    sb.append("    vanEquipment: ").append(toIndentedString(vanEquipment)).append("\n");
    sb.append("    flatbedStepDeckEquipment: ").append(toIndentedString(flatbedStepDeckEquipment)).append("\n");
    sb.append("    ownedTractors: ").append(toIndentedString(ownedTractors)).append("\n");
    sb.append("    ownedTrucks: ").append(toIndentedString(ownedTrucks)).append("\n");
    sb.append("    ownedTrailers: ").append(toIndentedString(ownedTrailers)).append("\n");
    sb.append("    termLeasedTractors: ").append(toIndentedString(termLeasedTractors)).append("\n");
    sb.append("    termLeasedTrucks: ").append(toIndentedString(termLeasedTrucks)).append("\n");
    sb.append("    termLeasedTrailers: ").append(toIndentedString(termLeasedTrailers)).append("\n");
    sb.append("    tripLeasedTractors: ").append(toIndentedString(tripLeasedTractors)).append("\n");
    sb.append("    tripLeasedTrucks: ").append(toIndentedString(tripLeasedTrucks)).append("\n");
    sb.append("    tripLeasedTrailers: ").append(toIndentedString(tripLeasedTrailers)).append("\n");
    sb.append("    interstateAndIntrastateDrivers: ").append(toIndentedString(interstateAndIntrastateDrivers)).append("\n");
    sb.append("    cdLEmployedDrivers: ").append(toIndentedString(cdLEmployedDrivers)).append("\n");
    sb.append("    monthlyAverageLeasedDrivers: ").append(toIndentedString(monthlyAverageLeasedDrivers)).append("\n");
    sb.append("    interstateDriversTotal: ").append(toIndentedString(interstateDriversTotal)).append("\n");
    sb.append("    interstateDriversGT100Miles: ").append(toIndentedString(interstateDriversGT100Miles)).append("\n");
    sb.append("    interstateDriversLT100Miles: ").append(toIndentedString(interstateDriversLT100Miles)).append("\n");
    sb.append("    intrastateDriversTotal: ").append(toIndentedString(intrastateDriversTotal)).append("\n");
    sb.append("    intrastateDriversGT100Miles: ").append(toIndentedString(intrastateDriversGT100Miles)).append("\n");
    sb.append("    intrastateDriversLT100Miles: ").append(toIndentedString(intrastateDriversLT100Miles)).append("\n");
    sb.append("    powerOnly: ").append(toIndentedString(powerOnly)).append("\n");
    sb.append("    satelliteEquipment: ").append(toIndentedString(satelliteEquipment)).append("\n");
    sb.append("    teamDrivers: ").append(toIndentedString(teamDrivers)).append("\n");
    sb.append("    dropTrailer: ").append(toIndentedString(dropTrailer)).append("\n");
    sb.append("    elDCompliant: ").append(toIndentedString(elDCompliant)).append("\n");
    sb.append("    elDCompliantBy: ").append(toIndentedString(elDCompliantBy)).append("\n");
    sb.append("    elDIdentifier: ").append(toIndentedString(elDIdentifier)).append("\n");
    sb.append("    numberOfTractors: ").append(toIndentedString(numberOfTractors)).append("\n");
    sb.append("    numberOfVans: ").append(toIndentedString(numberOfVans)).append("\n");
    sb.append("    numberOfReefers: ").append(toIndentedString(numberOfReefers)).append("\n");
    sb.append("    numberOfFlats: ").append(toIndentedString(numberOfFlats)).append("\n");
    sb.append("    numberOfStepDecks: ").append(toIndentedString(numberOfStepDecks)).append("\n");
    sb.append("    numberOfTanks: ").append(toIndentedString(numberOfTanks)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

