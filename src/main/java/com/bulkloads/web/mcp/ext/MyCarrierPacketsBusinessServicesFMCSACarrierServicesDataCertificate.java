package com.bulkloads.web.mcp.ext;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataCertificate
 */
@JsonPropertyOrder({MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataCertificate.JSON_PROPERTY_CERTIFICATE_I_D,
    MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataCertificate.JSON_PROPERTY_PRODUCER_NAME,
    MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataCertificate.JSON_PROPERTY_PRODUCER_ADDRESS,
    MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataCertificate.JSON_PROPERTY_PRODUCER_CITY,
    MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataCertificate.JSON_PROPERTY_PRODUCER_STATE,
    MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataCertificate.JSON_PROPERTY_PRODUCER_ZIP,
    MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataCertificate.JSON_PROPERTY_PRODUCER_PHONE,
    MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataCertificate.JSON_PROPERTY_PRODUCER_FAX,
    MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataCertificate.JSON_PROPERTY_PRODUCER_EMAIL,
    MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataCertificate.JSON_PROPERTY_PAID_FOR,
    MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataCertificate.JSON_PROPERTY_COVERAGES})
@JsonTypeName("MyCarrierPacketsBusiness.Services.FMCSACarrierServices.Data.Certificate")

public class MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataCertificate {

  public static final String JSON_PROPERTY_CERTIFICATE_I_D = "CertificateID";
  public static final String JSON_PROPERTY_PRODUCER_NAME = "ProducerName";
  public static final String JSON_PROPERTY_PRODUCER_ADDRESS = "ProducerAddress";
  public static final String JSON_PROPERTY_PRODUCER_CITY = "ProducerCity";
  public static final String JSON_PROPERTY_PRODUCER_STATE = "ProducerState";
  public static final String JSON_PROPERTY_PRODUCER_ZIP = "ProducerZip";
  public static final String JSON_PROPERTY_PRODUCER_PHONE = "ProducerPhone";
  public static final String JSON_PROPERTY_PRODUCER_FAX = "ProducerFax";
  public static final String JSON_PROPERTY_PRODUCER_EMAIL = "ProducerEmail";
  public static final String JSON_PROPERTY_PAID_FOR = "PaidFor";
  public static final String JSON_PROPERTY_COVERAGES = "Coverages";
  private String certificateID;
  private String producerName;
  private String producerAddress;
  private String producerCity;
  private String producerState;
  private String producerZip;
  private String producerPhone;
  private String producerFax;
  private String producerEmail;
  private String paidFor;
  private List<MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataCoverage> coverages;

  public MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataCertificate() {
  }

  public MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataCertificate certificateID(String certificateID) {

    this.certificateID = certificateID;
    return this;
  }

  /**
   * Get certificateID
   *
   * @return certificateID
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CERTIFICATE_I_D)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getCertificateID() {
    return certificateID;
  }


  @JsonProperty(JSON_PROPERTY_CERTIFICATE_I_D)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCertificateID(String certificateID) {
    this.certificateID = certificateID;
  }


  public MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataCertificate producerName(String producerName) {

    this.producerName = producerName;
    return this;
  }

  /**
   * Get producerName
   *
   * @return producerName
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_PRODUCER_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getProducerName() {
    return producerName;
  }


  @JsonProperty(JSON_PROPERTY_PRODUCER_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setProducerName(String producerName) {
    this.producerName = producerName;
  }


  public MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataCertificate producerAddress(String producerAddress) {

    this.producerAddress = producerAddress;
    return this;
  }

  /**
   * Get producerAddress
   *
   * @return producerAddress
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_PRODUCER_ADDRESS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getProducerAddress() {
    return producerAddress;
  }


  @JsonProperty(JSON_PROPERTY_PRODUCER_ADDRESS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setProducerAddress(String producerAddress) {
    this.producerAddress = producerAddress;
  }


  public MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataCertificate producerCity(String producerCity) {

    this.producerCity = producerCity;
    return this;
  }

  /**
   * Get producerCity
   *
   * @return producerCity
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_PRODUCER_CITY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getProducerCity() {
    return producerCity;
  }


  @JsonProperty(JSON_PROPERTY_PRODUCER_CITY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setProducerCity(String producerCity) {
    this.producerCity = producerCity;
  }


  public MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataCertificate producerState(String producerState) {

    this.producerState = producerState;
    return this;
  }

  /**
   * Get producerState
   *
   * @return producerState
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_PRODUCER_STATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getProducerState() {
    return producerState;
  }


  @JsonProperty(JSON_PROPERTY_PRODUCER_STATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setProducerState(String producerState) {
    this.producerState = producerState;
  }


  public MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataCertificate producerZip(String producerZip) {

    this.producerZip = producerZip;
    return this;
  }

  /**
   * Get producerZip
   *
   * @return producerZip
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_PRODUCER_ZIP)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getProducerZip() {
    return producerZip;
  }


  @JsonProperty(JSON_PROPERTY_PRODUCER_ZIP)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setProducerZip(String producerZip) {
    this.producerZip = producerZip;
  }


  public MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataCertificate producerPhone(String producerPhone) {

    this.producerPhone = producerPhone;
    return this;
  }

  /**
   * Get producerPhone
   *
   * @return producerPhone
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_PRODUCER_PHONE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getProducerPhone() {
    return producerPhone;
  }


  @JsonProperty(JSON_PROPERTY_PRODUCER_PHONE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setProducerPhone(String producerPhone) {
    this.producerPhone = producerPhone;
  }


  public MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataCertificate producerFax(String producerFax) {

    this.producerFax = producerFax;
    return this;
  }

  /**
   * Get producerFax
   *
   * @return producerFax
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_PRODUCER_FAX)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getProducerFax() {
    return producerFax;
  }


  @JsonProperty(JSON_PROPERTY_PRODUCER_FAX)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setProducerFax(String producerFax) {
    this.producerFax = producerFax;
  }


  public MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataCertificate producerEmail(String producerEmail) {

    this.producerEmail = producerEmail;
    return this;
  }

  /**
   * Get producerEmail
   *
   * @return producerEmail
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_PRODUCER_EMAIL)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getProducerEmail() {
    return producerEmail;
  }


  @JsonProperty(JSON_PROPERTY_PRODUCER_EMAIL)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setProducerEmail(String producerEmail) {
    this.producerEmail = producerEmail;
  }


  public MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataCertificate paidFor(String paidFor) {

    this.paidFor = paidFor;
    return this;
  }

  /**
   * Get paidFor
   *
   * @return paidFor
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_PAID_FOR)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getPaidFor() {
    return paidFor;
  }


  @JsonProperty(JSON_PROPERTY_PAID_FOR)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setPaidFor(String paidFor) {
    this.paidFor = paidFor;
  }


  public MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataCertificate coverages(
      List<MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataCoverage> coverages) {

    this.coverages = coverages;
    return this;
  }

  public MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataCertificate addCoveragesItem(
      MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataCoverage coveragesItem) {
    if (this.coverages == null) {
      this.coverages = new ArrayList<>();
    }
    this.coverages.add(coveragesItem);
    return this;
  }

  /**
   * Get coverages
   *
   * @return coverages
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_COVERAGES)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public List<MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataCoverage> getCoverages() {
    return coverages;
  }


  @JsonProperty(JSON_PROPERTY_COVERAGES)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCoverages(List<MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataCoverage> coverages) {
    this.coverages = coverages;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataCertificate myCarrierPacketsBusinessServicesFMCSACarrierServicesDataCertificate =
        (MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataCertificate) o;
    return Objects.equals(this.certificateID, myCarrierPacketsBusinessServicesFMCSACarrierServicesDataCertificate.certificateID) && Objects.equals(
        this.producerName, myCarrierPacketsBusinessServicesFMCSACarrierServicesDataCertificate.producerName) && Objects.equals(this.producerAddress,
        myCarrierPacketsBusinessServicesFMCSACarrierServicesDataCertificate.producerAddress) && Objects.equals(this.producerCity,
        myCarrierPacketsBusinessServicesFMCSACarrierServicesDataCertificate.producerCity) && Objects.equals(this.producerState,
        myCarrierPacketsBusinessServicesFMCSACarrierServicesDataCertificate.producerState) && Objects.equals(this.producerZip,
        myCarrierPacketsBusinessServicesFMCSACarrierServicesDataCertificate.producerZip) && Objects.equals(this.producerPhone,
        myCarrierPacketsBusinessServicesFMCSACarrierServicesDataCertificate.producerPhone) && Objects.equals(this.producerFax,
        myCarrierPacketsBusinessServicesFMCSACarrierServicesDataCertificate.producerFax) && Objects.equals(this.producerEmail,
        myCarrierPacketsBusinessServicesFMCSACarrierServicesDataCertificate.producerEmail) && Objects.equals(this.paidFor,
        myCarrierPacketsBusinessServicesFMCSACarrierServicesDataCertificate.paidFor) && Objects.equals(this.coverages,
        myCarrierPacketsBusinessServicesFMCSACarrierServicesDataCertificate.coverages);
  }

  @Override
  public int hashCode() {
    return Objects.hash(certificateID, producerName, producerAddress, producerCity, producerState, producerZip, producerPhone, producerFax, producerEmail,
        paidFor, coverages);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataCertificate {\n");
    sb.append("    certificateID: ").append(toIndentedString(certificateID)).append("\n");
    sb.append("    producerName: ").append(toIndentedString(producerName)).append("\n");
    sb.append("    producerAddress: ").append(toIndentedString(producerAddress)).append("\n");
    sb.append("    producerCity: ").append(toIndentedString(producerCity)).append("\n");
    sb.append("    producerState: ").append(toIndentedString(producerState)).append("\n");
    sb.append("    producerZip: ").append(toIndentedString(producerZip)).append("\n");
    sb.append("    producerPhone: ").append(toIndentedString(producerPhone)).append("\n");
    sb.append("    producerFax: ").append(toIndentedString(producerFax)).append("\n");
    sb.append("    producerEmail: ").append(toIndentedString(producerEmail)).append("\n");
    sb.append("    paidFor: ").append(toIndentedString(paidFor)).append("\n");
    sb.append("    coverages: ").append(toIndentedString(coverages)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

