package com.bulkloads.web.mcp.ext;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

/**
 * CarrierCargoHauledDto
 */
@JsonPropertyOrder({CarrierCargoHauledDto.JSON_PROPERTY_GENERAL_FREIGHT, CarrierCargoHauledDto.JSON_PROPERTY_LIQUIDS_GAS,
    CarrierCargoHauledDto.JSON_PROPERTY_CHEMICALS, CarrierCargoHauledDto.JSON_PROPERTY_HOUSEHOLD_GOODS,
    CarrierCargoHauledDto.JSON_PROPERTY_INTERMODAL_CONTAINERS, CarrierCargoHauledDto.JSON_PROPERTY_COMMODITIES_DRY_BULK,
    CarrierCargoHauledDto.JSON_PROPERTY_METAL_SHEETS_COILS_ROLLS, CarrierCargoHauledDto.JSO<PERSON>_PROPERTY_PASSENGERS,
    CarrierCargoHauledDto.JSON_PROPERTY_REFRIGERATED_FOOD, CarrierCargoHauledDto.JSON_PROPERTY_MOTOR_VEHICLES,
    CarrierCargoHauledDto.JSON_PROPERTY_OILFIELD_EQUIPMENT, CarrierCargoHauledDto.JSON_PROPERTY_BEVERAGES, CarrierCargoHauledDto.JSON_PROPERTY_DRIVEWAY_TOWAWAY,
    CarrierCargoHauledDto.JSON_PROPERTY_LIVESTOCK_CONTAINERS, CarrierCargoHauledDto.JSON_PROPERTY_PAPER_PRODUCTS,
    CarrierCargoHauledDto.JSON_PROPERTY_LOGS_POLES_BEAMS_LUMBER, CarrierCargoHauledDto.JSON_PROPERTY_GRAIN_FEED_HAY,
    CarrierCargoHauledDto.JSON_PROPERTY_UTILITY, CarrierCargoHauledDto.JSON_PROPERTY_BUILDING_MATERIALS, CarrierCargoHauledDto.JSON_PROPERTY_COAL_COKE,
    CarrierCargoHauledDto.JSON_PROPERTY_FARM_SUPPLIES, CarrierCargoHauledDto.JSON_PROPERTY_MOBILE_HOMES, CarrierCargoHauledDto.JSON_PROPERTY_MEAT,
    CarrierCargoHauledDto.JSON_PROPERTY_CONSTRUCTION, CarrierCargoHauledDto.JSON_PROPERTY_MACHINERY_LARGE_OBJECTS,
    CarrierCargoHauledDto.JSON_PROPERTY_GARBAGE_REFUSE_TRASH, CarrierCargoHauledDto.JSON_PROPERTY_WATER_WELL, CarrierCargoHauledDto.JSON_PROPERTY_FRESH_PRODUCE,
    CarrierCargoHauledDto.JSON_PROPERTY_US_MAIL, CarrierCargoHauledDto.JSON_PROPERTY_OTHER})

public class CarrierCargoHauledDto {

  public static final String JSON_PROPERTY_GENERAL_FREIGHT = "GeneralFreight";
  public static final String JSON_PROPERTY_LIQUIDS_GAS = "LiquidsGas";
  public static final String JSON_PROPERTY_CHEMICALS = "Chemicals";
  public static final String JSON_PROPERTY_HOUSEHOLD_GOODS = "HouseholdGoods";
  public static final String JSON_PROPERTY_INTERMODAL_CONTAINERS = "IntermodalContainers";
  public static final String JSON_PROPERTY_COMMODITIES_DRY_BULK = "CommoditiesDryBulk";
  public static final String JSON_PROPERTY_METAL_SHEETS_COILS_ROLLS = "MetalSheetsCoilsRolls";
  public static final String JSON_PROPERTY_PASSENGERS = "Passengers";
  public static final String JSON_PROPERTY_REFRIGERATED_FOOD = "RefrigeratedFood";
  public static final String JSON_PROPERTY_MOTOR_VEHICLES = "MotorVehicles";
  public static final String JSON_PROPERTY_OILFIELD_EQUIPMENT = "OilfieldEquipment";
  public static final String JSON_PROPERTY_BEVERAGES = "Beverages";
  public static final String JSON_PROPERTY_DRIVEWAY_TOWAWAY = "DrivewayTowaway";
  public static final String JSON_PROPERTY_LIVESTOCK_CONTAINERS = "LivestockContainers";
  public static final String JSON_PROPERTY_PAPER_PRODUCTS = "PaperProducts";
  public static final String JSON_PROPERTY_LOGS_POLES_BEAMS_LUMBER = "LogsPolesBeamsLumber";
  public static final String JSON_PROPERTY_GRAIN_FEED_HAY = "GrainFeedHay";
  public static final String JSON_PROPERTY_UTILITY = "Utility";
  public static final String JSON_PROPERTY_BUILDING_MATERIALS = "BuildingMaterials";
  public static final String JSON_PROPERTY_COAL_COKE = "CoalCoke";
  public static final String JSON_PROPERTY_FARM_SUPPLIES = "FarmSupplies";
  public static final String JSON_PROPERTY_MOBILE_HOMES = "MobileHomes";
  public static final String JSON_PROPERTY_MEAT = "Meat";
  public static final String JSON_PROPERTY_CONSTRUCTION = "Construction";
  public static final String JSON_PROPERTY_MACHINERY_LARGE_OBJECTS = "MachineryLargeObjects";
  public static final String JSON_PROPERTY_GARBAGE_REFUSE_TRASH = "GarbageRefuseTrash";
  public static final String JSON_PROPERTY_WATER_WELL = "WaterWell";
  public static final String JSON_PROPERTY_FRESH_PRODUCE = "FreshProduce";
  public static final String JSON_PROPERTY_US_MAIL = "USMail";
  public static final String JSON_PROPERTY_OTHER = "Other";
  private Boolean generalFreight;
  private Boolean liquidsGas;
  private Boolean chemicals;
  private Boolean householdGoods;
  private Boolean intermodalContainers;
  private Boolean commoditiesDryBulk;
  private Boolean metalSheetsCoilsRolls;
  private Boolean passengers;
  private Boolean refrigeratedFood;
  private Boolean motorVehicles;
  private Boolean oilfieldEquipment;
  private Boolean beverages;
  private Boolean drivewayTowaway;
  private Boolean livestockContainers;
  private Boolean paperProducts;
  private Boolean logsPolesBeamsLumber;
  private Boolean grainFeedHay;
  private Boolean utility;
  private Boolean buildingMaterials;
  private Boolean coalCoke;
  private Boolean farmSupplies;
  private Boolean mobileHomes;
  private Boolean meat;
  private Boolean construction;
  private Boolean machineryLargeObjects;
  private Boolean garbageRefuseTrash;
  private Boolean waterWell;
  private Boolean freshProduce;
  private Boolean usMail;
  private String other;

  public CarrierCargoHauledDto() {
  }

  public CarrierCargoHauledDto generalFreight(Boolean generalFreight) {

    this.generalFreight = generalFreight;
    return this;
  }

  /**
   * Get generalFreight
   *
   * @return generalFreight
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_GENERAL_FREIGHT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getGeneralFreight() {
    return generalFreight;
  }


  @JsonProperty(JSON_PROPERTY_GENERAL_FREIGHT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setGeneralFreight(Boolean generalFreight) {
    this.generalFreight = generalFreight;
  }


  public CarrierCargoHauledDto liquidsGas(Boolean liquidsGas) {

    this.liquidsGas = liquidsGas;
    return this;
  }

  /**
   * Get liquidsGas
   *
   * @return liquidsGas
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_LIQUIDS_GAS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getLiquidsGas() {
    return liquidsGas;
  }


  @JsonProperty(JSON_PROPERTY_LIQUIDS_GAS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setLiquidsGas(Boolean liquidsGas) {
    this.liquidsGas = liquidsGas;
  }


  public CarrierCargoHauledDto chemicals(Boolean chemicals) {

    this.chemicals = chemicals;
    return this;
  }

  /**
   * Get chemicals
   *
   * @return chemicals
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CHEMICALS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getChemicals() {
    return chemicals;
  }


  @JsonProperty(JSON_PROPERTY_CHEMICALS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setChemicals(Boolean chemicals) {
    this.chemicals = chemicals;
  }


  public CarrierCargoHauledDto householdGoods(Boolean householdGoods) {

    this.householdGoods = householdGoods;
    return this;
  }

  /**
   * Get householdGoods
   *
   * @return householdGoods
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_HOUSEHOLD_GOODS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getHouseholdGoods() {
    return householdGoods;
  }


  @JsonProperty(JSON_PROPERTY_HOUSEHOLD_GOODS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setHouseholdGoods(Boolean householdGoods) {
    this.householdGoods = householdGoods;
  }


  public CarrierCargoHauledDto intermodalContainers(Boolean intermodalContainers) {

    this.intermodalContainers = intermodalContainers;
    return this;
  }

  /**
   * Get intermodalContainers
   *
   * @return intermodalContainers
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_INTERMODAL_CONTAINERS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getIntermodalContainers() {
    return intermodalContainers;
  }


  @JsonProperty(JSON_PROPERTY_INTERMODAL_CONTAINERS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setIntermodalContainers(Boolean intermodalContainers) {
    this.intermodalContainers = intermodalContainers;
  }


  public CarrierCargoHauledDto commoditiesDryBulk(Boolean commoditiesDryBulk) {

    this.commoditiesDryBulk = commoditiesDryBulk;
    return this;
  }

  /**
   * Get commoditiesDryBulk
   *
   * @return commoditiesDryBulk
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_COMMODITIES_DRY_BULK)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getCommoditiesDryBulk() {
    return commoditiesDryBulk;
  }


  @JsonProperty(JSON_PROPERTY_COMMODITIES_DRY_BULK)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCommoditiesDryBulk(Boolean commoditiesDryBulk) {
    this.commoditiesDryBulk = commoditiesDryBulk;
  }


  public CarrierCargoHauledDto metalSheetsCoilsRolls(Boolean metalSheetsCoilsRolls) {

    this.metalSheetsCoilsRolls = metalSheetsCoilsRolls;
    return this;
  }

  /**
   * Get metalSheetsCoilsRolls
   *
   * @return metalSheetsCoilsRolls
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_METAL_SHEETS_COILS_ROLLS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getMetalSheetsCoilsRolls() {
    return metalSheetsCoilsRolls;
  }


  @JsonProperty(JSON_PROPERTY_METAL_SHEETS_COILS_ROLLS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setMetalSheetsCoilsRolls(Boolean metalSheetsCoilsRolls) {
    this.metalSheetsCoilsRolls = metalSheetsCoilsRolls;
  }


  public CarrierCargoHauledDto passengers(Boolean passengers) {

    this.passengers = passengers;
    return this;
  }

  /**
   * Get passengers
   *
   * @return passengers
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_PASSENGERS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getPassengers() {
    return passengers;
  }


  @JsonProperty(JSON_PROPERTY_PASSENGERS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setPassengers(Boolean passengers) {
    this.passengers = passengers;
  }


  public CarrierCargoHauledDto refrigeratedFood(Boolean refrigeratedFood) {

    this.refrigeratedFood = refrigeratedFood;
    return this;
  }

  /**
   * Get refrigeratedFood
   *
   * @return refrigeratedFood
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_REFRIGERATED_FOOD)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getRefrigeratedFood() {
    return refrigeratedFood;
  }


  @JsonProperty(JSON_PROPERTY_REFRIGERATED_FOOD)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setRefrigeratedFood(Boolean refrigeratedFood) {
    this.refrigeratedFood = refrigeratedFood;
  }


  public CarrierCargoHauledDto motorVehicles(Boolean motorVehicles) {

    this.motorVehicles = motorVehicles;
    return this;
  }

  /**
   * Get motorVehicles
   *
   * @return motorVehicles
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_MOTOR_VEHICLES)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getMotorVehicles() {
    return motorVehicles;
  }


  @JsonProperty(JSON_PROPERTY_MOTOR_VEHICLES)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setMotorVehicles(Boolean motorVehicles) {
    this.motorVehicles = motorVehicles;
  }


  public CarrierCargoHauledDto oilfieldEquipment(Boolean oilfieldEquipment) {

    this.oilfieldEquipment = oilfieldEquipment;
    return this;
  }

  /**
   * Get oilfieldEquipment
   *
   * @return oilfieldEquipment
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_OILFIELD_EQUIPMENT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getOilfieldEquipment() {
    return oilfieldEquipment;
  }


  @JsonProperty(JSON_PROPERTY_OILFIELD_EQUIPMENT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setOilfieldEquipment(Boolean oilfieldEquipment) {
    this.oilfieldEquipment = oilfieldEquipment;
  }


  public CarrierCargoHauledDto beverages(Boolean beverages) {

    this.beverages = beverages;
    return this;
  }

  /**
   * Get beverages
   *
   * @return beverages
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_BEVERAGES)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getBeverages() {
    return beverages;
  }


  @JsonProperty(JSON_PROPERTY_BEVERAGES)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setBeverages(Boolean beverages) {
    this.beverages = beverages;
  }


  public CarrierCargoHauledDto drivewayTowaway(Boolean drivewayTowaway) {

    this.drivewayTowaway = drivewayTowaway;
    return this;
  }

  /**
   * Get drivewayTowaway
   *
   * @return drivewayTowaway
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_DRIVEWAY_TOWAWAY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getDrivewayTowaway() {
    return drivewayTowaway;
  }


  @JsonProperty(JSON_PROPERTY_DRIVEWAY_TOWAWAY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setDrivewayTowaway(Boolean drivewayTowaway) {
    this.drivewayTowaway = drivewayTowaway;
  }


  public CarrierCargoHauledDto livestockContainers(Boolean livestockContainers) {

    this.livestockContainers = livestockContainers;
    return this;
  }

  /**
   * Get livestockContainers
   *
   * @return livestockContainers
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_LIVESTOCK_CONTAINERS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getLivestockContainers() {
    return livestockContainers;
  }


  @JsonProperty(JSON_PROPERTY_LIVESTOCK_CONTAINERS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setLivestockContainers(Boolean livestockContainers) {
    this.livestockContainers = livestockContainers;
  }


  public CarrierCargoHauledDto paperProducts(Boolean paperProducts) {

    this.paperProducts = paperProducts;
    return this;
  }

  /**
   * Get paperProducts
   *
   * @return paperProducts
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_PAPER_PRODUCTS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getPaperProducts() {
    return paperProducts;
  }


  @JsonProperty(JSON_PROPERTY_PAPER_PRODUCTS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setPaperProducts(Boolean paperProducts) {
    this.paperProducts = paperProducts;
  }


  public CarrierCargoHauledDto logsPolesBeamsLumber(Boolean logsPolesBeamsLumber) {

    this.logsPolesBeamsLumber = logsPolesBeamsLumber;
    return this;
  }

  /**
   * Get logsPolesBeamsLumber
   *
   * @return logsPolesBeamsLumber
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_LOGS_POLES_BEAMS_LUMBER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getLogsPolesBeamsLumber() {
    return logsPolesBeamsLumber;
  }


  @JsonProperty(JSON_PROPERTY_LOGS_POLES_BEAMS_LUMBER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setLogsPolesBeamsLumber(Boolean logsPolesBeamsLumber) {
    this.logsPolesBeamsLumber = logsPolesBeamsLumber;
  }


  public CarrierCargoHauledDto grainFeedHay(Boolean grainFeedHay) {

    this.grainFeedHay = grainFeedHay;
    return this;
  }

  /**
   * Get grainFeedHay
   *
   * @return grainFeedHay
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_GRAIN_FEED_HAY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getGrainFeedHay() {
    return grainFeedHay;
  }


  @JsonProperty(JSON_PROPERTY_GRAIN_FEED_HAY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setGrainFeedHay(Boolean grainFeedHay) {
    this.grainFeedHay = grainFeedHay;
  }


  public CarrierCargoHauledDto utility(Boolean utility) {

    this.utility = utility;
    return this;
  }

  /**
   * Get utility
   *
   * @return utility
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_UTILITY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getUtility() {
    return utility;
  }


  @JsonProperty(JSON_PROPERTY_UTILITY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setUtility(Boolean utility) {
    this.utility = utility;
  }


  public CarrierCargoHauledDto buildingMaterials(Boolean buildingMaterials) {

    this.buildingMaterials = buildingMaterials;
    return this;
  }

  /**
   * Get buildingMaterials
   *
   * @return buildingMaterials
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_BUILDING_MATERIALS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getBuildingMaterials() {
    return buildingMaterials;
  }


  @JsonProperty(JSON_PROPERTY_BUILDING_MATERIALS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setBuildingMaterials(Boolean buildingMaterials) {
    this.buildingMaterials = buildingMaterials;
  }


  public CarrierCargoHauledDto coalCoke(Boolean coalCoke) {

    this.coalCoke = coalCoke;
    return this;
  }

  /**
   * Get coalCoke
   *
   * @return coalCoke
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_COAL_COKE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getCoalCoke() {
    return coalCoke;
  }


  @JsonProperty(JSON_PROPERTY_COAL_COKE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCoalCoke(Boolean coalCoke) {
    this.coalCoke = coalCoke;
  }


  public CarrierCargoHauledDto farmSupplies(Boolean farmSupplies) {

    this.farmSupplies = farmSupplies;
    return this;
  }

  /**
   * Get farmSupplies
   *
   * @return farmSupplies
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_FARM_SUPPLIES)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getFarmSupplies() {
    return farmSupplies;
  }


  @JsonProperty(JSON_PROPERTY_FARM_SUPPLIES)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setFarmSupplies(Boolean farmSupplies) {
    this.farmSupplies = farmSupplies;
  }


  public CarrierCargoHauledDto mobileHomes(Boolean mobileHomes) {

    this.mobileHomes = mobileHomes;
    return this;
  }

  /**
   * Get mobileHomes
   *
   * @return mobileHomes
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_MOBILE_HOMES)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getMobileHomes() {
    return mobileHomes;
  }


  @JsonProperty(JSON_PROPERTY_MOBILE_HOMES)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setMobileHomes(Boolean mobileHomes) {
    this.mobileHomes = mobileHomes;
  }


  public CarrierCargoHauledDto meat(Boolean meat) {

    this.meat = meat;
    return this;
  }

  /**
   * Get meat
   *
   * @return meat
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_MEAT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getMeat() {
    return meat;
  }


  @JsonProperty(JSON_PROPERTY_MEAT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setMeat(Boolean meat) {
    this.meat = meat;
  }


  public CarrierCargoHauledDto construction(Boolean construction) {

    this.construction = construction;
    return this;
  }

  /**
   * Get construction
   *
   * @return construction
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CONSTRUCTION)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getConstruction() {
    return construction;
  }


  @JsonProperty(JSON_PROPERTY_CONSTRUCTION)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setConstruction(Boolean construction) {
    this.construction = construction;
  }


  public CarrierCargoHauledDto machineryLargeObjects(Boolean machineryLargeObjects) {

    this.machineryLargeObjects = machineryLargeObjects;
    return this;
  }

  /**
   * Get machineryLargeObjects
   *
   * @return machineryLargeObjects
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_MACHINERY_LARGE_OBJECTS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getMachineryLargeObjects() {
    return machineryLargeObjects;
  }


  @JsonProperty(JSON_PROPERTY_MACHINERY_LARGE_OBJECTS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setMachineryLargeObjects(Boolean machineryLargeObjects) {
    this.machineryLargeObjects = machineryLargeObjects;
  }


  public CarrierCargoHauledDto garbageRefuseTrash(Boolean garbageRefuseTrash) {

    this.garbageRefuseTrash = garbageRefuseTrash;
    return this;
  }

  /**
   * Get garbageRefuseTrash
   *
   * @return garbageRefuseTrash
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_GARBAGE_REFUSE_TRASH)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getGarbageRefuseTrash() {
    return garbageRefuseTrash;
  }


  @JsonProperty(JSON_PROPERTY_GARBAGE_REFUSE_TRASH)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setGarbageRefuseTrash(Boolean garbageRefuseTrash) {
    this.garbageRefuseTrash = garbageRefuseTrash;
  }


  public CarrierCargoHauledDto waterWell(Boolean waterWell) {

    this.waterWell = waterWell;
    return this;
  }

  /**
   * Get waterWell
   *
   * @return waterWell
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_WATER_WELL)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getWaterWell() {
    return waterWell;
  }


  @JsonProperty(JSON_PROPERTY_WATER_WELL)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setWaterWell(Boolean waterWell) {
    this.waterWell = waterWell;
  }


  public CarrierCargoHauledDto freshProduce(Boolean freshProduce) {

    this.freshProduce = freshProduce;
    return this;
  }

  /**
   * Get freshProduce
   *
   * @return freshProduce
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_FRESH_PRODUCE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getFreshProduce() {
    return freshProduce;
  }


  @JsonProperty(JSON_PROPERTY_FRESH_PRODUCE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setFreshProduce(Boolean freshProduce) {
    this.freshProduce = freshProduce;
  }


  public CarrierCargoHauledDto usMail(Boolean usMail) {

    this.usMail = usMail;
    return this;
  }

  /**
   * Get usMail
   *
   * @return usMail
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_US_MAIL)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getUsMail() {
    return usMail;
  }


  @JsonProperty(JSON_PROPERTY_US_MAIL)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setUsMail(Boolean usMail) {
    this.usMail = usMail;
  }


  public CarrierCargoHauledDto other(String other) {

    this.other = other;
    return this;
  }

  /**
   * Get other
   *
   * @return other
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_OTHER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getOther() {
    return other;
  }


  @JsonProperty(JSON_PROPERTY_OTHER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setOther(String other) {
    this.other = other;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    CarrierCargoHauledDto carrierCargoHauledDto = (CarrierCargoHauledDto) o;
    return Objects.equals(this.generalFreight, carrierCargoHauledDto.generalFreight) && Objects.equals(this.liquidsGas, carrierCargoHauledDto.liquidsGas)
           && Objects.equals(this.chemicals, carrierCargoHauledDto.chemicals) && Objects.equals(this.householdGoods, carrierCargoHauledDto.householdGoods)
           && Objects.equals(this.intermodalContainers, carrierCargoHauledDto.intermodalContainers) && Objects.equals(this.commoditiesDryBulk,
        carrierCargoHauledDto.commoditiesDryBulk) && Objects.equals(this.metalSheetsCoilsRolls, carrierCargoHauledDto.metalSheetsCoilsRolls) && Objects.equals(
        this.passengers, carrierCargoHauledDto.passengers) && Objects.equals(this.refrigeratedFood, carrierCargoHauledDto.refrigeratedFood) && Objects.equals(
        this.motorVehicles, carrierCargoHauledDto.motorVehicles) && Objects.equals(this.oilfieldEquipment, carrierCargoHauledDto.oilfieldEquipment)
           && Objects.equals(this.beverages, carrierCargoHauledDto.beverages) && Objects.equals(this.drivewayTowaway, carrierCargoHauledDto.drivewayTowaway)
           && Objects.equals(this.livestockContainers, carrierCargoHauledDto.livestockContainers) && Objects.equals(this.paperProducts,
        carrierCargoHauledDto.paperProducts) && Objects.equals(this.logsPolesBeamsLumber, carrierCargoHauledDto.logsPolesBeamsLumber) && Objects.equals(
        this.grainFeedHay, carrierCargoHauledDto.grainFeedHay) && Objects.equals(this.utility, carrierCargoHauledDto.utility) && Objects.equals(
        this.buildingMaterials, carrierCargoHauledDto.buildingMaterials) && Objects.equals(this.coalCoke, carrierCargoHauledDto.coalCoke) && Objects.equals(
        this.farmSupplies, carrierCargoHauledDto.farmSupplies) && Objects.equals(this.mobileHomes, carrierCargoHauledDto.mobileHomes) && Objects.equals(
        this.meat, carrierCargoHauledDto.meat) && Objects.equals(this.construction, carrierCargoHauledDto.construction) && Objects.equals(
        this.machineryLargeObjects, carrierCargoHauledDto.machineryLargeObjects) && Objects.equals(this.garbageRefuseTrash,
        carrierCargoHauledDto.garbageRefuseTrash) && Objects.equals(this.waterWell, carrierCargoHauledDto.waterWell) && Objects.equals(this.freshProduce,
        carrierCargoHauledDto.freshProduce) && Objects.equals(this.usMail, carrierCargoHauledDto.usMail) && Objects.equals(this.other,
        carrierCargoHauledDto.other);
  }

  @Override
  public int hashCode() {
    return Objects.hash(generalFreight, liquidsGas, chemicals, householdGoods, intermodalContainers, commoditiesDryBulk, metalSheetsCoilsRolls, passengers,
        refrigeratedFood, motorVehicles, oilfieldEquipment, beverages, drivewayTowaway, livestockContainers, paperProducts, logsPolesBeamsLumber, grainFeedHay,
        utility, buildingMaterials, coalCoke, farmSupplies, mobileHomes, meat, construction, machineryLargeObjects, garbageRefuseTrash, waterWell, freshProduce,
        usMail, other);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class CarrierCargoHauledDto {\n");
    sb.append("    generalFreight: ").append(toIndentedString(generalFreight)).append("\n");
    sb.append("    liquidsGas: ").append(toIndentedString(liquidsGas)).append("\n");
    sb.append("    chemicals: ").append(toIndentedString(chemicals)).append("\n");
    sb.append("    householdGoods: ").append(toIndentedString(householdGoods)).append("\n");
    sb.append("    intermodalContainers: ").append(toIndentedString(intermodalContainers)).append("\n");
    sb.append("    commoditiesDryBulk: ").append(toIndentedString(commoditiesDryBulk)).append("\n");
    sb.append("    metalSheetsCoilsRolls: ").append(toIndentedString(metalSheetsCoilsRolls)).append("\n");
    sb.append("    passengers: ").append(toIndentedString(passengers)).append("\n");
    sb.append("    refrigeratedFood: ").append(toIndentedString(refrigeratedFood)).append("\n");
    sb.append("    motorVehicles: ").append(toIndentedString(motorVehicles)).append("\n");
    sb.append("    oilfieldEquipment: ").append(toIndentedString(oilfieldEquipment)).append("\n");
    sb.append("    beverages: ").append(toIndentedString(beverages)).append("\n");
    sb.append("    drivewayTowaway: ").append(toIndentedString(drivewayTowaway)).append("\n");
    sb.append("    livestockContainers: ").append(toIndentedString(livestockContainers)).append("\n");
    sb.append("    paperProducts: ").append(toIndentedString(paperProducts)).append("\n");
    sb.append("    logsPolesBeamsLumber: ").append(toIndentedString(logsPolesBeamsLumber)).append("\n");
    sb.append("    grainFeedHay: ").append(toIndentedString(grainFeedHay)).append("\n");
    sb.append("    utility: ").append(toIndentedString(utility)).append("\n");
    sb.append("    buildingMaterials: ").append(toIndentedString(buildingMaterials)).append("\n");
    sb.append("    coalCoke: ").append(toIndentedString(coalCoke)).append("\n");
    sb.append("    farmSupplies: ").append(toIndentedString(farmSupplies)).append("\n");
    sb.append("    mobileHomes: ").append(toIndentedString(mobileHomes)).append("\n");
    sb.append("    meat: ").append(toIndentedString(meat)).append("\n");
    sb.append("    construction: ").append(toIndentedString(construction)).append("\n");
    sb.append("    machineryLargeObjects: ").append(toIndentedString(machineryLargeObjects)).append("\n");
    sb.append("    garbageRefuseTrash: ").append(toIndentedString(garbageRefuseTrash)).append("\n");
    sb.append("    waterWell: ").append(toIndentedString(waterWell)).append("\n");
    sb.append("    freshProduce: ").append(toIndentedString(freshProduce)).append("\n");
    sb.append("    usMail: ").append(toIndentedString(usMail)).append("\n");
    sb.append("    other: ").append(toIndentedString(other)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

