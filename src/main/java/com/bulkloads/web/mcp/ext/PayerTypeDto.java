package com.bulkloads.web.mcp.ext;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

/**
 * PayerTypeDto
 */
@JsonPropertyOrder({PayerTypeDto.JSON_PROPERTY_PAYER_TYPE_I_D, PayerTypeDto.JSON_PROPERTY_NAME})

public class PayerTypeDto {

  public static final String JSON_PROPERTY_PAYER_TYPE_I_D = "PayerTypeID";
  public static final String JSON_PROPERTY_NAME = "Name";
  private Integer payerTypeID;
  private String name;

  public PayerTypeDto() {
  }

  public PayerTypeDto payerTypeID(Integer payerTypeID) {

    this.payerTypeID = payerTypeID;
    return this;
  }

  /**
   * Get payerTypeID
   *
   * @return payerTypeID
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_PAYER_TYPE_I_D)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Integer getPayerTypeID() {
    return payerTypeID;
  }


  @JsonProperty(JSON_PROPERTY_PAYER_TYPE_I_D)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setPayerTypeID(Integer payerTypeID) {
    this.payerTypeID = payerTypeID;
  }


  public PayerTypeDto name(String name) {

    this.name = name;
    return this;
  }

  /**
   * Get name
   *
   * @return name
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getName() {
    return name;
  }


  @JsonProperty(JSON_PROPERTY_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setName(String name) {
    this.name = name;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PayerTypeDto payerTypeDto = (PayerTypeDto) o;
    return Objects.equals(this.payerTypeID, payerTypeDto.payerTypeID) && Objects.equals(this.name, payerTypeDto.name);
  }

  @Override
  public int hashCode() {
    return Objects.hash(payerTypeID, name);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PayerTypeDto {\n");
    sb.append("    payerTypeID: ").append(toIndentedString(payerTypeID)).append("\n");
    sb.append("    name: ").append(toIndentedString(name)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

