package com.bulkloads.web.mcp.ext;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

/**
 * CarrierCertificationDto
 */
@JsonPropertyOrder({CarrierCertificationDto.JSON_PROPERTY_HAZMAT, CarrierCertificationDto.JSON_PROPERTY_HAZMAT_NUMBER,
    CarrierCertificationDto.JSON_PROPERTY_SMART_WAY, CarrierCertificationDto.JSON_PROPERTY_C_A_R_B, CarrierCertificationDto.JSON_PROPERTY_T_W_I_C,
    CarrierCertificationDto.JSON_PROPERTY_CT_P_A_T_CERTIFIED, CarrierCertificationDto.JSON_PROPERTY_CT_P_A_T_S_V_I_NUMBER,
    CarrierCertificationDto.JSON_PROPERTY_TANKER_ENDORSED, CarrierCertificationDto.JSON_PROPERTY_TANKER_ENDORSED_NUM_OF_DRIVERS,
    CarrierCertificationDto.JSON_PROPERTY_C_B_P, CarrierCertificationDto.JSON_PROPERTY_C_B_S_A, CarrierCertificationDto.JSON_PROPERTY_A_N_A_M,
    CarrierCertificationDto.JSON_PROPERTY_A_C_E, CarrierCertificationDto.JSON_PROPERTY_A_C_I, CarrierCertificationDto.JSON_PROPERTY_C_S_A,
    CarrierCertificationDto.JSON_PROPERTY_F_A_S_T, CarrierCertificationDto.JSON_PROPERTY_P_I_P})

public class CarrierCertificationDto {

  public static final String JSON_PROPERTY_HAZMAT = "Hazmat";
  public static final String JSON_PROPERTY_HAZMAT_NUMBER = "HazmatNumber";
  public static final String JSON_PROPERTY_SMART_WAY = "SmartWay";
  public static final String JSON_PROPERTY_C_A_R_B = "CARB";
  public static final String JSON_PROPERTY_T_W_I_C = "TWIC";
  public static final String JSON_PROPERTY_CT_P_A_T_CERTIFIED = "CTPATCertified";
  public static final String JSON_PROPERTY_CT_P_A_T_S_V_I_NUMBER = "CTPATSVINumber";
  public static final String JSON_PROPERTY_TANKER_ENDORSED = "TankerEndorsed";
  public static final String JSON_PROPERTY_TANKER_ENDORSED_NUM_OF_DRIVERS = "TankerEndorsedNumOfDrivers";
  public static final String JSON_PROPERTY_C_B_P = "CBP";
  public static final String JSON_PROPERTY_C_B_S_A = "CBSA";
  public static final String JSON_PROPERTY_A_N_A_M = "ANAM";
  public static final String JSON_PROPERTY_A_C_E = "ACE";
  public static final String JSON_PROPERTY_A_C_I = "ACI";
  public static final String JSON_PROPERTY_C_S_A = "CSA";
  public static final String JSON_PROPERTY_F_A_S_T = "FAST";
  public static final String JSON_PROPERTY_P_I_P = "PIP";
  private Boolean hazmat;
  private String hazmatNumber;
  private Boolean smartWay;
  private Boolean CARB;
  private Boolean TWIC;
  private Boolean ctPATCertified;
  private String ctPATSVINumber;
  private Boolean tankerEndorsed;
  private Integer tankerEndorsedNumOfDrivers;
  private Boolean CBP;
  private Boolean CBSA;
  private Boolean ANAM;
  private Boolean ACE;
  private Boolean ACI;
  private Boolean CSA;
  private Boolean FAST;
  private Boolean PIP;

  public CarrierCertificationDto() {
  }

  public CarrierCertificationDto hazmat(Boolean hazmat) {

    this.hazmat = hazmat;
    return this;
  }

  /**
   * Get hazmat
   *
   * @return hazmat
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_HAZMAT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getHazmat() {
    return hazmat;
  }


  @JsonProperty(JSON_PROPERTY_HAZMAT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setHazmat(Boolean hazmat) {
    this.hazmat = hazmat;
  }


  public CarrierCertificationDto hazmatNumber(String hazmatNumber) {

    this.hazmatNumber = hazmatNumber;
    return this;
  }

  /**
   * Get hazmatNumber
   *
   * @return hazmatNumber
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_HAZMAT_NUMBER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getHazmatNumber() {
    return hazmatNumber;
  }


  @JsonProperty(JSON_PROPERTY_HAZMAT_NUMBER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setHazmatNumber(String hazmatNumber) {
    this.hazmatNumber = hazmatNumber;
  }


  public CarrierCertificationDto smartWay(Boolean smartWay) {

    this.smartWay = smartWay;
    return this;
  }

  /**
   * Get smartWay
   *
   * @return smartWay
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_SMART_WAY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getSmartWay() {
    return smartWay;
  }


  @JsonProperty(JSON_PROPERTY_SMART_WAY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setSmartWay(Boolean smartWay) {
    this.smartWay = smartWay;
  }


  public CarrierCertificationDto CARB(Boolean CARB) {

    this.CARB = CARB;
    return this;
  }

  /**
   * Get CARB
   *
   * @return CARB
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_C_A_R_B)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getCARB() {
    return CARB;
  }


  @JsonProperty(JSON_PROPERTY_C_A_R_B)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCARB(Boolean CARB) {
    this.CARB = CARB;
  }


  public CarrierCertificationDto TWIC(Boolean TWIC) {

    this.TWIC = TWIC;
    return this;
  }

  /**
   * Get TWIC
   *
   * @return TWIC
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_T_W_I_C)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getTWIC() {
    return TWIC;
  }


  @JsonProperty(JSON_PROPERTY_T_W_I_C)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setTWIC(Boolean TWIC) {
    this.TWIC = TWIC;
  }


  public CarrierCertificationDto ctPATCertified(Boolean ctPATCertified) {

    this.ctPATCertified = ctPATCertified;
    return this;
  }

  /**
   * Get ctPATCertified
   *
   * @return ctPATCertified
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CT_P_A_T_CERTIFIED)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getCtPATCertified() {
    return ctPATCertified;
  }


  @JsonProperty(JSON_PROPERTY_CT_P_A_T_CERTIFIED)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCtPATCertified(Boolean ctPATCertified) {
    this.ctPATCertified = ctPATCertified;
  }


  public CarrierCertificationDto ctPATSVINumber(String ctPATSVINumber) {

    this.ctPATSVINumber = ctPATSVINumber;
    return this;
  }

  /**
   * Get ctPATSVINumber
   *
   * @return ctPATSVINumber
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CT_P_A_T_S_V_I_NUMBER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getCtPATSVINumber() {
    return ctPATSVINumber;
  }


  @JsonProperty(JSON_PROPERTY_CT_P_A_T_S_V_I_NUMBER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCtPATSVINumber(String ctPATSVINumber) {
    this.ctPATSVINumber = ctPATSVINumber;
  }


  public CarrierCertificationDto tankerEndorsed(Boolean tankerEndorsed) {

    this.tankerEndorsed = tankerEndorsed;
    return this;
  }

  /**
   * Get tankerEndorsed
   *
   * @return tankerEndorsed
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_TANKER_ENDORSED)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getTankerEndorsed() {
    return tankerEndorsed;
  }


  @JsonProperty(JSON_PROPERTY_TANKER_ENDORSED)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setTankerEndorsed(Boolean tankerEndorsed) {
    this.tankerEndorsed = tankerEndorsed;
  }


  public CarrierCertificationDto tankerEndorsedNumOfDrivers(Integer tankerEndorsedNumOfDrivers) {

    this.tankerEndorsedNumOfDrivers = tankerEndorsedNumOfDrivers;
    return this;
  }

  /**
   * Get tankerEndorsedNumOfDrivers
   *
   * @return tankerEndorsedNumOfDrivers
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_TANKER_ENDORSED_NUM_OF_DRIVERS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Integer getTankerEndorsedNumOfDrivers() {
    return tankerEndorsedNumOfDrivers;
  }


  @JsonProperty(JSON_PROPERTY_TANKER_ENDORSED_NUM_OF_DRIVERS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setTankerEndorsedNumOfDrivers(Integer tankerEndorsedNumOfDrivers) {
    this.tankerEndorsedNumOfDrivers = tankerEndorsedNumOfDrivers;
  }


  public CarrierCertificationDto CBP(Boolean CBP) {

    this.CBP = CBP;
    return this;
  }

  /**
   * Get CBP
   *
   * @return CBP
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_C_B_P)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getCBP() {
    return CBP;
  }


  @JsonProperty(JSON_PROPERTY_C_B_P)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCBP(Boolean CBP) {
    this.CBP = CBP;
  }


  public CarrierCertificationDto CBSA(Boolean CBSA) {

    this.CBSA = CBSA;
    return this;
  }

  /**
   * Get CBSA
   *
   * @return CBSA
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_C_B_S_A)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getCBSA() {
    return CBSA;
  }


  @JsonProperty(JSON_PROPERTY_C_B_S_A)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCBSA(Boolean CBSA) {
    this.CBSA = CBSA;
  }


  public CarrierCertificationDto ANAM(Boolean ANAM) {

    this.ANAM = ANAM;
    return this;
  }

  /**
   * Get ANAM
   *
   * @return ANAM
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_A_N_A_M)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getANAM() {
    return ANAM;
  }


  @JsonProperty(JSON_PROPERTY_A_N_A_M)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setANAM(Boolean ANAM) {
    this.ANAM = ANAM;
  }


  public CarrierCertificationDto ACE(Boolean ACE) {

    this.ACE = ACE;
    return this;
  }

  /**
   * Get ACE
   *
   * @return ACE
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_A_C_E)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getACE() {
    return ACE;
  }


  @JsonProperty(JSON_PROPERTY_A_C_E)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setACE(Boolean ACE) {
    this.ACE = ACE;
  }


  public CarrierCertificationDto ACI(Boolean ACI) {

    this.ACI = ACI;
    return this;
  }

  /**
   * Get ACI
   *
   * @return ACI
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_A_C_I)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getACI() {
    return ACI;
  }


  @JsonProperty(JSON_PROPERTY_A_C_I)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setACI(Boolean ACI) {
    this.ACI = ACI;
  }


  public CarrierCertificationDto CSA(Boolean CSA) {

    this.CSA = CSA;
    return this;
  }

  /**
   * Get CSA
   *
   * @return CSA
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_C_S_A)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getCSA() {
    return CSA;
  }


  @JsonProperty(JSON_PROPERTY_C_S_A)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCSA(Boolean CSA) {
    this.CSA = CSA;
  }


  public CarrierCertificationDto FAST(Boolean FAST) {

    this.FAST = FAST;
    return this;
  }

  /**
   * Get FAST
   *
   * @return FAST
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_F_A_S_T)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getFAST() {
    return FAST;
  }


  @JsonProperty(JSON_PROPERTY_F_A_S_T)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setFAST(Boolean FAST) {
    this.FAST = FAST;
  }


  public CarrierCertificationDto PIP(Boolean PIP) {

    this.PIP = PIP;
    return this;
  }

  /**
   * Get PIP
   *
   * @return PIP
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_P_I_P)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getPIP() {
    return PIP;
  }


  @JsonProperty(JSON_PROPERTY_P_I_P)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setPIP(Boolean PIP) {
    this.PIP = PIP;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    CarrierCertificationDto carrierCertificationDto = (CarrierCertificationDto) o;
    return Objects.equals(this.hazmat, carrierCertificationDto.hazmat) && Objects.equals(this.hazmatNumber, carrierCertificationDto.hazmatNumber)
           && Objects.equals(this.smartWay, carrierCertificationDto.smartWay) && Objects.equals(this.CARB, carrierCertificationDto.CARB) && Objects.equals(
        this.TWIC, carrierCertificationDto.TWIC) && Objects.equals(this.ctPATCertified, carrierCertificationDto.ctPATCertified) && Objects.equals(
        this.ctPATSVINumber, carrierCertificationDto.ctPATSVINumber) && Objects.equals(this.tankerEndorsed, carrierCertificationDto.tankerEndorsed)
           && Objects.equals(this.tankerEndorsedNumOfDrivers, carrierCertificationDto.tankerEndorsedNumOfDrivers) && Objects.equals(this.CBP,
        carrierCertificationDto.CBP) && Objects.equals(this.CBSA, carrierCertificationDto.CBSA) && Objects.equals(this.ANAM, carrierCertificationDto.ANAM)
           && Objects.equals(this.ACE, carrierCertificationDto.ACE) && Objects.equals(this.ACI, carrierCertificationDto.ACI) && Objects.equals(this.CSA,
        carrierCertificationDto.CSA) && Objects.equals(this.FAST, carrierCertificationDto.FAST) && Objects.equals(this.PIP, carrierCertificationDto.PIP);
  }

  @Override
  public int hashCode() {
    return Objects.hash(hazmat, hazmatNumber, smartWay, CARB, TWIC, ctPATCertified, ctPATSVINumber, tankerEndorsed, tankerEndorsedNumOfDrivers, CBP, CBSA, ANAM,
        ACE, ACI, CSA, FAST, PIP);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class CarrierCertificationDto {\n");
    sb.append("    hazmat: ").append(toIndentedString(hazmat)).append("\n");
    sb.append("    hazmatNumber: ").append(toIndentedString(hazmatNumber)).append("\n");
    sb.append("    smartWay: ").append(toIndentedString(smartWay)).append("\n");
    sb.append("    CARB: ").append(toIndentedString(CARB)).append("\n");
    sb.append("    TWIC: ").append(toIndentedString(TWIC)).append("\n");
    sb.append("    ctPATCertified: ").append(toIndentedString(ctPATCertified)).append("\n");
    sb.append("    ctPATSVINumber: ").append(toIndentedString(ctPATSVINumber)).append("\n");
    sb.append("    tankerEndorsed: ").append(toIndentedString(tankerEndorsed)).append("\n");
    sb.append("    tankerEndorsedNumOfDrivers: ").append(toIndentedString(tankerEndorsedNumOfDrivers)).append("\n");
    sb.append("    CBP: ").append(toIndentedString(CBP)).append("\n");
    sb.append("    CBSA: ").append(toIndentedString(CBSA)).append("\n");
    sb.append("    ANAM: ").append(toIndentedString(ANAM)).append("\n");
    sb.append("    ACE: ").append(toIndentedString(ACE)).append("\n");
    sb.append("    ACI: ").append(toIndentedString(ACI)).append("\n");
    sb.append("    CSA: ").append(toIndentedString(CSA)).append("\n");
    sb.append("    FAST: ").append(toIndentedString(FAST)).append("\n");
    sb.append("    PIP: ").append(toIndentedString(PIP)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

