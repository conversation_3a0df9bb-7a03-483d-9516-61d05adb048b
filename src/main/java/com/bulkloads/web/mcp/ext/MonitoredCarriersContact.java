package com.bulkloads.web.mcp.ext;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

/**
 * MonitoredCarriersContact
 */
@JsonPropertyOrder({MonitoredCarriersContact.JSON_PROPERTY_FIRST_NAME, MonitoredCarriersContact.JSON_PROPERTY_LAST_NAME,
    MonitoredCarriersContact.JSON_PROPERTY_TITLE, MonitoredCarriersContact.JSON_PROPERTY_PHONE, MonitoredCarriersContact.JSON_PROPERTY_EMAIL,
    MonitoredCarriersContact.JSON_PROPERTY_AUTHORIZED_FOR_PACKETS, MonitoredCarriersContact.JSON_PROPERTY_VERIFICATION_STATUS})

public class MonitoredCarriersContact {

  public static final String JSON_PROPERTY_FIRST_NAME = "FirstName";
  public static final String JSON_PROPERTY_LAST_NAME = "LastName";
  public static final String JSON_PROPERTY_TITLE = "Title";
  public static final String JSON_PROPERTY_PHONE = "Phone";
  public static final String JSON_PROPERTY_EMAIL = "Email";
  public static final String JSON_PROPERTY_AUTHORIZED_FOR_PACKETS = "AuthorizedForPackets";
  public static final String JSON_PROPERTY_VERIFICATION_STATUS = "VerificationStatus";
  private String firstName;
  private String lastName;
  private String title;
  private String phone;
  private String email;
  private Boolean authorizedForPackets;
  private String verificationStatus;

  public MonitoredCarriersContact() {
  }

  public MonitoredCarriersContact firstName(String firstName) {

    this.firstName = firstName;
    return this;
  }

  /**
   * Get firstName
   *
   * @return firstName
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_FIRST_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getFirstName() {
    return firstName;
  }


  @JsonProperty(JSON_PROPERTY_FIRST_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setFirstName(String firstName) {
    this.firstName = firstName;
  }


  public MonitoredCarriersContact lastName(String lastName) {

    this.lastName = lastName;
    return this;
  }

  /**
   * Get lastName
   *
   * @return lastName
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_LAST_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getLastName() {
    return lastName;
  }


  @JsonProperty(JSON_PROPERTY_LAST_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setLastName(String lastName) {
    this.lastName = lastName;
  }


  public MonitoredCarriersContact title(String title) {

    this.title = title;
    return this;
  }

  /**
   * Get title
   *
   * @return title
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_TITLE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getTitle() {
    return title;
  }


  @JsonProperty(JSON_PROPERTY_TITLE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setTitle(String title) {
    this.title = title;
  }


  public MonitoredCarriersContact phone(String phone) {

    this.phone = phone;
    return this;
  }

  /**
   * Get phone
   *
   * @return phone
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_PHONE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getPhone() {
    return phone;
  }


  @JsonProperty(JSON_PROPERTY_PHONE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setPhone(String phone) {
    this.phone = phone;
  }


  public MonitoredCarriersContact email(String email) {

    this.email = email;
    return this;
  }

  /**
   * Get email
   *
   * @return email
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_EMAIL)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getEmail() {
    return email;
  }


  @JsonProperty(JSON_PROPERTY_EMAIL)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setEmail(String email) {
    this.email = email;
  }


  public MonitoredCarriersContact authorizedForPackets(Boolean authorizedForPackets) {

    this.authorizedForPackets = authorizedForPackets;
    return this;
  }

  /**
   * Get authorizedForPackets
   *
   * @return authorizedForPackets
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_AUTHORIZED_FOR_PACKETS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getAuthorizedForPackets() {
    return authorizedForPackets;
  }


  @JsonProperty(JSON_PROPERTY_AUTHORIZED_FOR_PACKETS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setAuthorizedForPackets(Boolean authorizedForPackets) {
    this.authorizedForPackets = authorizedForPackets;
  }


  public MonitoredCarriersContact verificationStatus(String verificationStatus) {

    this.verificationStatus = verificationStatus;
    return this;
  }

  /**
   * Get verificationStatus
   *
   * @return verificationStatus
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_VERIFICATION_STATUS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getVerificationStatus() {
    return verificationStatus;
  }


  @JsonProperty(JSON_PROPERTY_VERIFICATION_STATUS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setVerificationStatus(String verificationStatus) {
    this.verificationStatus = verificationStatus;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    MonitoredCarriersContact monitoredCarriersContact = (MonitoredCarriersContact) o;
    return Objects.equals(this.firstName, monitoredCarriersContact.firstName) && Objects.equals(this.lastName, monitoredCarriersContact.lastName)
           && Objects.equals(this.title, monitoredCarriersContact.title) && Objects.equals(this.phone, monitoredCarriersContact.phone) && Objects.equals(
        this.email, monitoredCarriersContact.email) && Objects.equals(this.authorizedForPackets, monitoredCarriersContact.authorizedForPackets)
           && Objects.equals(this.verificationStatus, monitoredCarriersContact.verificationStatus);
  }

  @Override
  public int hashCode() {
    return Objects.hash(firstName, lastName, title, phone, email, authorizedForPackets, verificationStatus);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class MonitoredCarriersContact {\n");
    sb.append("    firstName: ").append(toIndentedString(firstName)).append("\n");
    sb.append("    lastName: ").append(toIndentedString(lastName)).append("\n");
    sb.append("    title: ").append(toIndentedString(title)).append("\n");
    sb.append("    phone: ").append(toIndentedString(phone)).append("\n");
    sb.append("    email: ").append(toIndentedString(email)).append("\n");
    sb.append("    authorizedForPackets: ").append(toIndentedString(authorizedForPackets)).append("\n");
    sb.append("    verificationStatus: ").append(toIndentedString(verificationStatus)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

