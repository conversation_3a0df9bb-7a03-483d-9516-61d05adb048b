package com.bulkloads.web.mcp.ext;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

/**
 * MonitoredCarrierDataDto
 */
@JsonPropertyOrder({MonitoredCarrierDataDto.JSON_PROPERTY_PAGE_NUMBER, MonitoredCarrierDataDto.JSON_PROPERTY_PAGE_SIZE,
    MonitoredCarrierDataDto.JSON_PROPERTY_TOTAL_PAGES, MonitoredCarrierDataDto.JSON_PROPERTY_TOTAL_COUNT, MonitoredCarrierDataDto.JSON_PROPERTY_SUCCEEDED,
    MonitoredCarrierDataDto.JSON_PROPERTY_MESSAGE, MonitoredCarrierDataDto.JSON_PROPERTY_DATA})

public class MonitoredCarrierDataDto {

  public static final String JSON_PROPERTY_PAGE_NUMBER = "pageNumber";
  public static final String JSON_PROPERTY_PAGE_SIZE = "pageSize";
  public static final String JSON_PROPERTY_TOTAL_PAGES = "totalPages";
  public static final String JSON_PROPERTY_TOTAL_COUNT = "totalCount";
  public static final String JSON_PROPERTY_SUCCEEDED = "succeeded";
  public static final String JSON_PROPERTY_MESSAGE = "message";
  public static final String JSON_PROPERTY_DATA = "data";
  private Integer pageNumber;
  private Integer pageSize;
  private Integer totalPages;
  private Integer totalCount;
  private Boolean succeeded;
  private String message;
  private List<MyCarrierPacketsApiFMCSACarrierDetails> data;

  public MonitoredCarrierDataDto() {
  }

  public MonitoredCarrierDataDto pageNumber(Integer pageNumber) {

    this.pageNumber = pageNumber;
    return this;
  }

  /**
   * Get pageNumber
   *
   * @return pageNumber
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_PAGE_NUMBER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Integer getPageNumber() {
    return pageNumber;
  }


  @JsonProperty(JSON_PROPERTY_PAGE_NUMBER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setPageNumber(Integer pageNumber) {
    this.pageNumber = pageNumber;
  }


  public MonitoredCarrierDataDto pageSize(Integer pageSize) {

    this.pageSize = pageSize;
    return this;
  }

  /**
   * Get pageSize
   *
   * @return pageSize
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_PAGE_SIZE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Integer getPageSize() {
    return pageSize;
  }


  @JsonProperty(JSON_PROPERTY_PAGE_SIZE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setPageSize(Integer pageSize) {
    this.pageSize = pageSize;
  }


  public MonitoredCarrierDataDto totalPages(Integer totalPages) {

    this.totalPages = totalPages;
    return this;
  }

  /**
   * Get totalPages
   *
   * @return totalPages
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_TOTAL_PAGES)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Integer getTotalPages() {
    return totalPages;
  }


  @JsonProperty(JSON_PROPERTY_TOTAL_PAGES)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setTotalPages(Integer totalPages) {
    this.totalPages = totalPages;
  }


  public MonitoredCarrierDataDto totalCount(Integer totalCount) {

    this.totalCount = totalCount;
    return this;
  }

  /**
   * Get totalCount
   *
   * @return totalCount
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_TOTAL_COUNT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Integer getTotalCount() {
    return totalCount;
  }


  @JsonProperty(JSON_PROPERTY_TOTAL_COUNT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setTotalCount(Integer totalCount) {
    this.totalCount = totalCount;
  }


  public MonitoredCarrierDataDto succeeded(Boolean succeeded) {

    this.succeeded = succeeded;
    return this;
  }

  /**
   * Get succeeded
   *
   * @return succeeded
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_SUCCEEDED)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getSucceeded() {
    return succeeded;
  }


  @JsonProperty(JSON_PROPERTY_SUCCEEDED)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setSucceeded(Boolean succeeded) {
    this.succeeded = succeeded;
  }


  public MonitoredCarrierDataDto message(String message) {

    this.message = message;
    return this;
  }

  /**
   * Get message
   *
   * @return message
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_MESSAGE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getMessage() {
    return message;
  }


  @JsonProperty(JSON_PROPERTY_MESSAGE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setMessage(String message) {
    this.message = message;
  }


  public MonitoredCarrierDataDto data(List<MyCarrierPacketsApiFMCSACarrierDetails> data) {

    this.data = data;
    return this;
  }

  public MonitoredCarrierDataDto addDataItem(MyCarrierPacketsApiFMCSACarrierDetails dataItem) {
    if (this.data == null) {
      this.data = new ArrayList<>();
    }
    this.data.add(dataItem);
    return this;
  }

  /**
   * Get data
   *
   * @return data
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_DATA)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public List<MyCarrierPacketsApiFMCSACarrierDetails> getData() {
    return data;
  }


  @JsonProperty(JSON_PROPERTY_DATA)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setData(List<MyCarrierPacketsApiFMCSACarrierDetails> data) {
    this.data = data;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    MonitoredCarrierDataDto monitoredCarrierDataDto = (MonitoredCarrierDataDto) o;
    return Objects.equals(this.pageNumber, monitoredCarrierDataDto.pageNumber) && Objects.equals(this.pageSize, monitoredCarrierDataDto.pageSize)
           && Objects.equals(this.totalPages, monitoredCarrierDataDto.totalPages) && Objects.equals(this.totalCount, monitoredCarrierDataDto.totalCount)
           && Objects.equals(this.succeeded, monitoredCarrierDataDto.succeeded) && Objects.equals(this.message, monitoredCarrierDataDto.message)
           && Objects.equals(this.data, monitoredCarrierDataDto.data);
  }

  @Override
  public int hashCode() {
    return Objects.hash(pageNumber, pageSize, totalPages, totalCount, succeeded, message, data);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class MonitoredCarrierDataDto {\n");
    sb.append("    pageNumber: ").append(toIndentedString(pageNumber)).append("\n");
    sb.append("    pageSize: ").append(toIndentedString(pageSize)).append("\n");
    sb.append("    totalPages: ").append(toIndentedString(totalPages)).append("\n");
    sb.append("    totalCount: ").append(toIndentedString(totalCount)).append("\n");
    sb.append("    succeeded: ").append(toIndentedString(succeeded)).append("\n");
    sb.append("    message: ").append(toIndentedString(message)).append("\n");
    sb.append("    data: ").append(toIndentedString(data)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

