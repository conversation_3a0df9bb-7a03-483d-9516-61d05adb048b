package com.bulkloads.web.mcp.ext;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * MyCarrierPacketsApiFMCSARiskAssessmentDetail
 */
@JsonPropertyOrder({
    MyCarrierPacketsApiFMCSARiskAssessmentDetail.JSON_PROPERTY_TOTAL_POINTS,
    MyCarrierPacketsApiFMCSARiskAssessmentDetail.JSON_PROPERTY_OVERALL_RATING,
    MyCarrierPacketsApiFMCSARiskAssessmentDetail.JSON_PROPERTY_INFRACTIONS
})
@JsonTypeName("MyCarrierPacketsApi.FMCSA.RiskAssessmentDetail")

public class MyCarrierPacketsApiFMCSARiskAssessmentDetail {

  public static final String JSON_PROPERTY_TOTAL_POINTS = "TotalPoints";
  public static final String JSON_PROPERTY_OVERALL_RATING = "OverallRating";
  public static final String JSON_PROPERTY_INFRACTIONS = "Infractions";
  private Integer totalPoints;
  private String overallRating;
  private List<MyCarrierPacketsApiFMCSARiskAssessmentInfraction> infractions;

  public MyCarrierPacketsApiFMCSARiskAssessmentDetail() {
  }

  public MyCarrierPacketsApiFMCSARiskAssessmentDetail totalPoints(Integer totalPoints) {

    this.totalPoints = totalPoints;
    return this;
  }

  /**
   * Get totalPoints
   *
   * @return totalPoints
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_TOTAL_POINTS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Integer getTotalPoints() {
    return totalPoints;
  }


  @JsonProperty(JSON_PROPERTY_TOTAL_POINTS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setTotalPoints(Integer totalPoints) {
    this.totalPoints = totalPoints;
  }


  public MyCarrierPacketsApiFMCSARiskAssessmentDetail overallRating(String overallRating) {

    this.overallRating = overallRating;
    return this;
  }

  /**
   * Get overallRating
   *
   * @return overallRating
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_OVERALL_RATING)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getOverallRating() {
    return overallRating;
  }


  @JsonProperty(JSON_PROPERTY_OVERALL_RATING)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setOverallRating(String overallRating) {
    this.overallRating = overallRating;
  }


  public MyCarrierPacketsApiFMCSARiskAssessmentDetail infractions(List<MyCarrierPacketsApiFMCSARiskAssessmentInfraction> infractions) {

    this.infractions = infractions;
    return this;
  }

  public MyCarrierPacketsApiFMCSARiskAssessmentDetail addInfractionsItem(MyCarrierPacketsApiFMCSARiskAssessmentInfraction infractionsItem) {
    if (this.infractions == null) {
      this.infractions = new ArrayList<>();
    }
    this.infractions.add(infractionsItem);
    return this;
  }

  /**
   * Get infractions
   *
   * @return infractions
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_INFRACTIONS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public List<MyCarrierPacketsApiFMCSARiskAssessmentInfraction> getInfractions() {
    return infractions;
  }


  @JsonProperty(JSON_PROPERTY_INFRACTIONS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setInfractions(List<MyCarrierPacketsApiFMCSARiskAssessmentInfraction> infractions) {
    this.infractions = infractions;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    MyCarrierPacketsApiFMCSARiskAssessmentDetail myCarrierPacketsApiFMCSARiskAssessmentDetail = (MyCarrierPacketsApiFMCSARiskAssessmentDetail) o;
    return Objects.equals(this.totalPoints, myCarrierPacketsApiFMCSARiskAssessmentDetail.totalPoints) &&
           Objects.equals(this.overallRating, myCarrierPacketsApiFMCSARiskAssessmentDetail.overallRating) &&
           Objects.equals(this.infractions, myCarrierPacketsApiFMCSARiskAssessmentDetail.infractions);
  }

  @Override
  public int hashCode() {
    return Objects.hash(totalPoints, overallRating, infractions);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class MyCarrierPacketsApiFMCSARiskAssessmentDetail {\n");
    sb.append("    totalPoints: ").append(toIndentedString(totalPoints)).append("\n");
    sb.append("    overallRating: ").append(toIndentedString(overallRating)).append("\n");
    sb.append("    infractions: ").append(toIndentedString(infractions)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

