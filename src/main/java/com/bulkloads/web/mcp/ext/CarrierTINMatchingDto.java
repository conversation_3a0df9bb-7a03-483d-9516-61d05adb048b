package com.bulkloads.web.mcp.ext;

import java.time.LocalDateTime;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

/**
 * CarrierTINMatchingDto
 */
@JsonPropertyOrder({CarrierTINMatchingDto.JSON_PROPERTY_TI_N_TYPE_I_D, CarrierTINMatchingDto.JSON_PROPERTY_T_I_N, CarrierTINMatchingDto.JSON_PROPERTY_TI_N_NAME,
    CarrierTINMatchingDto.JSON_PROPERTY_TI_N_MATCHING_STATUS_I_D, CarrierTINMatchingDto.JSON_PROPERTY_TI_N_MATCHING_RESULT_I_D,
    CarrierTINMatchingDto.JSON_PROPERTY_CREATED_ON_UTC, CarrierTINMatchingDto.JSON_PROPERTY_SUBMITTED_ON_UTC,
    CarrierTINMatchingDto.JSON_PROPERTY_PROCESSED_ON_UTC, CarrierTINMatchingDto.JSON_PROPERTY_CONTACT_EMAIL,
    CarrierTINMatchingDto.JSON_PROPERTY_CONTACT_PHONE_NUMBER, CarrierTINMatchingDto.JSON_PROPERTY_MATCHING_RESULT,
    CarrierTINMatchingDto.JSON_PROPERTY_MATCHING_STATUS})

public class CarrierTINMatchingDto {

  public static final String JSON_PROPERTY_TI_N_TYPE_I_D = "TINTypeID";
  public static final String JSON_PROPERTY_T_I_N = "TIN";
  public static final String JSON_PROPERTY_TI_N_NAME = "TINName";
  public static final String JSON_PROPERTY_TI_N_MATCHING_STATUS_I_D = "TINMatchingStatusID";
  public static final String JSON_PROPERTY_TI_N_MATCHING_RESULT_I_D = "TINMatchingResultID";
  public static final String JSON_PROPERTY_CREATED_ON_UTC = "CreatedOnUtc";
  public static final String JSON_PROPERTY_SUBMITTED_ON_UTC = "SubmittedOnUtc";
  public static final String JSON_PROPERTY_PROCESSED_ON_UTC = "ProcessedOnUtc";
  public static final String JSON_PROPERTY_CONTACT_EMAIL = "ContactEmail";
  public static final String JSON_PROPERTY_CONTACT_PHONE_NUMBER = "ContactPhoneNumber";
  public static final String JSON_PROPERTY_MATCHING_RESULT = "MatchingResult";
  public static final String JSON_PROPERTY_MATCHING_STATUS = "MatchingStatus";
  private Integer tiNTypeID;
  private String TIN;
  private String tiNName;
  private Integer tiNMatchingStatusID;
  private Integer tiNMatchingResultID;
  private LocalDateTime createdOnUtc;
  private LocalDateTime submittedOnUtc;
  private LocalDateTime processedOnUtc;
  private String contactEmail;
  private String contactPhoneNumber;
  private String matchingResult;
  private String matchingStatus;

  public CarrierTINMatchingDto() {
  }

  public CarrierTINMatchingDto tiNTypeID(Integer tiNTypeID) {

    this.tiNTypeID = tiNTypeID;
    return this;
  }

  /**
   * Get tiNTypeID
   *
   * @return tiNTypeID
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_TI_N_TYPE_I_D)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Integer getTiNTypeID() {
    return tiNTypeID;
  }


  @JsonProperty(JSON_PROPERTY_TI_N_TYPE_I_D)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setTiNTypeID(Integer tiNTypeID) {
    this.tiNTypeID = tiNTypeID;
  }


  public CarrierTINMatchingDto TIN(String TIN) {

    this.TIN = TIN;
    return this;
  }

  /**
   * Get TIN
   *
   * @return TIN
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_T_I_N)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getTIN() {
    return TIN;
  }


  @JsonProperty(JSON_PROPERTY_T_I_N)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setTIN(String TIN) {
    this.TIN = TIN;
  }


  public CarrierTINMatchingDto tiNName(String tiNName) {

    this.tiNName = tiNName;
    return this;
  }

  /**
   * Get tiNName
   *
   * @return tiNName
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_TI_N_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getTiNName() {
    return tiNName;
  }


  @JsonProperty(JSON_PROPERTY_TI_N_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setTiNName(String tiNName) {
    this.tiNName = tiNName;
  }


  public CarrierTINMatchingDto tiNMatchingStatusID(Integer tiNMatchingStatusID) {

    this.tiNMatchingStatusID = tiNMatchingStatusID;
    return this;
  }

  /**
   * Get tiNMatchingStatusID
   *
   * @return tiNMatchingStatusID
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_TI_N_MATCHING_STATUS_I_D)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Integer getTiNMatchingStatusID() {
    return tiNMatchingStatusID;
  }


  @JsonProperty(JSON_PROPERTY_TI_N_MATCHING_STATUS_I_D)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setTiNMatchingStatusID(Integer tiNMatchingStatusID) {
    this.tiNMatchingStatusID = tiNMatchingStatusID;
  }


  public CarrierTINMatchingDto tiNMatchingResultID(Integer tiNMatchingResultID) {

    this.tiNMatchingResultID = tiNMatchingResultID;
    return this;
  }

  /**
   * Get tiNMatchingResultID
   *
   * @return tiNMatchingResultID
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_TI_N_MATCHING_RESULT_I_D)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Integer getTiNMatchingResultID() {
    return tiNMatchingResultID;
  }


  @JsonProperty(JSON_PROPERTY_TI_N_MATCHING_RESULT_I_D)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setTiNMatchingResultID(Integer tiNMatchingResultID) {
    this.tiNMatchingResultID = tiNMatchingResultID;
  }


  public CarrierTINMatchingDto createdOnUtc(LocalDateTime createdOnUtc) {

    this.createdOnUtc = createdOnUtc;
    return this;
  }

  /**
   * Get createdOnUtc
   *
   * @return createdOnUtc
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CREATED_ON_UTC)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public LocalDateTime getCreatedOnUtc() {
    return createdOnUtc;
  }


  @JsonProperty(JSON_PROPERTY_CREATED_ON_UTC)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCreatedOnUtc(LocalDateTime createdOnUtc) {
    this.createdOnUtc = createdOnUtc;
  }


  public CarrierTINMatchingDto submittedOnUtc(LocalDateTime submittedOnUtc) {

    this.submittedOnUtc = submittedOnUtc;
    return this;
  }

  /**
   * Get submittedOnUtc
   *
   * @return submittedOnUtc
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_SUBMITTED_ON_UTC)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public LocalDateTime getSubmittedOnUtc() {
    return submittedOnUtc;
  }


  @JsonProperty(JSON_PROPERTY_SUBMITTED_ON_UTC)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setSubmittedOnUtc(LocalDateTime submittedOnUtc) {
    this.submittedOnUtc = submittedOnUtc;
  }


  public CarrierTINMatchingDto processedOnUtc(LocalDateTime processedOnUtc) {

    this.processedOnUtc = processedOnUtc;
    return this;
  }

  /**
   * Get processedOnUtc
   *
   * @return processedOnUtc
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_PROCESSED_ON_UTC)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public LocalDateTime getProcessedOnUtc() {
    return processedOnUtc;
  }


  @JsonProperty(JSON_PROPERTY_PROCESSED_ON_UTC)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setProcessedOnUtc(LocalDateTime processedOnUtc) {
    this.processedOnUtc = processedOnUtc;
  }


  public CarrierTINMatchingDto contactEmail(String contactEmail) {

    this.contactEmail = contactEmail;
    return this;
  }

  /**
   * Get contactEmail
   *
   * @return contactEmail
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CONTACT_EMAIL)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getContactEmail() {
    return contactEmail;
  }


  @JsonProperty(JSON_PROPERTY_CONTACT_EMAIL)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setContactEmail(String contactEmail) {
    this.contactEmail = contactEmail;
  }


  public CarrierTINMatchingDto contactPhoneNumber(String contactPhoneNumber) {

    this.contactPhoneNumber = contactPhoneNumber;
    return this;
  }

  /**
   * Get contactPhoneNumber
   *
   * @return contactPhoneNumber
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CONTACT_PHONE_NUMBER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getContactPhoneNumber() {
    return contactPhoneNumber;
  }


  @JsonProperty(JSON_PROPERTY_CONTACT_PHONE_NUMBER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setContactPhoneNumber(String contactPhoneNumber) {
    this.contactPhoneNumber = contactPhoneNumber;
  }


  public CarrierTINMatchingDto matchingResult(String matchingResult) {

    this.matchingResult = matchingResult;
    return this;
  }

  /**
   * Get matchingResult
   *
   * @return matchingResult
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_MATCHING_RESULT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getMatchingResult() {
    return matchingResult;
  }


  @JsonProperty(JSON_PROPERTY_MATCHING_RESULT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setMatchingResult(String matchingResult) {
    this.matchingResult = matchingResult;
  }


  public CarrierTINMatchingDto matchingStatus(String matchingStatus) {

    this.matchingStatus = matchingStatus;
    return this;
  }

  /**
   * Get matchingStatus
   *
   * @return matchingStatus
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_MATCHING_STATUS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getMatchingStatus() {
    return matchingStatus;
  }


  @JsonProperty(JSON_PROPERTY_MATCHING_STATUS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setMatchingStatus(String matchingStatus) {
    this.matchingStatus = matchingStatus;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    CarrierTINMatchingDto carrierTINMatchingDto = (CarrierTINMatchingDto) o;
    return Objects.equals(this.tiNTypeID, carrierTINMatchingDto.tiNTypeID) && Objects.equals(this.TIN, carrierTINMatchingDto.TIN) && Objects.equals(
        this.tiNName, carrierTINMatchingDto.tiNName) && Objects.equals(this.tiNMatchingStatusID, carrierTINMatchingDto.tiNMatchingStatusID) && Objects.equals(
        this.tiNMatchingResultID, carrierTINMatchingDto.tiNMatchingResultID) && Objects.equals(this.createdOnUtc, carrierTINMatchingDto.createdOnUtc)
           && Objects.equals(this.submittedOnUtc, carrierTINMatchingDto.submittedOnUtc) && Objects.equals(this.processedOnUtc,
        carrierTINMatchingDto.processedOnUtc) && Objects.equals(this.contactEmail, carrierTINMatchingDto.contactEmail) && Objects.equals(
        this.contactPhoneNumber, carrierTINMatchingDto.contactPhoneNumber) && Objects.equals(this.matchingResult, carrierTINMatchingDto.matchingResult)
           && Objects.equals(this.matchingStatus, carrierTINMatchingDto.matchingStatus);
  }

  @Override
  public int hashCode() {
    return Objects.hash(tiNTypeID, TIN, tiNName, tiNMatchingStatusID, tiNMatchingResultID, createdOnUtc, submittedOnUtc, processedOnUtc, contactEmail,
        contactPhoneNumber, matchingResult, matchingStatus);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class CarrierTINMatchingDto {\n");
    sb.append("    tiNTypeID: ").append(toIndentedString(tiNTypeID)).append("\n");
    sb.append("    TIN: ").append(toIndentedString(TIN)).append("\n");
    sb.append("    tiNName: ").append(toIndentedString(tiNName)).append("\n");
    sb.append("    tiNMatchingStatusID: ").append(toIndentedString(tiNMatchingStatusID)).append("\n");
    sb.append("    tiNMatchingResultID: ").append(toIndentedString(tiNMatchingResultID)).append("\n");
    sb.append("    createdOnUtc: ").append(toIndentedString(createdOnUtc)).append("\n");
    sb.append("    submittedOnUtc: ").append(toIndentedString(submittedOnUtc)).append("\n");
    sb.append("    processedOnUtc: ").append(toIndentedString(processedOnUtc)).append("\n");
    sb.append("    contactEmail: ").append(toIndentedString(contactEmail)).append("\n");
    sb.append("    contactPhoneNumber: ").append(toIndentedString(contactPhoneNumber)).append("\n");
    sb.append("    matchingResult: ").append(toIndentedString(matchingResult)).append("\n");
    sb.append("    matchingStatus: ").append(toIndentedString(matchingStatus)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

