package com.bulkloads.web.mcp.ext;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

/**
 * CustomerPaymentTermDto
 */
@JsonPropertyOrder({CustomerPaymentTermDto.JSON_PROPERTY_PAYMENT_TERM_I_D, CustomerPaymentTermDto.JSON_PROPERTY_DAYS, CustomerPaymentTermDto.JSON_PROPERTY_TERM,
    CustomerPaymentTermDto.JSON_PROPERTY_QUICK_PAY, CustomerPaymentTermDto.JSON_PROPERTY_PAYMENT_FEE_TYPE,
    CustomerPaymentTermDto.JSON_PROPERTY_PAYMENT_FEE_AMOUNT, CustomerPaymentTermDto.JSON_PROPERTY_CUSTOMER_I_D})

public class CustomerPaymentTermDto {

  public static final String JSON_PROPERTY_PAYMENT_TERM_I_D = "PaymentTermID";
  public static final String JSON_PROPERTY_DAYS = "Days";
  public static final String JSON_PROPERTY_TERM = "Term";
  public static final String JSON_PROPERTY_QUICK_PAY = "QuickPay";
  public static final String JSON_PROPERTY_PAYMENT_FEE_TYPE = "PaymentFeeType";
  public static final String JSON_PROPERTY_PAYMENT_FEE_AMOUNT = "PaymentFeeAmount";
  public static final String JSON_PROPERTY_CUSTOMER_I_D = "CustomerID";
  private Integer paymentTermID;
  private Integer days;
  private String term;
  private Boolean quickPay;
  private String paymentFeeType;
  private Double paymentFeeAmount;
  private Integer customerID;

  public CustomerPaymentTermDto() {
  }

  public CustomerPaymentTermDto paymentTermID(Integer paymentTermID) {

    this.paymentTermID = paymentTermID;
    return this;
  }

  /**
   * Get paymentTermID
   *
   * @return paymentTermID
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_PAYMENT_TERM_I_D)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Integer getPaymentTermID() {
    return paymentTermID;
  }


  @JsonProperty(JSON_PROPERTY_PAYMENT_TERM_I_D)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setPaymentTermID(Integer paymentTermID) {
    this.paymentTermID = paymentTermID;
  }


  public CustomerPaymentTermDto days(Integer days) {

    this.days = days;
    return this;
  }

  /**
   * Get days
   *
   * @return days
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_DAYS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Integer getDays() {
    return days;
  }


  @JsonProperty(JSON_PROPERTY_DAYS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setDays(Integer days) {
    this.days = days;
  }


  public CustomerPaymentTermDto term(String term) {

    this.term = term;
    return this;
  }

  /**
   * Get term
   *
   * @return term
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_TERM)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getTerm() {
    return term;
  }


  @JsonProperty(JSON_PROPERTY_TERM)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setTerm(String term) {
    this.term = term;
  }


  public CustomerPaymentTermDto quickPay(Boolean quickPay) {

    this.quickPay = quickPay;
    return this;
  }

  /**
   * Get quickPay
   *
   * @return quickPay
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_QUICK_PAY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getQuickPay() {
    return quickPay;
  }


  @JsonProperty(JSON_PROPERTY_QUICK_PAY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setQuickPay(Boolean quickPay) {
    this.quickPay = quickPay;
  }


  public CustomerPaymentTermDto paymentFeeType(String paymentFeeType) {

    this.paymentFeeType = paymentFeeType;
    return this;
  }

  /**
   * Get paymentFeeType
   *
   * @return paymentFeeType
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_PAYMENT_FEE_TYPE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getPaymentFeeType() {
    return paymentFeeType;
  }


  @JsonProperty(JSON_PROPERTY_PAYMENT_FEE_TYPE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setPaymentFeeType(String paymentFeeType) {
    this.paymentFeeType = paymentFeeType;
  }


  public CustomerPaymentTermDto paymentFeeAmount(Double paymentFeeAmount) {

    this.paymentFeeAmount = paymentFeeAmount;
    return this;
  }

  /**
   * Get paymentFeeAmount
   *
   * @return paymentFeeAmount
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_PAYMENT_FEE_AMOUNT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Double getPaymentFeeAmount() {
    return paymentFeeAmount;
  }


  @JsonProperty(JSON_PROPERTY_PAYMENT_FEE_AMOUNT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setPaymentFeeAmount(Double paymentFeeAmount) {
    this.paymentFeeAmount = paymentFeeAmount;
  }


  public CustomerPaymentTermDto customerID(Integer customerID) {

    this.customerID = customerID;
    return this;
  }

  /**
   * Get customerID
   *
   * @return customerID
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CUSTOMER_I_D)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Integer getCustomerID() {
    return customerID;
  }


  @JsonProperty(JSON_PROPERTY_CUSTOMER_I_D)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCustomerID(Integer customerID) {
    this.customerID = customerID;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    CustomerPaymentTermDto customerPaymentTermDto = (CustomerPaymentTermDto) o;
    return Objects.equals(this.paymentTermID, customerPaymentTermDto.paymentTermID) && Objects.equals(this.days, customerPaymentTermDto.days) && Objects.equals(
        this.term, customerPaymentTermDto.term) && Objects.equals(this.quickPay, customerPaymentTermDto.quickPay) && Objects.equals(this.paymentFeeType,
        customerPaymentTermDto.paymentFeeType) && Objects.equals(this.paymentFeeAmount, customerPaymentTermDto.paymentFeeAmount) && Objects.equals(
        this.customerID, customerPaymentTermDto.customerID);
  }

  @Override
  public int hashCode() {
    return Objects.hash(paymentTermID, days, term, quickPay, paymentFeeType, paymentFeeAmount, customerID);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class CustomerPaymentTermDto {\n");
    sb.append("    paymentTermID: ").append(toIndentedString(paymentTermID)).append("\n");
    sb.append("    days: ").append(toIndentedString(days)).append("\n");
    sb.append("    term: ").append(toIndentedString(term)).append("\n");
    sb.append("    quickPay: ").append(toIndentedString(quickPay)).append("\n");
    sb.append("    paymentFeeType: ").append(toIndentedString(paymentFeeType)).append("\n");
    sb.append("    paymentFeeAmount: ").append(toIndentedString(paymentFeeAmount)).append("\n");
    sb.append("    customerID: ").append(toIndentedString(customerID)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

