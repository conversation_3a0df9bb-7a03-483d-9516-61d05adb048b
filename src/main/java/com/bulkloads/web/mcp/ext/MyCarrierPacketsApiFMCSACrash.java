package com.bulkloads.web.mcp.ext;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * MyCarrierPacketsApiFMCSACrash
 */
@JsonPropertyOrder({MyCarrierPacketsApiFMCSACrash.JSON_PROPERTY_CRASH_FATAL_U_S, MyCarrierPacketsApiFMCSACrash.JSON_PROPERTY_CRASH_INJURY_U_S,
    MyCarrierPacketsApiFMCSACrash.JSON_PROPERTY_CRASH_TOW_U_S, MyCarrierPacketsApiFMCSACrash.JSON_PROPERTY_CRASH_TOTAL_U_S,
    MyCarrierPacketsApiFMCSACrash.JSON_PROPERTY_CRASH_FATAL_C_A_N, MyCarrierPacketsApiFMCSACrash.JSON_PROPERTY_CRASH_INJURY_C_A_N,
    MyCarrierPacketsApiFMCSACrash.JSON_PROPERTY_CRASH_TOW_C_A_N, MyCarrierPacketsApiFMCSACrash.JSON_PROPERTY_CRASH_TOTAL_C_A_N})
@JsonTypeName("MyCarrierPacketsApi.FMCSA.Crash")

public class MyCarrierPacketsApiFMCSACrash {

  public static final String JSON_PROPERTY_CRASH_FATAL_U_S = "crashFatalUS";
  public static final String JSON_PROPERTY_CRASH_INJURY_U_S = "crashInjuryUS";
  public static final String JSON_PROPERTY_CRASH_TOW_U_S = "crashTowUS";
  public static final String JSON_PROPERTY_CRASH_TOTAL_U_S = "crashTotalUS";
  public static final String JSON_PROPERTY_CRASH_FATAL_C_A_N = "crashFatalCAN";
  public static final String JSON_PROPERTY_CRASH_INJURY_C_A_N = "crashInjuryCAN";
  public static final String JSON_PROPERTY_CRASH_TOW_C_A_N = "crashTowCAN";
  public static final String JSON_PROPERTY_CRASH_TOTAL_C_A_N = "crashTotalCAN";
  private String crashFatalUS;
  private String crashInjuryUS;
  private String crashTowUS;
  private String crashTotalUS;
  private String crashFatalCAN;
  private String crashInjuryCAN;
  private String crashTowCAN;
  private String crashTotalCAN;

  public MyCarrierPacketsApiFMCSACrash() {
  }

  public MyCarrierPacketsApiFMCSACrash crashFatalUS(String crashFatalUS) {

    this.crashFatalUS = crashFatalUS;
    return this;
  }

  /**
   * Get crashFatalUS
   *
   * @return crashFatalUS
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CRASH_FATAL_U_S)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getCrashFatalUS() {
    return crashFatalUS;
  }


  @JsonProperty(JSON_PROPERTY_CRASH_FATAL_U_S)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCrashFatalUS(String crashFatalUS) {
    this.crashFatalUS = crashFatalUS;
  }


  public MyCarrierPacketsApiFMCSACrash crashInjuryUS(String crashInjuryUS) {

    this.crashInjuryUS = crashInjuryUS;
    return this;
  }

  /**
   * Get crashInjuryUS
   *
   * @return crashInjuryUS
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CRASH_INJURY_U_S)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getCrashInjuryUS() {
    return crashInjuryUS;
  }


  @JsonProperty(JSON_PROPERTY_CRASH_INJURY_U_S)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCrashInjuryUS(String crashInjuryUS) {
    this.crashInjuryUS = crashInjuryUS;
  }


  public MyCarrierPacketsApiFMCSACrash crashTowUS(String crashTowUS) {

    this.crashTowUS = crashTowUS;
    return this;
  }

  /**
   * Get crashTowUS
   *
   * @return crashTowUS
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CRASH_TOW_U_S)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getCrashTowUS() {
    return crashTowUS;
  }


  @JsonProperty(JSON_PROPERTY_CRASH_TOW_U_S)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCrashTowUS(String crashTowUS) {
    this.crashTowUS = crashTowUS;
  }


  public MyCarrierPacketsApiFMCSACrash crashTotalUS(String crashTotalUS) {

    this.crashTotalUS = crashTotalUS;
    return this;
  }

  /**
   * Get crashTotalUS
   *
   * @return crashTotalUS
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CRASH_TOTAL_U_S)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getCrashTotalUS() {
    return crashTotalUS;
  }


  @JsonProperty(JSON_PROPERTY_CRASH_TOTAL_U_S)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCrashTotalUS(String crashTotalUS) {
    this.crashTotalUS = crashTotalUS;
  }


  public MyCarrierPacketsApiFMCSACrash crashFatalCAN(String crashFatalCAN) {

    this.crashFatalCAN = crashFatalCAN;
    return this;
  }

  /**
   * Get crashFatalCAN
   *
   * @return crashFatalCAN
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CRASH_FATAL_C_A_N)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getCrashFatalCAN() {
    return crashFatalCAN;
  }


  @JsonProperty(JSON_PROPERTY_CRASH_FATAL_C_A_N)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCrashFatalCAN(String crashFatalCAN) {
    this.crashFatalCAN = crashFatalCAN;
  }


  public MyCarrierPacketsApiFMCSACrash crashInjuryCAN(String crashInjuryCAN) {

    this.crashInjuryCAN = crashInjuryCAN;
    return this;
  }

  /**
   * Get crashInjuryCAN
   *
   * @return crashInjuryCAN
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CRASH_INJURY_C_A_N)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getCrashInjuryCAN() {
    return crashInjuryCAN;
  }


  @JsonProperty(JSON_PROPERTY_CRASH_INJURY_C_A_N)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCrashInjuryCAN(String crashInjuryCAN) {
    this.crashInjuryCAN = crashInjuryCAN;
  }


  public MyCarrierPacketsApiFMCSACrash crashTowCAN(String crashTowCAN) {

    this.crashTowCAN = crashTowCAN;
    return this;
  }

  /**
   * Get crashTowCAN
   *
   * @return crashTowCAN
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CRASH_TOW_C_A_N)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getCrashTowCAN() {
    return crashTowCAN;
  }


  @JsonProperty(JSON_PROPERTY_CRASH_TOW_C_A_N)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCrashTowCAN(String crashTowCAN) {
    this.crashTowCAN = crashTowCAN;
  }


  public MyCarrierPacketsApiFMCSACrash crashTotalCAN(String crashTotalCAN) {

    this.crashTotalCAN = crashTotalCAN;
    return this;
  }

  /**
   * Get crashTotalCAN
   *
   * @return crashTotalCAN
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CRASH_TOTAL_C_A_N)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getCrashTotalCAN() {
    return crashTotalCAN;
  }


  @JsonProperty(JSON_PROPERTY_CRASH_TOTAL_C_A_N)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCrashTotalCAN(String crashTotalCAN) {
    this.crashTotalCAN = crashTotalCAN;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    MyCarrierPacketsApiFMCSACrash myCarrierPacketsApiFMCSACrash = (MyCarrierPacketsApiFMCSACrash) o;
    return Objects.equals(this.crashFatalUS, myCarrierPacketsApiFMCSACrash.crashFatalUS) && Objects.equals(this.crashInjuryUS,
        myCarrierPacketsApiFMCSACrash.crashInjuryUS) && Objects.equals(this.crashTowUS, myCarrierPacketsApiFMCSACrash.crashTowUS) && Objects.equals(
        this.crashTotalUS, myCarrierPacketsApiFMCSACrash.crashTotalUS) && Objects.equals(this.crashFatalCAN, myCarrierPacketsApiFMCSACrash.crashFatalCAN)
           && Objects.equals(this.crashInjuryCAN, myCarrierPacketsApiFMCSACrash.crashInjuryCAN) && Objects.equals(this.crashTowCAN,
        myCarrierPacketsApiFMCSACrash.crashTowCAN) && Objects.equals(this.crashTotalCAN, myCarrierPacketsApiFMCSACrash.crashTotalCAN);
  }

  @Override
  public int hashCode() {
    return Objects.hash(crashFatalUS, crashInjuryUS, crashTowUS, crashTotalUS, crashFatalCAN, crashInjuryCAN, crashTowCAN, crashTotalCAN);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class MyCarrierPacketsApiFMCSACrash {\n");
    sb.append("    crashFatalUS: ").append(toIndentedString(crashFatalUS)).append("\n");
    sb.append("    crashInjuryUS: ").append(toIndentedString(crashInjuryUS)).append("\n");
    sb.append("    crashTowUS: ").append(toIndentedString(crashTowUS)).append("\n");
    sb.append("    crashTotalUS: ").append(toIndentedString(crashTotalUS)).append("\n");
    sb.append("    crashFatalCAN: ").append(toIndentedString(crashFatalCAN)).append("\n");
    sb.append("    crashInjuryCAN: ").append(toIndentedString(crashInjuryCAN)).append("\n");
    sb.append("    crashTowCAN: ").append(toIndentedString(crashTowCAN)).append("\n");
    sb.append("    crashTotalCAN: ").append(toIndentedString(crashTotalCAN)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

