package com.bulkloads.web.mcp.ext;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

/**
 * CarrierELDProviderDto
 */
@JsonPropertyOrder({CarrierELDProviderDto.JSON_PROPERTY_COMPLIANCE_STATUS_I_D, CarrierELDProviderDto.JSON_PROPERTY_COMPLIANCE_STATUS,
    CarrierELDProviderDto.JSON_PROPERTY_PROVIDER_NAME, CarrierELDProviderDto.JSON_PROPERTY_PROVIDER_IDENTIFIER,
    CarrierELDProviderDto.JSON_PROPERTY_EXEMPTION_I_D, CarrierELDProviderDto.JSON_PROPERTY_EXEMPTION, CarrierELDProviderDto.JSON_PROPERTY_COMPLIANT_BY})

public class CarrierELDProviderDto {

  public static final String JSON_PROPERTY_COMPLIANCE_STATUS_I_D = "ComplianceStatusID";
  public static final String JSON_PROPERTY_COMPLIANCE_STATUS = "ComplianceStatus";
  public static final String JSON_PROPERTY_PROVIDER_NAME = "ProviderName";
  public static final String JSON_PROPERTY_PROVIDER_IDENTIFIER = "ProviderIdentifier";
  public static final String JSON_PROPERTY_EXEMPTION_I_D = "ExemptionID";
  public static final String JSON_PROPERTY_EXEMPTION = "Exemption";
  public static final String JSON_PROPERTY_COMPLIANT_BY = "CompliantBy";
  private Integer complianceStatusID;
  private String complianceStatus;
  private String providerName;
  private String providerIdentifier;
  private Integer exemptionID;
  private String exemption;
  private String compliantBy;

  public CarrierELDProviderDto() {
  }

  public CarrierELDProviderDto complianceStatusID(Integer complianceStatusID) {

    this.complianceStatusID = complianceStatusID;
    return this;
  }

  /**
   * Get complianceStatusID
   *
   * @return complianceStatusID
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_COMPLIANCE_STATUS_I_D)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Integer getComplianceStatusID() {
    return complianceStatusID;
  }


  @JsonProperty(JSON_PROPERTY_COMPLIANCE_STATUS_I_D)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setComplianceStatusID(Integer complianceStatusID) {
    this.complianceStatusID = complianceStatusID;
  }


  public CarrierELDProviderDto complianceStatus(String complianceStatus) {

    this.complianceStatus = complianceStatus;
    return this;
  }

  /**
   * Get complianceStatus
   *
   * @return complianceStatus
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_COMPLIANCE_STATUS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getComplianceStatus() {
    return complianceStatus;
  }


  @JsonProperty(JSON_PROPERTY_COMPLIANCE_STATUS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setComplianceStatus(String complianceStatus) {
    this.complianceStatus = complianceStatus;
  }


  public CarrierELDProviderDto providerName(String providerName) {

    this.providerName = providerName;
    return this;
  }

  /**
   * Get providerName
   *
   * @return providerName
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_PROVIDER_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getProviderName() {
    return providerName;
  }


  @JsonProperty(JSON_PROPERTY_PROVIDER_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setProviderName(String providerName) {
    this.providerName = providerName;
  }


  public CarrierELDProviderDto providerIdentifier(String providerIdentifier) {

    this.providerIdentifier = providerIdentifier;
    return this;
  }

  /**
   * Get providerIdentifier
   *
   * @return providerIdentifier
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_PROVIDER_IDENTIFIER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getProviderIdentifier() {
    return providerIdentifier;
  }


  @JsonProperty(JSON_PROPERTY_PROVIDER_IDENTIFIER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setProviderIdentifier(String providerIdentifier) {
    this.providerIdentifier = providerIdentifier;
  }


  public CarrierELDProviderDto exemptionID(Integer exemptionID) {

    this.exemptionID = exemptionID;
    return this;
  }

  /**
   * Get exemptionID
   *
   * @return exemptionID
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_EXEMPTION_I_D)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Integer getExemptionID() {
    return exemptionID;
  }


  @JsonProperty(JSON_PROPERTY_EXEMPTION_I_D)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setExemptionID(Integer exemptionID) {
    this.exemptionID = exemptionID;
  }


  public CarrierELDProviderDto exemption(String exemption) {

    this.exemption = exemption;
    return this;
  }

  /**
   * Get exemption
   *
   * @return exemption
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_EXEMPTION)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getExemption() {
    return exemption;
  }


  @JsonProperty(JSON_PROPERTY_EXEMPTION)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setExemption(String exemption) {
    this.exemption = exemption;
  }


  public CarrierELDProviderDto compliantBy(String compliantBy) {

    this.compliantBy = compliantBy;
    return this;
  }

  /**
   * Get compliantBy
   *
   * @return compliantBy
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_COMPLIANT_BY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getCompliantBy() {
    return compliantBy;
  }


  @JsonProperty(JSON_PROPERTY_COMPLIANT_BY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCompliantBy(String compliantBy) {
    this.compliantBy = compliantBy;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    CarrierELDProviderDto carrierELDProviderDto = (CarrierELDProviderDto) o;
    return Objects.equals(this.complianceStatusID, carrierELDProviderDto.complianceStatusID) && Objects.equals(this.complianceStatus,
        carrierELDProviderDto.complianceStatus) && Objects.equals(this.providerName, carrierELDProviderDto.providerName) && Objects.equals(
        this.providerIdentifier, carrierELDProviderDto.providerIdentifier) && Objects.equals(this.exemptionID, carrierELDProviderDto.exemptionID)
           && Objects.equals(this.exemption, carrierELDProviderDto.exemption) && Objects.equals(this.compliantBy, carrierELDProviderDto.compliantBy);
  }

  @Override
  public int hashCode() {
    return Objects.hash(complianceStatusID, complianceStatus, providerName, providerIdentifier, exemptionID, exemption, compliantBy);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class CarrierELDProviderDto {\n");
    sb.append("    complianceStatusID: ").append(toIndentedString(complianceStatusID)).append("\n");
    sb.append("    complianceStatus: ").append(toIndentedString(complianceStatus)).append("\n");
    sb.append("    providerName: ").append(toIndentedString(providerName)).append("\n");
    sb.append("    providerIdentifier: ").append(toIndentedString(providerIdentifier)).append("\n");
    sb.append("    exemptionID: ").append(toIndentedString(exemptionID)).append("\n");
    sb.append("    exemption: ").append(toIndentedString(exemption)).append("\n");
    sb.append("    compliantBy: ").append(toIndentedString(compliantBy)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

