package com.bulkloads.web.mcp.ext;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * MyCarrierPacketsBusinessCarriersCarrierRating
 */
@JsonPropertyOrder({MyCarrierPacketsBusinessCarriersCarrierRating.JSON_PROPERTY_CARRIER_I_D,
    MyCarrierPacketsBusinessCarriersCarrierRating.JSON_PROPERTY_CUSTOMER_I_D, MyCarrierPacketsBusinessCarriersCarrierRating.JSON_PROPERTY_CUSTOMER_RATING,
    MyCarrierPacketsBusinessCarriersCarrierRating.JSON_PROPERTY_RATING_SUM, MyCarrierPacketsBusinessCarriersCarrierRating.JSON_PROPERTY_TOTAL_RATINGS,
    MyCarrierPacketsBusinessCarriersCarrierRating.JSON_PROPERTY_LOW_RATINGS, MyCarrierPacketsBusinessCarriersCarrierRating.JSON_PROPERTY_TOTAL_RATING_PERCENT,
    MyCarrierPacketsBusinessCarriersCarrierRating.JSON_PROPERTY_CUSTOMER_RATING_PERCENT,
    MyCarrierPacketsBusinessCarriersCarrierRating.JSON_PROPERTY_RATING_VALUE, MyCarrierPacketsBusinessCarriersCarrierRating.JSON_PROPERTY_RATING_VALUE_TEXT,
    MyCarrierPacketsBusinessCarriersCarrierRating.JSON_PROPERTY_AVG_RATING_TEXT,
    MyCarrierPacketsBusinessCarriersCarrierRating.JSON_PROPERTY_AVG_RATING_BASIS_TEXT,
    MyCarrierPacketsBusinessCarriersCarrierRating.JSON_PROPERTY_AVG_RATING_TEXT_PLUS_RATING_BASIS_TEXT,
    MyCarrierPacketsBusinessCarriersCarrierRating.JSON_PROPERTY_CUSTOMER_RATING_TEXT,
    MyCarrierPacketsBusinessCarriersCarrierRating.JSON_PROPERTY_HAS_COMPLETED_PACKET})
@JsonTypeName("MyCarrierPacketsBusiness.Carriers.CarrierRating")

public class MyCarrierPacketsBusinessCarriersCarrierRating {

  public static final String JSON_PROPERTY_CARRIER_I_D = "CarrierID";
  public static final String JSON_PROPERTY_CUSTOMER_I_D = "CustomerID";
  public static final String JSON_PROPERTY_CUSTOMER_RATING = "CustomerRating";
  public static final String JSON_PROPERTY_RATING_SUM = "RatingSum";
  public static final String JSON_PROPERTY_TOTAL_RATINGS = "TotalRatings";
  public static final String JSON_PROPERTY_LOW_RATINGS = "LowRatings";
  public static final String JSON_PROPERTY_TOTAL_RATING_PERCENT = "TotalRatingPercent";
  public static final String JSON_PROPERTY_CUSTOMER_RATING_PERCENT = "CustomerRatingPercent";
  public static final String JSON_PROPERTY_RATING_VALUE = "RatingValue";
  public static final String JSON_PROPERTY_RATING_VALUE_TEXT = "RatingValueText";
  public static final String JSON_PROPERTY_AVG_RATING_TEXT = "AvgRatingText";
  public static final String JSON_PROPERTY_AVG_RATING_BASIS_TEXT = "AvgRatingBasisText";
  public static final String JSON_PROPERTY_AVG_RATING_TEXT_PLUS_RATING_BASIS_TEXT = "AvgRatingTextPlusRatingBasisText";
  public static final String JSON_PROPERTY_CUSTOMER_RATING_TEXT = "CustomerRatingText";
  public static final String JSON_PROPERTY_HAS_COMPLETED_PACKET = "HasCompletedPacket";
  private Integer carrierID;
  private Integer customerID;
  private Integer customerRating;
  private Integer ratingSum;
  private Integer totalRatings;
  private Integer lowRatings;
  private Integer totalRatingPercent;
  private Integer customerRatingPercent;
  private Double ratingValue;
  private String ratingValueText;
  private String avgRatingText;
  private String avgRatingBasisText;
  private String avgRatingTextPlusRatingBasisText;
  private String customerRatingText;
  private Boolean hasCompletedPacket;

  public MyCarrierPacketsBusinessCarriersCarrierRating() {
  }

  @JsonCreator
  public MyCarrierPacketsBusinessCarriersCarrierRating(@JsonProperty(JSON_PROPERTY_TOTAL_RATING_PERCENT) Integer totalRatingPercent,
      @JsonProperty(JSON_PROPERTY_CUSTOMER_RATING_PERCENT) Integer customerRatingPercent, @JsonProperty(JSON_PROPERTY_RATING_VALUE) Double ratingValue,
      @JsonProperty(JSON_PROPERTY_RATING_VALUE_TEXT) String ratingValueText, @JsonProperty(JSON_PROPERTY_AVG_RATING_TEXT) String avgRatingText,
      @JsonProperty(JSON_PROPERTY_AVG_RATING_BASIS_TEXT) String avgRatingBasisText,
      @JsonProperty(JSON_PROPERTY_AVG_RATING_TEXT_PLUS_RATING_BASIS_TEXT) String avgRatingTextPlusRatingBasisText,
      @JsonProperty(JSON_PROPERTY_CUSTOMER_RATING_TEXT) String customerRatingText) {
    this();
    this.totalRatingPercent = totalRatingPercent;
    this.customerRatingPercent = customerRatingPercent;
    this.ratingValue = ratingValue;
    this.ratingValueText = ratingValueText;
    this.avgRatingText = avgRatingText;
    this.avgRatingBasisText = avgRatingBasisText;
    this.avgRatingTextPlusRatingBasisText = avgRatingTextPlusRatingBasisText;
    this.customerRatingText = customerRatingText;
  }

  public MyCarrierPacketsBusinessCarriersCarrierRating carrierID(Integer carrierID) {

    this.carrierID = carrierID;
    return this;
  }

  /**
   * Get carrierID
   *
   * @return carrierID
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CARRIER_I_D)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Integer getCarrierID() {
    return carrierID;
  }


  @JsonProperty(JSON_PROPERTY_CARRIER_I_D)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCarrierID(Integer carrierID) {
    this.carrierID = carrierID;
  }


  public MyCarrierPacketsBusinessCarriersCarrierRating customerID(Integer customerID) {

    this.customerID = customerID;
    return this;
  }

  /**
   * Get customerID
   *
   * @return customerID
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CUSTOMER_I_D)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Integer getCustomerID() {
    return customerID;
  }


  @JsonProperty(JSON_PROPERTY_CUSTOMER_I_D)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCustomerID(Integer customerID) {
    this.customerID = customerID;
  }


  public MyCarrierPacketsBusinessCarriersCarrierRating customerRating(Integer customerRating) {

    this.customerRating = customerRating;
    return this;
  }

  /**
   * Get customerRating
   *
   * @return customerRating
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CUSTOMER_RATING)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Integer getCustomerRating() {
    return customerRating;
  }


  @JsonProperty(JSON_PROPERTY_CUSTOMER_RATING)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCustomerRating(Integer customerRating) {
    this.customerRating = customerRating;
  }


  public MyCarrierPacketsBusinessCarriersCarrierRating ratingSum(Integer ratingSum) {

    this.ratingSum = ratingSum;
    return this;
  }

  /**
   * Get ratingSum
   *
   * @return ratingSum
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_RATING_SUM)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Integer getRatingSum() {
    return ratingSum;
  }


  @JsonProperty(JSON_PROPERTY_RATING_SUM)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setRatingSum(Integer ratingSum) {
    this.ratingSum = ratingSum;
  }


  public MyCarrierPacketsBusinessCarriersCarrierRating totalRatings(Integer totalRatings) {

    this.totalRatings = totalRatings;
    return this;
  }

  /**
   * Get totalRatings
   *
   * @return totalRatings
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_TOTAL_RATINGS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Integer getTotalRatings() {
    return totalRatings;
  }


  @JsonProperty(JSON_PROPERTY_TOTAL_RATINGS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setTotalRatings(Integer totalRatings) {
    this.totalRatings = totalRatings;
  }


  public MyCarrierPacketsBusinessCarriersCarrierRating lowRatings(Integer lowRatings) {

    this.lowRatings = lowRatings;
    return this;
  }

  /**
   * Get lowRatings
   *
   * @return lowRatings
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_LOW_RATINGS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Integer getLowRatings() {
    return lowRatings;
  }


  @JsonProperty(JSON_PROPERTY_LOW_RATINGS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setLowRatings(Integer lowRatings) {
    this.lowRatings = lowRatings;
  }


  /**
   * Get totalRatingPercent
   *
   * @return totalRatingPercent
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_TOTAL_RATING_PERCENT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Integer getTotalRatingPercent() {
    return totalRatingPercent;
  }


  /**
   * Get customerRatingPercent
   *
   * @return customerRatingPercent
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CUSTOMER_RATING_PERCENT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Integer getCustomerRatingPercent() {
    return customerRatingPercent;
  }


  /**
   * Get ratingValue
   *
   * @return ratingValue
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_RATING_VALUE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Double getRatingValue() {
    return ratingValue;
  }


  /**
   * Get ratingValueText
   *
   * @return ratingValueText
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_RATING_VALUE_TEXT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getRatingValueText() {
    return ratingValueText;
  }


  /**
   * Get avgRatingText
   *
   * @return avgRatingText
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_AVG_RATING_TEXT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getAvgRatingText() {
    return avgRatingText;
  }


  /**
   * Get avgRatingBasisText
   *
   * @return avgRatingBasisText
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_AVG_RATING_BASIS_TEXT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getAvgRatingBasisText() {
    return avgRatingBasisText;
  }


  /**
   * Get avgRatingTextPlusRatingBasisText
   *
   * @return avgRatingTextPlusRatingBasisText
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_AVG_RATING_TEXT_PLUS_RATING_BASIS_TEXT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getAvgRatingTextPlusRatingBasisText() {
    return avgRatingTextPlusRatingBasisText;
  }


  /**
   * Get customerRatingText
   *
   * @return customerRatingText
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CUSTOMER_RATING_TEXT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getCustomerRatingText() {
    return customerRatingText;
  }


  public MyCarrierPacketsBusinessCarriersCarrierRating hasCompletedPacket(Boolean hasCompletedPacket) {

    this.hasCompletedPacket = hasCompletedPacket;
    return this;
  }

  /**
   * Get hasCompletedPacket
   *
   * @return hasCompletedPacket
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_HAS_COMPLETED_PACKET)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getHasCompletedPacket() {
    return hasCompletedPacket;
  }


  @JsonProperty(JSON_PROPERTY_HAS_COMPLETED_PACKET)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setHasCompletedPacket(Boolean hasCompletedPacket) {
    this.hasCompletedPacket = hasCompletedPacket;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    MyCarrierPacketsBusinessCarriersCarrierRating myCarrierPacketsBusinessCarriersCarrierRating = (MyCarrierPacketsBusinessCarriersCarrierRating) o;
    return Objects.equals(this.carrierID, myCarrierPacketsBusinessCarriersCarrierRating.carrierID) && Objects.equals(this.customerID,
        myCarrierPacketsBusinessCarriersCarrierRating.customerID) && Objects.equals(this.customerRating,
        myCarrierPacketsBusinessCarriersCarrierRating.customerRating) && Objects.equals(this.ratingSum, myCarrierPacketsBusinessCarriersCarrierRating.ratingSum)
           && Objects.equals(this.totalRatings, myCarrierPacketsBusinessCarriersCarrierRating.totalRatings) && Objects.equals(this.lowRatings,
        myCarrierPacketsBusinessCarriersCarrierRating.lowRatings) && Objects.equals(this.totalRatingPercent,
        myCarrierPacketsBusinessCarriersCarrierRating.totalRatingPercent) && Objects.equals(this.customerRatingPercent,
        myCarrierPacketsBusinessCarriersCarrierRating.customerRatingPercent) && Objects.equals(this.ratingValue,
        myCarrierPacketsBusinessCarriersCarrierRating.ratingValue) && Objects.equals(this.ratingValueText,
        myCarrierPacketsBusinessCarriersCarrierRating.ratingValueText) && Objects.equals(this.avgRatingText,
        myCarrierPacketsBusinessCarriersCarrierRating.avgRatingText) && Objects.equals(this.avgRatingBasisText,
        myCarrierPacketsBusinessCarriersCarrierRating.avgRatingBasisText) && Objects.equals(this.avgRatingTextPlusRatingBasisText,
        myCarrierPacketsBusinessCarriersCarrierRating.avgRatingTextPlusRatingBasisText) && Objects.equals(this.customerRatingText,
        myCarrierPacketsBusinessCarriersCarrierRating.customerRatingText) && Objects.equals(this.hasCompletedPacket,
        myCarrierPacketsBusinessCarriersCarrierRating.hasCompletedPacket);
  }

  @Override
  public int hashCode() {
    return Objects.hash(carrierID, customerID, customerRating, ratingSum, totalRatings, lowRatings, totalRatingPercent, customerRatingPercent, ratingValue,
        ratingValueText, avgRatingText, avgRatingBasisText, avgRatingTextPlusRatingBasisText, customerRatingText, hasCompletedPacket);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class MyCarrierPacketsBusinessCarriersCarrierRating {\n");
    sb.append("    carrierID: ").append(toIndentedString(carrierID)).append("\n");
    sb.append("    customerID: ").append(toIndentedString(customerID)).append("\n");
    sb.append("    customerRating: ").append(toIndentedString(customerRating)).append("\n");
    sb.append("    ratingSum: ").append(toIndentedString(ratingSum)).append("\n");
    sb.append("    totalRatings: ").append(toIndentedString(totalRatings)).append("\n");
    sb.append("    lowRatings: ").append(toIndentedString(lowRatings)).append("\n");
    sb.append("    totalRatingPercent: ").append(toIndentedString(totalRatingPercent)).append("\n");
    sb.append("    customerRatingPercent: ").append(toIndentedString(customerRatingPercent)).append("\n");
    sb.append("    ratingValue: ").append(toIndentedString(ratingValue)).append("\n");
    sb.append("    ratingValueText: ").append(toIndentedString(ratingValueText)).append("\n");
    sb.append("    avgRatingText: ").append(toIndentedString(avgRatingText)).append("\n");
    sb.append("    avgRatingBasisText: ").append(toIndentedString(avgRatingBasisText)).append("\n");
    sb.append("    avgRatingTextPlusRatingBasisText: ").append(toIndentedString(avgRatingTextPlusRatingBasisText)).append("\n");
    sb.append("    customerRatingText: ").append(toIndentedString(customerRatingText)).append("\n");
    sb.append("    hasCompletedPacket: ").append(toIndentedString(hasCompletedPacket)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

