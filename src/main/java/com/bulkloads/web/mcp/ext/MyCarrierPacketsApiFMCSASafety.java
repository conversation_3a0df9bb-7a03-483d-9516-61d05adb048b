package com.bulkloads.web.mcp.ext;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * MyCarrierPacketsApiFMCSASafety
 */
@JsonPropertyOrder({MyCarrierPacketsApiFMCSASafety.JSON_PROPERTY_RATING, MyCarrierPacketsApiFMCSASafety.JSON_PROPERTY_RATING_DATE,
    MyCarrierPacketsApiFMCSASafety.JSON_PROPERTY_UNSAFE_DRV_P_C_T, MyCarrierPacketsApiFMCSASafety.JSON_PROPERTY_UNSAFE_DRV_O_T,
    MyCarrierPacketsApiFMCSASafety.JSON_PROPERTY_UNSAFE_DRV_S_V, MyCarrierPacketsApiFMCSASafety.JSON_PROPERTY_UNSAFE_DRV_ALERT,
    MyCarrierPacketsApiFMCSASafety.JSON_PROPERTY_UNSAFE_DRV_TREND, MyCarrierPacketsApiFMCSASafety.JSON_PROPERTY_UNSAFE_DRV_C_N_T,
    MyCarrierPacketsApiFMCSASafety.JSON_PROPERTY_HOS_P_C_T, MyCarrierPacketsApiFMCSASafety.JSON_PROPERTY_HOS_O_T,
    MyCarrierPacketsApiFMCSASafety.JSON_PROPERTY_HOS_S_V, MyCarrierPacketsApiFMCSASafety.JSON_PROPERTY_HOS_ALERT,
    MyCarrierPacketsApiFMCSASafety.JSON_PROPERTY_HOS_TREND, MyCarrierPacketsApiFMCSASafety.JSON_PROPERTY_HOS_C_N_T,
    MyCarrierPacketsApiFMCSASafety.JSON_PROPERTY_DRV_FIT_P_C_T, MyCarrierPacketsApiFMCSASafety.JSON_PROPERTY_DRV_FIT_O_T,
    MyCarrierPacketsApiFMCSASafety.JSON_PROPERTY_DRV_FIT_S_V, MyCarrierPacketsApiFMCSASafety.JSON_PROPERTY_DRV_FIT_ALERT,
    MyCarrierPacketsApiFMCSASafety.JSON_PROPERTY_DRV_FIT_TREND, MyCarrierPacketsApiFMCSASafety.JSON_PROPERTY_DRV_FIT_C_N_T,
    MyCarrierPacketsApiFMCSASafety.JSON_PROPERTY_CONTROL_SUB_P_C_T, MyCarrierPacketsApiFMCSASafety.JSON_PROPERTY_CONTROL_SUB_O_T,
    MyCarrierPacketsApiFMCSASafety.JSON_PROPERTY_CONTROL_SUB_S_V, MyCarrierPacketsApiFMCSASafety.JSON_PROPERTY_CONTROL_SUB_ALERT,
    MyCarrierPacketsApiFMCSASafety.JSON_PROPERTY_CONTROL_SUB_TREND, MyCarrierPacketsApiFMCSASafety.JSON_PROPERTY_CONTROL_SUB_C_N_T,
    MyCarrierPacketsApiFMCSASafety.JSON_PROPERTY_VEH_MAINT_P_C_T, MyCarrierPacketsApiFMCSASafety.JSON_PROPERTY_VEH_MAINT_O_T,
    MyCarrierPacketsApiFMCSASafety.JSON_PROPERTY_VEH_MAINT_S_V, MyCarrierPacketsApiFMCSASafety.JSON_PROPERTY_VEH_MAINT_ALERT,
    MyCarrierPacketsApiFMCSASafety.JSON_PROPERTY_VEH_MAINT_TREND, MyCarrierPacketsApiFMCSASafety.JSON_PROPERTY_VEH_MAINT_C_N_T,
    MyCarrierPacketsApiFMCSASafety.JSON_PROPERTY_HAZ_MAT_P_C_T, MyCarrierPacketsApiFMCSASafety.JSON_PROPERTY_HAZ_MAT_O_T,
    MyCarrierPacketsApiFMCSASafety.JSON_PROPERTY_HAZ_MAT_S_V, MyCarrierPacketsApiFMCSASafety.JSON_PROPERTY_HAZ_MAT_ALERT,
    MyCarrierPacketsApiFMCSASafety.JSON_PROPERTY_HAZ_MAT_TREND, MyCarrierPacketsApiFMCSASafety.JSON_PROPERTY_HAZ_MAT_C_N_T})
@JsonTypeName("MyCarrierPacketsApi.FMCSA.Safety")

public class MyCarrierPacketsApiFMCSASafety {

  public static final String JSON_PROPERTY_RATING = "rating";
  public static final String JSON_PROPERTY_RATING_DATE = "ratingDate";
  public static final String JSON_PROPERTY_UNSAFE_DRV_P_C_T = "unsafeDrvPCT";
  public static final String JSON_PROPERTY_UNSAFE_DRV_O_T = "unsafeDrvOT";
  public static final String JSON_PROPERTY_UNSAFE_DRV_S_V = "unsafeDrvSV";
  public static final String JSON_PROPERTY_UNSAFE_DRV_ALERT = "unsafeDrvAlert";
  public static final String JSON_PROPERTY_UNSAFE_DRV_TREND = "unsafeDrvTrend";
  public static final String JSON_PROPERTY_UNSAFE_DRV_C_N_T = "unsafeDrvCNT";
  public static final String JSON_PROPERTY_HOS_P_C_T = "hosPCT";
  public static final String JSON_PROPERTY_HOS_O_T = "hosOT";
  public static final String JSON_PROPERTY_HOS_S_V = "hosSV";
  public static final String JSON_PROPERTY_HOS_ALERT = "hosAlert";
  public static final String JSON_PROPERTY_HOS_TREND = "hosTrend";
  public static final String JSON_PROPERTY_HOS_C_N_T = "hosCNT";
  public static final String JSON_PROPERTY_DRV_FIT_P_C_T = "drvFitPCT";
  public static final String JSON_PROPERTY_DRV_FIT_O_T = "drvFitOT";
  public static final String JSON_PROPERTY_DRV_FIT_S_V = "drvFitSV";
  public static final String JSON_PROPERTY_DRV_FIT_ALERT = "drvFitAlert";
  public static final String JSON_PROPERTY_DRV_FIT_TREND = "drvFitTrend";
  public static final String JSON_PROPERTY_DRV_FIT_C_N_T = "drvFitCNT";
  public static final String JSON_PROPERTY_CONTROL_SUB_P_C_T = "controlSubPCT";
  public static final String JSON_PROPERTY_CONTROL_SUB_O_T = "controlSubOT";
  public static final String JSON_PROPERTY_CONTROL_SUB_S_V = "controlSubSV";
  public static final String JSON_PROPERTY_CONTROL_SUB_ALERT = "controlSubAlert";
  public static final String JSON_PROPERTY_CONTROL_SUB_TREND = "controlSubTrend";
  public static final String JSON_PROPERTY_CONTROL_SUB_C_N_T = "controlSubCNT";
  public static final String JSON_PROPERTY_VEH_MAINT_P_C_T = "vehMaintPCT";
  public static final String JSON_PROPERTY_VEH_MAINT_O_T = "vehMaintOT";
  public static final String JSON_PROPERTY_VEH_MAINT_S_V = "vehMaintSV";
  public static final String JSON_PROPERTY_VEH_MAINT_ALERT = "vehMaintAlert";
  public static final String JSON_PROPERTY_VEH_MAINT_TREND = "vehMaintTrend";
  public static final String JSON_PROPERTY_VEH_MAINT_C_N_T = "vehMaintCNT";
  public static final String JSON_PROPERTY_HAZ_MAT_P_C_T = "hazMatPCT";
  public static final String JSON_PROPERTY_HAZ_MAT_O_T = "hazMatOT";
  public static final String JSON_PROPERTY_HAZ_MAT_S_V = "hazMatSV";
  public static final String JSON_PROPERTY_HAZ_MAT_ALERT = "hazMatAlert";
  public static final String JSON_PROPERTY_HAZ_MAT_TREND = "hazMatTrend";
  public static final String JSON_PROPERTY_HAZ_MAT_C_N_T = "hazMatCNT";
  private String rating;
  private String ratingDate;
  private String unsafeDrvPCT;
  private String unsafeDrvOT;
  private String unsafeDrvSV;
  private String unsafeDrvAlert;
  private String unsafeDrvTrend;
  private Integer unsafeDrvCNT;
  private String hosPCT;
  private String hosOT;
  private String hosSV;
  private String hosAlert;
  private String hosTrend;
  private Integer hosCNT;
  private String drvFitPCT;
  private String drvFitOT;
  private String drvFitSV;
  private String drvFitAlert;
  private String drvFitTrend;
  private Integer drvFitCNT;
  private String controlSubPCT;
  private String controlSubOT;
  private String controlSubSV;
  private String controlSubAlert;
  private String controlSubTrend;
  private Integer controlSubCNT;
  private String vehMaintPCT;
  private String vehMaintOT;
  private String vehMaintSV;
  private String vehMaintAlert;
  private String vehMaintTrend;
  private Integer vehMaintCNT;
  private String hazMatPCT;
  private String hazMatOT;
  private String hazMatSV;
  private String hazMatAlert;
  private String hazMatTrend;
  private Integer hazMatCNT;

  public MyCarrierPacketsApiFMCSASafety() {
  }

  public MyCarrierPacketsApiFMCSASafety rating(String rating) {

    this.rating = rating;
    return this;
  }

  /**
   * Get rating
   *
   * @return rating
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_RATING)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getRating() {
    return rating;
  }


  @JsonProperty(JSON_PROPERTY_RATING)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setRating(String rating) {
    this.rating = rating;
  }


  public MyCarrierPacketsApiFMCSASafety ratingDate(String ratingDate) {

    this.ratingDate = ratingDate;
    return this;
  }

  /**
   * Get ratingDate
   *
   * @return ratingDate
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_RATING_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getRatingDate() {
    return ratingDate;
  }


  @JsonProperty(JSON_PROPERTY_RATING_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setRatingDate(String ratingDate) {
    this.ratingDate = ratingDate;
  }


  public MyCarrierPacketsApiFMCSASafety unsafeDrvPCT(String unsafeDrvPCT) {

    this.unsafeDrvPCT = unsafeDrvPCT;
    return this;
  }

  /**
   * Get unsafeDrvPCT
   *
   * @return unsafeDrvPCT
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_UNSAFE_DRV_P_C_T)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getUnsafeDrvPCT() {
    return unsafeDrvPCT;
  }


  @JsonProperty(JSON_PROPERTY_UNSAFE_DRV_P_C_T)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setUnsafeDrvPCT(String unsafeDrvPCT) {
    this.unsafeDrvPCT = unsafeDrvPCT;
  }


  public MyCarrierPacketsApiFMCSASafety unsafeDrvOT(String unsafeDrvOT) {

    this.unsafeDrvOT = unsafeDrvOT;
    return this;
  }

  /**
   * Get unsafeDrvOT
   *
   * @return unsafeDrvOT
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_UNSAFE_DRV_O_T)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getUnsafeDrvOT() {
    return unsafeDrvOT;
  }


  @JsonProperty(JSON_PROPERTY_UNSAFE_DRV_O_T)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setUnsafeDrvOT(String unsafeDrvOT) {
    this.unsafeDrvOT = unsafeDrvOT;
  }


  public MyCarrierPacketsApiFMCSASafety unsafeDrvSV(String unsafeDrvSV) {

    this.unsafeDrvSV = unsafeDrvSV;
    return this;
  }

  /**
   * Get unsafeDrvSV
   *
   * @return unsafeDrvSV
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_UNSAFE_DRV_S_V)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getUnsafeDrvSV() {
    return unsafeDrvSV;
  }


  @JsonProperty(JSON_PROPERTY_UNSAFE_DRV_S_V)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setUnsafeDrvSV(String unsafeDrvSV) {
    this.unsafeDrvSV = unsafeDrvSV;
  }


  public MyCarrierPacketsApiFMCSASafety unsafeDrvAlert(String unsafeDrvAlert) {

    this.unsafeDrvAlert = unsafeDrvAlert;
    return this;
  }

  /**
   * Get unsafeDrvAlert
   *
   * @return unsafeDrvAlert
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_UNSAFE_DRV_ALERT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getUnsafeDrvAlert() {
    return unsafeDrvAlert;
  }


  @JsonProperty(JSON_PROPERTY_UNSAFE_DRV_ALERT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setUnsafeDrvAlert(String unsafeDrvAlert) {
    this.unsafeDrvAlert = unsafeDrvAlert;
  }


  public MyCarrierPacketsApiFMCSASafety unsafeDrvTrend(String unsafeDrvTrend) {

    this.unsafeDrvTrend = unsafeDrvTrend;
    return this;
  }

  /**
   * Get unsafeDrvTrend
   *
   * @return unsafeDrvTrend
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_UNSAFE_DRV_TREND)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getUnsafeDrvTrend() {
    return unsafeDrvTrend;
  }


  @JsonProperty(JSON_PROPERTY_UNSAFE_DRV_TREND)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setUnsafeDrvTrend(String unsafeDrvTrend) {
    this.unsafeDrvTrend = unsafeDrvTrend;
  }


  public MyCarrierPacketsApiFMCSASafety unsafeDrvCNT(Integer unsafeDrvCNT) {

    this.unsafeDrvCNT = unsafeDrvCNT;
    return this;
  }

  /**
   * Get unsafeDrvCNT
   *
   * @return unsafeDrvCNT
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_UNSAFE_DRV_C_N_T)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Integer getUnsafeDrvCNT() {
    return unsafeDrvCNT;
  }


  @JsonProperty(JSON_PROPERTY_UNSAFE_DRV_C_N_T)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setUnsafeDrvCNT(Integer unsafeDrvCNT) {
    this.unsafeDrvCNT = unsafeDrvCNT;
  }


  public MyCarrierPacketsApiFMCSASafety hosPCT(String hosPCT) {

    this.hosPCT = hosPCT;
    return this;
  }

  /**
   * Get hosPCT
   *
   * @return hosPCT
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_HOS_P_C_T)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getHosPCT() {
    return hosPCT;
  }


  @JsonProperty(JSON_PROPERTY_HOS_P_C_T)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setHosPCT(String hosPCT) {
    this.hosPCT = hosPCT;
  }


  public MyCarrierPacketsApiFMCSASafety hosOT(String hosOT) {

    this.hosOT = hosOT;
    return this;
  }

  /**
   * Get hosOT
   *
   * @return hosOT
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_HOS_O_T)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getHosOT() {
    return hosOT;
  }


  @JsonProperty(JSON_PROPERTY_HOS_O_T)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setHosOT(String hosOT) {
    this.hosOT = hosOT;
  }


  public MyCarrierPacketsApiFMCSASafety hosSV(String hosSV) {

    this.hosSV = hosSV;
    return this;
  }

  /**
   * Get hosSV
   *
   * @return hosSV
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_HOS_S_V)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getHosSV() {
    return hosSV;
  }


  @JsonProperty(JSON_PROPERTY_HOS_S_V)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setHosSV(String hosSV) {
    this.hosSV = hosSV;
  }


  public MyCarrierPacketsApiFMCSASafety hosAlert(String hosAlert) {

    this.hosAlert = hosAlert;
    return this;
  }

  /**
   * Get hosAlert
   *
   * @return hosAlert
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_HOS_ALERT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getHosAlert() {
    return hosAlert;
  }


  @JsonProperty(JSON_PROPERTY_HOS_ALERT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setHosAlert(String hosAlert) {
    this.hosAlert = hosAlert;
  }


  public MyCarrierPacketsApiFMCSASafety hosTrend(String hosTrend) {

    this.hosTrend = hosTrend;
    return this;
  }

  /**
   * Get hosTrend
   *
   * @return hosTrend
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_HOS_TREND)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getHosTrend() {
    return hosTrend;
  }


  @JsonProperty(JSON_PROPERTY_HOS_TREND)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setHosTrend(String hosTrend) {
    this.hosTrend = hosTrend;
  }


  public MyCarrierPacketsApiFMCSASafety hosCNT(Integer hosCNT) {

    this.hosCNT = hosCNT;
    return this;
  }

  /**
   * Get hosCNT
   *
   * @return hosCNT
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_HOS_C_N_T)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Integer getHosCNT() {
    return hosCNT;
  }


  @JsonProperty(JSON_PROPERTY_HOS_C_N_T)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setHosCNT(Integer hosCNT) {
    this.hosCNT = hosCNT;
  }


  public MyCarrierPacketsApiFMCSASafety drvFitPCT(String drvFitPCT) {

    this.drvFitPCT = drvFitPCT;
    return this;
  }

  /**
   * Get drvFitPCT
   *
   * @return drvFitPCT
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_DRV_FIT_P_C_T)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getDrvFitPCT() {
    return drvFitPCT;
  }


  @JsonProperty(JSON_PROPERTY_DRV_FIT_P_C_T)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setDrvFitPCT(String drvFitPCT) {
    this.drvFitPCT = drvFitPCT;
  }


  public MyCarrierPacketsApiFMCSASafety drvFitOT(String drvFitOT) {

    this.drvFitOT = drvFitOT;
    return this;
  }

  /**
   * Get drvFitOT
   *
   * @return drvFitOT
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_DRV_FIT_O_T)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getDrvFitOT() {
    return drvFitOT;
  }


  @JsonProperty(JSON_PROPERTY_DRV_FIT_O_T)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setDrvFitOT(String drvFitOT) {
    this.drvFitOT = drvFitOT;
  }


  public MyCarrierPacketsApiFMCSASafety drvFitSV(String drvFitSV) {

    this.drvFitSV = drvFitSV;
    return this;
  }

  /**
   * Get drvFitSV
   *
   * @return drvFitSV
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_DRV_FIT_S_V)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getDrvFitSV() {
    return drvFitSV;
  }


  @JsonProperty(JSON_PROPERTY_DRV_FIT_S_V)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setDrvFitSV(String drvFitSV) {
    this.drvFitSV = drvFitSV;
  }


  public MyCarrierPacketsApiFMCSASafety drvFitAlert(String drvFitAlert) {

    this.drvFitAlert = drvFitAlert;
    return this;
  }

  /**
   * Get drvFitAlert
   *
   * @return drvFitAlert
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_DRV_FIT_ALERT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getDrvFitAlert() {
    return drvFitAlert;
  }


  @JsonProperty(JSON_PROPERTY_DRV_FIT_ALERT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setDrvFitAlert(String drvFitAlert) {
    this.drvFitAlert = drvFitAlert;
  }


  public MyCarrierPacketsApiFMCSASafety drvFitTrend(String drvFitTrend) {

    this.drvFitTrend = drvFitTrend;
    return this;
  }

  /**
   * Get drvFitTrend
   *
   * @return drvFitTrend
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_DRV_FIT_TREND)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getDrvFitTrend() {
    return drvFitTrend;
  }


  @JsonProperty(JSON_PROPERTY_DRV_FIT_TREND)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setDrvFitTrend(String drvFitTrend) {
    this.drvFitTrend = drvFitTrend;
  }


  public MyCarrierPacketsApiFMCSASafety drvFitCNT(Integer drvFitCNT) {

    this.drvFitCNT = drvFitCNT;
    return this;
  }

  /**
   * Get drvFitCNT
   *
   * @return drvFitCNT
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_DRV_FIT_C_N_T)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Integer getDrvFitCNT() {
    return drvFitCNT;
  }


  @JsonProperty(JSON_PROPERTY_DRV_FIT_C_N_T)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setDrvFitCNT(Integer drvFitCNT) {
    this.drvFitCNT = drvFitCNT;
  }


  public MyCarrierPacketsApiFMCSASafety controlSubPCT(String controlSubPCT) {

    this.controlSubPCT = controlSubPCT;
    return this;
  }

  /**
   * Get controlSubPCT
   *
   * @return controlSubPCT
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CONTROL_SUB_P_C_T)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getControlSubPCT() {
    return controlSubPCT;
  }


  @JsonProperty(JSON_PROPERTY_CONTROL_SUB_P_C_T)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setControlSubPCT(String controlSubPCT) {
    this.controlSubPCT = controlSubPCT;
  }


  public MyCarrierPacketsApiFMCSASafety controlSubOT(String controlSubOT) {

    this.controlSubOT = controlSubOT;
    return this;
  }

  /**
   * Get controlSubOT
   *
   * @return controlSubOT
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CONTROL_SUB_O_T)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getControlSubOT() {
    return controlSubOT;
  }


  @JsonProperty(JSON_PROPERTY_CONTROL_SUB_O_T)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setControlSubOT(String controlSubOT) {
    this.controlSubOT = controlSubOT;
  }


  public MyCarrierPacketsApiFMCSASafety controlSubSV(String controlSubSV) {

    this.controlSubSV = controlSubSV;
    return this;
  }

  /**
   * Get controlSubSV
   *
   * @return controlSubSV
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CONTROL_SUB_S_V)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getControlSubSV() {
    return controlSubSV;
  }


  @JsonProperty(JSON_PROPERTY_CONTROL_SUB_S_V)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setControlSubSV(String controlSubSV) {
    this.controlSubSV = controlSubSV;
  }


  public MyCarrierPacketsApiFMCSASafety controlSubAlert(String controlSubAlert) {

    this.controlSubAlert = controlSubAlert;
    return this;
  }

  /**
   * Get controlSubAlert
   *
   * @return controlSubAlert
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CONTROL_SUB_ALERT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getControlSubAlert() {
    return controlSubAlert;
  }


  @JsonProperty(JSON_PROPERTY_CONTROL_SUB_ALERT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setControlSubAlert(String controlSubAlert) {
    this.controlSubAlert = controlSubAlert;
  }


  public MyCarrierPacketsApiFMCSASafety controlSubTrend(String controlSubTrend) {

    this.controlSubTrend = controlSubTrend;
    return this;
  }

  /**
   * Get controlSubTrend
   *
   * @return controlSubTrend
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CONTROL_SUB_TREND)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getControlSubTrend() {
    return controlSubTrend;
  }


  @JsonProperty(JSON_PROPERTY_CONTROL_SUB_TREND)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setControlSubTrend(String controlSubTrend) {
    this.controlSubTrend = controlSubTrend;
  }


  public MyCarrierPacketsApiFMCSASafety controlSubCNT(Integer controlSubCNT) {

    this.controlSubCNT = controlSubCNT;
    return this;
  }

  /**
   * Get controlSubCNT
   *
   * @return controlSubCNT
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CONTROL_SUB_C_N_T)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Integer getControlSubCNT() {
    return controlSubCNT;
  }


  @JsonProperty(JSON_PROPERTY_CONTROL_SUB_C_N_T)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setControlSubCNT(Integer controlSubCNT) {
    this.controlSubCNT = controlSubCNT;
  }


  public MyCarrierPacketsApiFMCSASafety vehMaintPCT(String vehMaintPCT) {

    this.vehMaintPCT = vehMaintPCT;
    return this;
  }

  /**
   * Get vehMaintPCT
   *
   * @return vehMaintPCT
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_VEH_MAINT_P_C_T)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getVehMaintPCT() {
    return vehMaintPCT;
  }


  @JsonProperty(JSON_PROPERTY_VEH_MAINT_P_C_T)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setVehMaintPCT(String vehMaintPCT) {
    this.vehMaintPCT = vehMaintPCT;
  }


  public MyCarrierPacketsApiFMCSASafety vehMaintOT(String vehMaintOT) {

    this.vehMaintOT = vehMaintOT;
    return this;
  }

  /**
   * Get vehMaintOT
   *
   * @return vehMaintOT
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_VEH_MAINT_O_T)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getVehMaintOT() {
    return vehMaintOT;
  }


  @JsonProperty(JSON_PROPERTY_VEH_MAINT_O_T)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setVehMaintOT(String vehMaintOT) {
    this.vehMaintOT = vehMaintOT;
  }


  public MyCarrierPacketsApiFMCSASafety vehMaintSV(String vehMaintSV) {

    this.vehMaintSV = vehMaintSV;
    return this;
  }

  /**
   * Get vehMaintSV
   *
   * @return vehMaintSV
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_VEH_MAINT_S_V)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getVehMaintSV() {
    return vehMaintSV;
  }


  @JsonProperty(JSON_PROPERTY_VEH_MAINT_S_V)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setVehMaintSV(String vehMaintSV) {
    this.vehMaintSV = vehMaintSV;
  }


  public MyCarrierPacketsApiFMCSASafety vehMaintAlert(String vehMaintAlert) {

    this.vehMaintAlert = vehMaintAlert;
    return this;
  }

  /**
   * Get vehMaintAlert
   *
   * @return vehMaintAlert
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_VEH_MAINT_ALERT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getVehMaintAlert() {
    return vehMaintAlert;
  }


  @JsonProperty(JSON_PROPERTY_VEH_MAINT_ALERT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setVehMaintAlert(String vehMaintAlert) {
    this.vehMaintAlert = vehMaintAlert;
  }


  public MyCarrierPacketsApiFMCSASafety vehMaintTrend(String vehMaintTrend) {

    this.vehMaintTrend = vehMaintTrend;
    return this;
  }

  /**
   * Get vehMaintTrend
   *
   * @return vehMaintTrend
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_VEH_MAINT_TREND)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getVehMaintTrend() {
    return vehMaintTrend;
  }


  @JsonProperty(JSON_PROPERTY_VEH_MAINT_TREND)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setVehMaintTrend(String vehMaintTrend) {
    this.vehMaintTrend = vehMaintTrend;
  }


  public MyCarrierPacketsApiFMCSASafety vehMaintCNT(Integer vehMaintCNT) {

    this.vehMaintCNT = vehMaintCNT;
    return this;
  }

  /**
   * Get vehMaintCNT
   *
   * @return vehMaintCNT
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_VEH_MAINT_C_N_T)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Integer getVehMaintCNT() {
    return vehMaintCNT;
  }


  @JsonProperty(JSON_PROPERTY_VEH_MAINT_C_N_T)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setVehMaintCNT(Integer vehMaintCNT) {
    this.vehMaintCNT = vehMaintCNT;
  }


  public MyCarrierPacketsApiFMCSASafety hazMatPCT(String hazMatPCT) {

    this.hazMatPCT = hazMatPCT;
    return this;
  }

  /**
   * Get hazMatPCT
   *
   * @return hazMatPCT
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_HAZ_MAT_P_C_T)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getHazMatPCT() {
    return hazMatPCT;
  }


  @JsonProperty(JSON_PROPERTY_HAZ_MAT_P_C_T)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setHazMatPCT(String hazMatPCT) {
    this.hazMatPCT = hazMatPCT;
  }


  public MyCarrierPacketsApiFMCSASafety hazMatOT(String hazMatOT) {

    this.hazMatOT = hazMatOT;
    return this;
  }

  /**
   * Get hazMatOT
   *
   * @return hazMatOT
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_HAZ_MAT_O_T)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getHazMatOT() {
    return hazMatOT;
  }


  @JsonProperty(JSON_PROPERTY_HAZ_MAT_O_T)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setHazMatOT(String hazMatOT) {
    this.hazMatOT = hazMatOT;
  }


  public MyCarrierPacketsApiFMCSASafety hazMatSV(String hazMatSV) {

    this.hazMatSV = hazMatSV;
    return this;
  }

  /**
   * Get hazMatSV
   *
   * @return hazMatSV
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_HAZ_MAT_S_V)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getHazMatSV() {
    return hazMatSV;
  }


  @JsonProperty(JSON_PROPERTY_HAZ_MAT_S_V)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setHazMatSV(String hazMatSV) {
    this.hazMatSV = hazMatSV;
  }


  public MyCarrierPacketsApiFMCSASafety hazMatAlert(String hazMatAlert) {

    this.hazMatAlert = hazMatAlert;
    return this;
  }

  /**
   * Get hazMatAlert
   *
   * @return hazMatAlert
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_HAZ_MAT_ALERT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getHazMatAlert() {
    return hazMatAlert;
  }


  @JsonProperty(JSON_PROPERTY_HAZ_MAT_ALERT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setHazMatAlert(String hazMatAlert) {
    this.hazMatAlert = hazMatAlert;
  }


  public MyCarrierPacketsApiFMCSASafety hazMatTrend(String hazMatTrend) {

    this.hazMatTrend = hazMatTrend;
    return this;
  }

  /**
   * Get hazMatTrend
   *
   * @return hazMatTrend
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_HAZ_MAT_TREND)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getHazMatTrend() {
    return hazMatTrend;
  }


  @JsonProperty(JSON_PROPERTY_HAZ_MAT_TREND)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setHazMatTrend(String hazMatTrend) {
    this.hazMatTrend = hazMatTrend;
  }


  public MyCarrierPacketsApiFMCSASafety hazMatCNT(Integer hazMatCNT) {

    this.hazMatCNT = hazMatCNT;
    return this;
  }

  /**
   * Get hazMatCNT
   *
   * @return hazMatCNT
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_HAZ_MAT_C_N_T)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Integer getHazMatCNT() {
    return hazMatCNT;
  }


  @JsonProperty(JSON_PROPERTY_HAZ_MAT_C_N_T)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setHazMatCNT(Integer hazMatCNT) {
    this.hazMatCNT = hazMatCNT;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    MyCarrierPacketsApiFMCSASafety myCarrierPacketsApiFMCSASafety = (MyCarrierPacketsApiFMCSASafety) o;
    return Objects.equals(this.rating, myCarrierPacketsApiFMCSASafety.rating) && Objects.equals(this.ratingDate, myCarrierPacketsApiFMCSASafety.ratingDate)
           && Objects.equals(this.unsafeDrvPCT, myCarrierPacketsApiFMCSASafety.unsafeDrvPCT) && Objects.equals(this.unsafeDrvOT,
        myCarrierPacketsApiFMCSASafety.unsafeDrvOT) && Objects.equals(this.unsafeDrvSV, myCarrierPacketsApiFMCSASafety.unsafeDrvSV) && Objects.equals(
        this.unsafeDrvAlert, myCarrierPacketsApiFMCSASafety.unsafeDrvAlert) && Objects.equals(this.unsafeDrvTrend,
        myCarrierPacketsApiFMCSASafety.unsafeDrvTrend) && Objects.equals(this.unsafeDrvCNT, myCarrierPacketsApiFMCSASafety.unsafeDrvCNT) && Objects.equals(
        this.hosPCT, myCarrierPacketsApiFMCSASafety.hosPCT) && Objects.equals(this.hosOT, myCarrierPacketsApiFMCSASafety.hosOT) && Objects.equals(this.hosSV,
        myCarrierPacketsApiFMCSASafety.hosSV) && Objects.equals(this.hosAlert, myCarrierPacketsApiFMCSASafety.hosAlert) && Objects.equals(this.hosTrend,
        myCarrierPacketsApiFMCSASafety.hosTrend) && Objects.equals(this.hosCNT, myCarrierPacketsApiFMCSASafety.hosCNT) && Objects.equals(this.drvFitPCT,
        myCarrierPacketsApiFMCSASafety.drvFitPCT) && Objects.equals(this.drvFitOT, myCarrierPacketsApiFMCSASafety.drvFitOT) && Objects.equals(this.drvFitSV,
        myCarrierPacketsApiFMCSASafety.drvFitSV) && Objects.equals(this.drvFitAlert, myCarrierPacketsApiFMCSASafety.drvFitAlert) && Objects.equals(
        this.drvFitTrend, myCarrierPacketsApiFMCSASafety.drvFitTrend) && Objects.equals(this.drvFitCNT, myCarrierPacketsApiFMCSASafety.drvFitCNT)
           && Objects.equals(this.controlSubPCT, myCarrierPacketsApiFMCSASafety.controlSubPCT) && Objects.equals(this.controlSubOT,
        myCarrierPacketsApiFMCSASafety.controlSubOT) && Objects.equals(this.controlSubSV, myCarrierPacketsApiFMCSASafety.controlSubSV) && Objects.equals(
        this.controlSubAlert, myCarrierPacketsApiFMCSASafety.controlSubAlert) && Objects.equals(this.controlSubTrend,
        myCarrierPacketsApiFMCSASafety.controlSubTrend) && Objects.equals(this.controlSubCNT, myCarrierPacketsApiFMCSASafety.controlSubCNT) && Objects.equals(
        this.vehMaintPCT, myCarrierPacketsApiFMCSASafety.vehMaintPCT) && Objects.equals(this.vehMaintOT, myCarrierPacketsApiFMCSASafety.vehMaintOT)
           && Objects.equals(this.vehMaintSV, myCarrierPacketsApiFMCSASafety.vehMaintSV) && Objects.equals(this.vehMaintAlert,
        myCarrierPacketsApiFMCSASafety.vehMaintAlert) && Objects.equals(this.vehMaintTrend, myCarrierPacketsApiFMCSASafety.vehMaintTrend) && Objects.equals(
        this.vehMaintCNT, myCarrierPacketsApiFMCSASafety.vehMaintCNT) && Objects.equals(this.hazMatPCT, myCarrierPacketsApiFMCSASafety.hazMatPCT)
           && Objects.equals(this.hazMatOT, myCarrierPacketsApiFMCSASafety.hazMatOT) && Objects.equals(this.hazMatSV, myCarrierPacketsApiFMCSASafety.hazMatSV)
           && Objects.equals(this.hazMatAlert, myCarrierPacketsApiFMCSASafety.hazMatAlert) && Objects.equals(this.hazMatTrend,
        myCarrierPacketsApiFMCSASafety.hazMatTrend) && Objects.equals(this.hazMatCNT, myCarrierPacketsApiFMCSASafety.hazMatCNT);
  }

  @Override
  public int hashCode() {
    return Objects.hash(rating, ratingDate, unsafeDrvPCT, unsafeDrvOT, unsafeDrvSV, unsafeDrvAlert, unsafeDrvTrend, unsafeDrvCNT, hosPCT, hosOT, hosSV,
        hosAlert, hosTrend, hosCNT, drvFitPCT, drvFitOT, drvFitSV, drvFitAlert, drvFitTrend, drvFitCNT, controlSubPCT, controlSubOT, controlSubSV,
        controlSubAlert, controlSubTrend, controlSubCNT, vehMaintPCT, vehMaintOT, vehMaintSV, vehMaintAlert, vehMaintTrend, vehMaintCNT, hazMatPCT, hazMatOT,
        hazMatSV, hazMatAlert, hazMatTrend, hazMatCNT);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class MyCarrierPacketsApiFMCSASafety {\n");
    sb.append("    rating: ").append(toIndentedString(rating)).append("\n");
    sb.append("    ratingDate: ").append(toIndentedString(ratingDate)).append("\n");
    sb.append("    unsafeDrvPCT: ").append(toIndentedString(unsafeDrvPCT)).append("\n");
    sb.append("    unsafeDrvOT: ").append(toIndentedString(unsafeDrvOT)).append("\n");
    sb.append("    unsafeDrvSV: ").append(toIndentedString(unsafeDrvSV)).append("\n");
    sb.append("    unsafeDrvAlert: ").append(toIndentedString(unsafeDrvAlert)).append("\n");
    sb.append("    unsafeDrvTrend: ").append(toIndentedString(unsafeDrvTrend)).append("\n");
    sb.append("    unsafeDrvCNT: ").append(toIndentedString(unsafeDrvCNT)).append("\n");
    sb.append("    hosPCT: ").append(toIndentedString(hosPCT)).append("\n");
    sb.append("    hosOT: ").append(toIndentedString(hosOT)).append("\n");
    sb.append("    hosSV: ").append(toIndentedString(hosSV)).append("\n");
    sb.append("    hosAlert: ").append(toIndentedString(hosAlert)).append("\n");
    sb.append("    hosTrend: ").append(toIndentedString(hosTrend)).append("\n");
    sb.append("    hosCNT: ").append(toIndentedString(hosCNT)).append("\n");
    sb.append("    drvFitPCT: ").append(toIndentedString(drvFitPCT)).append("\n");
    sb.append("    drvFitOT: ").append(toIndentedString(drvFitOT)).append("\n");
    sb.append("    drvFitSV: ").append(toIndentedString(drvFitSV)).append("\n");
    sb.append("    drvFitAlert: ").append(toIndentedString(drvFitAlert)).append("\n");
    sb.append("    drvFitTrend: ").append(toIndentedString(drvFitTrend)).append("\n");
    sb.append("    drvFitCNT: ").append(toIndentedString(drvFitCNT)).append("\n");
    sb.append("    controlSubPCT: ").append(toIndentedString(controlSubPCT)).append("\n");
    sb.append("    controlSubOT: ").append(toIndentedString(controlSubOT)).append("\n");
    sb.append("    controlSubSV: ").append(toIndentedString(controlSubSV)).append("\n");
    sb.append("    controlSubAlert: ").append(toIndentedString(controlSubAlert)).append("\n");
    sb.append("    controlSubTrend: ").append(toIndentedString(controlSubTrend)).append("\n");
    sb.append("    controlSubCNT: ").append(toIndentedString(controlSubCNT)).append("\n");
    sb.append("    vehMaintPCT: ").append(toIndentedString(vehMaintPCT)).append("\n");
    sb.append("    vehMaintOT: ").append(toIndentedString(vehMaintOT)).append("\n");
    sb.append("    vehMaintSV: ").append(toIndentedString(vehMaintSV)).append("\n");
    sb.append("    vehMaintAlert: ").append(toIndentedString(vehMaintAlert)).append("\n");
    sb.append("    vehMaintTrend: ").append(toIndentedString(vehMaintTrend)).append("\n");
    sb.append("    vehMaintCNT: ").append(toIndentedString(vehMaintCNT)).append("\n");
    sb.append("    hazMatPCT: ").append(toIndentedString(hazMatPCT)).append("\n");
    sb.append("    hazMatOT: ").append(toIndentedString(hazMatOT)).append("\n");
    sb.append("    hazMatSV: ").append(toIndentedString(hazMatSV)).append("\n");
    sb.append("    hazMatAlert: ").append(toIndentedString(hazMatAlert)).append("\n");
    sb.append("    hazMatTrend: ").append(toIndentedString(hazMatTrend)).append("\n");
    sb.append("    hazMatCNT: ").append(toIndentedString(hazMatCNT)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

