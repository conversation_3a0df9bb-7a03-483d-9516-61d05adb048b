package com.bulkloads.web.mcp.ext;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * MyCarrierPacketsApiFMCSACargo
 */
@JsonPropertyOrder({MyCarrierPacketsApiFMCSACargo.JSON_PROPERTY_HAZMAT_INDICATOR, MyCarrierPacketsApiFMCSACargo.JSON_PROPERTY_CARGO_GEN_FREIGHT,
    MyCarrierPacketsApiFMCSACargo.JSON_PROPERTY_CARGO_HOUSEHOLD, MyCarrierPacketsApiFMCSACargo.JSON_PROPERTY_CARGO_METAL,
    MyCarrierPacketsApiFMCSACargo.JSON_PROPERTY_CARGO_MOTOR_VEH, MyCarrierPacketsApiFMCSACargo.JSON_PROPERTY_CARGO_DRIVE_TOW,
    MyCarrierPacketsApiFMCSACargo.JSON_PROPERTY_CARGO_LOG_POLE, MyCarrierPacketsApiFMCSACargo.JSON_PROPERTY_CARGO_BLDG_MATERIAL,
    MyCarrierPacketsApiFMCSACargo.JSON_PROPERTY_CARGO_MOBILE_HOME, MyCarrierPacketsApiFMCSACargo.JSON_PROPERTY_CARGO_MACH_LARGE,
    MyCarrierPacketsApiFMCSACargo.JSON_PROPERTY_CARGO_PRODUCE, MyCarrierPacketsApiFMCSACargo.JSON_PROPERTY_CARGO_LIQ_GAS,
    MyCarrierPacketsApiFMCSACargo.JSON_PROPERTY_CARGO_INTERMODAL, MyCarrierPacketsApiFMCSACargo.JSON_PROPERTY_CARGO_PASSENGERS,
    MyCarrierPacketsApiFMCSACargo.JSON_PROPERTY_CARGO_OILFIELD, MyCarrierPacketsApiFMCSACargo.JSON_PROPERTY_CARGO_LIVESTOCK,
    MyCarrierPacketsApiFMCSACargo.JSON_PROPERTY_CARGO_GRAINFEED, MyCarrierPacketsApiFMCSACargo.JSON_PROPERTY_CARGO_COALCOKE,
    MyCarrierPacketsApiFMCSACargo.JSON_PROPERTY_CARGO_MEAT, MyCarrierPacketsApiFMCSACargo.JSON_PROPERTY_CARGO_GARBAGE,
    MyCarrierPacketsApiFMCSACargo.JSON_PROPERTY_CARGO_U_S_MAIL, MyCarrierPacketsApiFMCSACargo.JSON_PROPERTY_CARGO_CHEMICALS,
    MyCarrierPacketsApiFMCSACargo.JSON_PROPERTY_CARGO_DRY_BULK, MyCarrierPacketsApiFMCSACargo.JSON_PROPERTY_CARGO_REFRIGERATED,
    MyCarrierPacketsApiFMCSACargo.JSON_PROPERTY_CARGO_BEVERAGES, MyCarrierPacketsApiFMCSACargo.JSON_PROPERTY_CARGO_PAPER_PROD,
    MyCarrierPacketsApiFMCSACargo.JSON_PROPERTY_CARGO_UTILITIES, MyCarrierPacketsApiFMCSACargo.JSON_PROPERTY_CARGO_FARM_SUPPLIES,
    MyCarrierPacketsApiFMCSACargo.JSON_PROPERTY_CARGO_CONSTRUCTION, MyCarrierPacketsApiFMCSACargo.JSON_PROPERTY_CARGO_WATERWELL,
    MyCarrierPacketsApiFMCSACargo.JSON_PROPERTY_CARGO_OTHER, MyCarrierPacketsApiFMCSACargo.JSON_PROPERTY_CARGO_OTHER_DESC})
@JsonTypeName("MyCarrierPacketsApi.FMCSA.Cargo")

public class MyCarrierPacketsApiFMCSACargo {

  public static final String JSON_PROPERTY_HAZMAT_INDICATOR = "hazmatIndicator";
  public static final String JSON_PROPERTY_CARGO_GEN_FREIGHT = "cargoGenFreight";
  public static final String JSON_PROPERTY_CARGO_HOUSEHOLD = "cargoHousehold";
  public static final String JSON_PROPERTY_CARGO_METAL = "cargoMetal";
  public static final String JSON_PROPERTY_CARGO_MOTOR_VEH = "cargoMotorVeh";
  public static final String JSON_PROPERTY_CARGO_DRIVE_TOW = "cargoDriveTow";
  public static final String JSON_PROPERTY_CARGO_LOG_POLE = "cargoLogPole";
  public static final String JSON_PROPERTY_CARGO_BLDG_MATERIAL = "cargoBldgMaterial";
  public static final String JSON_PROPERTY_CARGO_MOBILE_HOME = "cargoMobileHome";
  public static final String JSON_PROPERTY_CARGO_MACH_LARGE = "cargoMachLarge";
  public static final String JSON_PROPERTY_CARGO_PRODUCE = "cargoProduce";
  public static final String JSON_PROPERTY_CARGO_LIQ_GAS = "cargoLiqGas";
  public static final String JSON_PROPERTY_CARGO_INTERMODAL = "cargoIntermodal";
  public static final String JSON_PROPERTY_CARGO_PASSENGERS = "cargoPassengers";
  public static final String JSON_PROPERTY_CARGO_OILFIELD = "cargoOilfield";
  public static final String JSON_PROPERTY_CARGO_LIVESTOCK = "cargoLivestock";
  public static final String JSON_PROPERTY_CARGO_GRAINFEED = "cargoGrainfeed";
  public static final String JSON_PROPERTY_CARGO_COALCOKE = "cargoCoalcoke";
  public static final String JSON_PROPERTY_CARGO_MEAT = "cargoMeat";
  public static final String JSON_PROPERTY_CARGO_GARBAGE = "cargoGarbage";
  public static final String JSON_PROPERTY_CARGO_U_S_MAIL = "cargoUSMail";
  public static final String JSON_PROPERTY_CARGO_CHEMICALS = "cargoChemicals";
  public static final String JSON_PROPERTY_CARGO_DRY_BULK = "cargoDryBulk";
  public static final String JSON_PROPERTY_CARGO_REFRIGERATED = "cargoRefrigerated";
  public static final String JSON_PROPERTY_CARGO_BEVERAGES = "cargoBeverages";
  public static final String JSON_PROPERTY_CARGO_PAPER_PROD = "cargoPaperProd";
  public static final String JSON_PROPERTY_CARGO_UTILITIES = "cargoUtilities";
  public static final String JSON_PROPERTY_CARGO_FARM_SUPPLIES = "cargoFarmSupplies";
  public static final String JSON_PROPERTY_CARGO_CONSTRUCTION = "cargoConstruction";
  public static final String JSON_PROPERTY_CARGO_WATERWELL = "cargoWaterwell";
  public static final String JSON_PROPERTY_CARGO_OTHER = "cargoOther";
  public static final String JSON_PROPERTY_CARGO_OTHER_DESC = "cargoOtherDesc";
  private String hazmatIndicator;
  private String cargoGenFreight;
  private String cargoHousehold;
  private String cargoMetal;
  private String cargoMotorVeh;
  private String cargoDriveTow;
  private String cargoLogPole;
  private String cargoBldgMaterial;
  private String cargoMobileHome;
  private String cargoMachLarge;
  private String cargoProduce;
  private String cargoLiqGas;
  private String cargoIntermodal;
  private String cargoPassengers;
  private String cargoOilfield;
  private String cargoLivestock;
  private String cargoGrainfeed;
  private String cargoCoalcoke;
  private String cargoMeat;
  private String cargoGarbage;
  private String cargoUSMail;
  private String cargoChemicals;
  private String cargoDryBulk;
  private String cargoRefrigerated;
  private String cargoBeverages;
  private String cargoPaperProd;
  private String cargoUtilities;
  private String cargoFarmSupplies;
  private String cargoConstruction;
  private String cargoWaterwell;
  private String cargoOther;
  private String cargoOtherDesc;

  public MyCarrierPacketsApiFMCSACargo() {
  }

  public MyCarrierPacketsApiFMCSACargo hazmatIndicator(String hazmatIndicator) {

    this.hazmatIndicator = hazmatIndicator;
    return this;
  }

  /**
   * Get hazmatIndicator
   *
   * @return hazmatIndicator
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_HAZMAT_INDICATOR)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getHazmatIndicator() {
    return hazmatIndicator;
  }


  @JsonProperty(JSON_PROPERTY_HAZMAT_INDICATOR)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setHazmatIndicator(String hazmatIndicator) {
    this.hazmatIndicator = hazmatIndicator;
  }


  public MyCarrierPacketsApiFMCSACargo cargoGenFreight(String cargoGenFreight) {

    this.cargoGenFreight = cargoGenFreight;
    return this;
  }

  /**
   * Get cargoGenFreight
   *
   * @return cargoGenFreight
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CARGO_GEN_FREIGHT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getCargoGenFreight() {
    return cargoGenFreight;
  }


  @JsonProperty(JSON_PROPERTY_CARGO_GEN_FREIGHT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCargoGenFreight(String cargoGenFreight) {
    this.cargoGenFreight = cargoGenFreight;
  }


  public MyCarrierPacketsApiFMCSACargo cargoHousehold(String cargoHousehold) {

    this.cargoHousehold = cargoHousehold;
    return this;
  }

  /**
   * Get cargoHousehold
   *
   * @return cargoHousehold
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CARGO_HOUSEHOLD)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getCargoHousehold() {
    return cargoHousehold;
  }


  @JsonProperty(JSON_PROPERTY_CARGO_HOUSEHOLD)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCargoHousehold(String cargoHousehold) {
    this.cargoHousehold = cargoHousehold;
  }


  public MyCarrierPacketsApiFMCSACargo cargoMetal(String cargoMetal) {

    this.cargoMetal = cargoMetal;
    return this;
  }

  /**
   * Get cargoMetal
   *
   * @return cargoMetal
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CARGO_METAL)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getCargoMetal() {
    return cargoMetal;
  }


  @JsonProperty(JSON_PROPERTY_CARGO_METAL)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCargoMetal(String cargoMetal) {
    this.cargoMetal = cargoMetal;
  }


  public MyCarrierPacketsApiFMCSACargo cargoMotorVeh(String cargoMotorVeh) {

    this.cargoMotorVeh = cargoMotorVeh;
    return this;
  }

  /**
   * Get cargoMotorVeh
   *
   * @return cargoMotorVeh
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CARGO_MOTOR_VEH)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getCargoMotorVeh() {
    return cargoMotorVeh;
  }


  @JsonProperty(JSON_PROPERTY_CARGO_MOTOR_VEH)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCargoMotorVeh(String cargoMotorVeh) {
    this.cargoMotorVeh = cargoMotorVeh;
  }


  public MyCarrierPacketsApiFMCSACargo cargoDriveTow(String cargoDriveTow) {

    this.cargoDriveTow = cargoDriveTow;
    return this;
  }

  /**
   * Get cargoDriveTow
   *
   * @return cargoDriveTow
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CARGO_DRIVE_TOW)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getCargoDriveTow() {
    return cargoDriveTow;
  }


  @JsonProperty(JSON_PROPERTY_CARGO_DRIVE_TOW)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCargoDriveTow(String cargoDriveTow) {
    this.cargoDriveTow = cargoDriveTow;
  }


  public MyCarrierPacketsApiFMCSACargo cargoLogPole(String cargoLogPole) {

    this.cargoLogPole = cargoLogPole;
    return this;
  }

  /**
   * Get cargoLogPole
   *
   * @return cargoLogPole
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CARGO_LOG_POLE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getCargoLogPole() {
    return cargoLogPole;
  }


  @JsonProperty(JSON_PROPERTY_CARGO_LOG_POLE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCargoLogPole(String cargoLogPole) {
    this.cargoLogPole = cargoLogPole;
  }


  public MyCarrierPacketsApiFMCSACargo cargoBldgMaterial(String cargoBldgMaterial) {

    this.cargoBldgMaterial = cargoBldgMaterial;
    return this;
  }

  /**
   * Get cargoBldgMaterial
   *
   * @return cargoBldgMaterial
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CARGO_BLDG_MATERIAL)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getCargoBldgMaterial() {
    return cargoBldgMaterial;
  }


  @JsonProperty(JSON_PROPERTY_CARGO_BLDG_MATERIAL)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCargoBldgMaterial(String cargoBldgMaterial) {
    this.cargoBldgMaterial = cargoBldgMaterial;
  }


  public MyCarrierPacketsApiFMCSACargo cargoMobileHome(String cargoMobileHome) {

    this.cargoMobileHome = cargoMobileHome;
    return this;
  }

  /**
   * Get cargoMobileHome
   *
   * @return cargoMobileHome
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CARGO_MOBILE_HOME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getCargoMobileHome() {
    return cargoMobileHome;
  }


  @JsonProperty(JSON_PROPERTY_CARGO_MOBILE_HOME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCargoMobileHome(String cargoMobileHome) {
    this.cargoMobileHome = cargoMobileHome;
  }


  public MyCarrierPacketsApiFMCSACargo cargoMachLarge(String cargoMachLarge) {

    this.cargoMachLarge = cargoMachLarge;
    return this;
  }

  /**
   * Get cargoMachLarge
   *
   * @return cargoMachLarge
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CARGO_MACH_LARGE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getCargoMachLarge() {
    return cargoMachLarge;
  }


  @JsonProperty(JSON_PROPERTY_CARGO_MACH_LARGE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCargoMachLarge(String cargoMachLarge) {
    this.cargoMachLarge = cargoMachLarge;
  }


  public MyCarrierPacketsApiFMCSACargo cargoProduce(String cargoProduce) {

    this.cargoProduce = cargoProduce;
    return this;
  }

  /**
   * Get cargoProduce
   *
   * @return cargoProduce
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CARGO_PRODUCE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getCargoProduce() {
    return cargoProduce;
  }


  @JsonProperty(JSON_PROPERTY_CARGO_PRODUCE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCargoProduce(String cargoProduce) {
    this.cargoProduce = cargoProduce;
  }


  public MyCarrierPacketsApiFMCSACargo cargoLiqGas(String cargoLiqGas) {

    this.cargoLiqGas = cargoLiqGas;
    return this;
  }

  /**
   * Get cargoLiqGas
   *
   * @return cargoLiqGas
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CARGO_LIQ_GAS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getCargoLiqGas() {
    return cargoLiqGas;
  }


  @JsonProperty(JSON_PROPERTY_CARGO_LIQ_GAS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCargoLiqGas(String cargoLiqGas) {
    this.cargoLiqGas = cargoLiqGas;
  }


  public MyCarrierPacketsApiFMCSACargo cargoIntermodal(String cargoIntermodal) {

    this.cargoIntermodal = cargoIntermodal;
    return this;
  }

  /**
   * Get cargoIntermodal
   *
   * @return cargoIntermodal
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CARGO_INTERMODAL)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getCargoIntermodal() {
    return cargoIntermodal;
  }


  @JsonProperty(JSON_PROPERTY_CARGO_INTERMODAL)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCargoIntermodal(String cargoIntermodal) {
    this.cargoIntermodal = cargoIntermodal;
  }


  public MyCarrierPacketsApiFMCSACargo cargoPassengers(String cargoPassengers) {

    this.cargoPassengers = cargoPassengers;
    return this;
  }

  /**
   * Get cargoPassengers
   *
   * @return cargoPassengers
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CARGO_PASSENGERS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getCargoPassengers() {
    return cargoPassengers;
  }


  @JsonProperty(JSON_PROPERTY_CARGO_PASSENGERS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCargoPassengers(String cargoPassengers) {
    this.cargoPassengers = cargoPassengers;
  }


  public MyCarrierPacketsApiFMCSACargo cargoOilfield(String cargoOilfield) {

    this.cargoOilfield = cargoOilfield;
    return this;
  }

  /**
   * Get cargoOilfield
   *
   * @return cargoOilfield
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CARGO_OILFIELD)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getCargoOilfield() {
    return cargoOilfield;
  }


  @JsonProperty(JSON_PROPERTY_CARGO_OILFIELD)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCargoOilfield(String cargoOilfield) {
    this.cargoOilfield = cargoOilfield;
  }


  public MyCarrierPacketsApiFMCSACargo cargoLivestock(String cargoLivestock) {

    this.cargoLivestock = cargoLivestock;
    return this;
  }

  /**
   * Get cargoLivestock
   *
   * @return cargoLivestock
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CARGO_LIVESTOCK)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getCargoLivestock() {
    return cargoLivestock;
  }


  @JsonProperty(JSON_PROPERTY_CARGO_LIVESTOCK)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCargoLivestock(String cargoLivestock) {
    this.cargoLivestock = cargoLivestock;
  }


  public MyCarrierPacketsApiFMCSACargo cargoGrainfeed(String cargoGrainfeed) {

    this.cargoGrainfeed = cargoGrainfeed;
    return this;
  }

  /**
   * Get cargoGrainfeed
   *
   * @return cargoGrainfeed
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CARGO_GRAINFEED)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getCargoGrainfeed() {
    return cargoGrainfeed;
  }


  @JsonProperty(JSON_PROPERTY_CARGO_GRAINFEED)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCargoGrainfeed(String cargoGrainfeed) {
    this.cargoGrainfeed = cargoGrainfeed;
  }


  public MyCarrierPacketsApiFMCSACargo cargoCoalcoke(String cargoCoalcoke) {

    this.cargoCoalcoke = cargoCoalcoke;
    return this;
  }

  /**
   * Get cargoCoalcoke
   *
   * @return cargoCoalcoke
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CARGO_COALCOKE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getCargoCoalcoke() {
    return cargoCoalcoke;
  }


  @JsonProperty(JSON_PROPERTY_CARGO_COALCOKE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCargoCoalcoke(String cargoCoalcoke) {
    this.cargoCoalcoke = cargoCoalcoke;
  }


  public MyCarrierPacketsApiFMCSACargo cargoMeat(String cargoMeat) {

    this.cargoMeat = cargoMeat;
    return this;
  }

  /**
   * Get cargoMeat
   *
   * @return cargoMeat
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CARGO_MEAT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getCargoMeat() {
    return cargoMeat;
  }


  @JsonProperty(JSON_PROPERTY_CARGO_MEAT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCargoMeat(String cargoMeat) {
    this.cargoMeat = cargoMeat;
  }


  public MyCarrierPacketsApiFMCSACargo cargoGarbage(String cargoGarbage) {

    this.cargoGarbage = cargoGarbage;
    return this;
  }

  /**
   * Get cargoGarbage
   *
   * @return cargoGarbage
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CARGO_GARBAGE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getCargoGarbage() {
    return cargoGarbage;
  }


  @JsonProperty(JSON_PROPERTY_CARGO_GARBAGE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCargoGarbage(String cargoGarbage) {
    this.cargoGarbage = cargoGarbage;
  }


  public MyCarrierPacketsApiFMCSACargo cargoUSMail(String cargoUSMail) {

    this.cargoUSMail = cargoUSMail;
    return this;
  }

  /**
   * Get cargoUSMail
   *
   * @return cargoUSMail
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CARGO_U_S_MAIL)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getCargoUSMail() {
    return cargoUSMail;
  }


  @JsonProperty(JSON_PROPERTY_CARGO_U_S_MAIL)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCargoUSMail(String cargoUSMail) {
    this.cargoUSMail = cargoUSMail;
  }


  public MyCarrierPacketsApiFMCSACargo cargoChemicals(String cargoChemicals) {

    this.cargoChemicals = cargoChemicals;
    return this;
  }

  /**
   * Get cargoChemicals
   *
   * @return cargoChemicals
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CARGO_CHEMICALS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getCargoChemicals() {
    return cargoChemicals;
  }


  @JsonProperty(JSON_PROPERTY_CARGO_CHEMICALS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCargoChemicals(String cargoChemicals) {
    this.cargoChemicals = cargoChemicals;
  }


  public MyCarrierPacketsApiFMCSACargo cargoDryBulk(String cargoDryBulk) {

    this.cargoDryBulk = cargoDryBulk;
    return this;
  }

  /**
   * Get cargoDryBulk
   *
   * @return cargoDryBulk
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CARGO_DRY_BULK)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getCargoDryBulk() {
    return cargoDryBulk;
  }


  @JsonProperty(JSON_PROPERTY_CARGO_DRY_BULK)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCargoDryBulk(String cargoDryBulk) {
    this.cargoDryBulk = cargoDryBulk;
  }


  public MyCarrierPacketsApiFMCSACargo cargoRefrigerated(String cargoRefrigerated) {

    this.cargoRefrigerated = cargoRefrigerated;
    return this;
  }

  /**
   * Get cargoRefrigerated
   *
   * @return cargoRefrigerated
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CARGO_REFRIGERATED)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getCargoRefrigerated() {
    return cargoRefrigerated;
  }


  @JsonProperty(JSON_PROPERTY_CARGO_REFRIGERATED)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCargoRefrigerated(String cargoRefrigerated) {
    this.cargoRefrigerated = cargoRefrigerated;
  }


  public MyCarrierPacketsApiFMCSACargo cargoBeverages(String cargoBeverages) {

    this.cargoBeverages = cargoBeverages;
    return this;
  }

  /**
   * Get cargoBeverages
   *
   * @return cargoBeverages
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CARGO_BEVERAGES)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getCargoBeverages() {
    return cargoBeverages;
  }


  @JsonProperty(JSON_PROPERTY_CARGO_BEVERAGES)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCargoBeverages(String cargoBeverages) {
    this.cargoBeverages = cargoBeverages;
  }


  public MyCarrierPacketsApiFMCSACargo cargoPaperProd(String cargoPaperProd) {

    this.cargoPaperProd = cargoPaperProd;
    return this;
  }

  /**
   * Get cargoPaperProd
   *
   * @return cargoPaperProd
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CARGO_PAPER_PROD)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getCargoPaperProd() {
    return cargoPaperProd;
  }


  @JsonProperty(JSON_PROPERTY_CARGO_PAPER_PROD)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCargoPaperProd(String cargoPaperProd) {
    this.cargoPaperProd = cargoPaperProd;
  }


  public MyCarrierPacketsApiFMCSACargo cargoUtilities(String cargoUtilities) {

    this.cargoUtilities = cargoUtilities;
    return this;
  }

  /**
   * Get cargoUtilities
   *
   * @return cargoUtilities
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CARGO_UTILITIES)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getCargoUtilities() {
    return cargoUtilities;
  }


  @JsonProperty(JSON_PROPERTY_CARGO_UTILITIES)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCargoUtilities(String cargoUtilities) {
    this.cargoUtilities = cargoUtilities;
  }


  public MyCarrierPacketsApiFMCSACargo cargoFarmSupplies(String cargoFarmSupplies) {

    this.cargoFarmSupplies = cargoFarmSupplies;
    return this;
  }

  /**
   * Get cargoFarmSupplies
   *
   * @return cargoFarmSupplies
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CARGO_FARM_SUPPLIES)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getCargoFarmSupplies() {
    return cargoFarmSupplies;
  }


  @JsonProperty(JSON_PROPERTY_CARGO_FARM_SUPPLIES)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCargoFarmSupplies(String cargoFarmSupplies) {
    this.cargoFarmSupplies = cargoFarmSupplies;
  }


  public MyCarrierPacketsApiFMCSACargo cargoConstruction(String cargoConstruction) {

    this.cargoConstruction = cargoConstruction;
    return this;
  }

  /**
   * Get cargoConstruction
   *
   * @return cargoConstruction
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CARGO_CONSTRUCTION)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getCargoConstruction() {
    return cargoConstruction;
  }


  @JsonProperty(JSON_PROPERTY_CARGO_CONSTRUCTION)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCargoConstruction(String cargoConstruction) {
    this.cargoConstruction = cargoConstruction;
  }


  public MyCarrierPacketsApiFMCSACargo cargoWaterwell(String cargoWaterwell) {

    this.cargoWaterwell = cargoWaterwell;
    return this;
  }

  /**
   * Get cargoWaterwell
   *
   * @return cargoWaterwell
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CARGO_WATERWELL)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getCargoWaterwell() {
    return cargoWaterwell;
  }


  @JsonProperty(JSON_PROPERTY_CARGO_WATERWELL)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCargoWaterwell(String cargoWaterwell) {
    this.cargoWaterwell = cargoWaterwell;
  }


  public MyCarrierPacketsApiFMCSACargo cargoOther(String cargoOther) {

    this.cargoOther = cargoOther;
    return this;
  }

  /**
   * Get cargoOther
   *
   * @return cargoOther
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CARGO_OTHER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getCargoOther() {
    return cargoOther;
  }


  @JsonProperty(JSON_PROPERTY_CARGO_OTHER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCargoOther(String cargoOther) {
    this.cargoOther = cargoOther;
  }


  public MyCarrierPacketsApiFMCSACargo cargoOtherDesc(String cargoOtherDesc) {

    this.cargoOtherDesc = cargoOtherDesc;
    return this;
  }

  /**
   * Get cargoOtherDesc
   *
   * @return cargoOtherDesc
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CARGO_OTHER_DESC)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getCargoOtherDesc() {
    return cargoOtherDesc;
  }


  @JsonProperty(JSON_PROPERTY_CARGO_OTHER_DESC)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCargoOtherDesc(String cargoOtherDesc) {
    this.cargoOtherDesc = cargoOtherDesc;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    MyCarrierPacketsApiFMCSACargo myCarrierPacketsApiFMCSACargo = (MyCarrierPacketsApiFMCSACargo) o;
    return Objects.equals(this.hazmatIndicator, myCarrierPacketsApiFMCSACargo.hazmatIndicator) && Objects.equals(this.cargoGenFreight,
        myCarrierPacketsApiFMCSACargo.cargoGenFreight) && Objects.equals(this.cargoHousehold, myCarrierPacketsApiFMCSACargo.cargoHousehold) && Objects.equals(
        this.cargoMetal, myCarrierPacketsApiFMCSACargo.cargoMetal) && Objects.equals(this.cargoMotorVeh, myCarrierPacketsApiFMCSACargo.cargoMotorVeh)
           && Objects.equals(this.cargoDriveTow, myCarrierPacketsApiFMCSACargo.cargoDriveTow) && Objects.equals(this.cargoLogPole,
        myCarrierPacketsApiFMCSACargo.cargoLogPole) && Objects.equals(this.cargoBldgMaterial, myCarrierPacketsApiFMCSACargo.cargoBldgMaterial)
           && Objects.equals(this.cargoMobileHome, myCarrierPacketsApiFMCSACargo.cargoMobileHome) && Objects.equals(this.cargoMachLarge,
        myCarrierPacketsApiFMCSACargo.cargoMachLarge) && Objects.equals(this.cargoProduce, myCarrierPacketsApiFMCSACargo.cargoProduce) && Objects.equals(
        this.cargoLiqGas, myCarrierPacketsApiFMCSACargo.cargoLiqGas) && Objects.equals(this.cargoIntermodal, myCarrierPacketsApiFMCSACargo.cargoIntermodal)
           && Objects.equals(this.cargoPassengers, myCarrierPacketsApiFMCSACargo.cargoPassengers) && Objects.equals(this.cargoOilfield,
        myCarrierPacketsApiFMCSACargo.cargoOilfield) && Objects.equals(this.cargoLivestock, myCarrierPacketsApiFMCSACargo.cargoLivestock) && Objects.equals(
        this.cargoGrainfeed, myCarrierPacketsApiFMCSACargo.cargoGrainfeed) && Objects.equals(this.cargoCoalcoke, myCarrierPacketsApiFMCSACargo.cargoCoalcoke)
           && Objects.equals(this.cargoMeat, myCarrierPacketsApiFMCSACargo.cargoMeat) && Objects.equals(this.cargoGarbage,
        myCarrierPacketsApiFMCSACargo.cargoGarbage) && Objects.equals(this.cargoUSMail, myCarrierPacketsApiFMCSACargo.cargoUSMail) && Objects.equals(
        this.cargoChemicals, myCarrierPacketsApiFMCSACargo.cargoChemicals) && Objects.equals(this.cargoDryBulk, myCarrierPacketsApiFMCSACargo.cargoDryBulk)
           && Objects.equals(this.cargoRefrigerated, myCarrierPacketsApiFMCSACargo.cargoRefrigerated) && Objects.equals(this.cargoBeverages,
        myCarrierPacketsApiFMCSACargo.cargoBeverages) && Objects.equals(this.cargoPaperProd, myCarrierPacketsApiFMCSACargo.cargoPaperProd) && Objects.equals(
        this.cargoUtilities, myCarrierPacketsApiFMCSACargo.cargoUtilities) && Objects.equals(this.cargoFarmSupplies,
        myCarrierPacketsApiFMCSACargo.cargoFarmSupplies) && Objects.equals(this.cargoConstruction, myCarrierPacketsApiFMCSACargo.cargoConstruction)
           && Objects.equals(this.cargoWaterwell, myCarrierPacketsApiFMCSACargo.cargoWaterwell) && Objects.equals(this.cargoOther,
        myCarrierPacketsApiFMCSACargo.cargoOther) && Objects.equals(this.cargoOtherDesc, myCarrierPacketsApiFMCSACargo.cargoOtherDesc);
  }

  @Override
  public int hashCode() {
    return Objects.hash(hazmatIndicator, cargoGenFreight, cargoHousehold, cargoMetal, cargoMotorVeh, cargoDriveTow, cargoLogPole, cargoBldgMaterial,
        cargoMobileHome, cargoMachLarge, cargoProduce, cargoLiqGas, cargoIntermodal, cargoPassengers, cargoOilfield, cargoLivestock, cargoGrainfeed,
        cargoCoalcoke, cargoMeat, cargoGarbage, cargoUSMail, cargoChemicals, cargoDryBulk, cargoRefrigerated, cargoBeverages, cargoPaperProd, cargoUtilities,
        cargoFarmSupplies, cargoConstruction, cargoWaterwell, cargoOther, cargoOtherDesc);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class MyCarrierPacketsApiFMCSACargo {\n");
    sb.append("    hazmatIndicator: ").append(toIndentedString(hazmatIndicator)).append("\n");
    sb.append("    cargoGenFreight: ").append(toIndentedString(cargoGenFreight)).append("\n");
    sb.append("    cargoHousehold: ").append(toIndentedString(cargoHousehold)).append("\n");
    sb.append("    cargoMetal: ").append(toIndentedString(cargoMetal)).append("\n");
    sb.append("    cargoMotorVeh: ").append(toIndentedString(cargoMotorVeh)).append("\n");
    sb.append("    cargoDriveTow: ").append(toIndentedString(cargoDriveTow)).append("\n");
    sb.append("    cargoLogPole: ").append(toIndentedString(cargoLogPole)).append("\n");
    sb.append("    cargoBldgMaterial: ").append(toIndentedString(cargoBldgMaterial)).append("\n");
    sb.append("    cargoMobileHome: ").append(toIndentedString(cargoMobileHome)).append("\n");
    sb.append("    cargoMachLarge: ").append(toIndentedString(cargoMachLarge)).append("\n");
    sb.append("    cargoProduce: ").append(toIndentedString(cargoProduce)).append("\n");
    sb.append("    cargoLiqGas: ").append(toIndentedString(cargoLiqGas)).append("\n");
    sb.append("    cargoIntermodal: ").append(toIndentedString(cargoIntermodal)).append("\n");
    sb.append("    cargoPassengers: ").append(toIndentedString(cargoPassengers)).append("\n");
    sb.append("    cargoOilfield: ").append(toIndentedString(cargoOilfield)).append("\n");
    sb.append("    cargoLivestock: ").append(toIndentedString(cargoLivestock)).append("\n");
    sb.append("    cargoGrainfeed: ").append(toIndentedString(cargoGrainfeed)).append("\n");
    sb.append("    cargoCoalcoke: ").append(toIndentedString(cargoCoalcoke)).append("\n");
    sb.append("    cargoMeat: ").append(toIndentedString(cargoMeat)).append("\n");
    sb.append("    cargoGarbage: ").append(toIndentedString(cargoGarbage)).append("\n");
    sb.append("    cargoUSMail: ").append(toIndentedString(cargoUSMail)).append("\n");
    sb.append("    cargoChemicals: ").append(toIndentedString(cargoChemicals)).append("\n");
    sb.append("    cargoDryBulk: ").append(toIndentedString(cargoDryBulk)).append("\n");
    sb.append("    cargoRefrigerated: ").append(toIndentedString(cargoRefrigerated)).append("\n");
    sb.append("    cargoBeverages: ").append(toIndentedString(cargoBeverages)).append("\n");
    sb.append("    cargoPaperProd: ").append(toIndentedString(cargoPaperProd)).append("\n");
    sb.append("    cargoUtilities: ").append(toIndentedString(cargoUtilities)).append("\n");
    sb.append("    cargoFarmSupplies: ").append(toIndentedString(cargoFarmSupplies)).append("\n");
    sb.append("    cargoConstruction: ").append(toIndentedString(cargoConstruction)).append("\n");
    sb.append("    cargoWaterwell: ").append(toIndentedString(cargoWaterwell)).append("\n");
    sb.append("    cargoOther: ").append(toIndentedString(cargoOther)).append("\n");
    sb.append("    cargoOtherDesc: ").append(toIndentedString(cargoOtherDesc)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

