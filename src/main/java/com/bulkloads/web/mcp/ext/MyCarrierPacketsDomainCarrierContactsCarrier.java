package com.bulkloads.web.mcp.ext;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * MyCarrierPacketsDomainCarrierContactsCarrier
 */
@JsonPropertyOrder({MyCarrierPacketsDomainCarrierContactsCarrier.JSON_PROPERTY_DO_T_NUMBER,
    MyCarrierPacketsDomainCarrierContactsCarrier.JSON_PROPERTY_DOCKET_NUMBER, MyCarrierPacketsDomainCarrierContactsCarrier.JSON_PROPERTY_LEGAL_NAME,
    MyCarrierPacketsDomainCarrierContactsCarrier.JSON_PROPERTY_DB_A_NAME, MyCarrierPacketsDomainCarrierContactsCarrier.JSON_PROPERTY_CONTACTS})
@JsonTypeName("MyCarrierPacketsDomain.CarrierContacts.Carrier")

public class MyCarrierPacketsDomainCarrierContactsCarrier {

  public static final String JSON_PROPERTY_DO_T_NUMBER = "DOTNumber";
  public static final String JSON_PROPERTY_DOCKET_NUMBER = "DocketNumber";
  public static final String JSON_PROPERTY_LEGAL_NAME = "LegalName";
  public static final String JSON_PROPERTY_DB_A_NAME = "DBAName";
  public static final String JSON_PROPERTY_CONTACTS = "Contacts";
  private Integer doTNumber;
  private String docketNumber;
  private String legalName;
  private String dbAName;
  private List<MyCarrierPacketsDomainCarrierContactsCarrierContact> contacts;

  public MyCarrierPacketsDomainCarrierContactsCarrier() {
  }

  public MyCarrierPacketsDomainCarrierContactsCarrier doTNumber(Integer doTNumber) {

    this.doTNumber = doTNumber;
    return this;
  }

  /**
   * Get doTNumber
   *
   * @return doTNumber
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_DO_T_NUMBER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Integer getDoTNumber() {
    return doTNumber;
  }


  @JsonProperty(JSON_PROPERTY_DO_T_NUMBER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setDoTNumber(Integer doTNumber) {
    this.doTNumber = doTNumber;
  }


  public MyCarrierPacketsDomainCarrierContactsCarrier docketNumber(String docketNumber) {

    this.docketNumber = docketNumber;
    return this;
  }

  /**
   * Get docketNumber
   *
   * @return docketNumber
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_DOCKET_NUMBER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getDocketNumber() {
    return docketNumber;
  }


  @JsonProperty(JSON_PROPERTY_DOCKET_NUMBER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setDocketNumber(String docketNumber) {
    this.docketNumber = docketNumber;
  }


  public MyCarrierPacketsDomainCarrierContactsCarrier legalName(String legalName) {

    this.legalName = legalName;
    return this;
  }

  /**
   * Get legalName
   *
   * @return legalName
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_LEGAL_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getLegalName() {
    return legalName;
  }


  @JsonProperty(JSON_PROPERTY_LEGAL_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setLegalName(String legalName) {
    this.legalName = legalName;
  }


  public MyCarrierPacketsDomainCarrierContactsCarrier dbAName(String dbAName) {

    this.dbAName = dbAName;
    return this;
  }

  /**
   * Get dbAName
   *
   * @return dbAName
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_DB_A_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getDbAName() {
    return dbAName;
  }


  @JsonProperty(JSON_PROPERTY_DB_A_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setDbAName(String dbAName) {
    this.dbAName = dbAName;
  }


  public MyCarrierPacketsDomainCarrierContactsCarrier contacts(List<MyCarrierPacketsDomainCarrierContactsCarrierContact> contacts) {

    this.contacts = contacts;
    return this;
  }

  public MyCarrierPacketsDomainCarrierContactsCarrier addContactsItem(MyCarrierPacketsDomainCarrierContactsCarrierContact contactsItem) {
    if (this.contacts == null) {
      this.contacts = new ArrayList<>();
    }
    this.contacts.add(contactsItem);
    return this;
  }

  /**
   * Get contacts
   *
   * @return contacts
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CONTACTS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public List<MyCarrierPacketsDomainCarrierContactsCarrierContact> getContacts() {
    return contacts;
  }


  @JsonProperty(JSON_PROPERTY_CONTACTS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setContacts(List<MyCarrierPacketsDomainCarrierContactsCarrierContact> contacts) {
    this.contacts = contacts;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    MyCarrierPacketsDomainCarrierContactsCarrier myCarrierPacketsDomainCarrierContactsCarrier = (MyCarrierPacketsDomainCarrierContactsCarrier) o;
    return Objects.equals(this.doTNumber, myCarrierPacketsDomainCarrierContactsCarrier.doTNumber) && Objects.equals(this.docketNumber,
        myCarrierPacketsDomainCarrierContactsCarrier.docketNumber) && Objects.equals(this.legalName, myCarrierPacketsDomainCarrierContactsCarrier.legalName)
           && Objects.equals(this.dbAName, myCarrierPacketsDomainCarrierContactsCarrier.dbAName) && Objects.equals(this.contacts,
        myCarrierPacketsDomainCarrierContactsCarrier.contacts);
  }

  @Override
  public int hashCode() {
    return Objects.hash(doTNumber, docketNumber, legalName, dbAName, contacts);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class MyCarrierPacketsDomainCarrierContactsCarrier {\n");
    sb.append("    doTNumber: ").append(toIndentedString(doTNumber)).append("\n");
    sb.append("    docketNumber: ").append(toIndentedString(docketNumber)).append("\n");
    sb.append("    legalName: ").append(toIndentedString(legalName)).append("\n");
    sb.append("    dbAName: ").append(toIndentedString(dbAName)).append("\n");
    sb.append("    contacts: ").append(toIndentedString(contacts)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

