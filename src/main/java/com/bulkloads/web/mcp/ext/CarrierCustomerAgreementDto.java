package com.bulkloads.web.mcp.ext;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

/**
 * CarrierCustomerAgreementDto
 */
@JsonPropertyOrder({CarrierCustomerAgreementDto.JSON_PROPERTY_SIGNATURE_DATE, CarrierCustomerAgreementDto.JSON_PROPERTY_SIGNATURE_PERSON,
    CarrierCustomerAgreementDto.JSON_PROPERTY_SIGNATURE_PERSON_TITLE, CarrierCustomerAgreementDto.JSON_PROPERTY_SIGNATURE_PERSON_USER_NAME,
    CarrierCustomerAgreementDto.JSO<PERSON>_PROPERTY_SIGNATURE_PERSON_EMAIL, CarrierCustomerAgreementDto.JSON_PROPERTY_SIGNATURE_PERSON_PHONE_NUMBER,
    CarrierCustomerAgreementDto.JSON_PROPERTY_CUSTOMER_AGREEMENT, CarrierCustomerAgreementDto.JSON_PROPERTY_CARRIER_CUSTOMER_AGREEMENT_IMAGES,
    CarrierCustomerAgreementDto.JSON_PROPERTY_IS_ACTIVE, CarrierCustomerAgreementDto.JSON_PROPERTY_IP_ADDRESS})

public class CarrierCustomerAgreementDto {

  public static final String JSON_PROPERTY_SIGNATURE_DATE = "SignatureDate";
  public static final String JSON_PROPERTY_SIGNATURE_PERSON = "SignaturePerson";
  public static final String JSON_PROPERTY_SIGNATURE_PERSON_TITLE = "SignaturePersonTitle";
  public static final String JSON_PROPERTY_SIGNATURE_PERSON_USER_NAME = "SignaturePersonUserName";
  public static final String JSON_PROPERTY_SIGNATURE_PERSON_EMAIL = "SignaturePersonEmail";
  public static final String JSON_PROPERTY_SIGNATURE_PERSON_PHONE_NUMBER = "SignaturePersonPhoneNumber";
  public static final String JSON_PROPERTY_CUSTOMER_AGREEMENT = "CustomerAgreement";
  public static final String JSON_PROPERTY_CARRIER_CUSTOMER_AGREEMENT_IMAGES = "CarrierCustomerAgreementImages";
  public static final String JSON_PROPERTY_IS_ACTIVE = "IsActive";
  public static final String JSON_PROPERTY_IP_ADDRESS = "IPAddress";
  private LocalDateTime signatureDate;
  private String signaturePerson;
  private String signaturePersonTitle;
  private String signaturePersonUserName;
  private String signaturePersonEmail;
  private String signaturePersonPhoneNumber;
  private CustomerAgreementDto customerAgreement;
  private List<CarrierCustomerAgreementImageDto> carrierCustomerAgreementImages;
  private Boolean isActive;
  private String ipAddress;

  public CarrierCustomerAgreementDto() {
  }

  public CarrierCustomerAgreementDto signatureDate(LocalDateTime signatureDate) {

    this.signatureDate = signatureDate;
    return this;
  }

  /**
   * Get signatureDate
   *
   * @return signatureDate
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_SIGNATURE_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public LocalDateTime getSignatureDate() {
    return signatureDate;
  }


  @JsonProperty(JSON_PROPERTY_SIGNATURE_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setSignatureDate(LocalDateTime signatureDate) {
    this.signatureDate = signatureDate;
  }


  public CarrierCustomerAgreementDto signaturePerson(String signaturePerson) {

    this.signaturePerson = signaturePerson;
    return this;
  }

  /**
   * Get signaturePerson
   *
   * @return signaturePerson
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_SIGNATURE_PERSON)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getSignaturePerson() {
    return signaturePerson;
  }


  @JsonProperty(JSON_PROPERTY_SIGNATURE_PERSON)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setSignaturePerson(String signaturePerson) {
    this.signaturePerson = signaturePerson;
  }


  public CarrierCustomerAgreementDto signaturePersonTitle(String signaturePersonTitle) {

    this.signaturePersonTitle = signaturePersonTitle;
    return this;
  }

  /**
   * Get signaturePersonTitle
   *
   * @return signaturePersonTitle
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_SIGNATURE_PERSON_TITLE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getSignaturePersonTitle() {
    return signaturePersonTitle;
  }


  @JsonProperty(JSON_PROPERTY_SIGNATURE_PERSON_TITLE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setSignaturePersonTitle(String signaturePersonTitle) {
    this.signaturePersonTitle = signaturePersonTitle;
  }


  public CarrierCustomerAgreementDto signaturePersonUserName(String signaturePersonUserName) {

    this.signaturePersonUserName = signaturePersonUserName;
    return this;
  }

  /**
   * Get signaturePersonUserName
   *
   * @return signaturePersonUserName
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_SIGNATURE_PERSON_USER_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getSignaturePersonUserName() {
    return signaturePersonUserName;
  }


  @JsonProperty(JSON_PROPERTY_SIGNATURE_PERSON_USER_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setSignaturePersonUserName(String signaturePersonUserName) {
    this.signaturePersonUserName = signaturePersonUserName;
  }


  public CarrierCustomerAgreementDto signaturePersonEmail(String signaturePersonEmail) {

    this.signaturePersonEmail = signaturePersonEmail;
    return this;
  }

  /**
   * Get signaturePersonEmail
   *
   * @return signaturePersonEmail
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_SIGNATURE_PERSON_EMAIL)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getSignaturePersonEmail() {
    return signaturePersonEmail;
  }


  @JsonProperty(JSON_PROPERTY_SIGNATURE_PERSON_EMAIL)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setSignaturePersonEmail(String signaturePersonEmail) {
    this.signaturePersonEmail = signaturePersonEmail;
  }


  public CarrierCustomerAgreementDto signaturePersonPhoneNumber(String signaturePersonPhoneNumber) {

    this.signaturePersonPhoneNumber = signaturePersonPhoneNumber;
    return this;
  }

  /**
   * Get signaturePersonPhoneNumber
   *
   * @return signaturePersonPhoneNumber
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_SIGNATURE_PERSON_PHONE_NUMBER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getSignaturePersonPhoneNumber() {
    return signaturePersonPhoneNumber;
  }


  @JsonProperty(JSON_PROPERTY_SIGNATURE_PERSON_PHONE_NUMBER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setSignaturePersonPhoneNumber(String signaturePersonPhoneNumber) {
    this.signaturePersonPhoneNumber = signaturePersonPhoneNumber;
  }


  public CarrierCustomerAgreementDto customerAgreement(CustomerAgreementDto customerAgreement) {

    this.customerAgreement = customerAgreement;
    return this;
  }

  /**
   * Get customerAgreement
   *
   * @return customerAgreement
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CUSTOMER_AGREEMENT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public CustomerAgreementDto getCustomerAgreement() {
    return customerAgreement;
  }


  @JsonProperty(JSON_PROPERTY_CUSTOMER_AGREEMENT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCustomerAgreement(CustomerAgreementDto customerAgreement) {
    this.customerAgreement = customerAgreement;
  }


  public CarrierCustomerAgreementDto carrierCustomerAgreementImages(List<CarrierCustomerAgreementImageDto> carrierCustomerAgreementImages) {

    this.carrierCustomerAgreementImages = carrierCustomerAgreementImages;
    return this;
  }

  public CarrierCustomerAgreementDto addCarrierCustomerAgreementImagesItem(CarrierCustomerAgreementImageDto carrierCustomerAgreementImagesItem) {
    if (this.carrierCustomerAgreementImages == null) {
      this.carrierCustomerAgreementImages = new ArrayList<>();
    }
    this.carrierCustomerAgreementImages.add(carrierCustomerAgreementImagesItem);
    return this;
  }

  /**
   * Get carrierCustomerAgreementImages
   *
   * @return carrierCustomerAgreementImages
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CARRIER_CUSTOMER_AGREEMENT_IMAGES)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public List<CarrierCustomerAgreementImageDto> getCarrierCustomerAgreementImages() {
    return carrierCustomerAgreementImages;
  }


  @JsonProperty(JSON_PROPERTY_CARRIER_CUSTOMER_AGREEMENT_IMAGES)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCarrierCustomerAgreementImages(List<CarrierCustomerAgreementImageDto> carrierCustomerAgreementImages) {
    this.carrierCustomerAgreementImages = carrierCustomerAgreementImages;
  }


  public CarrierCustomerAgreementDto isActive(Boolean isActive) {

    this.isActive = isActive;
    return this;
  }

  /**
   * Get isActive
   *
   * @return isActive
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_IS_ACTIVE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getIsActive() {
    return isActive;
  }


  @JsonProperty(JSON_PROPERTY_IS_ACTIVE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setIsActive(Boolean isActive) {
    this.isActive = isActive;
  }


  public CarrierCustomerAgreementDto ipAddress(String ipAddress) {

    this.ipAddress = ipAddress;
    return this;
  }

  /**
   * Get ipAddress
   *
   * @return ipAddress
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_IP_ADDRESS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getIpAddress() {
    return ipAddress;
  }


  @JsonProperty(JSON_PROPERTY_IP_ADDRESS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setIpAddress(String ipAddress) {
    this.ipAddress = ipAddress;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    CarrierCustomerAgreementDto carrierCustomerAgreementDto = (CarrierCustomerAgreementDto) o;
    return Objects.equals(this.signatureDate, carrierCustomerAgreementDto.signatureDate) && Objects.equals(this.signaturePerson,
        carrierCustomerAgreementDto.signaturePerson) && Objects.equals(this.signaturePersonTitle, carrierCustomerAgreementDto.signaturePersonTitle)
           && Objects.equals(this.signaturePersonUserName, carrierCustomerAgreementDto.signaturePersonUserName) && Objects.equals(this.signaturePersonEmail,
        carrierCustomerAgreementDto.signaturePersonEmail) && Objects.equals(this.signaturePersonPhoneNumber,
        carrierCustomerAgreementDto.signaturePersonPhoneNumber) && Objects.equals(this.customerAgreement, carrierCustomerAgreementDto.customerAgreement)
           && Objects.equals(this.carrierCustomerAgreementImages, carrierCustomerAgreementDto.carrierCustomerAgreementImages) && Objects.equals(this.isActive,
        carrierCustomerAgreementDto.isActive) && Objects.equals(this.ipAddress, carrierCustomerAgreementDto.ipAddress);
  }

  @Override
  public int hashCode() {
    return Objects.hash(signatureDate, signaturePerson, signaturePersonTitle, signaturePersonUserName, signaturePersonEmail, signaturePersonPhoneNumber,
        customerAgreement, carrierCustomerAgreementImages, isActive, ipAddress);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class CarrierCustomerAgreementDto {\n");
    sb.append("    signatureDate: ").append(toIndentedString(signatureDate)).append("\n");
    sb.append("    signaturePerson: ").append(toIndentedString(signaturePerson)).append("\n");
    sb.append("    signaturePersonTitle: ").append(toIndentedString(signaturePersonTitle)).append("\n");
    sb.append("    signaturePersonUserName: ").append(toIndentedString(signaturePersonUserName)).append("\n");
    sb.append("    signaturePersonEmail: ").append(toIndentedString(signaturePersonEmail)).append("\n");
    sb.append("    signaturePersonPhoneNumber: ").append(toIndentedString(signaturePersonPhoneNumber)).append("\n");
    sb.append("    customerAgreement: ").append(toIndentedString(customerAgreement)).append("\n");
    sb.append("    carrierCustomerAgreementImages: ").append(toIndentedString(carrierCustomerAgreementImages)).append("\n");
    sb.append("    isActive: ").append(toIndentedString(isActive)).append("\n");
    sb.append("    ipAddress: ").append(toIndentedString(ipAddress)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

