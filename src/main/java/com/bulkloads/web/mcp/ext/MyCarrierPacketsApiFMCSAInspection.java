package com.bulkloads.web.mcp.ext;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * MyCarrierPacketsApiFMCSAInspection
 */
@JsonPropertyOrder({MyCarrierPacketsApiFMCSAInspection.JSON_PROPERTY_INSPECT_VEH_U_S, MyCarrierPacketsApiFMCSAInspection.JSON_PROPERTY_INSPECT_VEH_O_O_S_U_S,
    MyCarrierPacketsApiFMCSAInspection.JSON_PROPERTY_INSPECT_VEH_O_O_S_PCT_U_S, MyCarrierPacketsApiFMCSAInspection.JSON_PROPERTY_INSPECT_DRV_U_S,
    MyCarrierPacketsApiFMCSAInspection.JSON_PROPERTY_INSPECT_DRV_O_O_S_U_S, MyCarrierPacketsApiFMCSAInspection.JSON_PROPERTY_INSPECT_DRV_O_O_S_PCT_U_S,
    MyCarrierPacketsApiFMCSAInspection.JSON_PROPERTY_INSPECT_HAZ_U_S, MyCarrierPacketsApiFMCSAInspection.JSON_PROPERTY_INSPECT_HAZ_O_O_S_U_S,
    MyCarrierPacketsApiFMCSAInspection.JSON_PROPERTY_INSPECT_HAZ_O_O_S_PCT_U_S, MyCarrierPacketsApiFMCSAInspection.JSON_PROPERTY_INSPECT_I_E_P_U_S,
    MyCarrierPacketsApiFMCSAInspection.JSON_PROPERTY_INSPECT_I_E_P_O_O_S_U_S, MyCarrierPacketsApiFMCSAInspection.JSON_PROPERTY_INSPECT_I_E_P_O_O_S_PCT_U_S,
    MyCarrierPacketsApiFMCSAInspection.JSON_PROPERTY_INSPECT_TOTAL_I_E_P_U_S, MyCarrierPacketsApiFMCSAInspection.JSON_PROPERTY_INSPECT_TOTAL_U_S,
    MyCarrierPacketsApiFMCSAInspection.JSON_PROPERTY_INSPECT_VEH_C_A_N, MyCarrierPacketsApiFMCSAInspection.JSON_PROPERTY_INSPECT_VEH_O_O_S_C_A_N,
    MyCarrierPacketsApiFMCSAInspection.JSON_PROPERTY_INSPECT_VEH_O_O_S_PCT_C_A_N, MyCarrierPacketsApiFMCSAInspection.JSON_PROPERTY_INSPECT_DRV_C_A_N,
    MyCarrierPacketsApiFMCSAInspection.JSON_PROPERTY_INSPECT_DRV_O_O_S_C_A_N, MyCarrierPacketsApiFMCSAInspection.JSON_PROPERTY_INSPECT_DRV_O_O_S_PCT_C_A_N,
    MyCarrierPacketsApiFMCSAInspection.JSON_PROPERTY_INSPECT_TOTAL_C_A_N})
@JsonTypeName("MyCarrierPacketsApi.FMCSA.Inspection")

public class MyCarrierPacketsApiFMCSAInspection {

  public static final String JSON_PROPERTY_INSPECT_VEH_U_S = "inspectVehUS";
  public static final String JSON_PROPERTY_INSPECT_VEH_O_O_S_U_S = "inspectVehOOSUS";
  public static final String JSON_PROPERTY_INSPECT_VEH_O_O_S_PCT_U_S = "inspectVehOOSPctUS";
  public static final String JSON_PROPERTY_INSPECT_DRV_U_S = "inspectDrvUS";
  public static final String JSON_PROPERTY_INSPECT_DRV_O_O_S_U_S = "inspectDrvOOSUS";
  public static final String JSON_PROPERTY_INSPECT_DRV_O_O_S_PCT_U_S = "inspectDrvOOSPctUS";
  public static final String JSON_PROPERTY_INSPECT_HAZ_U_S = "inspectHazUS";
  public static final String JSON_PROPERTY_INSPECT_HAZ_O_O_S_U_S = "inspectHazOOSUS";
  public static final String JSON_PROPERTY_INSPECT_HAZ_O_O_S_PCT_U_S = "inspectHazOOSPctUS";
  public static final String JSON_PROPERTY_INSPECT_I_E_P_U_S = "inspectIEPUS";
  public static final String JSON_PROPERTY_INSPECT_I_E_P_O_O_S_U_S = "inspectIEPOOSUS";
  public static final String JSON_PROPERTY_INSPECT_I_E_P_O_O_S_PCT_U_S = "inspectIEPOOSPctUS";
  public static final String JSON_PROPERTY_INSPECT_TOTAL_I_E_P_U_S = "inspectTotalIEPUS";
  public static final String JSON_PROPERTY_INSPECT_TOTAL_U_S = "inspectTotalUS";
  public static final String JSON_PROPERTY_INSPECT_VEH_C_A_N = "inspectVehCAN";
  public static final String JSON_PROPERTY_INSPECT_VEH_O_O_S_C_A_N = "inspectVehOOSCAN";
  public static final String JSON_PROPERTY_INSPECT_VEH_O_O_S_PCT_C_A_N = "inspectVehOOSPctCAN";
  public static final String JSON_PROPERTY_INSPECT_DRV_C_A_N = "inspectDrvCAN";
  public static final String JSON_PROPERTY_INSPECT_DRV_O_O_S_C_A_N = "inspectDrvOOSCAN";
  public static final String JSON_PROPERTY_INSPECT_DRV_O_O_S_PCT_C_A_N = "inspectDrvOOSPctCAN";
  public static final String JSON_PROPERTY_INSPECT_TOTAL_C_A_N = "inspectTotalCAN";
  private String inspectVehUS;
  private String inspectVehOOSUS;
  private String inspectVehOOSPctUS;
  private String inspectDrvUS;
  private String inspectDrvOOSUS;
  private String inspectDrvOOSPctUS;
  private String inspectHazUS;
  private String inspectHazOOSUS;
  private String inspectHazOOSPctUS;
  private String inspectIEPUS;
  private String inspectIEPOOSUS;
  private String inspectIEPOOSPctUS;
  private String inspectTotalIEPUS;
  private String inspectTotalUS;
  private String inspectVehCAN;
  private String inspectVehOOSCAN;
  private String inspectVehOOSPctCAN;
  private String inspectDrvCAN;
  private String inspectDrvOOSCAN;
  private String inspectDrvOOSPctCAN;
  private String inspectTotalCAN;

  public MyCarrierPacketsApiFMCSAInspection() {
  }

  public MyCarrierPacketsApiFMCSAInspection inspectVehUS(String inspectVehUS) {

    this.inspectVehUS = inspectVehUS;
    return this;
  }

  /**
   * Get inspectVehUS
   *
   * @return inspectVehUS
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_INSPECT_VEH_U_S)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getInspectVehUS() {
    return inspectVehUS;
  }


  @JsonProperty(JSON_PROPERTY_INSPECT_VEH_U_S)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setInspectVehUS(String inspectVehUS) {
    this.inspectVehUS = inspectVehUS;
  }


  public MyCarrierPacketsApiFMCSAInspection inspectVehOOSUS(String inspectVehOOSUS) {

    this.inspectVehOOSUS = inspectVehOOSUS;
    return this;
  }

  /**
   * Get inspectVehOOSUS
   *
   * @return inspectVehOOSUS
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_INSPECT_VEH_O_O_S_U_S)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getInspectVehOOSUS() {
    return inspectVehOOSUS;
  }


  @JsonProperty(JSON_PROPERTY_INSPECT_VEH_O_O_S_U_S)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setInspectVehOOSUS(String inspectVehOOSUS) {
    this.inspectVehOOSUS = inspectVehOOSUS;
  }


  public MyCarrierPacketsApiFMCSAInspection inspectVehOOSPctUS(String inspectVehOOSPctUS) {

    this.inspectVehOOSPctUS = inspectVehOOSPctUS;
    return this;
  }

  /**
   * Get inspectVehOOSPctUS
   *
   * @return inspectVehOOSPctUS
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_INSPECT_VEH_O_O_S_PCT_U_S)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getInspectVehOOSPctUS() {
    return inspectVehOOSPctUS;
  }


  @JsonProperty(JSON_PROPERTY_INSPECT_VEH_O_O_S_PCT_U_S)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setInspectVehOOSPctUS(String inspectVehOOSPctUS) {
    this.inspectVehOOSPctUS = inspectVehOOSPctUS;
  }


  public MyCarrierPacketsApiFMCSAInspection inspectDrvUS(String inspectDrvUS) {

    this.inspectDrvUS = inspectDrvUS;
    return this;
  }

  /**
   * Get inspectDrvUS
   *
   * @return inspectDrvUS
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_INSPECT_DRV_U_S)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getInspectDrvUS() {
    return inspectDrvUS;
  }


  @JsonProperty(JSON_PROPERTY_INSPECT_DRV_U_S)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setInspectDrvUS(String inspectDrvUS) {
    this.inspectDrvUS = inspectDrvUS;
  }


  public MyCarrierPacketsApiFMCSAInspection inspectDrvOOSUS(String inspectDrvOOSUS) {

    this.inspectDrvOOSUS = inspectDrvOOSUS;
    return this;
  }

  /**
   * Get inspectDrvOOSUS
   *
   * @return inspectDrvOOSUS
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_INSPECT_DRV_O_O_S_U_S)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getInspectDrvOOSUS() {
    return inspectDrvOOSUS;
  }


  @JsonProperty(JSON_PROPERTY_INSPECT_DRV_O_O_S_U_S)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setInspectDrvOOSUS(String inspectDrvOOSUS) {
    this.inspectDrvOOSUS = inspectDrvOOSUS;
  }


  public MyCarrierPacketsApiFMCSAInspection inspectDrvOOSPctUS(String inspectDrvOOSPctUS) {

    this.inspectDrvOOSPctUS = inspectDrvOOSPctUS;
    return this;
  }

  /**
   * Get inspectDrvOOSPctUS
   *
   * @return inspectDrvOOSPctUS
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_INSPECT_DRV_O_O_S_PCT_U_S)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getInspectDrvOOSPctUS() {
    return inspectDrvOOSPctUS;
  }


  @JsonProperty(JSON_PROPERTY_INSPECT_DRV_O_O_S_PCT_U_S)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setInspectDrvOOSPctUS(String inspectDrvOOSPctUS) {
    this.inspectDrvOOSPctUS = inspectDrvOOSPctUS;
  }


  public MyCarrierPacketsApiFMCSAInspection inspectHazUS(String inspectHazUS) {

    this.inspectHazUS = inspectHazUS;
    return this;
  }

  /**
   * Get inspectHazUS
   *
   * @return inspectHazUS
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_INSPECT_HAZ_U_S)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getInspectHazUS() {
    return inspectHazUS;
  }


  @JsonProperty(JSON_PROPERTY_INSPECT_HAZ_U_S)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setInspectHazUS(String inspectHazUS) {
    this.inspectHazUS = inspectHazUS;
  }


  public MyCarrierPacketsApiFMCSAInspection inspectHazOOSUS(String inspectHazOOSUS) {

    this.inspectHazOOSUS = inspectHazOOSUS;
    return this;
  }

  /**
   * Get inspectHazOOSUS
   *
   * @return inspectHazOOSUS
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_INSPECT_HAZ_O_O_S_U_S)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getInspectHazOOSUS() {
    return inspectHazOOSUS;
  }


  @JsonProperty(JSON_PROPERTY_INSPECT_HAZ_O_O_S_U_S)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setInspectHazOOSUS(String inspectHazOOSUS) {
    this.inspectHazOOSUS = inspectHazOOSUS;
  }


  public MyCarrierPacketsApiFMCSAInspection inspectHazOOSPctUS(String inspectHazOOSPctUS) {

    this.inspectHazOOSPctUS = inspectHazOOSPctUS;
    return this;
  }

  /**
   * Get inspectHazOOSPctUS
   *
   * @return inspectHazOOSPctUS
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_INSPECT_HAZ_O_O_S_PCT_U_S)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getInspectHazOOSPctUS() {
    return inspectHazOOSPctUS;
  }


  @JsonProperty(JSON_PROPERTY_INSPECT_HAZ_O_O_S_PCT_U_S)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setInspectHazOOSPctUS(String inspectHazOOSPctUS) {
    this.inspectHazOOSPctUS = inspectHazOOSPctUS;
  }


  public MyCarrierPacketsApiFMCSAInspection inspectIEPUS(String inspectIEPUS) {

    this.inspectIEPUS = inspectIEPUS;
    return this;
  }

  /**
   * Get inspectIEPUS
   *
   * @return inspectIEPUS
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_INSPECT_I_E_P_U_S)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getInspectIEPUS() {
    return inspectIEPUS;
  }


  @JsonProperty(JSON_PROPERTY_INSPECT_I_E_P_U_S)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setInspectIEPUS(String inspectIEPUS) {
    this.inspectIEPUS = inspectIEPUS;
  }


  public MyCarrierPacketsApiFMCSAInspection inspectIEPOOSUS(String inspectIEPOOSUS) {

    this.inspectIEPOOSUS = inspectIEPOOSUS;
    return this;
  }

  /**
   * Get inspectIEPOOSUS
   *
   * @return inspectIEPOOSUS
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_INSPECT_I_E_P_O_O_S_U_S)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getInspectIEPOOSUS() {
    return inspectIEPOOSUS;
  }


  @JsonProperty(JSON_PROPERTY_INSPECT_I_E_P_O_O_S_U_S)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setInspectIEPOOSUS(String inspectIEPOOSUS) {
    this.inspectIEPOOSUS = inspectIEPOOSUS;
  }


  public MyCarrierPacketsApiFMCSAInspection inspectIEPOOSPctUS(String inspectIEPOOSPctUS) {

    this.inspectIEPOOSPctUS = inspectIEPOOSPctUS;
    return this;
  }

  /**
   * Get inspectIEPOOSPctUS
   *
   * @return inspectIEPOOSPctUS
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_INSPECT_I_E_P_O_O_S_PCT_U_S)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getInspectIEPOOSPctUS() {
    return inspectIEPOOSPctUS;
  }


  @JsonProperty(JSON_PROPERTY_INSPECT_I_E_P_O_O_S_PCT_U_S)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setInspectIEPOOSPctUS(String inspectIEPOOSPctUS) {
    this.inspectIEPOOSPctUS = inspectIEPOOSPctUS;
  }


  public MyCarrierPacketsApiFMCSAInspection inspectTotalIEPUS(String inspectTotalIEPUS) {

    this.inspectTotalIEPUS = inspectTotalIEPUS;
    return this;
  }

  /**
   * Get inspectTotalIEPUS
   *
   * @return inspectTotalIEPUS
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_INSPECT_TOTAL_I_E_P_U_S)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getInspectTotalIEPUS() {
    return inspectTotalIEPUS;
  }


  @JsonProperty(JSON_PROPERTY_INSPECT_TOTAL_I_E_P_U_S)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setInspectTotalIEPUS(String inspectTotalIEPUS) {
    this.inspectTotalIEPUS = inspectTotalIEPUS;
  }


  public MyCarrierPacketsApiFMCSAInspection inspectTotalUS(String inspectTotalUS) {

    this.inspectTotalUS = inspectTotalUS;
    return this;
  }

  /**
   * Get inspectTotalUS
   *
   * @return inspectTotalUS
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_INSPECT_TOTAL_U_S)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getInspectTotalUS() {
    return inspectTotalUS;
  }


  @JsonProperty(JSON_PROPERTY_INSPECT_TOTAL_U_S)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setInspectTotalUS(String inspectTotalUS) {
    this.inspectTotalUS = inspectTotalUS;
  }


  public MyCarrierPacketsApiFMCSAInspection inspectVehCAN(String inspectVehCAN) {

    this.inspectVehCAN = inspectVehCAN;
    return this;
  }

  /**
   * Get inspectVehCAN
   *
   * @return inspectVehCAN
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_INSPECT_VEH_C_A_N)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getInspectVehCAN() {
    return inspectVehCAN;
  }


  @JsonProperty(JSON_PROPERTY_INSPECT_VEH_C_A_N)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setInspectVehCAN(String inspectVehCAN) {
    this.inspectVehCAN = inspectVehCAN;
  }


  public MyCarrierPacketsApiFMCSAInspection inspectVehOOSCAN(String inspectVehOOSCAN) {

    this.inspectVehOOSCAN = inspectVehOOSCAN;
    return this;
  }

  /**
   * Get inspectVehOOSCAN
   *
   * @return inspectVehOOSCAN
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_INSPECT_VEH_O_O_S_C_A_N)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getInspectVehOOSCAN() {
    return inspectVehOOSCAN;
  }


  @JsonProperty(JSON_PROPERTY_INSPECT_VEH_O_O_S_C_A_N)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setInspectVehOOSCAN(String inspectVehOOSCAN) {
    this.inspectVehOOSCAN = inspectVehOOSCAN;
  }


  public MyCarrierPacketsApiFMCSAInspection inspectVehOOSPctCAN(String inspectVehOOSPctCAN) {

    this.inspectVehOOSPctCAN = inspectVehOOSPctCAN;
    return this;
  }

  /**
   * Get inspectVehOOSPctCAN
   *
   * @return inspectVehOOSPctCAN
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_INSPECT_VEH_O_O_S_PCT_C_A_N)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getInspectVehOOSPctCAN() {
    return inspectVehOOSPctCAN;
  }


  @JsonProperty(JSON_PROPERTY_INSPECT_VEH_O_O_S_PCT_C_A_N)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setInspectVehOOSPctCAN(String inspectVehOOSPctCAN) {
    this.inspectVehOOSPctCAN = inspectVehOOSPctCAN;
  }


  public MyCarrierPacketsApiFMCSAInspection inspectDrvCAN(String inspectDrvCAN) {

    this.inspectDrvCAN = inspectDrvCAN;
    return this;
  }

  /**
   * Get inspectDrvCAN
   *
   * @return inspectDrvCAN
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_INSPECT_DRV_C_A_N)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getInspectDrvCAN() {
    return inspectDrvCAN;
  }


  @JsonProperty(JSON_PROPERTY_INSPECT_DRV_C_A_N)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setInspectDrvCAN(String inspectDrvCAN) {
    this.inspectDrvCAN = inspectDrvCAN;
  }


  public MyCarrierPacketsApiFMCSAInspection inspectDrvOOSCAN(String inspectDrvOOSCAN) {

    this.inspectDrvOOSCAN = inspectDrvOOSCAN;
    return this;
  }

  /**
   * Get inspectDrvOOSCAN
   *
   * @return inspectDrvOOSCAN
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_INSPECT_DRV_O_O_S_C_A_N)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getInspectDrvOOSCAN() {
    return inspectDrvOOSCAN;
  }


  @JsonProperty(JSON_PROPERTY_INSPECT_DRV_O_O_S_C_A_N)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setInspectDrvOOSCAN(String inspectDrvOOSCAN) {
    this.inspectDrvOOSCAN = inspectDrvOOSCAN;
  }


  public MyCarrierPacketsApiFMCSAInspection inspectDrvOOSPctCAN(String inspectDrvOOSPctCAN) {

    this.inspectDrvOOSPctCAN = inspectDrvOOSPctCAN;
    return this;
  }

  /**
   * Get inspectDrvOOSPctCAN
   *
   * @return inspectDrvOOSPctCAN
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_INSPECT_DRV_O_O_S_PCT_C_A_N)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getInspectDrvOOSPctCAN() {
    return inspectDrvOOSPctCAN;
  }


  @JsonProperty(JSON_PROPERTY_INSPECT_DRV_O_O_S_PCT_C_A_N)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setInspectDrvOOSPctCAN(String inspectDrvOOSPctCAN) {
    this.inspectDrvOOSPctCAN = inspectDrvOOSPctCAN;
  }


  public MyCarrierPacketsApiFMCSAInspection inspectTotalCAN(String inspectTotalCAN) {

    this.inspectTotalCAN = inspectTotalCAN;
    return this;
  }

  /**
   * Get inspectTotalCAN
   *
   * @return inspectTotalCAN
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_INSPECT_TOTAL_C_A_N)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getInspectTotalCAN() {
    return inspectTotalCAN;
  }


  @JsonProperty(JSON_PROPERTY_INSPECT_TOTAL_C_A_N)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setInspectTotalCAN(String inspectTotalCAN) {
    this.inspectTotalCAN = inspectTotalCAN;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    MyCarrierPacketsApiFMCSAInspection myCarrierPacketsApiFMCSAInspection = (MyCarrierPacketsApiFMCSAInspection) o;
    return Objects.equals(this.inspectVehUS, myCarrierPacketsApiFMCSAInspection.inspectVehUS) && Objects.equals(this.inspectVehOOSUS,
        myCarrierPacketsApiFMCSAInspection.inspectVehOOSUS) && Objects.equals(this.inspectVehOOSPctUS, myCarrierPacketsApiFMCSAInspection.inspectVehOOSPctUS)
           && Objects.equals(this.inspectDrvUS, myCarrierPacketsApiFMCSAInspection.inspectDrvUS) && Objects.equals(this.inspectDrvOOSUS,
        myCarrierPacketsApiFMCSAInspection.inspectDrvOOSUS) && Objects.equals(this.inspectDrvOOSPctUS, myCarrierPacketsApiFMCSAInspection.inspectDrvOOSPctUS)
           && Objects.equals(this.inspectHazUS, myCarrierPacketsApiFMCSAInspection.inspectHazUS) && Objects.equals(this.inspectHazOOSUS,
        myCarrierPacketsApiFMCSAInspection.inspectHazOOSUS) && Objects.equals(this.inspectHazOOSPctUS, myCarrierPacketsApiFMCSAInspection.inspectHazOOSPctUS)
           && Objects.equals(this.inspectIEPUS, myCarrierPacketsApiFMCSAInspection.inspectIEPUS) && Objects.equals(this.inspectIEPOOSUS,
        myCarrierPacketsApiFMCSAInspection.inspectIEPOOSUS) && Objects.equals(this.inspectIEPOOSPctUS, myCarrierPacketsApiFMCSAInspection.inspectIEPOOSPctUS)
           && Objects.equals(this.inspectTotalIEPUS, myCarrierPacketsApiFMCSAInspection.inspectTotalIEPUS) && Objects.equals(this.inspectTotalUS,
        myCarrierPacketsApiFMCSAInspection.inspectTotalUS) && Objects.equals(this.inspectVehCAN, myCarrierPacketsApiFMCSAInspection.inspectVehCAN)
           && Objects.equals(this.inspectVehOOSCAN, myCarrierPacketsApiFMCSAInspection.inspectVehOOSCAN) && Objects.equals(this.inspectVehOOSPctCAN,
        myCarrierPacketsApiFMCSAInspection.inspectVehOOSPctCAN) && Objects.equals(this.inspectDrvCAN, myCarrierPacketsApiFMCSAInspection.inspectDrvCAN)
           && Objects.equals(this.inspectDrvOOSCAN, myCarrierPacketsApiFMCSAInspection.inspectDrvOOSCAN) && Objects.equals(this.inspectDrvOOSPctCAN,
        myCarrierPacketsApiFMCSAInspection.inspectDrvOOSPctCAN) && Objects.equals(this.inspectTotalCAN, myCarrierPacketsApiFMCSAInspection.inspectTotalCAN);
  }

  @Override
  public int hashCode() {
    return Objects.hash(inspectVehUS, inspectVehOOSUS, inspectVehOOSPctUS, inspectDrvUS, inspectDrvOOSUS, inspectDrvOOSPctUS, inspectHazUS, inspectHazOOSUS,
        inspectHazOOSPctUS, inspectIEPUS, inspectIEPOOSUS, inspectIEPOOSPctUS, inspectTotalIEPUS, inspectTotalUS, inspectVehCAN, inspectVehOOSCAN,
        inspectVehOOSPctCAN, inspectDrvCAN, inspectDrvOOSCAN, inspectDrvOOSPctCAN, inspectTotalCAN);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class MyCarrierPacketsApiFMCSAInspection {\n");
    sb.append("    inspectVehUS: ").append(toIndentedString(inspectVehUS)).append("\n");
    sb.append("    inspectVehOOSUS: ").append(toIndentedString(inspectVehOOSUS)).append("\n");
    sb.append("    inspectVehOOSPctUS: ").append(toIndentedString(inspectVehOOSPctUS)).append("\n");
    sb.append("    inspectDrvUS: ").append(toIndentedString(inspectDrvUS)).append("\n");
    sb.append("    inspectDrvOOSUS: ").append(toIndentedString(inspectDrvOOSUS)).append("\n");
    sb.append("    inspectDrvOOSPctUS: ").append(toIndentedString(inspectDrvOOSPctUS)).append("\n");
    sb.append("    inspectHazUS: ").append(toIndentedString(inspectHazUS)).append("\n");
    sb.append("    inspectHazOOSUS: ").append(toIndentedString(inspectHazOOSUS)).append("\n");
    sb.append("    inspectHazOOSPctUS: ").append(toIndentedString(inspectHazOOSPctUS)).append("\n");
    sb.append("    inspectIEPUS: ").append(toIndentedString(inspectIEPUS)).append("\n");
    sb.append("    inspectIEPOOSUS: ").append(toIndentedString(inspectIEPOOSUS)).append("\n");
    sb.append("    inspectIEPOOSPctUS: ").append(toIndentedString(inspectIEPOOSPctUS)).append("\n");
    sb.append("    inspectTotalIEPUS: ").append(toIndentedString(inspectTotalIEPUS)).append("\n");
    sb.append("    inspectTotalUS: ").append(toIndentedString(inspectTotalUS)).append("\n");
    sb.append("    inspectVehCAN: ").append(toIndentedString(inspectVehCAN)).append("\n");
    sb.append("    inspectVehOOSCAN: ").append(toIndentedString(inspectVehOOSCAN)).append("\n");
    sb.append("    inspectVehOOSPctCAN: ").append(toIndentedString(inspectVehOOSPctCAN)).append("\n");
    sb.append("    inspectDrvCAN: ").append(toIndentedString(inspectDrvCAN)).append("\n");
    sb.append("    inspectDrvOOSCAN: ").append(toIndentedString(inspectDrvOOSCAN)).append("\n");
    sb.append("    inspectDrvOOSPctCAN: ").append(toIndentedString(inspectDrvOOSPctCAN)).append("\n");
    sb.append("    inspectTotalCAN: ").append(toIndentedString(inspectTotalCAN)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

