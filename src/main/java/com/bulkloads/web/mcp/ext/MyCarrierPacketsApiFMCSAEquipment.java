package com.bulkloads.web.mcp.ext;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * MyCarrierPacketsApiFMCSAEquipment
 */
@JsonPropertyOrder({MyCarrierPacketsApiFMCSAEquipment.JSON_PROPERTY_TRUCKS_TOTAL, MyCarrierPacketsApiFMCSAEquipment.JSON_PROPERTY_TOTAL_POWER,
    MyCarrierPacketsApiFMCSAEquipment.JSON_PROPERTY_FLEETSIZE, MyCarrierPacketsApiFMCSAEquipment.JSON_PROPERTY_TRUCKS_OWNED,
    MyCarrierPacketsApiFMCSAEquipment.JSON_PROPERTY_TRUCKS_TERM, MyCarrierPacketsApiFMCSAEquipment.JSON_PROPERTY_TRUCKS_TRIP,
    MyCarrierPacketsApiFMCSAEquipment.JSO<PERSON>_PROPERTY_TRAILERS_OWNED, MyCarrierPacketsApiFMCSAEquipment.JSON_PROPERTY_TRAILERS_TERM,
    MyCarrierPacketsApiFMCSAEquipment.JSON_PROPERTY_TRAILERS_TRIP, MyCarrierPacketsApiFMCSAEquipment.JSON_PROPERTY_TRACTORS_OWNED,
    MyCarrierPacketsApiFMCSAEquipment.JSON_PROPERTY_TRACTORS_TERM, MyCarrierPacketsApiFMCSAEquipment.JSON_PROPERTY_TRACTORS_TRIP})
@JsonTypeName("MyCarrierPacketsApi.FMCSA.Equipment")

public class MyCarrierPacketsApiFMCSAEquipment {

  public static final String JSON_PROPERTY_TRUCKS_TOTAL = "trucksTotal";
  public static final String JSON_PROPERTY_TOTAL_POWER = "totalPower";
  public static final String JSON_PROPERTY_FLEETSIZE = "fleetsize";
  public static final String JSON_PROPERTY_TRUCKS_OWNED = "trucksOwned";
  public static final String JSON_PROPERTY_TRUCKS_TERM = "trucksTerm";
  public static final String JSON_PROPERTY_TRUCKS_TRIP = "trucksTrip";
  public static final String JSON_PROPERTY_TRAILERS_OWNED = "trailersOwned";
  public static final String JSON_PROPERTY_TRAILERS_TERM = "trailersTerm";
  public static final String JSON_PROPERTY_TRAILERS_TRIP = "trailersTrip";
  public static final String JSON_PROPERTY_TRACTORS_OWNED = "tractorsOwned";
  public static final String JSON_PROPERTY_TRACTORS_TERM = "tractorsTerm";
  public static final String JSON_PROPERTY_TRACTORS_TRIP = "tractorsTrip";
  private String trucksTotal;
  private String totalPower;
  private String fleetsize;
  private String trucksOwned;
  private String trucksTerm;
  private String trucksTrip;
  private String trailersOwned;
  private String trailersTerm;
  private String trailersTrip;
  private String tractorsOwned;
  private String tractorsTerm;
  private String tractorsTrip;

  public MyCarrierPacketsApiFMCSAEquipment() {
  }

  public MyCarrierPacketsApiFMCSAEquipment trucksTotal(String trucksTotal) {

    this.trucksTotal = trucksTotal;
    return this;
  }

  /**
   * Get trucksTotal
   *
   * @return trucksTotal
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_TRUCKS_TOTAL)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getTrucksTotal() {
    return trucksTotal;
  }


  @JsonProperty(JSON_PROPERTY_TRUCKS_TOTAL)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setTrucksTotal(String trucksTotal) {
    this.trucksTotal = trucksTotal;
  }


  public MyCarrierPacketsApiFMCSAEquipment totalPower(String totalPower) {

    this.totalPower = totalPower;
    return this;
  }

  /**
   * Get totalPower
   *
   * @return totalPower
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_TOTAL_POWER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getTotalPower() {
    return totalPower;
  }


  @JsonProperty(JSON_PROPERTY_TOTAL_POWER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setTotalPower(String totalPower) {
    this.totalPower = totalPower;
  }


  public MyCarrierPacketsApiFMCSAEquipment fleetsize(String fleetsize) {

    this.fleetsize = fleetsize;
    return this;
  }

  /**
   * Get fleetsize
   *
   * @return fleetsize
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_FLEETSIZE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getFleetsize() {
    return fleetsize;
  }


  @JsonProperty(JSON_PROPERTY_FLEETSIZE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setFleetsize(String fleetsize) {
    this.fleetsize = fleetsize;
  }


  public MyCarrierPacketsApiFMCSAEquipment trucksOwned(String trucksOwned) {

    this.trucksOwned = trucksOwned;
    return this;
  }

  /**
   * Get trucksOwned
   *
   * @return trucksOwned
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_TRUCKS_OWNED)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getTrucksOwned() {
    return trucksOwned;
  }


  @JsonProperty(JSON_PROPERTY_TRUCKS_OWNED)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setTrucksOwned(String trucksOwned) {
    this.trucksOwned = trucksOwned;
  }


  public MyCarrierPacketsApiFMCSAEquipment trucksTerm(String trucksTerm) {

    this.trucksTerm = trucksTerm;
    return this;
  }

  /**
   * Get trucksTerm
   *
   * @return trucksTerm
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_TRUCKS_TERM)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getTrucksTerm() {
    return trucksTerm;
  }


  @JsonProperty(JSON_PROPERTY_TRUCKS_TERM)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setTrucksTerm(String trucksTerm) {
    this.trucksTerm = trucksTerm;
  }


  public MyCarrierPacketsApiFMCSAEquipment trucksTrip(String trucksTrip) {

    this.trucksTrip = trucksTrip;
    return this;
  }

  /**
   * Get trucksTrip
   *
   * @return trucksTrip
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_TRUCKS_TRIP)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getTrucksTrip() {
    return trucksTrip;
  }


  @JsonProperty(JSON_PROPERTY_TRUCKS_TRIP)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setTrucksTrip(String trucksTrip) {
    this.trucksTrip = trucksTrip;
  }


  public MyCarrierPacketsApiFMCSAEquipment trailersOwned(String trailersOwned) {

    this.trailersOwned = trailersOwned;
    return this;
  }

  /**
   * Get trailersOwned
   *
   * @return trailersOwned
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_TRAILERS_OWNED)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getTrailersOwned() {
    return trailersOwned;
  }


  @JsonProperty(JSON_PROPERTY_TRAILERS_OWNED)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setTrailersOwned(String trailersOwned) {
    this.trailersOwned = trailersOwned;
  }


  public MyCarrierPacketsApiFMCSAEquipment trailersTerm(String trailersTerm) {

    this.trailersTerm = trailersTerm;
    return this;
  }

  /**
   * Get trailersTerm
   *
   * @return trailersTerm
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_TRAILERS_TERM)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getTrailersTerm() {
    return trailersTerm;
  }


  @JsonProperty(JSON_PROPERTY_TRAILERS_TERM)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setTrailersTerm(String trailersTerm) {
    this.trailersTerm = trailersTerm;
  }


  public MyCarrierPacketsApiFMCSAEquipment trailersTrip(String trailersTrip) {

    this.trailersTrip = trailersTrip;
    return this;
  }

  /**
   * Get trailersTrip
   *
   * @return trailersTrip
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_TRAILERS_TRIP)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getTrailersTrip() {
    return trailersTrip;
  }


  @JsonProperty(JSON_PROPERTY_TRAILERS_TRIP)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setTrailersTrip(String trailersTrip) {
    this.trailersTrip = trailersTrip;
  }


  public MyCarrierPacketsApiFMCSAEquipment tractorsOwned(String tractorsOwned) {

    this.tractorsOwned = tractorsOwned;
    return this;
  }

  /**
   * Get tractorsOwned
   *
   * @return tractorsOwned
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_TRACTORS_OWNED)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getTractorsOwned() {
    return tractorsOwned;
  }


  @JsonProperty(JSON_PROPERTY_TRACTORS_OWNED)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setTractorsOwned(String tractorsOwned) {
    this.tractorsOwned = tractorsOwned;
  }


  public MyCarrierPacketsApiFMCSAEquipment tractorsTerm(String tractorsTerm) {

    this.tractorsTerm = tractorsTerm;
    return this;
  }

  /**
   * Get tractorsTerm
   *
   * @return tractorsTerm
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_TRACTORS_TERM)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getTractorsTerm() {
    return tractorsTerm;
  }


  @JsonProperty(JSON_PROPERTY_TRACTORS_TERM)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setTractorsTerm(String tractorsTerm) {
    this.tractorsTerm = tractorsTerm;
  }


  public MyCarrierPacketsApiFMCSAEquipment tractorsTrip(String tractorsTrip) {

    this.tractorsTrip = tractorsTrip;
    return this;
  }

  /**
   * Get tractorsTrip
   *
   * @return tractorsTrip
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_TRACTORS_TRIP)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getTractorsTrip() {
    return tractorsTrip;
  }


  @JsonProperty(JSON_PROPERTY_TRACTORS_TRIP)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setTractorsTrip(String tractorsTrip) {
    this.tractorsTrip = tractorsTrip;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    MyCarrierPacketsApiFMCSAEquipment myCarrierPacketsApiFMCSAEquipment = (MyCarrierPacketsApiFMCSAEquipment) o;
    return Objects.equals(this.trucksTotal, myCarrierPacketsApiFMCSAEquipment.trucksTotal) && Objects.equals(this.totalPower,
        myCarrierPacketsApiFMCSAEquipment.totalPower) && Objects.equals(this.fleetsize, myCarrierPacketsApiFMCSAEquipment.fleetsize) && Objects.equals(
        this.trucksOwned, myCarrierPacketsApiFMCSAEquipment.trucksOwned) && Objects.equals(this.trucksTerm, myCarrierPacketsApiFMCSAEquipment.trucksTerm)
           && Objects.equals(this.trucksTrip, myCarrierPacketsApiFMCSAEquipment.trucksTrip) && Objects.equals(this.trailersOwned,
        myCarrierPacketsApiFMCSAEquipment.trailersOwned) && Objects.equals(this.trailersTerm, myCarrierPacketsApiFMCSAEquipment.trailersTerm) && Objects.equals(
        this.trailersTrip, myCarrierPacketsApiFMCSAEquipment.trailersTrip) && Objects.equals(this.tractorsOwned,
        myCarrierPacketsApiFMCSAEquipment.tractorsOwned) && Objects.equals(this.tractorsTerm, myCarrierPacketsApiFMCSAEquipment.tractorsTerm) && Objects.equals(
        this.tractorsTrip, myCarrierPacketsApiFMCSAEquipment.tractorsTrip);
  }

  @Override
  public int hashCode() {
    return Objects.hash(trucksTotal, totalPower, fleetsize, trucksOwned, trucksTerm, trucksTrip, trailersOwned, trailersTerm, trailersTrip, tractorsOwned,
        tractorsTerm, tractorsTrip);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class MyCarrierPacketsApiFMCSAEquipment {\n");
    sb.append("    trucksTotal: ").append(toIndentedString(trucksTotal)).append("\n");
    sb.append("    totalPower: ").append(toIndentedString(totalPower)).append("\n");
    sb.append("    fleetsize: ").append(toIndentedString(fleetsize)).append("\n");
    sb.append("    trucksOwned: ").append(toIndentedString(trucksOwned)).append("\n");
    sb.append("    trucksTerm: ").append(toIndentedString(trucksTerm)).append("\n");
    sb.append("    trucksTrip: ").append(toIndentedString(trucksTrip)).append("\n");
    sb.append("    trailersOwned: ").append(toIndentedString(trailersOwned)).append("\n");
    sb.append("    trailersTerm: ").append(toIndentedString(trailersTerm)).append("\n");
    sb.append("    trailersTrip: ").append(toIndentedString(trailersTrip)).append("\n");
    sb.append("    tractorsOwned: ").append(toIndentedString(tractorsOwned)).append("\n");
    sb.append("    tractorsTerm: ").append(toIndentedString(tractorsTerm)).append("\n");
    sb.append("    tractorsTrip: ").append(toIndentedString(tractorsTrip)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

