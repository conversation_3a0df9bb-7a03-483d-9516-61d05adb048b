package com.bulkloads.web.mcp.ext;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * MyCarrierPacketsApiFMCSAOperation
 */
@JsonPropertyOrder({MyCarrierPacketsApiFMCSAOperation.JSON_PROPERTY_DOT_ADD_DATE, MyCarrierPacketsApiFMCSAOperation.JSON_PROPERTY_CARRIER_OPERATION,
    MyCarrierPacketsApiFMCSAOperation.JSON_PROPERTY_SHIPPER_OPERATION, MyCarrierPacketsApiFMCSAOperation.JSON_PROPERTY_MX_OPERATION_TYPE,
    MyCarrierPacketsApiFMCSAOperation.JSON_PROPERTY_MX_R_F_C_NUMBER, MyCarrierPacketsApiFMCSAOperation.JSO<PERSON>_PROPERTY_OUT_OF_SERVICE,
    MyCarrierPacketsApiFMCSAOperation.JSON_PROPERTY_OUT_OF_SERVICE_DATE, MyCarrierPacketsApiFMCSAOperation.JSON_PROPERTY_OUT_OF_SERVICE_REASON,
    MyCarrierPacketsApiFMCSAOperation.JSON_PROPERTY_ENTITY_CARRIER, MyCarrierPacketsApiFMCSAOperation.JSON_PROPERTY_ENTITY_SHIPPER,
    MyCarrierPacketsApiFMCSAOperation.JSON_PROPERTY_ENTITY_BROKER, MyCarrierPacketsApiFMCSAOperation.JSON_PROPERTY_ENTITY_FREIGHT_FOWARDER,
    MyCarrierPacketsApiFMCSAOperation.JSON_PROPERTY_ENTITY_CARGO_TANK, MyCarrierPacketsApiFMCSAOperation.JSON_PROPERTY_CLASS_AUTH_FOR_HIRE,
    MyCarrierPacketsApiFMCSAOperation.JSON_PROPERTY_CLASS_MIGRANT, MyCarrierPacketsApiFMCSAOperation.JSON_PROPERTY_CLASS_INDIAN_NATION,
    MyCarrierPacketsApiFMCSAOperation.JSON_PROPERTY_CLASS_EXEMPT_FOR_HIRE, MyCarrierPacketsApiFMCSAOperation.JSON_PROPERTY_CLASS_U_S_MAIL,
    MyCarrierPacketsApiFMCSAOperation.JSON_PROPERTY_CLASS_PRIVATE_PROPERTY, MyCarrierPacketsApiFMCSAOperation.JSON_PROPERTY_CLASS_FEDERAL_GOVERNMENT,
    MyCarrierPacketsApiFMCSAOperation.JSON_PROPERTY_CLASS_PRIV_PASS_BUSINESS, MyCarrierPacketsApiFMCSAOperation.JSON_PROPERTY_CLASS_STATE_GOVERNMENT,
    MyCarrierPacketsApiFMCSAOperation.JSON_PROPERTY_CLASS_PRIV_PASS_NON_BUSINESS, MyCarrierPacketsApiFMCSAOperation.JSON_PROPERTY_CLASS_LOCAL_GOVERNMENT,
    MyCarrierPacketsApiFMCSAOperation.JSON_PROPERTY_CLASS_OTHER, MyCarrierPacketsApiFMCSAOperation.JSON_PROPERTY_OPERATING_STATUS})
@JsonTypeName("MyCarrierPacketsApi.FMCSA.Operation")

public class MyCarrierPacketsApiFMCSAOperation {

  public static final String JSON_PROPERTY_DOT_ADD_DATE = "dotAddDate";
  public static final String JSON_PROPERTY_CARRIER_OPERATION = "carrierOperation";
  public static final String JSON_PROPERTY_SHIPPER_OPERATION = "shipperOperation";
  public static final String JSON_PROPERTY_MX_OPERATION_TYPE = "mxOperationType";
  public static final String JSON_PROPERTY_MX_R_F_C_NUMBER = "mxRFCNumber";
  public static final String JSON_PROPERTY_OUT_OF_SERVICE = "outOfService";
  public static final String JSON_PROPERTY_OUT_OF_SERVICE_DATE = "outOfServiceDate";
  public static final String JSON_PROPERTY_OUT_OF_SERVICE_REASON = "outOfServiceReason";
  public static final String JSON_PROPERTY_ENTITY_CARRIER = "entityCarrier";
  public static final String JSON_PROPERTY_ENTITY_SHIPPER = "entityShipper";
  public static final String JSON_PROPERTY_ENTITY_BROKER = "entityBroker";
  public static final String JSON_PROPERTY_ENTITY_FREIGHT_FOWARDER = "entityFreightFowarder";
  public static final String JSON_PROPERTY_ENTITY_CARGO_TANK = "entityCargoTank";
  public static final String JSON_PROPERTY_CLASS_AUTH_FOR_HIRE = "classAuthForHire";
  public static final String JSON_PROPERTY_CLASS_MIGRANT = "classMigrant";
  public static final String JSON_PROPERTY_CLASS_INDIAN_NATION = "classIndianNation";
  public static final String JSON_PROPERTY_CLASS_EXEMPT_FOR_HIRE = "classExemptForHire";
  public static final String JSON_PROPERTY_CLASS_U_S_MAIL = "classUSMail";
  public static final String JSON_PROPERTY_CLASS_PRIVATE_PROPERTY = "classPrivateProperty";
  public static final String JSON_PROPERTY_CLASS_FEDERAL_GOVERNMENT = "classFederalGovernment";
  public static final String JSON_PROPERTY_CLASS_PRIV_PASS_BUSINESS = "classPrivPassBusiness";
  public static final String JSON_PROPERTY_CLASS_STATE_GOVERNMENT = "classStateGovernment";
  public static final String JSON_PROPERTY_CLASS_PRIV_PASS_NON_BUSINESS = "classPrivPassNonBusiness";
  public static final String JSON_PROPERTY_CLASS_LOCAL_GOVERNMENT = "classLocalGovernment";
  public static final String JSON_PROPERTY_CLASS_OTHER = "classOther";
  public static final String JSON_PROPERTY_OPERATING_STATUS = "operatingStatus";
  private String dotAddDate;
  private String carrierOperation;
  private String shipperOperation;
  private String mxOperationType;
  private String mxRFCNumber;
  private String outOfService;
  private String outOfServiceDate;
  private String outOfServiceReason;
  private String entityCarrier;
  private String entityShipper;
  private String entityBroker;
  private String entityFreightFowarder;
  private String entityCargoTank;
  private String classAuthForHire;
  private String classMigrant;
  private String classIndianNation;
  private String classExemptForHire;
  private String classUSMail;
  private String classPrivateProperty;
  private String classFederalGovernment;
  private String classPrivPassBusiness;
  private String classStateGovernment;
  private String classPrivPassNonBusiness;
  private String classLocalGovernment;
  private String classOther;
  private String operatingStatus;

  public MyCarrierPacketsApiFMCSAOperation() {
  }

  public MyCarrierPacketsApiFMCSAOperation dotAddDate(String dotAddDate) {

    this.dotAddDate = dotAddDate;
    return this;
  }

  /**
   * Get dotAddDate
   *
   * @return dotAddDate
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_DOT_ADD_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getDotAddDate() {
    return dotAddDate;
  }


  @JsonProperty(JSON_PROPERTY_DOT_ADD_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setDotAddDate(String dotAddDate) {
    this.dotAddDate = dotAddDate;
  }


  public MyCarrierPacketsApiFMCSAOperation carrierOperation(String carrierOperation) {

    this.carrierOperation = carrierOperation;
    return this;
  }

  /**
   * Get carrierOperation
   *
   * @return carrierOperation
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CARRIER_OPERATION)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getCarrierOperation() {
    return carrierOperation;
  }


  @JsonProperty(JSON_PROPERTY_CARRIER_OPERATION)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCarrierOperation(String carrierOperation) {
    this.carrierOperation = carrierOperation;
  }


  public MyCarrierPacketsApiFMCSAOperation shipperOperation(String shipperOperation) {

    this.shipperOperation = shipperOperation;
    return this;
  }

  /**
   * Get shipperOperation
   *
   * @return shipperOperation
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_SHIPPER_OPERATION)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getShipperOperation() {
    return shipperOperation;
  }


  @JsonProperty(JSON_PROPERTY_SHIPPER_OPERATION)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setShipperOperation(String shipperOperation) {
    this.shipperOperation = shipperOperation;
  }


  public MyCarrierPacketsApiFMCSAOperation mxOperationType(String mxOperationType) {

    this.mxOperationType = mxOperationType;
    return this;
  }

  /**
   * Get mxOperationType
   *
   * @return mxOperationType
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_MX_OPERATION_TYPE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getMxOperationType() {
    return mxOperationType;
  }


  @JsonProperty(JSON_PROPERTY_MX_OPERATION_TYPE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setMxOperationType(String mxOperationType) {
    this.mxOperationType = mxOperationType;
  }


  public MyCarrierPacketsApiFMCSAOperation mxRFCNumber(String mxRFCNumber) {

    this.mxRFCNumber = mxRFCNumber;
    return this;
  }

  /**
   * Get mxRFCNumber
   *
   * @return mxRFCNumber
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_MX_R_F_C_NUMBER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getMxRFCNumber() {
    return mxRFCNumber;
  }


  @JsonProperty(JSON_PROPERTY_MX_R_F_C_NUMBER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setMxRFCNumber(String mxRFCNumber) {
    this.mxRFCNumber = mxRFCNumber;
  }


  public MyCarrierPacketsApiFMCSAOperation outOfService(String outOfService) {

    this.outOfService = outOfService;
    return this;
  }

  /**
   * Get outOfService
   *
   * @return outOfService
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_OUT_OF_SERVICE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getOutOfService() {
    return outOfService;
  }


  @JsonProperty(JSON_PROPERTY_OUT_OF_SERVICE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setOutOfService(String outOfService) {
    this.outOfService = outOfService;
  }


  public MyCarrierPacketsApiFMCSAOperation outOfServiceDate(String outOfServiceDate) {

    this.outOfServiceDate = outOfServiceDate;
    return this;
  }

  /**
   * Get outOfServiceDate
   *
   * @return outOfServiceDate
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_OUT_OF_SERVICE_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getOutOfServiceDate() {
    return outOfServiceDate;
  }


  @JsonProperty(JSON_PROPERTY_OUT_OF_SERVICE_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setOutOfServiceDate(String outOfServiceDate) {
    this.outOfServiceDate = outOfServiceDate;
  }


  public MyCarrierPacketsApiFMCSAOperation outOfServiceReason(String outOfServiceReason) {

    this.outOfServiceReason = outOfServiceReason;
    return this;
  }

  /**
   * Get outOfServiceReason
   *
   * @return outOfServiceReason
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_OUT_OF_SERVICE_REASON)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getOutOfServiceReason() {
    return outOfServiceReason;
  }


  @JsonProperty(JSON_PROPERTY_OUT_OF_SERVICE_REASON)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setOutOfServiceReason(String outOfServiceReason) {
    this.outOfServiceReason = outOfServiceReason;
  }


  public MyCarrierPacketsApiFMCSAOperation entityCarrier(String entityCarrier) {

    this.entityCarrier = entityCarrier;
    return this;
  }

  /**
   * Get entityCarrier
   *
   * @return entityCarrier
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_ENTITY_CARRIER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getEntityCarrier() {
    return entityCarrier;
  }


  @JsonProperty(JSON_PROPERTY_ENTITY_CARRIER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setEntityCarrier(String entityCarrier) {
    this.entityCarrier = entityCarrier;
  }


  public MyCarrierPacketsApiFMCSAOperation entityShipper(String entityShipper) {

    this.entityShipper = entityShipper;
    return this;
  }

  /**
   * Get entityShipper
   *
   * @return entityShipper
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_ENTITY_SHIPPER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getEntityShipper() {
    return entityShipper;
  }


  @JsonProperty(JSON_PROPERTY_ENTITY_SHIPPER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setEntityShipper(String entityShipper) {
    this.entityShipper = entityShipper;
  }


  public MyCarrierPacketsApiFMCSAOperation entityBroker(String entityBroker) {

    this.entityBroker = entityBroker;
    return this;
  }

  /**
   * Get entityBroker
   *
   * @return entityBroker
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_ENTITY_BROKER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getEntityBroker() {
    return entityBroker;
  }


  @JsonProperty(JSON_PROPERTY_ENTITY_BROKER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setEntityBroker(String entityBroker) {
    this.entityBroker = entityBroker;
  }


  public MyCarrierPacketsApiFMCSAOperation entityFreightFowarder(String entityFreightFowarder) {

    this.entityFreightFowarder = entityFreightFowarder;
    return this;
  }

  /**
   * Get entityFreightFowarder
   *
   * @return entityFreightFowarder
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_ENTITY_FREIGHT_FOWARDER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getEntityFreightFowarder() {
    return entityFreightFowarder;
  }


  @JsonProperty(JSON_PROPERTY_ENTITY_FREIGHT_FOWARDER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setEntityFreightFowarder(String entityFreightFowarder) {
    this.entityFreightFowarder = entityFreightFowarder;
  }


  public MyCarrierPacketsApiFMCSAOperation entityCargoTank(String entityCargoTank) {

    this.entityCargoTank = entityCargoTank;
    return this;
  }

  /**
   * Get entityCargoTank
   *
   * @return entityCargoTank
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_ENTITY_CARGO_TANK)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getEntityCargoTank() {
    return entityCargoTank;
  }


  @JsonProperty(JSON_PROPERTY_ENTITY_CARGO_TANK)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setEntityCargoTank(String entityCargoTank) {
    this.entityCargoTank = entityCargoTank;
  }


  public MyCarrierPacketsApiFMCSAOperation classAuthForHire(String classAuthForHire) {

    this.classAuthForHire = classAuthForHire;
    return this;
  }

  /**
   * Get classAuthForHire
   *
   * @return classAuthForHire
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CLASS_AUTH_FOR_HIRE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getClassAuthForHire() {
    return classAuthForHire;
  }


  @JsonProperty(JSON_PROPERTY_CLASS_AUTH_FOR_HIRE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setClassAuthForHire(String classAuthForHire) {
    this.classAuthForHire = classAuthForHire;
  }


  public MyCarrierPacketsApiFMCSAOperation classMigrant(String classMigrant) {

    this.classMigrant = classMigrant;
    return this;
  }

  /**
   * Get classMigrant
   *
   * @return classMigrant
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CLASS_MIGRANT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getClassMigrant() {
    return classMigrant;
  }


  @JsonProperty(JSON_PROPERTY_CLASS_MIGRANT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setClassMigrant(String classMigrant) {
    this.classMigrant = classMigrant;
  }


  public MyCarrierPacketsApiFMCSAOperation classIndianNation(String classIndianNation) {

    this.classIndianNation = classIndianNation;
    return this;
  }

  /**
   * Get classIndianNation
   *
   * @return classIndianNation
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CLASS_INDIAN_NATION)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getClassIndianNation() {
    return classIndianNation;
  }


  @JsonProperty(JSON_PROPERTY_CLASS_INDIAN_NATION)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setClassIndianNation(String classIndianNation) {
    this.classIndianNation = classIndianNation;
  }


  public MyCarrierPacketsApiFMCSAOperation classExemptForHire(String classExemptForHire) {

    this.classExemptForHire = classExemptForHire;
    return this;
  }

  /**
   * Get classExemptForHire
   *
   * @return classExemptForHire
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CLASS_EXEMPT_FOR_HIRE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getClassExemptForHire() {
    return classExemptForHire;
  }


  @JsonProperty(JSON_PROPERTY_CLASS_EXEMPT_FOR_HIRE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setClassExemptForHire(String classExemptForHire) {
    this.classExemptForHire = classExemptForHire;
  }


  public MyCarrierPacketsApiFMCSAOperation classUSMail(String classUSMail) {

    this.classUSMail = classUSMail;
    return this;
  }

  /**
   * Get classUSMail
   *
   * @return classUSMail
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CLASS_U_S_MAIL)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getClassUSMail() {
    return classUSMail;
  }


  @JsonProperty(JSON_PROPERTY_CLASS_U_S_MAIL)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setClassUSMail(String classUSMail) {
    this.classUSMail = classUSMail;
  }


  public MyCarrierPacketsApiFMCSAOperation classPrivateProperty(String classPrivateProperty) {

    this.classPrivateProperty = classPrivateProperty;
    return this;
  }

  /**
   * Get classPrivateProperty
   *
   * @return classPrivateProperty
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CLASS_PRIVATE_PROPERTY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getClassPrivateProperty() {
    return classPrivateProperty;
  }


  @JsonProperty(JSON_PROPERTY_CLASS_PRIVATE_PROPERTY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setClassPrivateProperty(String classPrivateProperty) {
    this.classPrivateProperty = classPrivateProperty;
  }


  public MyCarrierPacketsApiFMCSAOperation classFederalGovernment(String classFederalGovernment) {

    this.classFederalGovernment = classFederalGovernment;
    return this;
  }

  /**
   * Get classFederalGovernment
   *
   * @return classFederalGovernment
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CLASS_FEDERAL_GOVERNMENT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getClassFederalGovernment() {
    return classFederalGovernment;
  }


  @JsonProperty(JSON_PROPERTY_CLASS_FEDERAL_GOVERNMENT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setClassFederalGovernment(String classFederalGovernment) {
    this.classFederalGovernment = classFederalGovernment;
  }


  public MyCarrierPacketsApiFMCSAOperation classPrivPassBusiness(String classPrivPassBusiness) {

    this.classPrivPassBusiness = classPrivPassBusiness;
    return this;
  }

  /**
   * Get classPrivPassBusiness
   *
   * @return classPrivPassBusiness
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CLASS_PRIV_PASS_BUSINESS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getClassPrivPassBusiness() {
    return classPrivPassBusiness;
  }


  @JsonProperty(JSON_PROPERTY_CLASS_PRIV_PASS_BUSINESS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setClassPrivPassBusiness(String classPrivPassBusiness) {
    this.classPrivPassBusiness = classPrivPassBusiness;
  }


  public MyCarrierPacketsApiFMCSAOperation classStateGovernment(String classStateGovernment) {

    this.classStateGovernment = classStateGovernment;
    return this;
  }

  /**
   * Get classStateGovernment
   *
   * @return classStateGovernment
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CLASS_STATE_GOVERNMENT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getClassStateGovernment() {
    return classStateGovernment;
  }


  @JsonProperty(JSON_PROPERTY_CLASS_STATE_GOVERNMENT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setClassStateGovernment(String classStateGovernment) {
    this.classStateGovernment = classStateGovernment;
  }


  public MyCarrierPacketsApiFMCSAOperation classPrivPassNonBusiness(String classPrivPassNonBusiness) {

    this.classPrivPassNonBusiness = classPrivPassNonBusiness;
    return this;
  }

  /**
   * Get classPrivPassNonBusiness
   *
   * @return classPrivPassNonBusiness
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CLASS_PRIV_PASS_NON_BUSINESS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getClassPrivPassNonBusiness() {
    return classPrivPassNonBusiness;
  }


  @JsonProperty(JSON_PROPERTY_CLASS_PRIV_PASS_NON_BUSINESS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setClassPrivPassNonBusiness(String classPrivPassNonBusiness) {
    this.classPrivPassNonBusiness = classPrivPassNonBusiness;
  }


  public MyCarrierPacketsApiFMCSAOperation classLocalGovernment(String classLocalGovernment) {

    this.classLocalGovernment = classLocalGovernment;
    return this;
  }

  /**
   * Get classLocalGovernment
   *
   * @return classLocalGovernment
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CLASS_LOCAL_GOVERNMENT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getClassLocalGovernment() {
    return classLocalGovernment;
  }


  @JsonProperty(JSON_PROPERTY_CLASS_LOCAL_GOVERNMENT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setClassLocalGovernment(String classLocalGovernment) {
    this.classLocalGovernment = classLocalGovernment;
  }


  public MyCarrierPacketsApiFMCSAOperation classOther(String classOther) {

    this.classOther = classOther;
    return this;
  }

  /**
   * Get classOther
   *
   * @return classOther
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CLASS_OTHER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getClassOther() {
    return classOther;
  }


  @JsonProperty(JSON_PROPERTY_CLASS_OTHER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setClassOther(String classOther) {
    this.classOther = classOther;
  }


  public MyCarrierPacketsApiFMCSAOperation operatingStatus(String operatingStatus) {

    this.operatingStatus = operatingStatus;
    return this;
  }

  /**
   * Get operatingStatus
   *
   * @return operatingStatus
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_OPERATING_STATUS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getOperatingStatus() {
    return operatingStatus;
  }


  @JsonProperty(JSON_PROPERTY_OPERATING_STATUS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setOperatingStatus(String operatingStatus) {
    this.operatingStatus = operatingStatus;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    MyCarrierPacketsApiFMCSAOperation myCarrierPacketsApiFMCSAOperation = (MyCarrierPacketsApiFMCSAOperation) o;
    return Objects.equals(this.dotAddDate, myCarrierPacketsApiFMCSAOperation.dotAddDate) && Objects.equals(this.carrierOperation,
        myCarrierPacketsApiFMCSAOperation.carrierOperation) && Objects.equals(this.shipperOperation, myCarrierPacketsApiFMCSAOperation.shipperOperation)
           && Objects.equals(this.mxOperationType, myCarrierPacketsApiFMCSAOperation.mxOperationType) && Objects.equals(this.mxRFCNumber,
        myCarrierPacketsApiFMCSAOperation.mxRFCNumber) && Objects.equals(this.outOfService, myCarrierPacketsApiFMCSAOperation.outOfService) && Objects.equals(
        this.outOfServiceDate, myCarrierPacketsApiFMCSAOperation.outOfServiceDate) && Objects.equals(this.outOfServiceReason,
        myCarrierPacketsApiFMCSAOperation.outOfServiceReason) && Objects.equals(this.entityCarrier, myCarrierPacketsApiFMCSAOperation.entityCarrier)
           && Objects.equals(this.entityShipper, myCarrierPacketsApiFMCSAOperation.entityShipper) && Objects.equals(this.entityBroker,
        myCarrierPacketsApiFMCSAOperation.entityBroker) && Objects.equals(this.entityFreightFowarder, myCarrierPacketsApiFMCSAOperation.entityFreightFowarder)
           && Objects.equals(this.entityCargoTank, myCarrierPacketsApiFMCSAOperation.entityCargoTank) && Objects.equals(this.classAuthForHire,
        myCarrierPacketsApiFMCSAOperation.classAuthForHire) && Objects.equals(this.classMigrant, myCarrierPacketsApiFMCSAOperation.classMigrant)
           && Objects.equals(this.classIndianNation, myCarrierPacketsApiFMCSAOperation.classIndianNation) && Objects.equals(this.classExemptForHire,
        myCarrierPacketsApiFMCSAOperation.classExemptForHire) && Objects.equals(this.classUSMail, myCarrierPacketsApiFMCSAOperation.classUSMail)
           && Objects.equals(this.classPrivateProperty, myCarrierPacketsApiFMCSAOperation.classPrivateProperty) && Objects.equals(this.classFederalGovernment,
        myCarrierPacketsApiFMCSAOperation.classFederalGovernment) && Objects.equals(this.classPrivPassBusiness,
        myCarrierPacketsApiFMCSAOperation.classPrivPassBusiness) && Objects.equals(this.classStateGovernment,
        myCarrierPacketsApiFMCSAOperation.classStateGovernment) && Objects.equals(this.classPrivPassNonBusiness,
        myCarrierPacketsApiFMCSAOperation.classPrivPassNonBusiness) && Objects.equals(this.classLocalGovernment,
        myCarrierPacketsApiFMCSAOperation.classLocalGovernment) && Objects.equals(this.classOther, myCarrierPacketsApiFMCSAOperation.classOther)
           && Objects.equals(this.operatingStatus, myCarrierPacketsApiFMCSAOperation.operatingStatus);
  }

  @Override
  public int hashCode() {
    return Objects.hash(dotAddDate, carrierOperation, shipperOperation, mxOperationType, mxRFCNumber, outOfService, outOfServiceDate, outOfServiceReason,
        entityCarrier, entityShipper, entityBroker, entityFreightFowarder, entityCargoTank, classAuthForHire, classMigrant, classIndianNation,
        classExemptForHire, classUSMail, classPrivateProperty, classFederalGovernment, classPrivPassBusiness, classStateGovernment, classPrivPassNonBusiness,
        classLocalGovernment, classOther, operatingStatus);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class MyCarrierPacketsApiFMCSAOperation {\n");
    sb.append("    dotAddDate: ").append(toIndentedString(dotAddDate)).append("\n");
    sb.append("    carrierOperation: ").append(toIndentedString(carrierOperation)).append("\n");
    sb.append("    shipperOperation: ").append(toIndentedString(shipperOperation)).append("\n");
    sb.append("    mxOperationType: ").append(toIndentedString(mxOperationType)).append("\n");
    sb.append("    mxRFCNumber: ").append(toIndentedString(mxRFCNumber)).append("\n");
    sb.append("    outOfService: ").append(toIndentedString(outOfService)).append("\n");
    sb.append("    outOfServiceDate: ").append(toIndentedString(outOfServiceDate)).append("\n");
    sb.append("    outOfServiceReason: ").append(toIndentedString(outOfServiceReason)).append("\n");
    sb.append("    entityCarrier: ").append(toIndentedString(entityCarrier)).append("\n");
    sb.append("    entityShipper: ").append(toIndentedString(entityShipper)).append("\n");
    sb.append("    entityBroker: ").append(toIndentedString(entityBroker)).append("\n");
    sb.append("    entityFreightFowarder: ").append(toIndentedString(entityFreightFowarder)).append("\n");
    sb.append("    entityCargoTank: ").append(toIndentedString(entityCargoTank)).append("\n");
    sb.append("    classAuthForHire: ").append(toIndentedString(classAuthForHire)).append("\n");
    sb.append("    classMigrant: ").append(toIndentedString(classMigrant)).append("\n");
    sb.append("    classIndianNation: ").append(toIndentedString(classIndianNation)).append("\n");
    sb.append("    classExemptForHire: ").append(toIndentedString(classExemptForHire)).append("\n");
    sb.append("    classUSMail: ").append(toIndentedString(classUSMail)).append("\n");
    sb.append("    classPrivateProperty: ").append(toIndentedString(classPrivateProperty)).append("\n");
    sb.append("    classFederalGovernment: ").append(toIndentedString(classFederalGovernment)).append("\n");
    sb.append("    classPrivPassBusiness: ").append(toIndentedString(classPrivPassBusiness)).append("\n");
    sb.append("    classStateGovernment: ").append(toIndentedString(classStateGovernment)).append("\n");
    sb.append("    classPrivPassNonBusiness: ").append(toIndentedString(classPrivPassNonBusiness)).append("\n");
    sb.append("    classLocalGovernment: ").append(toIndentedString(classLocalGovernment)).append("\n");
    sb.append("    classOther: ").append(toIndentedString(classOther)).append("\n");
    sb.append("    operatingStatus: ").append(toIndentedString(operatingStatus)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

