package com.bulkloads.web.mcp.ext;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

/**
 * CarrierTruckTypeDto
 */
@JsonPropertyOrder({CarrierTruckTypeDto.JSON_PROPERTY_AUTO_CARRIER, CarrierTruckTypeDto.JSON_PROPERTY_BTRAIN, CarrierTruckTypeDto.JSON_PROPERTY_CONESTOGA,
    CarrierTruckTypeDto.JSON_PROPERTY_CONTAINER, CarrierTruckTypeDto.JSON_PROPERTY_CONTAINER_INSULATED,
    CarrierTruckTypeDto.JSON_PROPERTY_CONTAINER_REFRIGERATED, CarrierTruckTypeDto.JSON_PROPERTY_CONVEYOR, CarrierTruckTypeDto.JSON_PROPERTY_DOUBLE_DROP,
    CarrierTruckTypeDto.JSON_PROPERTY_DROP_DECK_LANDOLL, CarrierTruckTypeDto.JSON_PROPERTY_DUMP_TRAILER, CarrierTruckTypeDto.JSON_PROPERTY_FLATBED,
    CarrierTruckTypeDto.JSON_PROPERTY_FLATBED_AIR_RIDE, CarrierTruckTypeDto.JSON_PROPERTY_FLATBED_CONESTOGA, CarrierTruckTypeDto.JSON_PROPERTY_FLATBED_DOUBLE,
    CarrierTruckTypeDto.JSON_PROPERTY_FLATBED_HAZ_MAT, CarrierTruckTypeDto.JSON_PROPERTY_FLATBED_HOTSHOT, CarrierTruckTypeDto.JSON_PROPERTY_FLATBED_MAXI,
    CarrierTruckTypeDto.JSON_PROPERTY_FLATBED_OR_STEP_DECK, CarrierTruckTypeDto.JSON_PROPERTY_FLATBED_OVERDIMENSION,
    CarrierTruckTypeDto.JSON_PROPERTY_FLATBED_WITHCHAINS, CarrierTruckTypeDto.JSON_PROPERTY_FLATBED_WITH_SIDES,
    CarrierTruckTypeDto.JSON_PROPERTY_FLATBED_WITH_TARPS, CarrierTruckTypeDto.JSON_PROPERTY_FLATBED_WITH_TEAM,
    CarrierTruckTypeDto.JSON_PROPERTY_FLATBED_VAN_REEFER, CarrierTruckTypeDto.JSON_PROPERTY_HOPPER_BOTTOM,
    CarrierTruckTypeDto.JSON_PROPERTY_INSULATED_VAN_OR_REEFER, CarrierTruckTypeDto.JSON_PROPERTY_LOWBOY,
    CarrierTruckTypeDto.JSON_PROPERTY_LOWBOY_OR_REM_GOOSENECK, CarrierTruckTypeDto.JSON_PROPERTY_LOWBOY_OVERDIMENSION,
    CarrierTruckTypeDto.JSON_PROPERTY_MOVING_VAN, CarrierTruckTypeDto.JSON_PROPERTY_PNEUMATIC, CarrierTruckTypeDto.JSON_PROPERTY_POWER_ONLY,
    CarrierTruckTypeDto.JSON_PROPERTY_REEFER, CarrierTruckTypeDto.JSON_PROPERTY_REEFER_AIR_RIDE, CarrierTruckTypeDto.JSON_PROPERTY_REEFER_DOUBLE,
    CarrierTruckTypeDto.JSON_PROPERTY_REEFER_HAZ_MAT, CarrierTruckTypeDto.JSON_PROPERTY_REEFER_INTERMODAL, CarrierTruckTypeDto.JSON_PROPERTY_REEFER_LOGISTICS,
    CarrierTruckTypeDto.JSON_PROPERTY_REEFER_OR_VENTED_VAN, CarrierTruckTypeDto.JSON_PROPERTY_REEFER_PALLET_EXCHANGE,
    CarrierTruckTypeDto.JSON_PROPERTY_REEFER_WITH_TEAM, CarrierTruckTypeDto.JSON_PROPERTY_REMOVABLE_GOOSENECK, CarrierTruckTypeDto.JSON_PROPERTY_STEP_DECK,
    CarrierTruckTypeDto.JSON_PROPERTY_STEP_DECK_OR_REM_GOOSENECK, CarrierTruckTypeDto.JSON_PROPERTY_STEPDECK_CONESTOGA,
    CarrierTruckTypeDto.JSON_PROPERTY_STRAIGHT_BOX_TRUCK, CarrierTruckTypeDto.JSON_PROPERTY_STRETCH_TRAILER, CarrierTruckTypeDto.JSON_PROPERTY_TANKER_ALUMINUM,
    CarrierTruckTypeDto.JSON_PROPERTY_TANKER_INTERMODAL, CarrierTruckTypeDto.JSON_PROPERTY_TANKER_STEEL, CarrierTruckTypeDto.JSON_PROPERTY_TRUCK_AND_TRAILER,
    CarrierTruckTypeDto.JSON_PROPERTY_VAN, CarrierTruckTypeDto.JSON_PROPERTY_VAN_AIR_RIDE, CarrierTruckTypeDto.JSON_PROPERTY_VAN_BLANKET_WRAP,
    CarrierTruckTypeDto.JSON_PROPERTY_VAN_CONESTOGA, CarrierTruckTypeDto.JSON_PROPERTY_VAN_DOUBLE, CarrierTruckTypeDto.JSON_PROPERTY_VAN_HAZ_MAT,
    CarrierTruckTypeDto.JSON_PROPERTY_VAN_HOTSHOT, CarrierTruckTypeDto.JSON_PROPERTY_VAN_INSULATED, CarrierTruckTypeDto.JSON_PROPERTY_VAN_INTERMODAL,
    CarrierTruckTypeDto.JSON_PROPERTY_VAN_LIFT_GATE, CarrierTruckTypeDto.JSON_PROPERTY_VAN_LOGISTICS, CarrierTruckTypeDto.JSON_PROPERTY_VAN_OPEN_TOP,
    CarrierTruckTypeDto.JSON_PROPERTY_VAN_OR_FLATBED, CarrierTruckTypeDto.JSON_PROPERTY_VAN_OR_FLATBEDW_TARPS, CarrierTruckTypeDto.JSON_PROPERTY_VAN_OR_REEFER,
    CarrierTruckTypeDto.JSON_PROPERTY_VAN_PALLET_EXCHANGE, CarrierTruckTypeDto.JSON_PROPERTY_VAN_ROLLER_BED, CarrierTruckTypeDto.JSON_PROPERTY_VAN_TRIPLE,
    CarrierTruckTypeDto.JSON_PROPERTY_VAN_VENTED, CarrierTruckTypeDto.JSON_PROPERTY_VAN_WITH_CURTAINS, CarrierTruckTypeDto.JSON_PROPERTY_VAN_WITH_TEAM,
    CarrierTruckTypeDto.JSON_PROPERTY_ONE_TO_TWO_CAR_HAULER, CarrierTruckTypeDto.JSON_PROPERTY_THREE_CAR_HAULER,
    CarrierTruckTypeDto.JSON_PROPERTY_FOUR_TO_FIVE_CAR_HAULER, CarrierTruckTypeDto.JSON_PROPERTY_SIX_TO_SEVEN_CAR_HAULER,
    CarrierTruckTypeDto.JSON_PROPERTY_EIGHT_TO_TEN_CAR_HAULER, CarrierTruckTypeDto.JSON_PROPERTY_CHASSIS, CarrierTruckTypeDto.JSON_PROPERTY_PINTLE_HITCH,
    CarrierTruckTypeDto.JSON_PROPERTY_VAN_SPRINTER, CarrierTruckTypeDto.JSON_PROPERTY_TOTER})

public class CarrierTruckTypeDto {

  public static final String JSON_PROPERTY_AUTO_CARRIER = "AutoCarrier";
  public static final String JSON_PROPERTY_BTRAIN = "BTrain";
  public static final String JSON_PROPERTY_CONESTOGA = "Conestoga";
  public static final String JSON_PROPERTY_CONTAINER = "Container";
  public static final String JSON_PROPERTY_CONTAINER_INSULATED = "ContainerInsulated";
  public static final String JSON_PROPERTY_CONTAINER_REFRIGERATED = "ContainerRefrigerated";
  public static final String JSON_PROPERTY_CONVEYOR = "Conveyor";
  public static final String JSON_PROPERTY_DOUBLE_DROP = "DoubleDrop";
  public static final String JSON_PROPERTY_DROP_DECK_LANDOLL = "DropDeckLandoll";
  public static final String JSON_PROPERTY_DUMP_TRAILER = "DumpTrailer";
  public static final String JSON_PROPERTY_FLATBED = "Flatbed";
  public static final String JSON_PROPERTY_FLATBED_AIR_RIDE = "FlatbedAirRide";
  public static final String JSON_PROPERTY_FLATBED_CONESTOGA = "FlatbedConestoga";
  public static final String JSON_PROPERTY_FLATBED_DOUBLE = "FlatbedDouble";
  public static final String JSON_PROPERTY_FLATBED_HAZ_MAT = "FlatbedHazMat";
  public static final String JSON_PROPERTY_FLATBED_HOTSHOT = "FlatbedHotshot";
  public static final String JSON_PROPERTY_FLATBED_MAXI = "FlatbedMaxi";
  public static final String JSON_PROPERTY_FLATBED_OR_STEP_DECK = "FlatbedOrStepDeck";
  public static final String JSON_PROPERTY_FLATBED_OVERDIMENSION = "FlatbedOverdimension";
  public static final String JSON_PROPERTY_FLATBED_WITHCHAINS = "FlatbedWithchains";
  public static final String JSON_PROPERTY_FLATBED_WITH_SIDES = "FlatbedWithSides";
  public static final String JSON_PROPERTY_FLATBED_WITH_TARPS = "FlatbedWithTarps";
  public static final String JSON_PROPERTY_FLATBED_WITH_TEAM = "FlatbedWithTeam";
  public static final String JSON_PROPERTY_FLATBED_VAN_REEFER = "FlatbedVanReefer";
  public static final String JSON_PROPERTY_HOPPER_BOTTOM = "HopperBottom";
  public static final String JSON_PROPERTY_INSULATED_VAN_OR_REEFER = "InsulatedVanOrReefer";
  public static final String JSON_PROPERTY_LOWBOY = "Lowboy";
  public static final String JSON_PROPERTY_LOWBOY_OR_REM_GOOSENECK = "LowboyOrRemGooseneck";
  public static final String JSON_PROPERTY_LOWBOY_OVERDIMENSION = "LowboyOverdimension";
  public static final String JSON_PROPERTY_MOVING_VAN = "MovingVan";
  public static final String JSON_PROPERTY_PNEUMATIC = "Pneumatic";
  public static final String JSON_PROPERTY_POWER_ONLY = "PowerOnly";
  public static final String JSON_PROPERTY_REEFER = "Reefer";
  public static final String JSON_PROPERTY_REEFER_AIR_RIDE = "ReeferAirRide";
  public static final String JSON_PROPERTY_REEFER_DOUBLE = "ReeferDouble";
  public static final String JSON_PROPERTY_REEFER_HAZ_MAT = "ReeferHazMat";
  public static final String JSON_PROPERTY_REEFER_INTERMODAL = "ReeferIntermodal";
  public static final String JSON_PROPERTY_REEFER_LOGISTICS = "ReeferLogistics";
  public static final String JSON_PROPERTY_REEFER_OR_VENTED_VAN = "ReeferOrVentedVan";
  public static final String JSON_PROPERTY_REEFER_PALLET_EXCHANGE = "ReeferPalletExchange";
  public static final String JSON_PROPERTY_REEFER_WITH_TEAM = "ReeferWithTeam";
  public static final String JSON_PROPERTY_REMOVABLE_GOOSENECK = "RemovableGooseneck";
  public static final String JSON_PROPERTY_STEP_DECK = "StepDeck";
  public static final String JSON_PROPERTY_STEP_DECK_OR_REM_GOOSENECK = "StepDeckOrRemGooseneck";
  public static final String JSON_PROPERTY_STEPDECK_CONESTOGA = "StepdeckConestoga";
  public static final String JSON_PROPERTY_STRAIGHT_BOX_TRUCK = "StraightBoxTruck";
  public static final String JSON_PROPERTY_STRETCH_TRAILER = "StretchTrailer";
  public static final String JSON_PROPERTY_TANKER_ALUMINUM = "TankerAluminum";
  public static final String JSON_PROPERTY_TANKER_INTERMODAL = "TankerIntermodal";
  public static final String JSON_PROPERTY_TANKER_STEEL = "TankerSteel";
  public static final String JSON_PROPERTY_TRUCK_AND_TRAILER = "TruckAndTrailer";
  public static final String JSON_PROPERTY_VAN = "Van";
  public static final String JSON_PROPERTY_VAN_AIR_RIDE = "VanAirRide";
  public static final String JSON_PROPERTY_VAN_BLANKET_WRAP = "VanBlanketWrap";
  public static final String JSON_PROPERTY_VAN_CONESTOGA = "VanConestoga";
  public static final String JSON_PROPERTY_VAN_DOUBLE = "VanDouble";
  public static final String JSON_PROPERTY_VAN_HAZ_MAT = "VanHazMat";
  public static final String JSON_PROPERTY_VAN_HOTSHOT = "VanHotshot";
  public static final String JSON_PROPERTY_VAN_INSULATED = "VanInsulated";
  public static final String JSON_PROPERTY_VAN_INTERMODAL = "VanIntermodal";
  public static final String JSON_PROPERTY_VAN_LIFT_GATE = "VanLiftGate";
  public static final String JSON_PROPERTY_VAN_LOGISTICS = "VanLogistics";
  public static final String JSON_PROPERTY_VAN_OPEN_TOP = "VanOpenTop";
  public static final String JSON_PROPERTY_VAN_OR_FLATBED = "VanOrFlatbed";
  public static final String JSON_PROPERTY_VAN_OR_FLATBEDW_TARPS = "VanOrFlatbedwTarps";
  public static final String JSON_PROPERTY_VAN_OR_REEFER = "VanOrReefer";
  public static final String JSON_PROPERTY_VAN_PALLET_EXCHANGE = "VanPalletExchange";
  public static final String JSON_PROPERTY_VAN_ROLLER_BED = "VanRollerBed";
  public static final String JSON_PROPERTY_VAN_TRIPLE = "VanTriple";
  public static final String JSON_PROPERTY_VAN_VENTED = "VanVented";
  public static final String JSON_PROPERTY_VAN_WITH_CURTAINS = "VanWithCurtains";
  public static final String JSON_PROPERTY_VAN_WITH_TEAM = "VanWithTeam";
  public static final String JSON_PROPERTY_ONE_TO_TWO_CAR_HAULER = "OneToTwoCarHauler";
  public static final String JSON_PROPERTY_THREE_CAR_HAULER = "ThreeCarHauler";
  public static final String JSON_PROPERTY_FOUR_TO_FIVE_CAR_HAULER = "FourToFiveCarHauler";
  public static final String JSON_PROPERTY_SIX_TO_SEVEN_CAR_HAULER = "SixToSevenCarHauler";
  public static final String JSON_PROPERTY_EIGHT_TO_TEN_CAR_HAULER = "EightToTenCarHauler";
  public static final String JSON_PROPERTY_CHASSIS = "Chassis";
  public static final String JSON_PROPERTY_PINTLE_HITCH = "PintleHitch";
  public static final String JSON_PROPERTY_VAN_SPRINTER = "VanSprinter";
  public static final String JSON_PROPERTY_TOTER = "Toter";
  private Boolean autoCarrier;
  private Boolean btrain;
  private Boolean conestoga;
  private Boolean container;
  private Boolean containerInsulated;
  private Boolean containerRefrigerated;
  private Boolean conveyor;
  private Boolean doubleDrop;
  private Boolean dropDeckLandoll;
  private Boolean dumpTrailer;
  private Boolean flatbed;
  private Boolean flatbedAirRide;
  private Boolean flatbedConestoga;
  private Boolean flatbedDouble;
  private Boolean flatbedHazMat;
  private Boolean flatbedHotshot;
  private Boolean flatbedMaxi;
  private Boolean flatbedOrStepDeck;
  private Boolean flatbedOverdimension;
  private Boolean flatbedWithchains;
  private Boolean flatbedWithSides;
  private Boolean flatbedWithTarps;
  private Boolean flatbedWithTeam;
  private Boolean flatbedVanReefer;
  private Boolean hopperBottom;
  private Boolean insulatedVanOrReefer;
  private Boolean lowboy;
  private Boolean lowboyOrRemGooseneck;
  private Boolean lowboyOverdimension;
  private Boolean movingVan;
  private Boolean pneumatic;
  private Boolean powerOnly;
  private Boolean reefer;
  private Boolean reeferAirRide;
  private Boolean reeferDouble;
  private Boolean reeferHazMat;
  private Boolean reeferIntermodal;
  private Boolean reeferLogistics;
  private Boolean reeferOrVentedVan;
  private Boolean reeferPalletExchange;
  private Boolean reeferWithTeam;
  private Boolean removableGooseneck;
  private Boolean stepDeck;
  private Boolean stepDeckOrRemGooseneck;
  private Boolean stepdeckConestoga;
  private Boolean straightBoxTruck;
  private Boolean stretchTrailer;
  private Boolean tankerAluminum;
  private Boolean tankerIntermodal;
  private Boolean tankerSteel;
  private Boolean truckAndTrailer;
  private Boolean van;
  private Boolean vanAirRide;
  private Boolean vanBlanketWrap;
  private Boolean vanConestoga;
  private Boolean vanDouble;
  private Boolean vanHazMat;
  private Boolean vanHotshot;
  private Boolean vanInsulated;
  private Boolean vanIntermodal;
  private Boolean vanLiftGate;
  private Boolean vanLogistics;
  private Boolean vanOpenTop;
  private Boolean vanOrFlatbed;
  private Boolean vanOrFlatbedwTarps;
  private Boolean vanOrReefer;
  private Boolean vanPalletExchange;
  private Boolean vanRollerBed;
  private Boolean vanTriple;
  private Boolean vanVented;
  private Boolean vanWithCurtains;
  private Boolean vanWithTeam;
  private Boolean oneToTwoCarHauler;
  private Boolean threeCarHauler;
  private Boolean fourToFiveCarHauler;
  private Boolean sixToSevenCarHauler;
  private Boolean eightToTenCarHauler;
  private Boolean chassis;
  private Boolean pintleHitch;
  private Boolean vanSprinter;
  private Boolean toter;

  public CarrierTruckTypeDto() {
  }

  public CarrierTruckTypeDto autoCarrier(Boolean autoCarrier) {

    this.autoCarrier = autoCarrier;
    return this;
  }

  /**
   * Get autoCarrier
   *
   * @return autoCarrier
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_AUTO_CARRIER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getAutoCarrier() {
    return autoCarrier;
  }


  @JsonProperty(JSON_PROPERTY_AUTO_CARRIER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setAutoCarrier(Boolean autoCarrier) {
    this.autoCarrier = autoCarrier;
  }


  public CarrierTruckTypeDto btrain(Boolean btrain) {

    this.btrain = btrain;
    return this;
  }

  /**
   * Get btrain
   *
   * @return btrain
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_BTRAIN)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getBtrain() {
    return btrain;
  }


  @JsonProperty(JSON_PROPERTY_BTRAIN)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setBtrain(Boolean btrain) {
    this.btrain = btrain;
  }


  public CarrierTruckTypeDto conestoga(Boolean conestoga) {

    this.conestoga = conestoga;
    return this;
  }

  /**
   * Get conestoga
   *
   * @return conestoga
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CONESTOGA)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getConestoga() {
    return conestoga;
  }


  @JsonProperty(JSON_PROPERTY_CONESTOGA)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setConestoga(Boolean conestoga) {
    this.conestoga = conestoga;
  }


  public CarrierTruckTypeDto container(Boolean container) {

    this.container = container;
    return this;
  }

  /**
   * Get container
   *
   * @return container
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CONTAINER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getContainer() {
    return container;
  }


  @JsonProperty(JSON_PROPERTY_CONTAINER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setContainer(Boolean container) {
    this.container = container;
  }


  public CarrierTruckTypeDto containerInsulated(Boolean containerInsulated) {

    this.containerInsulated = containerInsulated;
    return this;
  }

  /**
   * Get containerInsulated
   *
   * @return containerInsulated
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CONTAINER_INSULATED)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getContainerInsulated() {
    return containerInsulated;
  }


  @JsonProperty(JSON_PROPERTY_CONTAINER_INSULATED)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setContainerInsulated(Boolean containerInsulated) {
    this.containerInsulated = containerInsulated;
  }


  public CarrierTruckTypeDto containerRefrigerated(Boolean containerRefrigerated) {

    this.containerRefrigerated = containerRefrigerated;
    return this;
  }

  /**
   * Get containerRefrigerated
   *
   * @return containerRefrigerated
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CONTAINER_REFRIGERATED)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getContainerRefrigerated() {
    return containerRefrigerated;
  }


  @JsonProperty(JSON_PROPERTY_CONTAINER_REFRIGERATED)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setContainerRefrigerated(Boolean containerRefrigerated) {
    this.containerRefrigerated = containerRefrigerated;
  }


  public CarrierTruckTypeDto conveyor(Boolean conveyor) {

    this.conveyor = conveyor;
    return this;
  }

  /**
   * Get conveyor
   *
   * @return conveyor
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CONVEYOR)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getConveyor() {
    return conveyor;
  }


  @JsonProperty(JSON_PROPERTY_CONVEYOR)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setConveyor(Boolean conveyor) {
    this.conveyor = conveyor;
  }


  public CarrierTruckTypeDto doubleDrop(Boolean doubleDrop) {

    this.doubleDrop = doubleDrop;
    return this;
  }

  /**
   * Get doubleDrop
   *
   * @return doubleDrop
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_DOUBLE_DROP)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getDoubleDrop() {
    return doubleDrop;
  }


  @JsonProperty(JSON_PROPERTY_DOUBLE_DROP)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setDoubleDrop(Boolean doubleDrop) {
    this.doubleDrop = doubleDrop;
  }


  public CarrierTruckTypeDto dropDeckLandoll(Boolean dropDeckLandoll) {

    this.dropDeckLandoll = dropDeckLandoll;
    return this;
  }

  /**
   * Get dropDeckLandoll
   *
   * @return dropDeckLandoll
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_DROP_DECK_LANDOLL)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getDropDeckLandoll() {
    return dropDeckLandoll;
  }


  @JsonProperty(JSON_PROPERTY_DROP_DECK_LANDOLL)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setDropDeckLandoll(Boolean dropDeckLandoll) {
    this.dropDeckLandoll = dropDeckLandoll;
  }


  public CarrierTruckTypeDto dumpTrailer(Boolean dumpTrailer) {

    this.dumpTrailer = dumpTrailer;
    return this;
  }

  /**
   * Get dumpTrailer
   *
   * @return dumpTrailer
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_DUMP_TRAILER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getDumpTrailer() {
    return dumpTrailer;
  }


  @JsonProperty(JSON_PROPERTY_DUMP_TRAILER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setDumpTrailer(Boolean dumpTrailer) {
    this.dumpTrailer = dumpTrailer;
  }


  public CarrierTruckTypeDto flatbed(Boolean flatbed) {

    this.flatbed = flatbed;
    return this;
  }

  /**
   * Get flatbed
   *
   * @return flatbed
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_FLATBED)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getFlatbed() {
    return flatbed;
  }


  @JsonProperty(JSON_PROPERTY_FLATBED)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setFlatbed(Boolean flatbed) {
    this.flatbed = flatbed;
  }


  public CarrierTruckTypeDto flatbedAirRide(Boolean flatbedAirRide) {

    this.flatbedAirRide = flatbedAirRide;
    return this;
  }

  /**
   * Get flatbedAirRide
   *
   * @return flatbedAirRide
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_FLATBED_AIR_RIDE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getFlatbedAirRide() {
    return flatbedAirRide;
  }


  @JsonProperty(JSON_PROPERTY_FLATBED_AIR_RIDE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setFlatbedAirRide(Boolean flatbedAirRide) {
    this.flatbedAirRide = flatbedAirRide;
  }


  public CarrierTruckTypeDto flatbedConestoga(Boolean flatbedConestoga) {

    this.flatbedConestoga = flatbedConestoga;
    return this;
  }

  /**
   * Get flatbedConestoga
   *
   * @return flatbedConestoga
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_FLATBED_CONESTOGA)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getFlatbedConestoga() {
    return flatbedConestoga;
  }


  @JsonProperty(JSON_PROPERTY_FLATBED_CONESTOGA)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setFlatbedConestoga(Boolean flatbedConestoga) {
    this.flatbedConestoga = flatbedConestoga;
  }


  public CarrierTruckTypeDto flatbedDouble(Boolean flatbedDouble) {

    this.flatbedDouble = flatbedDouble;
    return this;
  }

  /**
   * Get flatbedDouble
   *
   * @return flatbedDouble
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_FLATBED_DOUBLE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getFlatbedDouble() {
    return flatbedDouble;
  }


  @JsonProperty(JSON_PROPERTY_FLATBED_DOUBLE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setFlatbedDouble(Boolean flatbedDouble) {
    this.flatbedDouble = flatbedDouble;
  }


  public CarrierTruckTypeDto flatbedHazMat(Boolean flatbedHazMat) {

    this.flatbedHazMat = flatbedHazMat;
    return this;
  }

  /**
   * Get flatbedHazMat
   *
   * @return flatbedHazMat
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_FLATBED_HAZ_MAT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getFlatbedHazMat() {
    return flatbedHazMat;
  }


  @JsonProperty(JSON_PROPERTY_FLATBED_HAZ_MAT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setFlatbedHazMat(Boolean flatbedHazMat) {
    this.flatbedHazMat = flatbedHazMat;
  }


  public CarrierTruckTypeDto flatbedHotshot(Boolean flatbedHotshot) {

    this.flatbedHotshot = flatbedHotshot;
    return this;
  }

  /**
   * Get flatbedHotshot
   *
   * @return flatbedHotshot
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_FLATBED_HOTSHOT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getFlatbedHotshot() {
    return flatbedHotshot;
  }


  @JsonProperty(JSON_PROPERTY_FLATBED_HOTSHOT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setFlatbedHotshot(Boolean flatbedHotshot) {
    this.flatbedHotshot = flatbedHotshot;
  }


  public CarrierTruckTypeDto flatbedMaxi(Boolean flatbedMaxi) {

    this.flatbedMaxi = flatbedMaxi;
    return this;
  }

  /**
   * Get flatbedMaxi
   *
   * @return flatbedMaxi
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_FLATBED_MAXI)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getFlatbedMaxi() {
    return flatbedMaxi;
  }


  @JsonProperty(JSON_PROPERTY_FLATBED_MAXI)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setFlatbedMaxi(Boolean flatbedMaxi) {
    this.flatbedMaxi = flatbedMaxi;
  }


  public CarrierTruckTypeDto flatbedOrStepDeck(Boolean flatbedOrStepDeck) {

    this.flatbedOrStepDeck = flatbedOrStepDeck;
    return this;
  }

  /**
   * Get flatbedOrStepDeck
   *
   * @return flatbedOrStepDeck
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_FLATBED_OR_STEP_DECK)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getFlatbedOrStepDeck() {
    return flatbedOrStepDeck;
  }


  @JsonProperty(JSON_PROPERTY_FLATBED_OR_STEP_DECK)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setFlatbedOrStepDeck(Boolean flatbedOrStepDeck) {
    this.flatbedOrStepDeck = flatbedOrStepDeck;
  }


  public CarrierTruckTypeDto flatbedOverdimension(Boolean flatbedOverdimension) {

    this.flatbedOverdimension = flatbedOverdimension;
    return this;
  }

  /**
   * Get flatbedOverdimension
   *
   * @return flatbedOverdimension
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_FLATBED_OVERDIMENSION)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getFlatbedOverdimension() {
    return flatbedOverdimension;
  }


  @JsonProperty(JSON_PROPERTY_FLATBED_OVERDIMENSION)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setFlatbedOverdimension(Boolean flatbedOverdimension) {
    this.flatbedOverdimension = flatbedOverdimension;
  }


  public CarrierTruckTypeDto flatbedWithchains(Boolean flatbedWithchains) {

    this.flatbedWithchains = flatbedWithchains;
    return this;
  }

  /**
   * Get flatbedWithchains
   *
   * @return flatbedWithchains
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_FLATBED_WITHCHAINS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getFlatbedWithchains() {
    return flatbedWithchains;
  }


  @JsonProperty(JSON_PROPERTY_FLATBED_WITHCHAINS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setFlatbedWithchains(Boolean flatbedWithchains) {
    this.flatbedWithchains = flatbedWithchains;
  }


  public CarrierTruckTypeDto flatbedWithSides(Boolean flatbedWithSides) {

    this.flatbedWithSides = flatbedWithSides;
    return this;
  }

  /**
   * Get flatbedWithSides
   *
   * @return flatbedWithSides
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_FLATBED_WITH_SIDES)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getFlatbedWithSides() {
    return flatbedWithSides;
  }


  @JsonProperty(JSON_PROPERTY_FLATBED_WITH_SIDES)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setFlatbedWithSides(Boolean flatbedWithSides) {
    this.flatbedWithSides = flatbedWithSides;
  }


  public CarrierTruckTypeDto flatbedWithTarps(Boolean flatbedWithTarps) {

    this.flatbedWithTarps = flatbedWithTarps;
    return this;
  }

  /**
   * Get flatbedWithTarps
   *
   * @return flatbedWithTarps
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_FLATBED_WITH_TARPS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getFlatbedWithTarps() {
    return flatbedWithTarps;
  }


  @JsonProperty(JSON_PROPERTY_FLATBED_WITH_TARPS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setFlatbedWithTarps(Boolean flatbedWithTarps) {
    this.flatbedWithTarps = flatbedWithTarps;
  }


  public CarrierTruckTypeDto flatbedWithTeam(Boolean flatbedWithTeam) {

    this.flatbedWithTeam = flatbedWithTeam;
    return this;
  }

  /**
   * Get flatbedWithTeam
   *
   * @return flatbedWithTeam
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_FLATBED_WITH_TEAM)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getFlatbedWithTeam() {
    return flatbedWithTeam;
  }


  @JsonProperty(JSON_PROPERTY_FLATBED_WITH_TEAM)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setFlatbedWithTeam(Boolean flatbedWithTeam) {
    this.flatbedWithTeam = flatbedWithTeam;
  }


  public CarrierTruckTypeDto flatbedVanReefer(Boolean flatbedVanReefer) {

    this.flatbedVanReefer = flatbedVanReefer;
    return this;
  }

  /**
   * Get flatbedVanReefer
   *
   * @return flatbedVanReefer
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_FLATBED_VAN_REEFER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getFlatbedVanReefer() {
    return flatbedVanReefer;
  }


  @JsonProperty(JSON_PROPERTY_FLATBED_VAN_REEFER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setFlatbedVanReefer(Boolean flatbedVanReefer) {
    this.flatbedVanReefer = flatbedVanReefer;
  }


  public CarrierTruckTypeDto hopperBottom(Boolean hopperBottom) {

    this.hopperBottom = hopperBottom;
    return this;
  }

  /**
   * Get hopperBottom
   *
   * @return hopperBottom
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_HOPPER_BOTTOM)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getHopperBottom() {
    return hopperBottom;
  }


  @JsonProperty(JSON_PROPERTY_HOPPER_BOTTOM)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setHopperBottom(Boolean hopperBottom) {
    this.hopperBottom = hopperBottom;
  }


  public CarrierTruckTypeDto insulatedVanOrReefer(Boolean insulatedVanOrReefer) {

    this.insulatedVanOrReefer = insulatedVanOrReefer;
    return this;
  }

  /**
   * Get insulatedVanOrReefer
   *
   * @return insulatedVanOrReefer
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_INSULATED_VAN_OR_REEFER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getInsulatedVanOrReefer() {
    return insulatedVanOrReefer;
  }


  @JsonProperty(JSON_PROPERTY_INSULATED_VAN_OR_REEFER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setInsulatedVanOrReefer(Boolean insulatedVanOrReefer) {
    this.insulatedVanOrReefer = insulatedVanOrReefer;
  }


  public CarrierTruckTypeDto lowboy(Boolean lowboy) {

    this.lowboy = lowboy;
    return this;
  }

  /**
   * Get lowboy
   *
   * @return lowboy
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_LOWBOY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getLowboy() {
    return lowboy;
  }


  @JsonProperty(JSON_PROPERTY_LOWBOY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setLowboy(Boolean lowboy) {
    this.lowboy = lowboy;
  }


  public CarrierTruckTypeDto lowboyOrRemGooseneck(Boolean lowboyOrRemGooseneck) {

    this.lowboyOrRemGooseneck = lowboyOrRemGooseneck;
    return this;
  }

  /**
   * Get lowboyOrRemGooseneck
   *
   * @return lowboyOrRemGooseneck
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_LOWBOY_OR_REM_GOOSENECK)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getLowboyOrRemGooseneck() {
    return lowboyOrRemGooseneck;
  }


  @JsonProperty(JSON_PROPERTY_LOWBOY_OR_REM_GOOSENECK)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setLowboyOrRemGooseneck(Boolean lowboyOrRemGooseneck) {
    this.lowboyOrRemGooseneck = lowboyOrRemGooseneck;
  }


  public CarrierTruckTypeDto lowboyOverdimension(Boolean lowboyOverdimension) {

    this.lowboyOverdimension = lowboyOverdimension;
    return this;
  }

  /**
   * Get lowboyOverdimension
   *
   * @return lowboyOverdimension
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_LOWBOY_OVERDIMENSION)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getLowboyOverdimension() {
    return lowboyOverdimension;
  }


  @JsonProperty(JSON_PROPERTY_LOWBOY_OVERDIMENSION)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setLowboyOverdimension(Boolean lowboyOverdimension) {
    this.lowboyOverdimension = lowboyOverdimension;
  }


  public CarrierTruckTypeDto movingVan(Boolean movingVan) {

    this.movingVan = movingVan;
    return this;
  }

  /**
   * Get movingVan
   *
   * @return movingVan
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_MOVING_VAN)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getMovingVan() {
    return movingVan;
  }


  @JsonProperty(JSON_PROPERTY_MOVING_VAN)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setMovingVan(Boolean movingVan) {
    this.movingVan = movingVan;
  }


  public CarrierTruckTypeDto pneumatic(Boolean pneumatic) {

    this.pneumatic = pneumatic;
    return this;
  }

  /**
   * Get pneumatic
   *
   * @return pneumatic
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_PNEUMATIC)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getPneumatic() {
    return pneumatic;
  }


  @JsonProperty(JSON_PROPERTY_PNEUMATIC)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setPneumatic(Boolean pneumatic) {
    this.pneumatic = pneumatic;
  }


  public CarrierTruckTypeDto powerOnly(Boolean powerOnly) {

    this.powerOnly = powerOnly;
    return this;
  }

  /**
   * Get powerOnly
   *
   * @return powerOnly
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_POWER_ONLY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getPowerOnly() {
    return powerOnly;
  }


  @JsonProperty(JSON_PROPERTY_POWER_ONLY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setPowerOnly(Boolean powerOnly) {
    this.powerOnly = powerOnly;
  }


  public CarrierTruckTypeDto reefer(Boolean reefer) {

    this.reefer = reefer;
    return this;
  }

  /**
   * Get reefer
   *
   * @return reefer
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_REEFER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getReefer() {
    return reefer;
  }


  @JsonProperty(JSON_PROPERTY_REEFER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setReefer(Boolean reefer) {
    this.reefer = reefer;
  }


  public CarrierTruckTypeDto reeferAirRide(Boolean reeferAirRide) {

    this.reeferAirRide = reeferAirRide;
    return this;
  }

  /**
   * Get reeferAirRide
   *
   * @return reeferAirRide
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_REEFER_AIR_RIDE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getReeferAirRide() {
    return reeferAirRide;
  }


  @JsonProperty(JSON_PROPERTY_REEFER_AIR_RIDE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setReeferAirRide(Boolean reeferAirRide) {
    this.reeferAirRide = reeferAirRide;
  }


  public CarrierTruckTypeDto reeferDouble(Boolean reeferDouble) {

    this.reeferDouble = reeferDouble;
    return this;
  }

  /**
   * Get reeferDouble
   *
   * @return reeferDouble
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_REEFER_DOUBLE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getReeferDouble() {
    return reeferDouble;
  }


  @JsonProperty(JSON_PROPERTY_REEFER_DOUBLE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setReeferDouble(Boolean reeferDouble) {
    this.reeferDouble = reeferDouble;
  }


  public CarrierTruckTypeDto reeferHazMat(Boolean reeferHazMat) {

    this.reeferHazMat = reeferHazMat;
    return this;
  }

  /**
   * Get reeferHazMat
   *
   * @return reeferHazMat
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_REEFER_HAZ_MAT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getReeferHazMat() {
    return reeferHazMat;
  }


  @JsonProperty(JSON_PROPERTY_REEFER_HAZ_MAT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setReeferHazMat(Boolean reeferHazMat) {
    this.reeferHazMat = reeferHazMat;
  }


  public CarrierTruckTypeDto reeferIntermodal(Boolean reeferIntermodal) {

    this.reeferIntermodal = reeferIntermodal;
    return this;
  }

  /**
   * Get reeferIntermodal
   *
   * @return reeferIntermodal
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_REEFER_INTERMODAL)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getReeferIntermodal() {
    return reeferIntermodal;
  }


  @JsonProperty(JSON_PROPERTY_REEFER_INTERMODAL)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setReeferIntermodal(Boolean reeferIntermodal) {
    this.reeferIntermodal = reeferIntermodal;
  }


  public CarrierTruckTypeDto reeferLogistics(Boolean reeferLogistics) {

    this.reeferLogistics = reeferLogistics;
    return this;
  }

  /**
   * Get reeferLogistics
   *
   * @return reeferLogistics
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_REEFER_LOGISTICS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getReeferLogistics() {
    return reeferLogistics;
  }


  @JsonProperty(JSON_PROPERTY_REEFER_LOGISTICS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setReeferLogistics(Boolean reeferLogistics) {
    this.reeferLogistics = reeferLogistics;
  }


  public CarrierTruckTypeDto reeferOrVentedVan(Boolean reeferOrVentedVan) {

    this.reeferOrVentedVan = reeferOrVentedVan;
    return this;
  }

  /**
   * Get reeferOrVentedVan
   *
   * @return reeferOrVentedVan
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_REEFER_OR_VENTED_VAN)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getReeferOrVentedVan() {
    return reeferOrVentedVan;
  }


  @JsonProperty(JSON_PROPERTY_REEFER_OR_VENTED_VAN)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setReeferOrVentedVan(Boolean reeferOrVentedVan) {
    this.reeferOrVentedVan = reeferOrVentedVan;
  }


  public CarrierTruckTypeDto reeferPalletExchange(Boolean reeferPalletExchange) {

    this.reeferPalletExchange = reeferPalletExchange;
    return this;
  }

  /**
   * Get reeferPalletExchange
   *
   * @return reeferPalletExchange
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_REEFER_PALLET_EXCHANGE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getReeferPalletExchange() {
    return reeferPalletExchange;
  }


  @JsonProperty(JSON_PROPERTY_REEFER_PALLET_EXCHANGE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setReeferPalletExchange(Boolean reeferPalletExchange) {
    this.reeferPalletExchange = reeferPalletExchange;
  }


  public CarrierTruckTypeDto reeferWithTeam(Boolean reeferWithTeam) {

    this.reeferWithTeam = reeferWithTeam;
    return this;
  }

  /**
   * Get reeferWithTeam
   *
   * @return reeferWithTeam
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_REEFER_WITH_TEAM)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getReeferWithTeam() {
    return reeferWithTeam;
  }


  @JsonProperty(JSON_PROPERTY_REEFER_WITH_TEAM)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setReeferWithTeam(Boolean reeferWithTeam) {
    this.reeferWithTeam = reeferWithTeam;
  }


  public CarrierTruckTypeDto removableGooseneck(Boolean removableGooseneck) {

    this.removableGooseneck = removableGooseneck;
    return this;
  }

  /**
   * Get removableGooseneck
   *
   * @return removableGooseneck
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_REMOVABLE_GOOSENECK)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getRemovableGooseneck() {
    return removableGooseneck;
  }


  @JsonProperty(JSON_PROPERTY_REMOVABLE_GOOSENECK)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setRemovableGooseneck(Boolean removableGooseneck) {
    this.removableGooseneck = removableGooseneck;
  }


  public CarrierTruckTypeDto stepDeck(Boolean stepDeck) {

    this.stepDeck = stepDeck;
    return this;
  }

  /**
   * Get stepDeck
   *
   * @return stepDeck
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_STEP_DECK)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getStepDeck() {
    return stepDeck;
  }


  @JsonProperty(JSON_PROPERTY_STEP_DECK)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setStepDeck(Boolean stepDeck) {
    this.stepDeck = stepDeck;
  }


  public CarrierTruckTypeDto stepDeckOrRemGooseneck(Boolean stepDeckOrRemGooseneck) {

    this.stepDeckOrRemGooseneck = stepDeckOrRemGooseneck;
    return this;
  }

  /**
   * Get stepDeckOrRemGooseneck
   *
   * @return stepDeckOrRemGooseneck
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_STEP_DECK_OR_REM_GOOSENECK)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getStepDeckOrRemGooseneck() {
    return stepDeckOrRemGooseneck;
  }


  @JsonProperty(JSON_PROPERTY_STEP_DECK_OR_REM_GOOSENECK)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setStepDeckOrRemGooseneck(Boolean stepDeckOrRemGooseneck) {
    this.stepDeckOrRemGooseneck = stepDeckOrRemGooseneck;
  }


  public CarrierTruckTypeDto stepdeckConestoga(Boolean stepdeckConestoga) {

    this.stepdeckConestoga = stepdeckConestoga;
    return this;
  }

  /**
   * Get stepdeckConestoga
   *
   * @return stepdeckConestoga
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_STEPDECK_CONESTOGA)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getStepdeckConestoga() {
    return stepdeckConestoga;
  }


  @JsonProperty(JSON_PROPERTY_STEPDECK_CONESTOGA)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setStepdeckConestoga(Boolean stepdeckConestoga) {
    this.stepdeckConestoga = stepdeckConestoga;
  }


  public CarrierTruckTypeDto straightBoxTruck(Boolean straightBoxTruck) {

    this.straightBoxTruck = straightBoxTruck;
    return this;
  }

  /**
   * Get straightBoxTruck
   *
   * @return straightBoxTruck
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_STRAIGHT_BOX_TRUCK)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getStraightBoxTruck() {
    return straightBoxTruck;
  }


  @JsonProperty(JSON_PROPERTY_STRAIGHT_BOX_TRUCK)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setStraightBoxTruck(Boolean straightBoxTruck) {
    this.straightBoxTruck = straightBoxTruck;
  }


  public CarrierTruckTypeDto stretchTrailer(Boolean stretchTrailer) {

    this.stretchTrailer = stretchTrailer;
    return this;
  }

  /**
   * Get stretchTrailer
   *
   * @return stretchTrailer
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_STRETCH_TRAILER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getStretchTrailer() {
    return stretchTrailer;
  }


  @JsonProperty(JSON_PROPERTY_STRETCH_TRAILER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setStretchTrailer(Boolean stretchTrailer) {
    this.stretchTrailer = stretchTrailer;
  }


  public CarrierTruckTypeDto tankerAluminum(Boolean tankerAluminum) {

    this.tankerAluminum = tankerAluminum;
    return this;
  }

  /**
   * Get tankerAluminum
   *
   * @return tankerAluminum
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_TANKER_ALUMINUM)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getTankerAluminum() {
    return tankerAluminum;
  }


  @JsonProperty(JSON_PROPERTY_TANKER_ALUMINUM)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setTankerAluminum(Boolean tankerAluminum) {
    this.tankerAluminum = tankerAluminum;
  }


  public CarrierTruckTypeDto tankerIntermodal(Boolean tankerIntermodal) {

    this.tankerIntermodal = tankerIntermodal;
    return this;
  }

  /**
   * Get tankerIntermodal
   *
   * @return tankerIntermodal
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_TANKER_INTERMODAL)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getTankerIntermodal() {
    return tankerIntermodal;
  }


  @JsonProperty(JSON_PROPERTY_TANKER_INTERMODAL)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setTankerIntermodal(Boolean tankerIntermodal) {
    this.tankerIntermodal = tankerIntermodal;
  }


  public CarrierTruckTypeDto tankerSteel(Boolean tankerSteel) {

    this.tankerSteel = tankerSteel;
    return this;
  }

  /**
   * Get tankerSteel
   *
   * @return tankerSteel
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_TANKER_STEEL)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getTankerSteel() {
    return tankerSteel;
  }


  @JsonProperty(JSON_PROPERTY_TANKER_STEEL)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setTankerSteel(Boolean tankerSteel) {
    this.tankerSteel = tankerSteel;
  }


  public CarrierTruckTypeDto truckAndTrailer(Boolean truckAndTrailer) {

    this.truckAndTrailer = truckAndTrailer;
    return this;
  }

  /**
   * Get truckAndTrailer
   *
   * @return truckAndTrailer
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_TRUCK_AND_TRAILER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getTruckAndTrailer() {
    return truckAndTrailer;
  }


  @JsonProperty(JSON_PROPERTY_TRUCK_AND_TRAILER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setTruckAndTrailer(Boolean truckAndTrailer) {
    this.truckAndTrailer = truckAndTrailer;
  }


  public CarrierTruckTypeDto van(Boolean van) {

    this.van = van;
    return this;
  }

  /**
   * Get van
   *
   * @return van
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_VAN)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getVan() {
    return van;
  }


  @JsonProperty(JSON_PROPERTY_VAN)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setVan(Boolean van) {
    this.van = van;
  }


  public CarrierTruckTypeDto vanAirRide(Boolean vanAirRide) {

    this.vanAirRide = vanAirRide;
    return this;
  }

  /**
   * Get vanAirRide
   *
   * @return vanAirRide
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_VAN_AIR_RIDE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getVanAirRide() {
    return vanAirRide;
  }


  @JsonProperty(JSON_PROPERTY_VAN_AIR_RIDE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setVanAirRide(Boolean vanAirRide) {
    this.vanAirRide = vanAirRide;
  }


  public CarrierTruckTypeDto vanBlanketWrap(Boolean vanBlanketWrap) {

    this.vanBlanketWrap = vanBlanketWrap;
    return this;
  }

  /**
   * Get vanBlanketWrap
   *
   * @return vanBlanketWrap
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_VAN_BLANKET_WRAP)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getVanBlanketWrap() {
    return vanBlanketWrap;
  }


  @JsonProperty(JSON_PROPERTY_VAN_BLANKET_WRAP)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setVanBlanketWrap(Boolean vanBlanketWrap) {
    this.vanBlanketWrap = vanBlanketWrap;
  }


  public CarrierTruckTypeDto vanConestoga(Boolean vanConestoga) {

    this.vanConestoga = vanConestoga;
    return this;
  }

  /**
   * Get vanConestoga
   *
   * @return vanConestoga
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_VAN_CONESTOGA)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getVanConestoga() {
    return vanConestoga;
  }


  @JsonProperty(JSON_PROPERTY_VAN_CONESTOGA)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setVanConestoga(Boolean vanConestoga) {
    this.vanConestoga = vanConestoga;
  }


  public CarrierTruckTypeDto vanDouble(Boolean vanDouble) {

    this.vanDouble = vanDouble;
    return this;
  }

  /**
   * Get vanDouble
   *
   * @return vanDouble
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_VAN_DOUBLE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getVanDouble() {
    return vanDouble;
  }


  @JsonProperty(JSON_PROPERTY_VAN_DOUBLE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setVanDouble(Boolean vanDouble) {
    this.vanDouble = vanDouble;
  }


  public CarrierTruckTypeDto vanHazMat(Boolean vanHazMat) {

    this.vanHazMat = vanHazMat;
    return this;
  }

  /**
   * Get vanHazMat
   *
   * @return vanHazMat
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_VAN_HAZ_MAT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getVanHazMat() {
    return vanHazMat;
  }


  @JsonProperty(JSON_PROPERTY_VAN_HAZ_MAT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setVanHazMat(Boolean vanHazMat) {
    this.vanHazMat = vanHazMat;
  }


  public CarrierTruckTypeDto vanHotshot(Boolean vanHotshot) {

    this.vanHotshot = vanHotshot;
    return this;
  }

  /**
   * Get vanHotshot
   *
   * @return vanHotshot
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_VAN_HOTSHOT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getVanHotshot() {
    return vanHotshot;
  }


  @JsonProperty(JSON_PROPERTY_VAN_HOTSHOT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setVanHotshot(Boolean vanHotshot) {
    this.vanHotshot = vanHotshot;
  }


  public CarrierTruckTypeDto vanInsulated(Boolean vanInsulated) {

    this.vanInsulated = vanInsulated;
    return this;
  }

  /**
   * Get vanInsulated
   *
   * @return vanInsulated
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_VAN_INSULATED)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getVanInsulated() {
    return vanInsulated;
  }


  @JsonProperty(JSON_PROPERTY_VAN_INSULATED)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setVanInsulated(Boolean vanInsulated) {
    this.vanInsulated = vanInsulated;
  }


  public CarrierTruckTypeDto vanIntermodal(Boolean vanIntermodal) {

    this.vanIntermodal = vanIntermodal;
    return this;
  }

  /**
   * Get vanIntermodal
   *
   * @return vanIntermodal
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_VAN_INTERMODAL)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getVanIntermodal() {
    return vanIntermodal;
  }


  @JsonProperty(JSON_PROPERTY_VAN_INTERMODAL)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setVanIntermodal(Boolean vanIntermodal) {
    this.vanIntermodal = vanIntermodal;
  }


  public CarrierTruckTypeDto vanLiftGate(Boolean vanLiftGate) {

    this.vanLiftGate = vanLiftGate;
    return this;
  }

  /**
   * Get vanLiftGate
   *
   * @return vanLiftGate
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_VAN_LIFT_GATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getVanLiftGate() {
    return vanLiftGate;
  }


  @JsonProperty(JSON_PROPERTY_VAN_LIFT_GATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setVanLiftGate(Boolean vanLiftGate) {
    this.vanLiftGate = vanLiftGate;
  }


  public CarrierTruckTypeDto vanLogistics(Boolean vanLogistics) {

    this.vanLogistics = vanLogistics;
    return this;
  }

  /**
   * Get vanLogistics
   *
   * @return vanLogistics
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_VAN_LOGISTICS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getVanLogistics() {
    return vanLogistics;
  }


  @JsonProperty(JSON_PROPERTY_VAN_LOGISTICS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setVanLogistics(Boolean vanLogistics) {
    this.vanLogistics = vanLogistics;
  }


  public CarrierTruckTypeDto vanOpenTop(Boolean vanOpenTop) {

    this.vanOpenTop = vanOpenTop;
    return this;
  }

  /**
   * Get vanOpenTop
   *
   * @return vanOpenTop
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_VAN_OPEN_TOP)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getVanOpenTop() {
    return vanOpenTop;
  }


  @JsonProperty(JSON_PROPERTY_VAN_OPEN_TOP)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setVanOpenTop(Boolean vanOpenTop) {
    this.vanOpenTop = vanOpenTop;
  }


  public CarrierTruckTypeDto vanOrFlatbed(Boolean vanOrFlatbed) {

    this.vanOrFlatbed = vanOrFlatbed;
    return this;
  }

  /**
   * Get vanOrFlatbed
   *
   * @return vanOrFlatbed
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_VAN_OR_FLATBED)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getVanOrFlatbed() {
    return vanOrFlatbed;
  }


  @JsonProperty(JSON_PROPERTY_VAN_OR_FLATBED)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setVanOrFlatbed(Boolean vanOrFlatbed) {
    this.vanOrFlatbed = vanOrFlatbed;
  }


  public CarrierTruckTypeDto vanOrFlatbedwTarps(Boolean vanOrFlatbedwTarps) {

    this.vanOrFlatbedwTarps = vanOrFlatbedwTarps;
    return this;
  }

  /**
   * Get vanOrFlatbedwTarps
   *
   * @return vanOrFlatbedwTarps
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_VAN_OR_FLATBEDW_TARPS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getVanOrFlatbedwTarps() {
    return vanOrFlatbedwTarps;
  }


  @JsonProperty(JSON_PROPERTY_VAN_OR_FLATBEDW_TARPS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setVanOrFlatbedwTarps(Boolean vanOrFlatbedwTarps) {
    this.vanOrFlatbedwTarps = vanOrFlatbedwTarps;
  }


  public CarrierTruckTypeDto vanOrReefer(Boolean vanOrReefer) {

    this.vanOrReefer = vanOrReefer;
    return this;
  }

  /**
   * Get vanOrReefer
   *
   * @return vanOrReefer
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_VAN_OR_REEFER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getVanOrReefer() {
    return vanOrReefer;
  }


  @JsonProperty(JSON_PROPERTY_VAN_OR_REEFER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setVanOrReefer(Boolean vanOrReefer) {
    this.vanOrReefer = vanOrReefer;
  }


  public CarrierTruckTypeDto vanPalletExchange(Boolean vanPalletExchange) {

    this.vanPalletExchange = vanPalletExchange;
    return this;
  }

  /**
   * Get vanPalletExchange
   *
   * @return vanPalletExchange
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_VAN_PALLET_EXCHANGE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getVanPalletExchange() {
    return vanPalletExchange;
  }


  @JsonProperty(JSON_PROPERTY_VAN_PALLET_EXCHANGE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setVanPalletExchange(Boolean vanPalletExchange) {
    this.vanPalletExchange = vanPalletExchange;
  }


  public CarrierTruckTypeDto vanRollerBed(Boolean vanRollerBed) {

    this.vanRollerBed = vanRollerBed;
    return this;
  }

  /**
   * Get vanRollerBed
   *
   * @return vanRollerBed
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_VAN_ROLLER_BED)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getVanRollerBed() {
    return vanRollerBed;
  }


  @JsonProperty(JSON_PROPERTY_VAN_ROLLER_BED)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setVanRollerBed(Boolean vanRollerBed) {
    this.vanRollerBed = vanRollerBed;
  }


  public CarrierTruckTypeDto vanTriple(Boolean vanTriple) {

    this.vanTriple = vanTriple;
    return this;
  }

  /**
   * Get vanTriple
   *
   * @return vanTriple
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_VAN_TRIPLE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getVanTriple() {
    return vanTriple;
  }


  @JsonProperty(JSON_PROPERTY_VAN_TRIPLE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setVanTriple(Boolean vanTriple) {
    this.vanTriple = vanTriple;
  }


  public CarrierTruckTypeDto vanVented(Boolean vanVented) {

    this.vanVented = vanVented;
    return this;
  }

  /**
   * Get vanVented
   *
   * @return vanVented
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_VAN_VENTED)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getVanVented() {
    return vanVented;
  }


  @JsonProperty(JSON_PROPERTY_VAN_VENTED)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setVanVented(Boolean vanVented) {
    this.vanVented = vanVented;
  }


  public CarrierTruckTypeDto vanWithCurtains(Boolean vanWithCurtains) {

    this.vanWithCurtains = vanWithCurtains;
    return this;
  }

  /**
   * Get vanWithCurtains
   *
   * @return vanWithCurtains
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_VAN_WITH_CURTAINS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getVanWithCurtains() {
    return vanWithCurtains;
  }


  @JsonProperty(JSON_PROPERTY_VAN_WITH_CURTAINS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setVanWithCurtains(Boolean vanWithCurtains) {
    this.vanWithCurtains = vanWithCurtains;
  }


  public CarrierTruckTypeDto vanWithTeam(Boolean vanWithTeam) {

    this.vanWithTeam = vanWithTeam;
    return this;
  }

  /**
   * Get vanWithTeam
   *
   * @return vanWithTeam
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_VAN_WITH_TEAM)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getVanWithTeam() {
    return vanWithTeam;
  }


  @JsonProperty(JSON_PROPERTY_VAN_WITH_TEAM)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setVanWithTeam(Boolean vanWithTeam) {
    this.vanWithTeam = vanWithTeam;
  }


  public CarrierTruckTypeDto oneToTwoCarHauler(Boolean oneToTwoCarHauler) {

    this.oneToTwoCarHauler = oneToTwoCarHauler;
    return this;
  }

  /**
   * Get oneToTwoCarHauler
   *
   * @return oneToTwoCarHauler
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_ONE_TO_TWO_CAR_HAULER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getOneToTwoCarHauler() {
    return oneToTwoCarHauler;
  }


  @JsonProperty(JSON_PROPERTY_ONE_TO_TWO_CAR_HAULER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setOneToTwoCarHauler(Boolean oneToTwoCarHauler) {
    this.oneToTwoCarHauler = oneToTwoCarHauler;
  }


  public CarrierTruckTypeDto threeCarHauler(Boolean threeCarHauler) {

    this.threeCarHauler = threeCarHauler;
    return this;
  }

  /**
   * Get threeCarHauler
   *
   * @return threeCarHauler
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_THREE_CAR_HAULER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getThreeCarHauler() {
    return threeCarHauler;
  }


  @JsonProperty(JSON_PROPERTY_THREE_CAR_HAULER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setThreeCarHauler(Boolean threeCarHauler) {
    this.threeCarHauler = threeCarHauler;
  }


  public CarrierTruckTypeDto fourToFiveCarHauler(Boolean fourToFiveCarHauler) {

    this.fourToFiveCarHauler = fourToFiveCarHauler;
    return this;
  }

  /**
   * Get fourToFiveCarHauler
   *
   * @return fourToFiveCarHauler
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_FOUR_TO_FIVE_CAR_HAULER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getFourToFiveCarHauler() {
    return fourToFiveCarHauler;
  }


  @JsonProperty(JSON_PROPERTY_FOUR_TO_FIVE_CAR_HAULER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setFourToFiveCarHauler(Boolean fourToFiveCarHauler) {
    this.fourToFiveCarHauler = fourToFiveCarHauler;
  }


  public CarrierTruckTypeDto sixToSevenCarHauler(Boolean sixToSevenCarHauler) {

    this.sixToSevenCarHauler = sixToSevenCarHauler;
    return this;
  }

  /**
   * Get sixToSevenCarHauler
   *
   * @return sixToSevenCarHauler
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_SIX_TO_SEVEN_CAR_HAULER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getSixToSevenCarHauler() {
    return sixToSevenCarHauler;
  }


  @JsonProperty(JSON_PROPERTY_SIX_TO_SEVEN_CAR_HAULER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setSixToSevenCarHauler(Boolean sixToSevenCarHauler) {
    this.sixToSevenCarHauler = sixToSevenCarHauler;
  }


  public CarrierTruckTypeDto eightToTenCarHauler(Boolean eightToTenCarHauler) {

    this.eightToTenCarHauler = eightToTenCarHauler;
    return this;
  }

  /**
   * Get eightToTenCarHauler
   *
   * @return eightToTenCarHauler
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_EIGHT_TO_TEN_CAR_HAULER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getEightToTenCarHauler() {
    return eightToTenCarHauler;
  }


  @JsonProperty(JSON_PROPERTY_EIGHT_TO_TEN_CAR_HAULER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setEightToTenCarHauler(Boolean eightToTenCarHauler) {
    this.eightToTenCarHauler = eightToTenCarHauler;
  }


  public CarrierTruckTypeDto chassis(Boolean chassis) {

    this.chassis = chassis;
    return this;
  }

  /**
   * Get chassis
   *
   * @return chassis
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CHASSIS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getChassis() {
    return chassis;
  }


  @JsonProperty(JSON_PROPERTY_CHASSIS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setChassis(Boolean chassis) {
    this.chassis = chassis;
  }


  public CarrierTruckTypeDto pintleHitch(Boolean pintleHitch) {

    this.pintleHitch = pintleHitch;
    return this;
  }

  /**
   * Get pintleHitch
   *
   * @return pintleHitch
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_PINTLE_HITCH)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getPintleHitch() {
    return pintleHitch;
  }


  @JsonProperty(JSON_PROPERTY_PINTLE_HITCH)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setPintleHitch(Boolean pintleHitch) {
    this.pintleHitch = pintleHitch;
  }


  public CarrierTruckTypeDto vanSprinter(Boolean vanSprinter) {

    this.vanSprinter = vanSprinter;
    return this;
  }

  /**
   * Get vanSprinter
   *
   * @return vanSprinter
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_VAN_SPRINTER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getVanSprinter() {
    return vanSprinter;
  }


  @JsonProperty(JSON_PROPERTY_VAN_SPRINTER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setVanSprinter(Boolean vanSprinter) {
    this.vanSprinter = vanSprinter;
  }


  public CarrierTruckTypeDto toter(Boolean toter) {

    this.toter = toter;
    return this;
  }

  /**
   * Get toter
   *
   * @return toter
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_TOTER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getToter() {
    return toter;
  }


  @JsonProperty(JSON_PROPERTY_TOTER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setToter(Boolean toter) {
    this.toter = toter;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    CarrierTruckTypeDto carrierTruckTypeDto = (CarrierTruckTypeDto) o;
    return Objects.equals(this.autoCarrier, carrierTruckTypeDto.autoCarrier) && Objects.equals(this.btrain, carrierTruckTypeDto.btrain) && Objects.equals(
        this.conestoga, carrierTruckTypeDto.conestoga) && Objects.equals(this.container, carrierTruckTypeDto.container) && Objects.equals(
        this.containerInsulated, carrierTruckTypeDto.containerInsulated) && Objects.equals(this.containerRefrigerated,
        carrierTruckTypeDto.containerRefrigerated) && Objects.equals(this.conveyor, carrierTruckTypeDto.conveyor) && Objects.equals(this.doubleDrop,
        carrierTruckTypeDto.doubleDrop) && Objects.equals(this.dropDeckLandoll, carrierTruckTypeDto.dropDeckLandoll) && Objects.equals(this.dumpTrailer,
        carrierTruckTypeDto.dumpTrailer) && Objects.equals(this.flatbed, carrierTruckTypeDto.flatbed) && Objects.equals(this.flatbedAirRide,
        carrierTruckTypeDto.flatbedAirRide) && Objects.equals(this.flatbedConestoga, carrierTruckTypeDto.flatbedConestoga) && Objects.equals(this.flatbedDouble,
        carrierTruckTypeDto.flatbedDouble) && Objects.equals(this.flatbedHazMat, carrierTruckTypeDto.flatbedHazMat) && Objects.equals(this.flatbedHotshot,
        carrierTruckTypeDto.flatbedHotshot) && Objects.equals(this.flatbedMaxi, carrierTruckTypeDto.flatbedMaxi) && Objects.equals(this.flatbedOrStepDeck,
        carrierTruckTypeDto.flatbedOrStepDeck) && Objects.equals(this.flatbedOverdimension, carrierTruckTypeDto.flatbedOverdimension) && Objects.equals(
        this.flatbedWithchains, carrierTruckTypeDto.flatbedWithchains) && Objects.equals(this.flatbedWithSides, carrierTruckTypeDto.flatbedWithSides)
           && Objects.equals(this.flatbedWithTarps, carrierTruckTypeDto.flatbedWithTarps) && Objects.equals(this.flatbedWithTeam,
        carrierTruckTypeDto.flatbedWithTeam) && Objects.equals(this.flatbedVanReefer, carrierTruckTypeDto.flatbedVanReefer) && Objects.equals(this.hopperBottom,
        carrierTruckTypeDto.hopperBottom) && Objects.equals(this.insulatedVanOrReefer, carrierTruckTypeDto.insulatedVanOrReefer) && Objects.equals(this.lowboy,
        carrierTruckTypeDto.lowboy) && Objects.equals(this.lowboyOrRemGooseneck, carrierTruckTypeDto.lowboyOrRemGooseneck) && Objects.equals(
        this.lowboyOverdimension, carrierTruckTypeDto.lowboyOverdimension) && Objects.equals(this.movingVan, carrierTruckTypeDto.movingVan) && Objects.equals(
        this.pneumatic, carrierTruckTypeDto.pneumatic) && Objects.equals(this.powerOnly, carrierTruckTypeDto.powerOnly) && Objects.equals(this.reefer,
        carrierTruckTypeDto.reefer) && Objects.equals(this.reeferAirRide, carrierTruckTypeDto.reeferAirRide) && Objects.equals(this.reeferDouble,
        carrierTruckTypeDto.reeferDouble) && Objects.equals(this.reeferHazMat, carrierTruckTypeDto.reeferHazMat) && Objects.equals(this.reeferIntermodal,
        carrierTruckTypeDto.reeferIntermodal) && Objects.equals(this.reeferLogistics, carrierTruckTypeDto.reeferLogistics) && Objects.equals(
        this.reeferOrVentedVan, carrierTruckTypeDto.reeferOrVentedVan) && Objects.equals(this.reeferPalletExchange, carrierTruckTypeDto.reeferPalletExchange)
           && Objects.equals(this.reeferWithTeam, carrierTruckTypeDto.reeferWithTeam) && Objects.equals(this.removableGooseneck,
        carrierTruckTypeDto.removableGooseneck) && Objects.equals(this.stepDeck, carrierTruckTypeDto.stepDeck) && Objects.equals(this.stepDeckOrRemGooseneck,
        carrierTruckTypeDto.stepDeckOrRemGooseneck) && Objects.equals(this.stepdeckConestoga, carrierTruckTypeDto.stepdeckConestoga) && Objects.equals(
        this.straightBoxTruck, carrierTruckTypeDto.straightBoxTruck) && Objects.equals(this.stretchTrailer, carrierTruckTypeDto.stretchTrailer)
           && Objects.equals(this.tankerAluminum, carrierTruckTypeDto.tankerAluminum) && Objects.equals(this.tankerIntermodal,
        carrierTruckTypeDto.tankerIntermodal) && Objects.equals(this.tankerSteel, carrierTruckTypeDto.tankerSteel) && Objects.equals(this.truckAndTrailer,
        carrierTruckTypeDto.truckAndTrailer) && Objects.equals(this.van, carrierTruckTypeDto.van) && Objects.equals(this.vanAirRide,
        carrierTruckTypeDto.vanAirRide) && Objects.equals(this.vanBlanketWrap, carrierTruckTypeDto.vanBlanketWrap) && Objects.equals(this.vanConestoga,
        carrierTruckTypeDto.vanConestoga) && Objects.equals(this.vanDouble, carrierTruckTypeDto.vanDouble) && Objects.equals(this.vanHazMat,
        carrierTruckTypeDto.vanHazMat) && Objects.equals(this.vanHotshot, carrierTruckTypeDto.vanHotshot) && Objects.equals(this.vanInsulated,
        carrierTruckTypeDto.vanInsulated) && Objects.equals(this.vanIntermodal, carrierTruckTypeDto.vanIntermodal) && Objects.equals(this.vanLiftGate,
        carrierTruckTypeDto.vanLiftGate) && Objects.equals(this.vanLogistics, carrierTruckTypeDto.vanLogistics) && Objects.equals(this.vanOpenTop,
        carrierTruckTypeDto.vanOpenTop) && Objects.equals(this.vanOrFlatbed, carrierTruckTypeDto.vanOrFlatbed) && Objects.equals(this.vanOrFlatbedwTarps,
        carrierTruckTypeDto.vanOrFlatbedwTarps) && Objects.equals(this.vanOrReefer, carrierTruckTypeDto.vanOrReefer) && Objects.equals(this.vanPalletExchange,
        carrierTruckTypeDto.vanPalletExchange) && Objects.equals(this.vanRollerBed, carrierTruckTypeDto.vanRollerBed) && Objects.equals(this.vanTriple,
        carrierTruckTypeDto.vanTriple) && Objects.equals(this.vanVented, carrierTruckTypeDto.vanVented) && Objects.equals(this.vanWithCurtains,
        carrierTruckTypeDto.vanWithCurtains) && Objects.equals(this.vanWithTeam, carrierTruckTypeDto.vanWithTeam) && Objects.equals(this.oneToTwoCarHauler,
        carrierTruckTypeDto.oneToTwoCarHauler) && Objects.equals(this.threeCarHauler, carrierTruckTypeDto.threeCarHauler) && Objects.equals(
        this.fourToFiveCarHauler, carrierTruckTypeDto.fourToFiveCarHauler) && Objects.equals(this.sixToSevenCarHauler, carrierTruckTypeDto.sixToSevenCarHauler)
           && Objects.equals(this.eightToTenCarHauler, carrierTruckTypeDto.eightToTenCarHauler) && Objects.equals(this.chassis, carrierTruckTypeDto.chassis)
           && Objects.equals(this.pintleHitch, carrierTruckTypeDto.pintleHitch) && Objects.equals(this.vanSprinter, carrierTruckTypeDto.vanSprinter)
           && Objects.equals(this.toter, carrierTruckTypeDto.toter);
  }

  @Override
  public int hashCode() {
    return Objects.hash(autoCarrier, btrain, conestoga, container, containerInsulated, containerRefrigerated, conveyor, doubleDrop, dropDeckLandoll,
        dumpTrailer, flatbed, flatbedAirRide, flatbedConestoga, flatbedDouble, flatbedHazMat, flatbedHotshot, flatbedMaxi, flatbedOrStepDeck,
        flatbedOverdimension, flatbedWithchains, flatbedWithSides, flatbedWithTarps, flatbedWithTeam, flatbedVanReefer, hopperBottom, insulatedVanOrReefer,
        lowboy, lowboyOrRemGooseneck, lowboyOverdimension, movingVan, pneumatic, powerOnly, reefer, reeferAirRide, reeferDouble, reeferHazMat, reeferIntermodal,
        reeferLogistics, reeferOrVentedVan, reeferPalletExchange, reeferWithTeam, removableGooseneck, stepDeck, stepDeckOrRemGooseneck, stepdeckConestoga,
        straightBoxTruck, stretchTrailer, tankerAluminum, tankerIntermodal, tankerSteel, truckAndTrailer, van, vanAirRide, vanBlanketWrap, vanConestoga,
        vanDouble, vanHazMat, vanHotshot, vanInsulated, vanIntermodal, vanLiftGate, vanLogistics, vanOpenTop, vanOrFlatbed, vanOrFlatbedwTarps, vanOrReefer,
        vanPalletExchange, vanRollerBed, vanTriple, vanVented, vanWithCurtains, vanWithTeam, oneToTwoCarHauler, threeCarHauler, fourToFiveCarHauler,
        sixToSevenCarHauler, eightToTenCarHauler, chassis, pintleHitch, vanSprinter, toter);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class CarrierTruckTypeDto {\n");
    sb.append("    autoCarrier: ").append(toIndentedString(autoCarrier)).append("\n");
    sb.append("    btrain: ").append(toIndentedString(btrain)).append("\n");
    sb.append("    conestoga: ").append(toIndentedString(conestoga)).append("\n");
    sb.append("    container: ").append(toIndentedString(container)).append("\n");
    sb.append("    containerInsulated: ").append(toIndentedString(containerInsulated)).append("\n");
    sb.append("    containerRefrigerated: ").append(toIndentedString(containerRefrigerated)).append("\n");
    sb.append("    conveyor: ").append(toIndentedString(conveyor)).append("\n");
    sb.append("    doubleDrop: ").append(toIndentedString(doubleDrop)).append("\n");
    sb.append("    dropDeckLandoll: ").append(toIndentedString(dropDeckLandoll)).append("\n");
    sb.append("    dumpTrailer: ").append(toIndentedString(dumpTrailer)).append("\n");
    sb.append("    flatbed: ").append(toIndentedString(flatbed)).append("\n");
    sb.append("    flatbedAirRide: ").append(toIndentedString(flatbedAirRide)).append("\n");
    sb.append("    flatbedConestoga: ").append(toIndentedString(flatbedConestoga)).append("\n");
    sb.append("    flatbedDouble: ").append(toIndentedString(flatbedDouble)).append("\n");
    sb.append("    flatbedHazMat: ").append(toIndentedString(flatbedHazMat)).append("\n");
    sb.append("    flatbedHotshot: ").append(toIndentedString(flatbedHotshot)).append("\n");
    sb.append("    flatbedMaxi: ").append(toIndentedString(flatbedMaxi)).append("\n");
    sb.append("    flatbedOrStepDeck: ").append(toIndentedString(flatbedOrStepDeck)).append("\n");
    sb.append("    flatbedOverdimension: ").append(toIndentedString(flatbedOverdimension)).append("\n");
    sb.append("    flatbedWithchains: ").append(toIndentedString(flatbedWithchains)).append("\n");
    sb.append("    flatbedWithSides: ").append(toIndentedString(flatbedWithSides)).append("\n");
    sb.append("    flatbedWithTarps: ").append(toIndentedString(flatbedWithTarps)).append("\n");
    sb.append("    flatbedWithTeam: ").append(toIndentedString(flatbedWithTeam)).append("\n");
    sb.append("    flatbedVanReefer: ").append(toIndentedString(flatbedVanReefer)).append("\n");
    sb.append("    hopperBottom: ").append(toIndentedString(hopperBottom)).append("\n");
    sb.append("    insulatedVanOrReefer: ").append(toIndentedString(insulatedVanOrReefer)).append("\n");
    sb.append("    lowboy: ").append(toIndentedString(lowboy)).append("\n");
    sb.append("    lowboyOrRemGooseneck: ").append(toIndentedString(lowboyOrRemGooseneck)).append("\n");
    sb.append("    lowboyOverdimension: ").append(toIndentedString(lowboyOverdimension)).append("\n");
    sb.append("    movingVan: ").append(toIndentedString(movingVan)).append("\n");
    sb.append("    pneumatic: ").append(toIndentedString(pneumatic)).append("\n");
    sb.append("    powerOnly: ").append(toIndentedString(powerOnly)).append("\n");
    sb.append("    reefer: ").append(toIndentedString(reefer)).append("\n");
    sb.append("    reeferAirRide: ").append(toIndentedString(reeferAirRide)).append("\n");
    sb.append("    reeferDouble: ").append(toIndentedString(reeferDouble)).append("\n");
    sb.append("    reeferHazMat: ").append(toIndentedString(reeferHazMat)).append("\n");
    sb.append("    reeferIntermodal: ").append(toIndentedString(reeferIntermodal)).append("\n");
    sb.append("    reeferLogistics: ").append(toIndentedString(reeferLogistics)).append("\n");
    sb.append("    reeferOrVentedVan: ").append(toIndentedString(reeferOrVentedVan)).append("\n");
    sb.append("    reeferPalletExchange: ").append(toIndentedString(reeferPalletExchange)).append("\n");
    sb.append("    reeferWithTeam: ").append(toIndentedString(reeferWithTeam)).append("\n");
    sb.append("    removableGooseneck: ").append(toIndentedString(removableGooseneck)).append("\n");
    sb.append("    stepDeck: ").append(toIndentedString(stepDeck)).append("\n");
    sb.append("    stepDeckOrRemGooseneck: ").append(toIndentedString(stepDeckOrRemGooseneck)).append("\n");
    sb.append("    stepdeckConestoga: ").append(toIndentedString(stepdeckConestoga)).append("\n");
    sb.append("    straightBoxTruck: ").append(toIndentedString(straightBoxTruck)).append("\n");
    sb.append("    stretchTrailer: ").append(toIndentedString(stretchTrailer)).append("\n");
    sb.append("    tankerAluminum: ").append(toIndentedString(tankerAluminum)).append("\n");
    sb.append("    tankerIntermodal: ").append(toIndentedString(tankerIntermodal)).append("\n");
    sb.append("    tankerSteel: ").append(toIndentedString(tankerSteel)).append("\n");
    sb.append("    truckAndTrailer: ").append(toIndentedString(truckAndTrailer)).append("\n");
    sb.append("    van: ").append(toIndentedString(van)).append("\n");
    sb.append("    vanAirRide: ").append(toIndentedString(vanAirRide)).append("\n");
    sb.append("    vanBlanketWrap: ").append(toIndentedString(vanBlanketWrap)).append("\n");
    sb.append("    vanConestoga: ").append(toIndentedString(vanConestoga)).append("\n");
    sb.append("    vanDouble: ").append(toIndentedString(vanDouble)).append("\n");
    sb.append("    vanHazMat: ").append(toIndentedString(vanHazMat)).append("\n");
    sb.append("    vanHotshot: ").append(toIndentedString(vanHotshot)).append("\n");
    sb.append("    vanInsulated: ").append(toIndentedString(vanInsulated)).append("\n");
    sb.append("    vanIntermodal: ").append(toIndentedString(vanIntermodal)).append("\n");
    sb.append("    vanLiftGate: ").append(toIndentedString(vanLiftGate)).append("\n");
    sb.append("    vanLogistics: ").append(toIndentedString(vanLogistics)).append("\n");
    sb.append("    vanOpenTop: ").append(toIndentedString(vanOpenTop)).append("\n");
    sb.append("    vanOrFlatbed: ").append(toIndentedString(vanOrFlatbed)).append("\n");
    sb.append("    vanOrFlatbedwTarps: ").append(toIndentedString(vanOrFlatbedwTarps)).append("\n");
    sb.append("    vanOrReefer: ").append(toIndentedString(vanOrReefer)).append("\n");
    sb.append("    vanPalletExchange: ").append(toIndentedString(vanPalletExchange)).append("\n");
    sb.append("    vanRollerBed: ").append(toIndentedString(vanRollerBed)).append("\n");
    sb.append("    vanTriple: ").append(toIndentedString(vanTriple)).append("\n");
    sb.append("    vanVented: ").append(toIndentedString(vanVented)).append("\n");
    sb.append("    vanWithCurtains: ").append(toIndentedString(vanWithCurtains)).append("\n");
    sb.append("    vanWithTeam: ").append(toIndentedString(vanWithTeam)).append("\n");
    sb.append("    oneToTwoCarHauler: ").append(toIndentedString(oneToTwoCarHauler)).append("\n");
    sb.append("    threeCarHauler: ").append(toIndentedString(threeCarHauler)).append("\n");
    sb.append("    fourToFiveCarHauler: ").append(toIndentedString(fourToFiveCarHauler)).append("\n");
    sb.append("    sixToSevenCarHauler: ").append(toIndentedString(sixToSevenCarHauler)).append("\n");
    sb.append("    eightToTenCarHauler: ").append(toIndentedString(eightToTenCarHauler)).append("\n");
    sb.append("    chassis: ").append(toIndentedString(chassis)).append("\n");
    sb.append("    pintleHitch: ").append(toIndentedString(pintleHitch)).append("\n");
    sb.append("    vanSprinter: ").append(toIndentedString(vanSprinter)).append("\n");
    sb.append("    toter: ").append(toIndentedString(toter)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

