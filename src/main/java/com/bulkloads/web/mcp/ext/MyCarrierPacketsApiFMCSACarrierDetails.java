package com.bulkloads.web.mcp.ext;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * MyCarrierPacketsApiFMCSACarrierDetails
 */
@JsonPropertyOrder({MyCarrierPacketsApiFMCSACarrierDetails.JSON_PROPERTY_DOCKET_NUMBER, MyCarrierPacketsApiFMCSACarrierDetails.JSON_PROPERTY_DOT_NUMBER,
    MyCarrierPacketsApiFMCSACarrierDetails.JSON_PROPERTY_CARRIER_TYPE, MyCarrierPacketsApiFMCSACarrierDetails.JSON_PROPERTY_IS_MONITORED,
    MyCarrierPacketsApiFMCSACarrierDetails.JSON_PROPERTY_IS_BLOCKED, MyCarrierPacketsApiFMCSACarrierDetails.JSO<PERSON>_PROPERTY_IDENTITY,
    MyCarrierPacketsApiFMCSACarrierDetails.JSON_PROPERTY_AUTHORITY, MyCarrierPacketsApiFMCSACarrierDetails.JSON_PROPERTY_FM_C_S_A_INSURANCE,
    MyCarrierPacketsApiFMCSACarrierDetails.JSON_PROPERTY_CERT_DATA, MyCarrierPacketsApiFMCSACarrierDetails.JSON_PROPERTY_SAFETY,
    MyCarrierPacketsApiFMCSACarrierDetails.JSON_PROPERTY_INSPECTION, MyCarrierPacketsApiFMCSACarrierDetails.JSON_PROPERTY_CRASH,
    MyCarrierPacketsApiFMCSACarrierDetails.JSON_PROPERTY_REVIEW, MyCarrierPacketsApiFMCSACarrierDetails.JSON_PROPERTY_OPERATION,
    MyCarrierPacketsApiFMCSACarrierDetails.JSON_PROPERTY_CARGO, MyCarrierPacketsApiFMCSACarrierDetails.JSON_PROPERTY_DRIVERS,
    MyCarrierPacketsApiFMCSACarrierDetails.JSON_PROPERTY_EQUIPMENT, MyCarrierPacketsApiFMCSACarrierDetails.JSON_PROPERTY_OTHER,
    MyCarrierPacketsApiFMCSACarrierDetails.JSON_PROPERTY_RISK_ASSESSMENT, MyCarrierPacketsApiFMCSACarrierDetails.JSON_PROPERTY_RISK_ASSESSMENT_DETAILS,
    MyCarrierPacketsApiFMCSACarrierDetails.JSON_PROPERTY_CARRIER_RATINGS, MyCarrierPacketsApiFMCSACarrierDetails.JSON_PROPERTY_LATEST_INVITATION})
@JsonTypeName("MyCarrierPacketsApi.FMCSA.CarrierDetails")

public class MyCarrierPacketsApiFMCSACarrierDetails {

  public static final String JSON_PROPERTY_DOCKET_NUMBER = "docketNumber";
  public static final String JSON_PROPERTY_DOT_NUMBER = "dotNumber";
  public static final String JSON_PROPERTY_CARRIER_TYPE = "carrierType";
  public static final String JSON_PROPERTY_IS_MONITORED = "isMonitored";
  public static final String JSON_PROPERTY_IS_BLOCKED = "isBlocked";
  public static final String JSON_PROPERTY_IDENTITY = "Identity";
  public static final String JSON_PROPERTY_AUTHORITY = "Authority";
  public static final String JSON_PROPERTY_FM_C_S_A_INSURANCE = "FMCSAInsurance";
  public static final String JSON_PROPERTY_CERT_DATA = "CertData";
  public static final String JSON_PROPERTY_SAFETY = "Safety";
  public static final String JSON_PROPERTY_INSPECTION = "Inspection";
  public static final String JSON_PROPERTY_CRASH = "Crash";
  public static final String JSON_PROPERTY_REVIEW = "Review";
  public static final String JSON_PROPERTY_OPERATION = "Operation";
  public static final String JSON_PROPERTY_CARGO = "Cargo";
  public static final String JSON_PROPERTY_DRIVERS = "Drivers";
  public static final String JSON_PROPERTY_EQUIPMENT = "Equipment";
  public static final String JSON_PROPERTY_OTHER = "Other";
  public static final String JSON_PROPERTY_RISK_ASSESSMENT = "RiskAssessment";
  public static final String JSON_PROPERTY_RISK_ASSESSMENT_DETAILS = "RiskAssessmentDetails";
  public static final String JSON_PROPERTY_CARRIER_RATINGS = "CarrierRatings";
  public static final String JSON_PROPERTY_LATEST_INVITATION = "LatestInvitation";
  private String docketNumber;
  private MyCarrierPacketsApiFMCSADotNumber dotNumber;
  private String carrierType;
  private Boolean isMonitored;
  private Boolean isBlocked;
  private MyCarrierPacketsApiFMCSAIdentity identity;
  private MyCarrierPacketsApiFMCSAAuthority authority;
  private MyCarrierPacketsApiFMCSAFMCSAInsurance fmCSAInsurance;
  private MyCarrierPacketsApiFMCSACertData certData;
  private MyCarrierPacketsApiFMCSASafety safety;
  private MyCarrierPacketsApiFMCSAInspection inspection;
  private MyCarrierPacketsApiFMCSACrash crash;
  private MyCarrierPacketsApiFMCSAReview review;
  private MyCarrierPacketsApiFMCSAOperation operation;
  private MyCarrierPacketsApiFMCSACargo cargo;
  private MyCarrierPacketsApiFMCSADrivers drivers;
  private MyCarrierPacketsApiFMCSAEquipment equipment;
  private MyCarrierPacketsApiFMCSAOther other;
  private MyCarrierPacketsApiFMCSARiskAssessment riskAssessment;
  private MyCarrierPacketsApiFMCSARiskAssessmentDetails riskAssessmentDetails;
  private MyCarrierPacketsApiFMCSACarrierRatings carrierRatings;
  private MyCarrierPacketsApiFMCSALatestInvitation latestInvitation;

  public MyCarrierPacketsApiFMCSACarrierDetails() {
  }

  public MyCarrierPacketsApiFMCSACarrierDetails docketNumber(String docketNumber) {

    this.docketNumber = docketNumber;
    return this;
  }

  /**
   * Get docketNumber
   *
   * @return docketNumber
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_DOCKET_NUMBER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getDocketNumber() {
    return docketNumber;
  }


  @JsonProperty(JSON_PROPERTY_DOCKET_NUMBER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setDocketNumber(String docketNumber) {
    this.docketNumber = docketNumber;
  }


  public MyCarrierPacketsApiFMCSACarrierDetails dotNumber(MyCarrierPacketsApiFMCSADotNumber dotNumber) {

    this.dotNumber = dotNumber;
    return this;
  }

  /**
   * Get dotNumber
   *
   * @return dotNumber
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_DOT_NUMBER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public MyCarrierPacketsApiFMCSADotNumber getDotNumber() {
    return dotNumber;
  }


  @JsonProperty(JSON_PROPERTY_DOT_NUMBER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setDotNumber(MyCarrierPacketsApiFMCSADotNumber dotNumber) {
    this.dotNumber = dotNumber;
  }


  public MyCarrierPacketsApiFMCSACarrierDetails carrierType(String carrierType) {

    this.carrierType = carrierType;
    return this;
  }

  /**
   * Get carrierType
   *
   * @return carrierType
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CARRIER_TYPE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getCarrierType() {
    return carrierType;
  }


  @JsonProperty(JSON_PROPERTY_CARRIER_TYPE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCarrierType(String carrierType) {
    this.carrierType = carrierType;
  }


  public MyCarrierPacketsApiFMCSACarrierDetails isMonitored(Boolean isMonitored) {

    this.isMonitored = isMonitored;
    return this;
  }

  /**
   * Get isMonitored
   *
   * @return isMonitored
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_IS_MONITORED)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getIsMonitored() {
    return isMonitored;
  }


  @JsonProperty(JSON_PROPERTY_IS_MONITORED)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setIsMonitored(Boolean isMonitored) {
    this.isMonitored = isMonitored;
  }


  public MyCarrierPacketsApiFMCSACarrierDetails isBlocked(Boolean isBlocked) {

    this.isBlocked = isBlocked;
    return this;
  }

  /**
   * Get isBlocked
   *
   * @return isBlocked
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_IS_BLOCKED)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getIsBlocked() {
    return isBlocked;
  }


  @JsonProperty(JSON_PROPERTY_IS_BLOCKED)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setIsBlocked(Boolean isBlocked) {
    this.isBlocked = isBlocked;
  }


  public MyCarrierPacketsApiFMCSACarrierDetails identity(MyCarrierPacketsApiFMCSAIdentity identity) {

    this.identity = identity;
    return this;
  }

  /**
   * Get identity
   *
   * @return identity
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_IDENTITY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public MyCarrierPacketsApiFMCSAIdentity getIdentity() {
    return identity;
  }


  @JsonProperty(JSON_PROPERTY_IDENTITY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setIdentity(MyCarrierPacketsApiFMCSAIdentity identity) {
    this.identity = identity;
  }


  public MyCarrierPacketsApiFMCSACarrierDetails authority(MyCarrierPacketsApiFMCSAAuthority authority) {

    this.authority = authority;
    return this;
  }

  /**
   * Get authority
   *
   * @return authority
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_AUTHORITY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public MyCarrierPacketsApiFMCSAAuthority getAuthority() {
    return authority;
  }


  @JsonProperty(JSON_PROPERTY_AUTHORITY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setAuthority(MyCarrierPacketsApiFMCSAAuthority authority) {
    this.authority = authority;
  }


  public MyCarrierPacketsApiFMCSACarrierDetails fmCSAInsurance(MyCarrierPacketsApiFMCSAFMCSAInsurance fmCSAInsurance) {

    this.fmCSAInsurance = fmCSAInsurance;
    return this;
  }

  /**
   * Get fmCSAInsurance
   *
   * @return fmCSAInsurance
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_FM_C_S_A_INSURANCE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public MyCarrierPacketsApiFMCSAFMCSAInsurance getFmCSAInsurance() {
    return fmCSAInsurance;
  }


  @JsonProperty(JSON_PROPERTY_FM_C_S_A_INSURANCE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setFmCSAInsurance(MyCarrierPacketsApiFMCSAFMCSAInsurance fmCSAInsurance) {
    this.fmCSAInsurance = fmCSAInsurance;
  }


  public MyCarrierPacketsApiFMCSACarrierDetails certData(MyCarrierPacketsApiFMCSACertData certData) {

    this.certData = certData;
    return this;
  }

  /**
   * Get certData
   *
   * @return certData
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CERT_DATA)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public MyCarrierPacketsApiFMCSACertData getCertData() {
    return certData;
  }


  @JsonProperty(JSON_PROPERTY_CERT_DATA)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCertData(MyCarrierPacketsApiFMCSACertData certData) {
    this.certData = certData;
  }


  public MyCarrierPacketsApiFMCSACarrierDetails safety(MyCarrierPacketsApiFMCSASafety safety) {

    this.safety = safety;
    return this;
  }

  /**
   * Get safety
   *
   * @return safety
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_SAFETY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public MyCarrierPacketsApiFMCSASafety getSafety() {
    return safety;
  }


  @JsonProperty(JSON_PROPERTY_SAFETY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setSafety(MyCarrierPacketsApiFMCSASafety safety) {
    this.safety = safety;
  }


  public MyCarrierPacketsApiFMCSACarrierDetails inspection(MyCarrierPacketsApiFMCSAInspection inspection) {

    this.inspection = inspection;
    return this;
  }

  /**
   * Get inspection
   *
   * @return inspection
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_INSPECTION)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public MyCarrierPacketsApiFMCSAInspection getInspection() {
    return inspection;
  }


  @JsonProperty(JSON_PROPERTY_INSPECTION)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setInspection(MyCarrierPacketsApiFMCSAInspection inspection) {
    this.inspection = inspection;
  }


  public MyCarrierPacketsApiFMCSACarrierDetails crash(MyCarrierPacketsApiFMCSACrash crash) {

    this.crash = crash;
    return this;
  }

  /**
   * Get crash
   *
   * @return crash
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CRASH)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public MyCarrierPacketsApiFMCSACrash getCrash() {
    return crash;
  }


  @JsonProperty(JSON_PROPERTY_CRASH)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCrash(MyCarrierPacketsApiFMCSACrash crash) {
    this.crash = crash;
  }


  public MyCarrierPacketsApiFMCSACarrierDetails review(MyCarrierPacketsApiFMCSAReview review) {

    this.review = review;
    return this;
  }

  /**
   * Get review
   *
   * @return review
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_REVIEW)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public MyCarrierPacketsApiFMCSAReview getReview() {
    return review;
  }


  @JsonProperty(JSON_PROPERTY_REVIEW)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setReview(MyCarrierPacketsApiFMCSAReview review) {
    this.review = review;
  }


  public MyCarrierPacketsApiFMCSACarrierDetails operation(MyCarrierPacketsApiFMCSAOperation operation) {

    this.operation = operation;
    return this;
  }

  /**
   * Get operation
   *
   * @return operation
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_OPERATION)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public MyCarrierPacketsApiFMCSAOperation getOperation() {
    return operation;
  }


  @JsonProperty(JSON_PROPERTY_OPERATION)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setOperation(MyCarrierPacketsApiFMCSAOperation operation) {
    this.operation = operation;
  }


  public MyCarrierPacketsApiFMCSACarrierDetails cargo(MyCarrierPacketsApiFMCSACargo cargo) {

    this.cargo = cargo;
    return this;
  }

  /**
   * Get cargo
   *
   * @return cargo
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CARGO)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public MyCarrierPacketsApiFMCSACargo getCargo() {
    return cargo;
  }


  @JsonProperty(JSON_PROPERTY_CARGO)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCargo(MyCarrierPacketsApiFMCSACargo cargo) {
    this.cargo = cargo;
  }


  public MyCarrierPacketsApiFMCSACarrierDetails drivers(MyCarrierPacketsApiFMCSADrivers drivers) {

    this.drivers = drivers;
    return this;
  }

  /**
   * Get drivers
   *
   * @return drivers
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_DRIVERS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public MyCarrierPacketsApiFMCSADrivers getDrivers() {
    return drivers;
  }


  @JsonProperty(JSON_PROPERTY_DRIVERS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setDrivers(MyCarrierPacketsApiFMCSADrivers drivers) {
    this.drivers = drivers;
  }


  public MyCarrierPacketsApiFMCSACarrierDetails equipment(MyCarrierPacketsApiFMCSAEquipment equipment) {

    this.equipment = equipment;
    return this;
  }

  /**
   * Get equipment
   *
   * @return equipment
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_EQUIPMENT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public MyCarrierPacketsApiFMCSAEquipment getEquipment() {
    return equipment;
  }


  @JsonProperty(JSON_PROPERTY_EQUIPMENT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setEquipment(MyCarrierPacketsApiFMCSAEquipment equipment) {
    this.equipment = equipment;
  }


  public MyCarrierPacketsApiFMCSACarrierDetails other(MyCarrierPacketsApiFMCSAOther other) {

    this.other = other;
    return this;
  }

  /**
   * Get other
   *
   * @return other
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_OTHER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public MyCarrierPacketsApiFMCSAOther getOther() {
    return other;
  }


  @JsonProperty(JSON_PROPERTY_OTHER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setOther(MyCarrierPacketsApiFMCSAOther other) {
    this.other = other;
  }


  public MyCarrierPacketsApiFMCSACarrierDetails riskAssessment(MyCarrierPacketsApiFMCSARiskAssessment riskAssessment) {

    this.riskAssessment = riskAssessment;
    return this;
  }

  /**
   * Get riskAssessment
   *
   * @return riskAssessment
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_RISK_ASSESSMENT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public MyCarrierPacketsApiFMCSARiskAssessment getRiskAssessment() {
    return riskAssessment;
  }


  @JsonProperty(JSON_PROPERTY_RISK_ASSESSMENT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setRiskAssessment(MyCarrierPacketsApiFMCSARiskAssessment riskAssessment) {
    this.riskAssessment = riskAssessment;
  }


  public MyCarrierPacketsApiFMCSACarrierDetails riskAssessmentDetails(MyCarrierPacketsApiFMCSARiskAssessmentDetails riskAssessmentDetails) {

    this.riskAssessmentDetails = riskAssessmentDetails;
    return this;
  }

  /**
   * Get riskAssessmentDetails
   *
   * @return riskAssessmentDetails
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_RISK_ASSESSMENT_DETAILS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public MyCarrierPacketsApiFMCSARiskAssessmentDetails getRiskAssessmentDetails() {
    return riskAssessmentDetails;
  }


  @JsonProperty(JSON_PROPERTY_RISK_ASSESSMENT_DETAILS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setRiskAssessmentDetails(MyCarrierPacketsApiFMCSARiskAssessmentDetails riskAssessmentDetails) {
    this.riskAssessmentDetails = riskAssessmentDetails;
  }


  public MyCarrierPacketsApiFMCSACarrierDetails carrierRatings(MyCarrierPacketsApiFMCSACarrierRatings carrierRatings) {

    this.carrierRatings = carrierRatings;
    return this;
  }

  /**
   * Get carrierRatings
   *
   * @return carrierRatings
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CARRIER_RATINGS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public MyCarrierPacketsApiFMCSACarrierRatings getCarrierRatings() {
    return carrierRatings;
  }


  @JsonProperty(JSON_PROPERTY_CARRIER_RATINGS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCarrierRatings(MyCarrierPacketsApiFMCSACarrierRatings carrierRatings) {
    this.carrierRatings = carrierRatings;
  }


  public MyCarrierPacketsApiFMCSACarrierDetails latestInvitation(MyCarrierPacketsApiFMCSALatestInvitation latestInvitation) {

    this.latestInvitation = latestInvitation;
    return this;
  }

  /**
   * Get latestInvitation
   *
   * @return latestInvitation
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_LATEST_INVITATION)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public MyCarrierPacketsApiFMCSALatestInvitation getLatestInvitation() {
    return latestInvitation;
  }


  @JsonProperty(JSON_PROPERTY_LATEST_INVITATION)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setLatestInvitation(MyCarrierPacketsApiFMCSALatestInvitation latestInvitation) {
    this.latestInvitation = latestInvitation;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    MyCarrierPacketsApiFMCSACarrierDetails myCarrierPacketsApiFMCSACarrierDetails = (MyCarrierPacketsApiFMCSACarrierDetails) o;
    return Objects.equals(this.docketNumber, myCarrierPacketsApiFMCSACarrierDetails.docketNumber) && Objects.equals(this.dotNumber,
        myCarrierPacketsApiFMCSACarrierDetails.dotNumber) && Objects.equals(this.carrierType, myCarrierPacketsApiFMCSACarrierDetails.carrierType)
           && Objects.equals(this.isMonitored, myCarrierPacketsApiFMCSACarrierDetails.isMonitored) && Objects.equals(this.isBlocked,
        myCarrierPacketsApiFMCSACarrierDetails.isBlocked) && Objects.equals(this.identity, myCarrierPacketsApiFMCSACarrierDetails.identity) && Objects.equals(
        this.authority, myCarrierPacketsApiFMCSACarrierDetails.authority) && Objects.equals(this.fmCSAInsurance,
        myCarrierPacketsApiFMCSACarrierDetails.fmCSAInsurance) && Objects.equals(this.certData, myCarrierPacketsApiFMCSACarrierDetails.certData)
           && Objects.equals(this.safety, myCarrierPacketsApiFMCSACarrierDetails.safety) && Objects.equals(this.inspection,
        myCarrierPacketsApiFMCSACarrierDetails.inspection) && Objects.equals(this.crash, myCarrierPacketsApiFMCSACarrierDetails.crash) && Objects.equals(
        this.review, myCarrierPacketsApiFMCSACarrierDetails.review) && Objects.equals(this.operation, myCarrierPacketsApiFMCSACarrierDetails.operation)
           && Objects.equals(this.cargo, myCarrierPacketsApiFMCSACarrierDetails.cargo) && Objects.equals(this.drivers,
        myCarrierPacketsApiFMCSACarrierDetails.drivers) && Objects.equals(this.equipment, myCarrierPacketsApiFMCSACarrierDetails.equipment) && Objects.equals(
        this.other, myCarrierPacketsApiFMCSACarrierDetails.other) && Objects.equals(this.riskAssessment, myCarrierPacketsApiFMCSACarrierDetails.riskAssessment)
           && Objects.equals(this.riskAssessmentDetails, myCarrierPacketsApiFMCSACarrierDetails.riskAssessmentDetails) && Objects.equals(this.carrierRatings,
        myCarrierPacketsApiFMCSACarrierDetails.carrierRatings) && Objects.equals(this.latestInvitation,
        myCarrierPacketsApiFMCSACarrierDetails.latestInvitation);
  }

  @Override
  public int hashCode() {
    return Objects.hash(docketNumber, dotNumber, carrierType, isMonitored, isBlocked, identity, authority, fmCSAInsurance, certData, safety, inspection, crash,
        review, operation, cargo, drivers, equipment, other, riskAssessment, riskAssessmentDetails, carrierRatings, latestInvitation);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class MyCarrierPacketsApiFMCSACarrierDetails {\n");
    sb.append("    docketNumber: ").append(toIndentedString(docketNumber)).append("\n");
    sb.append("    dotNumber: ").append(toIndentedString(dotNumber)).append("\n");
    sb.append("    carrierType: ").append(toIndentedString(carrierType)).append("\n");
    sb.append("    isMonitored: ").append(toIndentedString(isMonitored)).append("\n");
    sb.append("    isBlocked: ").append(toIndentedString(isBlocked)).append("\n");
    sb.append("    identity: ").append(toIndentedString(identity)).append("\n");
    sb.append("    authority: ").append(toIndentedString(authority)).append("\n");
    sb.append("    fmCSAInsurance: ").append(toIndentedString(fmCSAInsurance)).append("\n");
    sb.append("    certData: ").append(toIndentedString(certData)).append("\n");
    sb.append("    safety: ").append(toIndentedString(safety)).append("\n");
    sb.append("    inspection: ").append(toIndentedString(inspection)).append("\n");
    sb.append("    crash: ").append(toIndentedString(crash)).append("\n");
    sb.append("    review: ").append(toIndentedString(review)).append("\n");
    sb.append("    operation: ").append(toIndentedString(operation)).append("\n");
    sb.append("    cargo: ").append(toIndentedString(cargo)).append("\n");
    sb.append("    drivers: ").append(toIndentedString(drivers)).append("\n");
    sb.append("    equipment: ").append(toIndentedString(equipment)).append("\n");
    sb.append("    other: ").append(toIndentedString(other)).append("\n");
    sb.append("    riskAssessment: ").append(toIndentedString(riskAssessment)).append("\n");
    sb.append("    riskAssessmentDetails: ").append(toIndentedString(riskAssessmentDetails)).append("\n");
    sb.append("    carrierRatings: ").append(toIndentedString(carrierRatings)).append("\n");
    sb.append("    latestInvitation: ").append(toIndentedString(latestInvitation)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

