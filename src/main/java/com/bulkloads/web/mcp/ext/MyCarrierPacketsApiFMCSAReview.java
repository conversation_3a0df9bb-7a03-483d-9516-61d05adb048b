package com.bulkloads.web.mcp.ext;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * MyCarrierPacketsApiFMCSAReview
 */
@JsonPropertyOrder({MyCarrierPacketsApiFMCSAReview.JSON_PROPERTY_REVIEW_TYPE, MyCarrierPacketsApiFMCSAReview.JSON_PROPERTY_REVIEW_DATE,
    MyCarrierPacketsApiFMCSAReview.JSON_PROPERTY_REVIEW_DOC_NUM, MyCarrierPacketsApiFMCSAReview.JSON_PROPERTY_REVIEW_MILES,
    MyCarrierPacketsApiFMCSAReview.JSON_PROPERTY_MCS150_DATE, MyCarrierPacketsApiFMCSAReview.JSON_PROPERTY_MCS150_MILE_YEAR,
    MyCarrierPacketsApiFMCSAReview.JSON_PROPERTY_MCS150_MILES, MyCarrierPacketsApiFMCSAReview.JSON_PROPERTY_ACCIDENT_RATE,
    MyCarrierPacketsApiFMCSAReview.JSON_PROPERTY_ACCIDENT_RATE_PREVENT})
@JsonTypeName("MyCarrierPacketsApi.FMCSA.Review")

public class MyCarrierPacketsApiFMCSAReview {

  public static final String JSON_PROPERTY_REVIEW_TYPE = "reviewType";
  public static final String JSON_PROPERTY_REVIEW_DATE = "reviewDate";
  public static final String JSON_PROPERTY_REVIEW_DOC_NUM = "reviewDocNum";
  public static final String JSON_PROPERTY_REVIEW_MILES = "reviewMiles";
  public static final String JSON_PROPERTY_MCS150_DATE = "mcs150Date";
  public static final String JSON_PROPERTY_MCS150_MILE_YEAR = "mcs150MileYear";
  public static final String JSON_PROPERTY_MCS150_MILES = "mcs150Miles";
  public static final String JSON_PROPERTY_ACCIDENT_RATE = "accidentRate";
  public static final String JSON_PROPERTY_ACCIDENT_RATE_PREVENT = "accidentRatePrevent";
  private String reviewType;
  private String reviewDate;
  private String reviewDocNum;
  private String reviewMiles;
  private String mcs150Date;
  private String mcs150MileYear;
  private String mcs150Miles;
  private String accidentRate;
  private String accidentRatePrevent;

  public MyCarrierPacketsApiFMCSAReview() {
  }

  public MyCarrierPacketsApiFMCSAReview reviewType(String reviewType) {

    this.reviewType = reviewType;
    return this;
  }

  /**
   * Get reviewType
   *
   * @return reviewType
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_REVIEW_TYPE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getReviewType() {
    return reviewType;
  }


  @JsonProperty(JSON_PROPERTY_REVIEW_TYPE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setReviewType(String reviewType) {
    this.reviewType = reviewType;
  }


  public MyCarrierPacketsApiFMCSAReview reviewDate(String reviewDate) {

    this.reviewDate = reviewDate;
    return this;
  }

  /**
   * Get reviewDate
   *
   * @return reviewDate
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_REVIEW_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getReviewDate() {
    return reviewDate;
  }


  @JsonProperty(JSON_PROPERTY_REVIEW_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setReviewDate(String reviewDate) {
    this.reviewDate = reviewDate;
  }


  public MyCarrierPacketsApiFMCSAReview reviewDocNum(String reviewDocNum) {

    this.reviewDocNum = reviewDocNum;
    return this;
  }

  /**
   * Get reviewDocNum
   *
   * @return reviewDocNum
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_REVIEW_DOC_NUM)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getReviewDocNum() {
    return reviewDocNum;
  }


  @JsonProperty(JSON_PROPERTY_REVIEW_DOC_NUM)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setReviewDocNum(String reviewDocNum) {
    this.reviewDocNum = reviewDocNum;
  }


  public MyCarrierPacketsApiFMCSAReview reviewMiles(String reviewMiles) {

    this.reviewMiles = reviewMiles;
    return this;
  }

  /**
   * Get reviewMiles
   *
   * @return reviewMiles
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_REVIEW_MILES)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getReviewMiles() {
    return reviewMiles;
  }


  @JsonProperty(JSON_PROPERTY_REVIEW_MILES)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setReviewMiles(String reviewMiles) {
    this.reviewMiles = reviewMiles;
  }


  public MyCarrierPacketsApiFMCSAReview mcs150Date(String mcs150Date) {

    this.mcs150Date = mcs150Date;
    return this;
  }

  /**
   * Get mcs150Date
   *
   * @return mcs150Date
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_MCS150_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getMcs150Date() {
    return mcs150Date;
  }


  @JsonProperty(JSON_PROPERTY_MCS150_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setMcs150Date(String mcs150Date) {
    this.mcs150Date = mcs150Date;
  }


  public MyCarrierPacketsApiFMCSAReview mcs150MileYear(String mcs150MileYear) {

    this.mcs150MileYear = mcs150MileYear;
    return this;
  }

  /**
   * Get mcs150MileYear
   *
   * @return mcs150MileYear
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_MCS150_MILE_YEAR)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getMcs150MileYear() {
    return mcs150MileYear;
  }


  @JsonProperty(JSON_PROPERTY_MCS150_MILE_YEAR)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setMcs150MileYear(String mcs150MileYear) {
    this.mcs150MileYear = mcs150MileYear;
  }


  public MyCarrierPacketsApiFMCSAReview mcs150Miles(String mcs150Miles) {

    this.mcs150Miles = mcs150Miles;
    return this;
  }

  /**
   * Get mcs150Miles
   *
   * @return mcs150Miles
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_MCS150_MILES)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getMcs150Miles() {
    return mcs150Miles;
  }


  @JsonProperty(JSON_PROPERTY_MCS150_MILES)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setMcs150Miles(String mcs150Miles) {
    this.mcs150Miles = mcs150Miles;
  }


  public MyCarrierPacketsApiFMCSAReview accidentRate(String accidentRate) {

    this.accidentRate = accidentRate;
    return this;
  }

  /**
   * Get accidentRate
   *
   * @return accidentRate
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_ACCIDENT_RATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getAccidentRate() {
    return accidentRate;
  }


  @JsonProperty(JSON_PROPERTY_ACCIDENT_RATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setAccidentRate(String accidentRate) {
    this.accidentRate = accidentRate;
  }


  public MyCarrierPacketsApiFMCSAReview accidentRatePrevent(String accidentRatePrevent) {

    this.accidentRatePrevent = accidentRatePrevent;
    return this;
  }

  /**
   * Get accidentRatePrevent
   *
   * @return accidentRatePrevent
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_ACCIDENT_RATE_PREVENT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getAccidentRatePrevent() {
    return accidentRatePrevent;
  }


  @JsonProperty(JSON_PROPERTY_ACCIDENT_RATE_PREVENT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setAccidentRatePrevent(String accidentRatePrevent) {
    this.accidentRatePrevent = accidentRatePrevent;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    MyCarrierPacketsApiFMCSAReview myCarrierPacketsApiFMCSAReview = (MyCarrierPacketsApiFMCSAReview) o;
    return Objects.equals(this.reviewType, myCarrierPacketsApiFMCSAReview.reviewType) && Objects.equals(this.reviewDate,
        myCarrierPacketsApiFMCSAReview.reviewDate) && Objects.equals(this.reviewDocNum, myCarrierPacketsApiFMCSAReview.reviewDocNum) && Objects.equals(
        this.reviewMiles, myCarrierPacketsApiFMCSAReview.reviewMiles) && Objects.equals(this.mcs150Date, myCarrierPacketsApiFMCSAReview.mcs150Date)
           && Objects.equals(this.mcs150MileYear, myCarrierPacketsApiFMCSAReview.mcs150MileYear) && Objects.equals(this.mcs150Miles,
        myCarrierPacketsApiFMCSAReview.mcs150Miles) && Objects.equals(this.accidentRate, myCarrierPacketsApiFMCSAReview.accidentRate) && Objects.equals(
        this.accidentRatePrevent, myCarrierPacketsApiFMCSAReview.accidentRatePrevent);
  }

  @Override
  public int hashCode() {
    return Objects.hash(reviewType, reviewDate, reviewDocNum, reviewMiles, mcs150Date, mcs150MileYear, mcs150Miles, accidentRate, accidentRatePrevent);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class MyCarrierPacketsApiFMCSAReview {\n");
    sb.append("    reviewType: ").append(toIndentedString(reviewType)).append("\n");
    sb.append("    reviewDate: ").append(toIndentedString(reviewDate)).append("\n");
    sb.append("    reviewDocNum: ").append(toIndentedString(reviewDocNum)).append("\n");
    sb.append("    reviewMiles: ").append(toIndentedString(reviewMiles)).append("\n");
    sb.append("    mcs150Date: ").append(toIndentedString(mcs150Date)).append("\n");
    sb.append("    mcs150MileYear: ").append(toIndentedString(mcs150MileYear)).append("\n");
    sb.append("    mcs150Miles: ").append(toIndentedString(mcs150Miles)).append("\n");
    sb.append("    accidentRate: ").append(toIndentedString(accidentRate)).append("\n");
    sb.append("    accidentRatePrevent: ").append(toIndentedString(accidentRatePrevent)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

