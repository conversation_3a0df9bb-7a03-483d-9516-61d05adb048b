package com.bulkloads.web.mcp.mapper;

import static com.bulkloads.common.StringUtil.parseFullName;
import static com.bulkloads.common.StringUtil.parseInteger;
import static com.bulkloads.common.StringUtil.properCase;
import static com.bulkloads.common.validation.ValidationUtils.isEmpty;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import com.bulkloads.web.addressbook.abcompany.domain.data.AbCompanyData;
import com.bulkloads.web.addressbook.abcompany.domain.entity.AbCompany;
import com.bulkloads.web.addressbook.abuser.domain.data.AbUserData;
import com.bulkloads.web.mcp.ext.CarrierCustomerAgreementDto;
import com.bulkloads.web.mcp.ext.CarrierDispatcherDto;
import com.bulkloads.web.mcp.ext.CarrierDriverDto;
import com.bulkloads.web.mcp.ext.CarrierDto;
import com.bulkloads.web.mcp.ext.CertificateDto;
import com.bulkloads.web.mcp.ext.CoverageDto;
import com.bulkloads.web.mcp.ext.MyCarrierPacketsApiFMCSACarrierDetails;
import com.bulkloads.web.mcp.ext.MyCarrierPacketsApiFMCSAFMCSACarrier;
import com.bulkloads.web.mcp.ext.MyCarrierPacketsApiFMCSAFMCSAInsurance;
import com.bulkloads.web.mcp.ext.MyCarrierPacketsApiFMCSAIdentity;
import com.bulkloads.web.mcp.ext.PolicyOutput;
import com.bulkloads.web.mcp.service.dto.McpAbCompanyResponse;
import com.bulkloads.web.mcp.service.dto.McpAbUserResponse;
import org.apache.commons.validator.routines.EmailValidator;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class McpMapper {

  public McpAbCompanyResponse carrierDtoToMcpLookupResponse(final CarrierDto carrier) {

    McpAbCompanyResponse result = new McpAbCompanyResponse();

    setBasicCarrierInfo(result, carrier);
    setAssureAdvantageInfo(result, carrier);
    setAbUsers(result, carrier);

    result.setUserTypeIds("20");

    result.setMcpDetailsUrl(buildMcpDetailsUrl(
        String.valueOf(result.getCensusNum()),
        result.getMcNum()));

    result.setMcpMonitored(true);
    return result;
  }

  public void mcpAbCompanyResponseToAbCompanyData(McpAbCompanyResponse response, AbCompanyData data) {
    data.setCompanyName(Optional.of(response.getCompanyName()));
  }

  public AbUserData mcpAbUserResponseToAbUserData(McpAbUserResponse response, AbCompany owningAbCompany) {
    AbUserData data = new AbUserData();

    String firstName = trimmed(response.getFirstName());

    if (firstName.length() >= 30) {
      firstName = trimmed(response.getFirstName()).substring(0, 30);
    }

    String lastName = trimmed(response.getLastName());
    if (lastName.length() >= 45) {
      lastName = trimmed(response.getLastName()).substring(0, 45);
    }

    data.setFirstName(Optional.of(firstName));
    data.setLastName(Optional.of(lastName));
    data.setEmail(Optional.of(validateEmail(trimmed(response.getEmail()))));
    data.setPhone1(Optional.of(trimmed(response.getPhone1())));
    data.setValidatePhone1(false);

    data.setAbCompany(owningAbCompany);

    if (isEmpty(validateEmail(trimmed(response.getEmail())))) {
      data.setPreferredContactMethod(Optional.of(""));
    }

    return data;
  }


  private void setBasicCarrierInfo(McpAbCompanyResponse abCompany, CarrierDto carrier) {

    abCompany.setOwnerContactName(carrier.getOwnerContactName());
    abCompany.setOwnerContactEmail(carrier.getOwnerContactEmail());
    abCompany.setOwnerContactPhone(carrier.getOwnerContactPhone());

    abCompany.setExternalAbCompanyId(String.valueOf(carrier.getDoTNumber()));
    abCompany.setCensusNum(carrier.getDoTNumber());
    abCompany.setMcNum(carrier.getMcNumber());

    if (!isEmpty(carrier.getLegalName())) {
      abCompany.setCompanyName(carrier.getLegalName().trim());

      if (!isEmpty(carrier.getDbAName())) {
        abCompany.setCompanyName((carrier.getLegalName() + " " + carrier.getDbAName()).trim());
      }
    }

    abCompany.setCompanyPhone(carrier.getPhone());
    abCompany.setCompanyEmail(carrier.getEmail());

    abCompany.setAddress(trimmed(carrier.getAddress1()));
    if (!isEmpty(carrier.getAddress1())) {
      abCompany.setAddress(abCompany.getAddress() + " " + carrier.getAddress2());
    }
    abCompany.setAddress(properCase(abCompany.getAddress()));

    String location = "";
    if (carrier.getCity() != null && carrier.getState() != null) {
      location = carrier.getCity() + ", " + carrier.getState();
    }
    if (carrier.getZipcode() != null && !carrier.getZipcode().isEmpty()) {
      location += " " + carrier.getZipcode().substring(0, 5);
    }
    if (carrier.getCountry() != null && !carrier.getCountry().isEmpty() && !carrier.getCountry().equals("United States")) {
      location += ", " + carrier.getCountry();
    }
    abCompany.setLocation(location);

    abCompany.setMailingAddress(trimmed(carrier.getMailingAddress1()));
    if (!isEmpty(carrier.getMailingAddress2())) {
      abCompany.setMailingAddress(abCompany.getMailingAddress() + " " + carrier.getMailingAddress2());
    }
    abCompany.setMailingAddress(properCase(abCompany.getMailingAddress()));

    String mailingLocation = "";
    if (carrier.getMailingCity() != null && carrier.getMailingState() != null) {
      mailingLocation = carrier.getMailingCity() + ", " + carrier.getMailingState();
    }
    if (carrier.getMailingZipcode() != null && !carrier.getMailingZipcode().isEmpty()) {
      mailingLocation += " " + carrier.getMailingZipcode().substring(0, 5);
    }
    if (carrier.getMailingCountry() != null && !carrier.getMailingCountry().isEmpty() && !carrier.getMailingCountry().equals("United States")) {
      mailingLocation += ", " + carrier.getMailingCountry();
    }
    abCompany.setMailingLocation(mailingLocation);

  }

  private void setAssureAdvantageInfo(McpAbCompanyResponse abCompany, CarrierDto carrier) {
    if (!isEmpty(carrier.getAssureAdvantage())) {
      MyCarrierPacketsApiFMCSAFMCSACarrier assureAdvantage = carrier.getAssureAdvantage().get(0);

      MyCarrierPacketsApiFMCSACarrierDetails carrierDetails = assureAdvantage.getCarrierDetails();
      if (carrierDetails != null) {

        if (carrierDetails.getDotNumber() != null) {
          abCompany.setExternalAbCompanyId(String.valueOf(carrierDetails.getDotNumber().getValue()));
          abCompany.setCensusNum(parseInteger(carrierDetails.getDotNumber().getValue()));
        }
        if (carrierDetails.getDocketNumber() != null) {
          abCompany.setMcNum(carrierDetails.getDocketNumber());
        }

        if (carrierDetails.getRiskAssessment() != null && carrierDetails.getRiskAssessment().getOverall() != null) {
          abCompany.setRiskAssessmentOverall(carrierDetails.getRiskAssessment().getOverall());
        }

        setCarrierIdentity(abCompany, carrierDetails);
        setCertificate(abCompany, carrierDetails);
        setFmcsa(abCompany, carrierDetails);

        if (carrierDetails.getIsMonitored() != null) {
          // Note: We store this information into mcpMonitoredRemote
          // instead of mcpMonitored because the UI needs mcpMonired to be 1
          abCompany.setMcpMonitoredRemote(carrierDetails.getIsMonitored());
        }
      }
    }
  }

  private void setCarrierIdentity(McpAbCompanyResponse abCompany, MyCarrierPacketsApiFMCSACarrierDetails carrierDetails) {
    if (carrierDetails.getIdentity() != null) {
      MyCarrierPacketsApiFMCSAIdentity identity = carrierDetails.getIdentity();

      if (!isEmpty(identity.getLegalName())) {
        abCompany.setLegalName(identity.getLegalName().trim());
        abCompany.setCompanyName(identity.getLegalName().trim());

        if (!isEmpty(identity.getDbaName())) {
          abCompany.setDbaName(identity.getDbaName().trim());
          abCompany.setCompanyName((identity.getLegalName() + " " + identity.getDbaName()).trim());
        }
      }

      if (identity.getBusinessPhone() != null && !identity.getBusinessPhone().isEmpty()) {
        abCompany.setCompanyPhone(identity.getBusinessPhone());
      }
      if (identity.getEmailAddress() != null && !identity.getEmailAddress().isEmpty()) {
        abCompany.setCompanyEmail(identity.getEmailAddress());
      }

      if (identity.getBusinessCity() != null && !identity.getBusinessCity().isEmpty()) {
        if (!isEmpty(trimmed(identity.getBusinessStreet()))) {
          abCompany.setAddress(properCase(identity.getBusinessStreet()));
        }
        abCompany.setCity(identity.getBusinessCity());
        abCompany.setState(identity.getBusinessState());
        abCompany.setZip(identity.getBusinessZipCode());
        abCompany.setCountry(identity.getBusinessCountry());
        String location = identity.getBusinessCity() + ", " + identity.getBusinessState();
        if (identity.getBusinessZipCode() != null && !identity.getBusinessZipCode().isEmpty()) {
          location += " " + identity.getBusinessZipCode().substring(0, 5);
        }
        if (identity.getBusinessCountry() != null && !identity.getBusinessCountry().isEmpty() && !identity.getBusinessCountry().equals("US")) {
          location += ", " + identity.getBusinessCountry();
        }
        abCompany.setLocation(location);
      }

      if (identity.getMailingCity() != null && !identity.getMailingCity().isEmpty()) {

        if (!isEmpty(trimmed(identity.getMailingStreet()))) {
          abCompany.setAddress(properCase(identity.getMailingStreet()));
        }

        String mailingLocation = identity.getMailingCity() + ", " + identity.getMailingState();
        if (identity.getMailingZipCode() != null && !identity.getMailingZipCode().isEmpty()) {
          mailingLocation += " " + identity.getMailingZipCode().substring(0, 5);
        }
        if (identity.getMailingCountry() != null && !identity.getMailingCountry().isEmpty() && !identity.getMailingCountry().equals("US")) {
          mailingLocation += ", " + identity.getMailingCountry();
        }
        abCompany.setMailingLocation(mailingLocation);
      }

    }
  }

  private void setCertificate(McpAbCompanyResponse abCompany, MyCarrierPacketsApiFMCSACarrierDetails carrierDetails) {

    if (carrierDetails.getCertData() != null && !CollectionUtils.isEmpty(carrierDetails.getCertData().getCertificate())) {
      CertificateDto cert = carrierDetails.getCertData().getCertificate().get(0);
      if (cert.getCoverage() != null && !CollectionUtils.isEmpty(cert.getCoverage())) {
        List<CoverageDto> covs = cert.getCoverage();
        for (CoverageDto item : covs) {
          if (item.getType() != null) {
            if (item.getType().equals("Auto")) {
              abCompany.setInsLiabCompany(item.getInsurerName());
              abCompany.setInsLiabPolicy(item.getPolicyNumber());
              abCompany.setInsLiabExpDate(item.getExpirationDate());

              abCompany.setInsLiabAmount(parseInteger(item.getCoverageLimit()));
              abCompany.setInsLiabPhone(cert.getProducerPhone());

              abCompany.setInsLiabContact(properCase(cert.getProducerName()));

              abCompany.setInsLiabNotes("");
              if (item.getInsurerAMBestRating() != null && !item.getInsurerAMBestRating().isEmpty()) {
                abCompany.setInsLiabNotes("AM Best Rating: " + item.getInsurerAMBestRating() + "\n");
              }
              if (item.getDeductable() != null && !item.getDeductable().isEmpty()) {
                abCompany.setInsLiabNotes("Deductible: " + item.getDeductable() + "\n");
              }
            } else if (item.getType().equals("Cargo")) {
              abCompany.setInsCargoCompany(item.getInsurerName());
              abCompany.setInsCargoPolicy(item.getPolicyNumber());
              abCompany.setInsCargoExpDate(item.getExpirationDate());
              abCompany.setInsCargoAmount(parseInteger(item.getCoverageLimit()));
              abCompany.setInsCargoPhone(cert.getProducerPhone());


              abCompany.setInsCargoContact(properCase(cert.getProducerName()));

              abCompany.setInsCargoNotes("");
              if (item.getInsurerAMBestRating() != null && !item.getInsurerAMBestRating().isEmpty()) {
                abCompany.setInsCargoNotes("AM Best Rating: " + item.getInsurerAMBestRating() + "\n");
              }
              if (item.getDeductable() != null && !item.getDeductable().isEmpty()) {
                abCompany.setInsCargoNotes("Deductible: " + item.getDeductable() + "\n");
              }
            } else if (item.getType().equals("WorkersCompensation")) {
              abCompany.setInsWorkCompany(item.getInsurerName());
              abCompany.setInsWorkPolicy(item.getPolicyNumber());
              abCompany.setInsWorkExpDate(item.getExpirationDate());
              abCompany.setInsWorkAmount(parseInteger(item.getCoverageLimit()));
              abCompany.setInsWorkPhone(cert.getProducerPhone());
              abCompany.setInsWorkContact(properCase(cert.getProducerName()));
              abCompany.setInsWorkNotes("");
              if (item.getInsurerAMBestRating() != null && !item.getInsurerAMBestRating().isEmpty()) {
                abCompany.setInsWorkNotes("AM Best Rating: " + item.getInsurerAMBestRating() + "\n");
              }
              if (item.getDeductable() != null && !item.getDeductable().isEmpty()) {
                abCompany.setInsWorkNotes("Deductible: " + item.getDeductable() + "\n");
              }
            }
          }
        }
      }
    }
  }

  private void setFmcsa(McpAbCompanyResponse abCompany, MyCarrierPacketsApiFMCSACarrierDetails carrierDetails) {

    if (carrierDetails.getFmCSAInsurance() != null && !CollectionUtils.isEmpty(carrierDetails.getFmCSAInsurance().getPolicyList())) {
      MyCarrierPacketsApiFMCSAFMCSAInsurance fmcsaInsurance = carrierDetails.getFmCSAInsurance();
      if (fmcsaInsurance.getPolicyList() != null && !CollectionUtils.isEmpty(fmcsaInsurance.getPolicyList())) {
        PolicyOutput policyOutput = fmcsaInsurance.getPolicyList().get(0);
        abCompany.setInsFmcsaCompany(policyOutput.getCompanyName());
        abCompany.setInsFmcsaPolicy(policyOutput.getPolicyNumber());
        abCompany.setInsFmcsaAddress(policyOutput.getAddress());
        abCompany.setInsFmcsaCity(policyOutput.getCity());
        abCompany.setInsFmcsaStateCode(policyOutput.getStateCode());
        abCompany.setInsFmcsaPostalCode(policyOutput.getPostalCode());
        abCompany.setInsFmcsaCountryCode(policyOutput.getCountryCode());
        abCompany.setInsFmcsaPhone(policyOutput.getPhone());
        abCompany.setInsFmcsaFax(policyOutput.getFax());
      }
    }
  }

  private void setAbUsers(McpAbCompanyResponse abCompany, CarrierDto carrier) {

    Set<String> uniqueFullNames = new HashSet<>();

    String ownerFullName = carrier.getOwnerContactName();
    if (!isEmpty(ownerFullName)) {
      uniqueFullNames.add(trimmedAndLowered(ownerFullName));
      addAbUser(abCompany,
                carrier.getOwnerContactName(),
                carrier.getOwnerContactEmail(),
                carrier.getOwnerContactPhone(),
                "owner");
    }

    if (!isEmpty(carrier.getCarrierDispatchers())) {

      for (CarrierDispatcherDto dispatcher : carrier.getCarrierDispatchers()) {
        if (!uniqueFullNames.contains(trimmedAndLowered(dispatcher.getDispatcherName()))) {
          uniqueFullNames.add(trimmedAndLowered(dispatcher.getDispatcherName()));
          addAbUser(abCompany,
                    dispatcher.getDispatcherName(),
                    dispatcher.getEmail(),
                    dispatcher.getPhoneNumber(),
                    "Dispatcher");
        }
      }
    }

    if (!isEmpty(carrier.getCarrierDrivers())) {
      for (CarrierDriverDto driver : carrier.getCarrierDrivers()) {
        if (!uniqueFullNames.contains(trimmedAndLowered(driver.getDriverName()))) {
          uniqueFullNames.add(trimmedAndLowered(driver.getDriverName()));

          addAbUser(abCompany,
                    driver.getDriverName(),
                    "",
                    driver.getCellPhone(),
                    "Driver");
        }
      }
    }

    if (!isEmpty(carrier.getCarrierCustomerAgreements())) {
      for (CarrierCustomerAgreementDto agreement : carrier.getCarrierCustomerAgreements()) {
        if (!uniqueFullNames.contains(trimmedAndLowered(agreement.getSignaturePerson()))) {
          uniqueFullNames.add(trimmedAndLowered(agreement.getSignaturePerson()));

          addAbUser(abCompany,
                    agreement.getSignaturePerson(),
                    agreement.getSignaturePersonEmail(),
                    agreement.getSignaturePersonPhoneNumber(),
                    "Signature Person");
        }
      }
    }
  }


  private void addAbUser(McpAbCompanyResponse abCompany, String fullName, String email, String phone, String notes) {
    McpAbUserResponse abUser = new McpAbUserResponse();

    Pair<String, String> result = parseFullName(fullName);
    abUser.setFirstName(result.getFirst());
    abUser.setLastName(result.getSecond());

    parseFullName(fullName, pair -> {
      abUser.setFirstName(pair.getFirst());
      abUser.setLastName(pair.getSecond());
      abUser.setName(abUser.getFirstName() + " " + abUser.getLastName());
    });

    abUser.setEmail(validateEmail(email));
    abUser.setPhone1(trimmed(phone));
    abUser.setAbUserNotes(notes);
    abCompany.getAbUsers().add(abUser);

  }


  private static String trimmed(String name) {
    if (name == null) {
      return "";
    }
    return name.trim().replaceAll("  +", " ");
  }

  private static String trimmedAndLowered(String name) {
    return trimmed(name).toLowerCase();
  }

  private static String validateEmail(String email) {
    if (isEmpty(email)) {
      return "";
    }
    if (EmailValidator.getInstance().isValid(email)) {
      return email.toLowerCase();
    }
    return "";
  }


  private String buildMcpDetailsUrl(String censusNum, String mcNum) {
    String mcNumPart = "";
    if (!isEmpty(mcNum)) {
      mcNumPart = "DocketNumber/" + mcNum;
    }

    return "https://mycarrierpackets.com/CarrierInformation/DOTNumber/" + censusNum + mcNumPart;
  }

}
