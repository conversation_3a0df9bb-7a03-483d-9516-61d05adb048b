package com.bulkloads.web.rate.mapper;

import java.util.List;
import com.bulkloads.web.contracts.mapper.CommonMapper;
import com.bulkloads.web.rate.repository.projection.RateResponseProjection;
import com.bulkloads.web.rate.service.dto.RateResponse;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;

@Mapper(componentModel = "spring",
    nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
    nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
    unmappedTargetPolicy = org.mapstruct.ReportingPolicy.IGNORE,
    uses = CommonMapper.class)
public abstract class RateMapper {

  public abstract RateResponse projectionToResponse(RateResponseProjection projection);
  
  public abstract List<RateResponse> projectionsToResponses(List<RateResponseProjection> projections);
}