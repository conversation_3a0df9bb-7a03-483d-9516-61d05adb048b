package com.bulkloads.web.rate.repository;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import com.bulkloads.web.rate.domain.entity.Rate;
import com.bulkloads.web.rate.domain.entity.RateSummary;
import com.bulkloads.web.rate.repository.projection.RateResponseProjection;
import com.bulkloads.web.routing.domain.vo.Coordinates;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface RateRepository extends JpaRepository<Rate, String> {

  @Query(value = """
      SELECT
      count(r.rate_id) as sampleCount,
      avg(r.rate_rate_per_mile) as avgRatePerMileFixed,
      avg(r.rate_mileage) as avgRateMileage,
      sum(r.rate_rate_per_mile *
          ifnull(least(2,power((50/(st_distance_sphere(
          point(:#{#origin.longitude}, :#{#origin.latitude}),
          point(rate_origin_long, rate_origin_lat))/1609.34 )),4)),2)
                          *
          ifnull(least(2, power((50/(st_distance_sphere(
          point(:#{#destination.longitude}, :#{#destination.latitude}),
          point(rate_destination_long, rate_destination_lat))/1609.34 )),4)),2)
      )/sum(
          ifnull(least(2,power((50/(st_distance_sphere(
          point(:#{#origin.longitude}, :#{#origin.latitude}),
          point(rate_origin_long, rate_origin_lat))/1609.34 )),4)),2)
                  *
          ifnull(least(2, power((50/(st_distance_sphere(
          point(:#{#destination.longitude}, :#{#destination.latitude}),
          point(rate_destination_long, rate_destination_lat))/1609.34 )),4)),2)
      ) as avgRatePerMile
      FROM rates r
      WHERE r.rate_rate_per_mile IS NOT NULL
      AND r.rate_rate_per_mile < 2.8
      AND (:laneDist <= 600 OR r.rate_mileage BETWEEN (:laneDist * 0.75) AND (:laneDist * 1.25))
      AND st_distance_sphere(
              point(:#{#origin.longitude}, :#{#origin.latitude}),
              point(rate_origin_long, rate_origin_lat))/1609.34 <= :originDistance
      AND st_distance_sphere(
              point(:#{#destination.longitude}, :#{#destination.latitude}),
              point(rate_destination_long, rate_destination_lat))/1609.34 <= :destinationDistance
      AND (:rateProductCategoryIds IS NULL OR r.rate_product_category_id IN (:rateProductCategoryIds))
      """, nativeQuery = true)
  RateSummary findLaneRate(@Param("origin") final Coordinates origin,
                           @Param("originDistance") final double originDistance,
                           @Param("destination") final Coordinates destination,
                           @Param("destinationDistance") final double destinationDistance,
                           @Param("laneDist") final BigDecimal laneDist,
                           @Param("rateProductCategoryIds") final List<Integer> rateProductCategoryIds);

  @Query(value = """
      SELECT st_distance_sphere(
          point(:#{#origin.longitude}, :#{#origin.latitude}),
          point(:#{#destination.longitude}, :#{#destination.latitude})
      )/1609.34
      """, nativeQuery = true)
  BigDecimal calculateLaneDistance(@Param("origin") final Coordinates origin,
                                   @Param("destination") final Coordinates destination);

  @Query(value = """
      SELECT
          gw_rate_type_unit as rateTypeUnit,
          gw_rate_type as rateType,
          gw_lbs as loadLbs,
          gw_rate as rateValue,
          gw_total_amount as loadDollarValue,
          gw_product as productName,
          gw_origin_city as originCity,
          gw_origin_state as originState,
          rate_origin_long as originLongitude,
          rate_origin_lat as originLatitude,
          gw_destination_city as destinationCity,
          gw_destination_state as destinationState,
          rate_destination_long as destinationLongitude,
          rate_destination_lat as destinationLatitude,
          rate_product_category_id as productCategoryId,
          rate_hauled_date as hauledDate,
          rate_mileage as loadMileage,
          rate_rate_per_mile as ratePerMile
      FROM rates r
      WHERE (:hauledDate IS NULL OR r.rate_hauled_date = :hauledDate)
      AND (:originLat IS NULL OR :originLong IS NULL OR :originRadius IS NULL OR
           (ST_Distance_Sphere(
               point(r.rate_origin_long, r.rate_origin_lat),
               point(:originLong, :originLat)
           )/1609.34) <= :originRadius)
      AND (:destinationLat IS NULL OR :destinationLong IS NULL OR :destinationRadius IS NULL OR
           (ST_Distance_Sphere(
               point(r.rate_destination_long, r.rate_destination_lat),
               point(:destinationLong, :destinationLat)
           )/1609.34) <= :destinationRadius)
      LIMIT 500
      """, nativeQuery = true)
  List<RateResponseProjection> searchRates(
      @Param("hauledDate") LocalDate hauledDate,
      @Param("originLat") Double originLat,
      @Param("originLong") Double originLong,
      @Param("originRadius") Double originRadius,
      @Param("destinationLat") Double destinationLat,
      @Param("destinationLong") Double destinationLong,
      @Param("destinationRadius") Double destinationRadius);
}
