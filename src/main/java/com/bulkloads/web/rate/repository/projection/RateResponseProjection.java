package com.bulkloads.web.rate.repository.projection;

import java.math.BigDecimal;
import java.time.LocalDate;

public interface RateResponseProjection {

  BigDecimal getRateTypeUnit();

  String getRateType();

  BigDecimal getLoadLbs();

  BigDecimal getRateValue();

  BigDecimal getLoadDollarValue();

  String getProductName();

  String getOriginCity();

  String getOriginState();

  Double getOriginLongitude();

  Double getOriginLatitude();

  String getDestinationCity();

  String getDestinationState();

  Double getDestinationLongitude();

  Double getDestinationLatitude();

  Integer getProductCategoryId();

  LocalDate getHauledDate();

  BigDecimal getLoadMileage();

  BigDecimal getRatePerMile();
}