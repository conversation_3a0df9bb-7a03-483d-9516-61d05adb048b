package com.bulkloads.web.rate.repository;

import java.util.List;
import com.bulkloads.web.rate.domain.entity.RateProductCategory;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface RateProductCategoryRepository extends JpaRepository<RateProductCategory, Integer> {

  List<RateProductCategory> findAllByRateProductCategoryInOrderByRateProductCategoryAsc(List<String> rateProductCategories);

}