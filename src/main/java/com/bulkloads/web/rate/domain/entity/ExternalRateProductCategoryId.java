package com.bulkloads.web.rate.domain.entity;

import java.io.Serializable;
import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@EqualsAndHashCode
@Embeddable
public class ExternalRateProductCategoryId implements Serializable {

  @Size(max = 10)
  @NotNull
  @Column(name = "external_rate_product_category_id")
  private String externalRateProductCategoryId;

  @NotNull
  @Column(name = "user_company_id")
  private Integer userCompanyId;

}