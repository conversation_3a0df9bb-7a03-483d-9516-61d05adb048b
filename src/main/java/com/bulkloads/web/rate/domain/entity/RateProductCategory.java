package com.bulkloads.web.rate.domain.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Getter;

@Entity
@Getter
@Table(name = "rate_product_categories")
public class RateProductCategory {

  @Id
  @Column(name = "rate_product_category_id")
  private Integer rateProductCategoryId;

  @Size(max = 50)
  @NotNull
  @Column(name = "rate_product_category")
  private String rateProductCategory;

  @Size(max = 100)
  @NotNull
  @Column(name = "rate_product_category_equipment")
  private String rateProductCategoryEquipment;

}