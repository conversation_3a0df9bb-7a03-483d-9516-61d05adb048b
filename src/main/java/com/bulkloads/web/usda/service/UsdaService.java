package com.bulkloads.web.usda.service;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;
import com.bulkloads.config.AppProperties;
import com.bulkloads.web.infra.email.EmailService;
import com.bulkloads.web.infra.email.domain.Attachment;
import com.bulkloads.web.infra.email.domain.EmailDetails;
import com.bulkloads.web.usda.repository.UsdaRepository;
import com.bulkloads.web.usda.service.dto.UsdaDataResponse;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RequiredArgsConstructor
public class UsdaService {

  private final UsdaRepository usdaRepository;
  private final UsdaCsvService usdaCsvService;
  private final EmailService emailService;
  private final AppProperties appProperties;

  @SneakyThrows
  public void generateUsdaReport() {
    log.info("Starting USDA report generation");

    final List<UsdaDataResponse> data = usdaRepository.getUsdaData();
    log.info("Retrieved {} records for USDA report", data.size());

    if (data.isEmpty()) {
      log.warn("No data found for USDA report");
      return;
    }

    Path csvFile = usdaCsvService.generateCsvFile(data);

//    sendEmailWithCsvAttachment(csvFile);

    // Clean up temporary file
    Files.deleteIfExists(csvFile);
    log.info("USDA report generation and email completed successfully");

  }

  private void sendEmailWithCsvAttachment(Path csvFile) throws IOException {
    String reportEmailAddress = appProperties.getUsda().getReportEmailAddress();

    if (reportEmailAddress == null || reportEmailAddress.trim().isEmpty()) {
      log.error("USDA report email address not configured");
      throw new IllegalStateException("USDA report email address not configured");
    }

    String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    String subject = String.format("USDA Load Report - %s", timestamp);
    String message = String.format(
        "Please find attached the USDA load report generated on %s.\n\n" +
            "This report contains load data for the past 4 weeks grouped by region, week, and mileage range.\n\n" +
            "Best regards,\nBulkloads System",
        timestamp
    );

    // Create email attachment
    Attachment attachment = Attachment.builder()
        .filename(csvFile.getFileName().toString())
//        .url(s3Url)
        .build();

    EmailDetails emailDetails = EmailDetails.builder()
        .toEmails(List.of(reportEmailAddress))
        .subject(subject)
        .message(message)
        .attachments(List.of(attachment))
        .description("USDA Load Report")
        .build();

    Optional<Integer> emailId = emailService.sendEmail(emailDetails);

    if (emailId.isPresent()) {
      log.info("USDA report email sent successfully with ID: {}", emailId.get());
    } else {
      log.warn("USDA report email was not sent (possibly filtered or duplicate)");
    }
  }
}
