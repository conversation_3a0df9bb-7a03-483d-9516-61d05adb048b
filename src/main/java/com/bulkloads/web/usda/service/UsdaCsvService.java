package com.bulkloads.web.usda.service;

import java.io.IOException;
import java.io.StringWriter;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import com.bulkloads.web.usda.service.dto.UsdaDataResponse;
import com.opencsv.CSVWriter;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RequiredArgsConstructor
public class UsdaCsvService {

  public Path generateCsvFile(final List<UsdaDataResponse> data) throws IOException {
    final String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
    final String fileName = String.format("usda_report_%s.csv", timestamp);
    final Path tempFile = Files.createTempFile("usda_report_", ".csv");
    
    try (final StringWriter stringWriter = new StringWriter();
         final CSVWriter csvWriter = new CSVWriter(stringWriter)) {
      
      String[] header = {"Region", "Week_Of", "Mileage", "Rate_Per_Mile", "Number_of_Loads"};
      csvWriter.writeNext(header);
      
      for (final UsdaDataResponse row : data) {
        final String[] line = {
            row.getRegion(),
            row.getWeekOf(),
            row.getMileage(),
            row.getRatePerMile() != null ? row.getRatePerMile().toString() : "",
            row.getNumberOfLoads() != null ? row.getNumberOfLoads().toString() : ""
        };
        csvWriter.writeNext(line);
      }
      
      Files.write(tempFile, stringWriter.toString().getBytes());
      log.info("Generated USDA CSV file: {} with {} records", tempFile.getFileName(), data.size());
      
      return tempFile;
    }
  }
}
