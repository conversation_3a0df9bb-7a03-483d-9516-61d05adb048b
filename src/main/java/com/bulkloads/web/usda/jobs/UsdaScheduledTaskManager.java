package com.bulkloads.web.usda.jobs;

import com.bulkloads.web.eld.service.EldSyncService;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;

@Slf4j
@Component
@RequiredArgsConstructor
public class UsdaScheduledTaskManager {

  private final EldSyncService eldSyncService;

  @Scheduled(cron = "0 */10 * * * *")
  @SchedulerLock(name = "syncVehicleLocations", lockAtMostFor = "7m", lockAtLeastFor = "1m")
  public void syncVehicleLocations() {
    long start = System.currentTimeMillis();
    eldSyncService.synchronizeUserCompanyEquipmentLocations();
    long duration = System.currentTimeMillis() - start;
    log.info("Executed syncVehicleLocations scheduled task. took: {} (s)", duration / 1000);
  }

}
