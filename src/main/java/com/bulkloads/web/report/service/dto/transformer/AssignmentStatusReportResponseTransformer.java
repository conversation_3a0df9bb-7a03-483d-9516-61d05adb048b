package com.bulkloads.web.report.service.dto.transformer;

import com.bulkloads.common.jpa.nativejpa.QueryParts;
import com.bulkloads.web.report.service.dto.AssignmentStatusReportResponse;
import org.hibernate.query.TupleTransformer;
import org.springframework.stereotype.Component;
import lombok.RequiredArgsConstructor;

@Component
@RequiredArgsConstructor
public class AssignmentStatusReportResponseTransformer implements TupleTransformer<AssignmentStatusReportResponse> {

  @Override
  public AssignmentStatusReportResponse transformTuple(Object[] columns, String[] aliases) {

    QueryParts parts = new QueryParts(columns, aliases);

    return AssignmentStatusReportResponse.builder()
        .assignmentStatus(parts.asString("assignment_status"))
        .loadCount(parts.asInteger("load_count"))
        .build();
  }
}
