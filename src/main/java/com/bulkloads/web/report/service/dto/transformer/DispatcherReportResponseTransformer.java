
package com.bulkloads.web.report.service.dto.transformer;

import com.bulkloads.common.jpa.nativejpa.QueryParts;
import com.bulkloads.web.report.service.dto.DispatcherReportResponse;
import org.hibernate.query.TupleTransformer;
import org.springframework.stereotype.Component;
import lombok.RequiredArgsConstructor;

@Component
@RequiredArgsConstructor
public class DispatcherReportResponseTransformer implements TupleTransformer<DispatcherReportResponse> {

  @Override
  public DispatcherReportResponse transformTuple(Object[] columns, String[] aliases) {

    QueryParts parts = new QueryParts(columns, aliases);

    return DispatcherReportResponse.builder()
        .userId(parts.asInteger("user_id"))
        .bucketFromDate(parts.asLocalDate("bucket_from_date"))
        .bucketToDate(parts.asLocalDate("bucket_to_date"))
        .loadCount(parts.asInteger("load_count"))
        .build();
  }
}
