
package com.bulkloads.web.report.service.dto.transformer;

import com.bulkloads.common.jpa.nativejpa.QueryParts;
import com.bulkloads.web.report.service.dto.PayablesReportResponse;
import org.hibernate.query.TupleTransformer;
import org.springframework.stereotype.Component;
import lombok.RequiredArgsConstructor;

@Component
@RequiredArgsConstructor
public class PayablesReportResponseTransformer implements TupleTransformer<PayablesReportResponse> {

  @Override
  public PayablesReportResponse transformTuple(Object[] columns, String[] aliases) {

    QueryParts parts = new QueryParts(columns, aliases);

    return PayablesReportResponse.builder()
        .bucketFromDate(parts.asLocalDate("bucket_from_date"))
        .bucketToDate(parts.asLocalDate("bucket_to_date"))
        .paymentSum(parts.asDouble("payment_sum"))
        .build();
  }
}
