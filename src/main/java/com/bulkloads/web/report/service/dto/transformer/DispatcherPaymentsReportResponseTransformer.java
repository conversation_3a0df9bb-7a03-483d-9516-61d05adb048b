package com.bulkloads.web.report.service.dto.transformer;

import com.bulkloads.common.jpa.nativejpa.QueryParts;
import com.bulkloads.web.report.service.dto.DispatcherPaymentsReportResponse;
import org.hibernate.query.TupleTransformer;
import org.springframework.stereotype.Component;
import lombok.RequiredArgsConstructor;

@Component
@RequiredArgsConstructor
public class DispatcherPaymentsReportResponseTransformer implements TupleTransformer<DispatcherPaymentsReportResponse> {

  @Override
  public DispatcherPaymentsReportResponse transformTuple(Object[] columns, String[] aliases) {

    QueryParts parts = new QueryParts(columns, aliases);

    return DispatcherPaymentsReportResponse.builder()
        .userId(parts.asInteger("user_id"))
        .firstName(parts.asString("first_name"))
        .lastName(parts.asString("last_name"))
        .paymentAverage(parts.asDouble("payment_average"))
        .build();
  }
}
