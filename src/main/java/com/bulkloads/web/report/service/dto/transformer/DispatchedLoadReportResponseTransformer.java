package com.bulkloads.web.report.service.dto.transformer;

import com.bulkloads.common.jpa.nativejpa.QueryParts;
import com.bulkloads.web.report.service.dto.DispatchedLoadReportResponse;
import org.hibernate.query.TupleTransformer;
import org.springframework.stereotype.Component;
import lombok.RequiredArgsConstructor;

@Component
@RequiredArgsConstructor
public class DispatchedLoadReportResponseTransformer implements TupleTransformer<DispatchedLoadReportResponse> {

  @Override
  public DispatchedLoadReportResponse transformTuple(Object[] columns, String[] aliases) {

    QueryParts parts = new QueryParts(columns, aliases);

    return DispatchedLoadReportResponse.builder()
        .userId(parts.asInteger("user_id"))
        .firstName(parts.asString("first_name"))
        .lastName(parts.asString("last_name"))
        .loadCount(parts.asInteger("load_count"))
        .build();
  }
}
