package com.bulkloads.web.report.repository.template;

public class GetDispatcher<PERSON>ueryTemplate {

  public static final String GET_DISPATCHER_QUERY_TEMPLATE = """
         select user_id,
           date(assigned_date) - interval day(assigned_date) - 1 day as bucket_from_date,
           date(assigned_date) - interval day(assigned_date) - 1 day + interval 1 month as bucket_to_date,
           count(*) as load_count
         from load_assignments
         where
           <% params.put("userCompanyId", userCompanyId) %>
           user_company_id = :userCompanyId
           <% params.put("startDate", startDate) %>
           <% params.put("endDate", endDate) %>
           and assigned_date between :startDate and :endDate
           and user_id is not null
         group by bucket_from_date, bucket_to_date, user_id
         order by bucket_from_date, bucket_to_date, user_id;
      """;

}
