package com.bulkloads.web.report.repository.template;

public class GetDispatcherPaymentsQueryTemplate {

  public static final String GET_DISPATCHER_PAYMENTS_QUERY_TEMPLATE = """
      select ui.user_id, ui.first_name, ui.last_name, avg(la.payment) as payment_average
      from load_assignments la
        inner join user_info ui on la.payment_by_user_id = ui.user_id
      where
        <% params.put("userCompanyId", userCompanyId) %>
        la.user_company_id = :userCompanyId
        and la.payment_by_user_id is not null
      group by ui.user_id, ui.first_name, ui.last_name
      order by ui.first_name, ui.last_name
      """;

}
