package com.bulkloads.web.report.repository.template;

public class GetAssignmentStatusQueryTemplate {

  public static final String GET_ASSIGNMENT_STATUS_QUERY_TEMPLATE = """
      select
        assignment_status,
        count(*) as load_count
      from
        load_assignments
      where
        <% params.put("userCompanyId", userCompanyId) %>
        user_company_id = :userCompanyId
      group by
        assignment_status
      order by assignment_status

      """;

}
