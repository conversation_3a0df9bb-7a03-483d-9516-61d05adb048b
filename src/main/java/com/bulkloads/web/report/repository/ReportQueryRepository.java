package com.bulkloads.web.report.repository;

import java.time.LocalDate;
import java.util.List;
import com.bulkloads.web.report.service.dto.AssignmentStatusReportResponse;
import com.bulkloads.web.report.service.dto.CarriersUsedReportResponse;
import com.bulkloads.web.report.service.dto.DispatchedLoadReportResponse;
import com.bulkloads.web.report.service.dto.DispatcherPaymentsReportResponse;
import com.bulkloads.web.report.service.dto.DispatcherReportResponse;
import com.bulkloads.web.report.service.dto.PayablesReportResponse;
import com.bulkloads.web.report.service.dto.ProductReportResponse;
import com.bulkloads.web.report.service.dto.ShippersUsedReportResponse;
import org.springframework.stereotype.Repository;

@Repository
public interface ReportQueryRepository {

  List<AssignmentStatusReportResponse> getAssignmentStatusReport(final int userCompanyId);

  List<DispatcherReportResponse> getDispatcherReport(
      final int userCompanyId,
      final LocalDate startDate,
      final LocalDate endDate);

  List<DispatchedLoadReportResponse> getDispatchedLoadReport(final int userCompanyId);

  List<ProductReportResponse> getProductReport(final int userCompanyId);

  List<ShippersUsedReportResponse> getShippersUsedReport(
      final int userCompanyId,
      final LocalDate startDate,
      final LocalDate endDate);

  List<CarriersUsedReportResponse> getCarriersUsedReport(
      final int userCompanyId,
      final LocalDate startDate,
      final LocalDate endDate);

  List<PayablesReportResponse> getPayablesReport(
      final int userCompanyId,
      final LocalDate startDate,
      final LocalDate endDate);

  List<DispatcherPaymentsReportResponse> getDispatcherPaymentsReport(final int userCompanyId);
}