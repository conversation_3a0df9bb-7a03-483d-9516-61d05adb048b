package com.bulkloads.web.addressbook.abuser.domain.entity;

import groovyjarjarantlr4.v4.runtime.misc.NotNull;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "ab_user_roles")
@Getter
@Setter
public class AbUserRole {

  @Id
  @NotNull
  @Column(name = "ab_user_role_id")
  private Integer abUserRoleId;

  @NotNull
  @Size(max = 45, message = "Up to 45 chars")
  @Column(name = "ab_user_role")
  private String abUserRole;


  @Column(name = "description")
  private String description;
}
