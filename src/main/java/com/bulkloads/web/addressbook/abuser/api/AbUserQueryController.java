package com.bulkloads.web.addressbook.abuser.api;

import java.time.Instant;
import java.util.List;
import com.bulkloads.common.Parsers;
import com.bulkloads.web.addressbook.abuser.service.AbUserService;
import com.bulkloads.web.addressbook.abuser.service.dto.AbUserAutoResponse;
import com.bulkloads.web.addressbook.abuser.service.dto.AbUserResponse;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.Positive;
import lombok.RequiredArgsConstructor;

@RestController
@RequestMapping("/rest/address_book/users")
@Tag(name = "Address Book")
@CrossOrigin(origins = "*", maxAge = 3600)
@Validated
@RequiredArgsConstructor
public class AbUserQueryController {

  final AbUserService abUserService;

  @GetMapping("/{ab_user_id}")
  public AbUserResponse getAbUserById(@PathVariable("ab_user_id") @Positive Integer abUserId) {
    return abUserService.getAbUser(abUserId);
  }

  @GetMapping
  public List<AbUserResponse> getAbUsers(
      @RequestParam(name = "ab_company_id", required = false) Integer abCompanyId,
      @RequestParam(name = "ab_user_group_id", required = false) Integer abUserGroupId,
      @RequestParam(name = "include_bill_to_data", required = false, defaultValue = "false") boolean includeBillToData,
      @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
      @RequestParam(name = "last_modified_date", required = false) Instant lastModifiedDate,
      @RequestParam(name = "include_deleted", required = false, defaultValue = "false") boolean includeDeleted,
      @RequestParam(name = "skip", required = false, defaultValue = "0") int skip,
      @RequestParam(name = "limit", required = false, defaultValue = "100") int limit) {

    return abUserService.getAbUsers(
        abCompanyId,
        abUserGroupId,
        includeBillToData,
        lastModifiedDate,
        includeDeleted,
        skip,
        limit);
  }


  @GetMapping("/auto")
  public List<AbUserAutoResponse> getAbUsersAuto(
      @RequestParam(name = "term", required = false) String term,
      @RequestParam(name = "include_bill_to_data", required = false, defaultValue = "false") boolean includeBillToData,
      @RequestParam(name = "only_bill_to_data", required = false, defaultValue = "false") boolean onlyBillToData,
      @RequestParam(name = "user_type_ids", required = false, defaultValue = "") String userTypeIds,
      @RequestParam(name = "ab_user_role_ids", required = false, defaultValue = "") String abUserRoleIds,
      @RequestParam(name = "ab_company_id", required = false) Integer abCompanyId,
      @RequestParam(name = "order", required = false, defaultValue = "") String order,
      @RequestParam(name = "skip", required = false, defaultValue = "0") int skip,
      @RequestParam(name = "limit", required = false, defaultValue = "100") int limit) {

    return abUserService.getAbUsersAuto(
        term,
        includeBillToData,
        onlyBillToData,
        Parsers.parseIntegerCsvToList(userTypeIds),
        Parsers.parseIntegerCsvToList(abUserRoleIds),
        abCompanyId,
        order,
        skip,
        limit
    );
  }
}
