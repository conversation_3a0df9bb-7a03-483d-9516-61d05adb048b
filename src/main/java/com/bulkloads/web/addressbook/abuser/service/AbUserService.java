package com.bulkloads.web.addressbook.abuser.service;

import static com.bulkloads.common.validation.ValidationUtils.isMissingOrIsEmpty;
import static com.bulkloads.config.AppConstants.WebSocket.ACTION;
import static com.bulkloads.config.AppConstants.WebSocket.Action.CREATED;
import static com.bulkloads.config.AppConstants.WebSocket.Action.DELETED;
import static com.bulkloads.config.AppConstants.WebSocket.Action.UPDATED;
import static com.bulkloads.config.AppConstants.WebSocket.Channel.AB_USERS;
import static com.bulkloads.config.AppConstants.WebSocket.DATA;
import static java.util.Objects.nonNull;

import java.time.Instant;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.bulkloads.common.Converters;
import com.bulkloads.common.UserUtil;
import com.bulkloads.common.jpa.JpaUtils;
import com.bulkloads.common.validation.Result;
import com.bulkloads.exception.BulkloadsException;
import com.bulkloads.exception.ValidationException;
import com.bulkloads.web.addressbook.abcompany.domain.entity.AbCompany;
import com.bulkloads.web.addressbook.abcompany.repository.AbCompanyRepository;
import com.bulkloads.web.addressbook.abuser.domain.AbUserDomainService;
import com.bulkloads.web.addressbook.abuser.domain.data.AbUserData;
import com.bulkloads.web.addressbook.abuser.domain.entity.AbUser;
import com.bulkloads.web.addressbook.abuser.domain.entity.AbUserRole;
import com.bulkloads.web.addressbook.abuser.event.AbUserEmailUpdatedEvent;
import com.bulkloads.web.addressbook.abuser.mapper.AbUserMapper;
import com.bulkloads.web.addressbook.abuser.repository.AbUserRepository;
import com.bulkloads.web.addressbook.abuser.service.dto.AbUserAutoResponse;
import com.bulkloads.web.addressbook.abuser.service.dto.AbUserRequest;
import com.bulkloads.web.addressbook.abuser.service.dto.AbUserResponse;
import com.bulkloads.web.addressbook.abuser.service.dto.AbUserTypeResponse;
import com.bulkloads.web.infra.websocket.WebSocketService;
import com.bulkloads.web.user.domain.entity.User;
import com.bulkloads.web.user.service.UserService;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RequiredArgsConstructor
public class AbUserService {

  private final AbUserMapper abUserMapper;
  private final AbUserRepository abUserRepository;
  private final AbUserDomainService abUserDomainService;
  private final AbCompanyRepository abCompanyRepository;
  private final UserService userService;
  private final WebSocketService webSocketService;

  private final JpaUtils jpaUtils;

  public AbUserResponse create(AbUserRequest request) {

    if (isMissingOrIsEmpty(request.getAbCompanyId())) {
      throw new BulkloadsException("ab_company_id is required");
    }
    AbCompany abCompany = findAbCompanyById(request.getAbCompanyId().orElseThrow());

    AbUserData abUserData = new AbUserData();
    abUserData.setAbCompany(abCompany);
    abUserMapper.requestToData(request, abUserData);
    Result<AbUser> result = abUserDomainService.create(abUserData);
    AbUser abUser = abUserRepository.save(result.orElseThrow());
    AbUserResponse response = getAbUser(abUser.getAbUserId());
    sendToWebSocket(CREATED, List.of(abUser.getAbUserId()), List.of(response));
    return response;
  }

  public AbUserResponse update(int abUserId, AbUserRequest request) {
    AbUser abUserToUpdate = findAbUserById(abUserId);
    AbUserData abUserData = new AbUserData();
    abUserData.setAbCompany(abUserToUpdate.getAbCompany());
    abUserMapper.requestToData(request, abUserData);
    Result<AbUser> result = abUserDomainService.update(abUserToUpdate, abUserData);
    AbUser abUserToSave = result.orElseThrow();
    emitAbUserEmailUpdateEvent(abUserToSave);
    AbUser abUser = abUserRepository.save(abUserToSave);
    AbUserResponse response = getAbUser(abUser.getAbUserId());
    sendToWebSocket(UPDATED, List.of(abUser.getAbUserId()), List.of(response));
    return response;
  }

  void emitAbUserEmailUpdateEvent(AbUser abUserToSave) {
    final Map<String, Object> dirtyFields = jpaUtils.findDirtyFields(abUserToSave);
    if (dirtyFields.containsKey("email")) {
      final AbUserEmailUpdatedEvent abUserEmailUpdatedEvent = new AbUserEmailUpdatedEvent(
          UserUtil.getUserIdOrThrow(),
          UserUtil.getUserCompanyIdOrThrow(),
          abUserToSave.getAbUserId(),
          abUserToSave.getEmail());
      log.debug("Emitting {}", abUserEmailUpdatedEvent);
      abUserToSave.registerDomainEvent(abUserEmailUpdatedEvent);
    }
  }

  public void remove(int abUserId) {
    final AbUser abUserToRemove = findAbUserById(abUserId);
    final Result<AbUser> result = abUserDomainService.remove(abUserToRemove);
    AbUser abUser = abUserRepository.save(result.orElseThrow());
    sendToWebSocket(DELETED, List.of(abUser.getAbUserId()), List.of());
  }

  public AbUserResponse getAbUser(Integer abUserId) {
    User user = userService.getLoggedInUser();

    List<Integer> userCompanyIds = new ArrayList<>();
    userCompanyIds.add(user.getUserCompany().getUserCompanyId());
    final Integer defaultBillToCompanyId = user.getDefaultBillToCompanyId();
    if (nonNull(defaultBillToCompanyId)) {
      userCompanyIds.add(defaultBillToCompanyId);
    }

    return abUserRepository.getAbUser(userCompanyIds, abUserId);
  }

  public List<AbUserResponse> getAbUsers(Integer abCompanyId, Integer abUserGroupId, boolean includeBillToData, Instant lastModifiedDate,
                                         boolean includeDeleted, Integer skip, Integer limit) {

    var abUserIdsForGroupId = abUserRepository.getAbUserIdsForGroupId(abUserGroupId);

    User user = userService.getLoggedInUser();

    List<Integer> userCompanyIds = new ArrayList<>();
    userCompanyIds.add(user.getUserCompany().getUserCompanyId());

    if (includeBillToData) {
      userCompanyIds.add(user.getDefaultBillToCompanyId());
      userCompanyIds.add(user.getUserCompany().getUserCompanyId());
    }

    return abUserRepository.getAbUsers(userCompanyIds, abUserIdsForGroupId, null, null, abCompanyId, abUserGroupId, lastModifiedDate, includeDeleted, skip,
                                       limit);
  }

  public List<AbUserTypeResponse> getAbUserTypes() {
    return abUserRepository.getAbUserTypes();
  }

  public List<AbUserRole> getAbUserRoles() {
    return abUserRepository.getAbUserRoles();
  }

  public List<AbUserAutoResponse> getAbUsersAuto(String term, boolean includeBillToData, boolean onlyBillToData, List<Integer> userTypeIds,
                                                 List<Integer> abUserRoleIds, Integer abCompanyId, String order, int skip, int limit) {

    final User user = userService.getLoggedInUser();

    List<Integer> userCompanyIds = new ArrayList<>();

    if (includeBillToData) {
      userCompanyIds.add(user.getUserCompany().getUserCompanyId());
      userCompanyIds.add(user.getDefaultBillToCompanyId());
    } else if (onlyBillToData) {
      userCompanyIds.add(user.getDefaultBillToCompanyId());
    } else {
      userCompanyIds.add(user.getUserCompany().getUserCompanyId());
    }

    return abUserRepository.getAbUsersAuto(userCompanyIds, term, userTypeIds, abUserRoleIds, abCompanyId, order, skip, limit);
  }

  public AbCompany findAbCompanyById(final int abCompanyId) {
    final int companyId = UserUtil.getUserCompanyIdOrThrow();
    return abCompanyRepository.findByAbCompanyIdAndUserCompanyUserCompanyId(abCompanyId, companyId)
        .orElseThrow(() -> new ValidationException("ab_company_id", "You are not allowed to update this company"));
  }

  private AbUser findAbUserById(int abUserId) {
    final int companyId = UserUtil.getUserCompanyIdOrThrow();
    return abUserRepository.findAllByAbUserIdAndUserCompanyUserCompanyId(abUserId, companyId)
        .orElseThrow(() -> new ValidationException("ab_user_id", "The user was not found"));
  }

  public void sendToWebSocket(
      final String action,
      final List<Integer> abUserIds,
      final List<AbUserResponse> data) {

    webSocketService.sendToWebSocket(AB_USERS, buildWebsocketMessage(action, abUserIds, data));
  }

  private Map<String, Object> buildWebsocketMessage(final String action,
                                                    final List<Integer> abUserIds,
                                                    final List<AbUserResponse> data) {

    final Map<String, Object> message = new HashMap<>();
    message.put(ACTION, action);
    message.put("ab_user_ids", Converters.asCsList(abUserIds));
    if (!action.equals(DELETED) && data != null) {
      message.put(DATA, data);
    }
    return message;
  }



}
