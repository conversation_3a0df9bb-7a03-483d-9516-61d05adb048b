package com.bulkloads.web.addressbook.abuser.service.dto.transformers;


import com.bulkloads.common.jpa.nativejpa.QueryParts;
import com.bulkloads.web.addressbook.abuser.service.dto.AbUserResponse;
import org.hibernate.query.TupleTransformer;
import org.springframework.stereotype.Component;
import lombok.RequiredArgsConstructor;

@Component
@RequiredArgsConstructor
public class AbUserResponseTransformer implements TupleTransformer<AbUserResponse> {

  @Override
  public AbUserResponse transformTuple(Object[] columns, String[] aliases) {
    QueryParts parts = new QueryParts(columns, aliases);

    AbUserResponse response = new AbUserResponse();
    response.setAbUserId(parts.asInteger("ab_user_id"));
    response.setExternalAbUserId(parts.asString("external_ab_user_id"));
    response.setBlUserId(parts.asInteger("bl_user_id"));
    response.setAbCompanyId(parts.asInteger("ab_company_id"));
    response.setExternalAbCompanyId(parts.asString("external_ab_company_id"));
    response.setCompanyName(parts.asString("company_name"));
    response.setFirstName(parts.asString("first_name"));
    response.setLastName(parts.asString("last_name"));
    response.setPhone1(parts.asString("phone_1"));
    response.setEmail(parts.asString("email"));
    response.setPreferredContactMethod(parts.asString("preferred_contact_method"));
    response.setAbUserNotes(parts.asString("ab_user_notes"));
    response.setLocation(parts.asString("location"));
    response.setUserTypeIds(parts.asIntegerListFromCsv("user_type_ids"));
    response.setUserTypes(parts.asStringListFromCsv("user_types"));
    response.setActive(parts.asBoolean("active"));
    response.setBadEmailDate(parts.asInstant("bad_email_date"));
    response.setBadEmailReason(parts.asString("bad_email_reason"));
    response.setAbUserRoleIds(parts.asIntegerListFromCsv("ab_user_role_ids"));
    response.setAbUserRoles(parts.asStringListFromCsv("ab_user_roles"));
    response.setModifiedDate(parts.asInstant("modified_date"));
    response.setDeleted(parts.asBoolean("deleted"));
    response.setLastTruckUserCompanyEquipmentId(parts.asInteger("last_truck_user_company_equipment_id"));
    response.setLastTrailerUserCompanyEquipmentId(parts.asInteger("last_trailer_user_company_equipment_id"));
    return response;
  }

}
