package com.bulkloads.web.addressbook.abuser.repository;

import java.time.Instant;
import java.util.List;
import com.bulkloads.web.addressbook.abuser.service.dto.AbUserAutoResponse;
import com.bulkloads.web.addressbook.abuser.service.dto.AbUserResponse;
import com.bulkloads.web.addressbook.abuser.service.dto.AbUserTypeResponse;
import org.springframework.stereotype.Repository;

@Repository
public interface AbUserQueryRepository {

  AbUserResponse getAbUser(
      List<Integer> userCompanyIds,
      Integer abUserId
  );

  List<AbUserResponse> getAbUsersIncludingDeletedAbCompany(List<Integer> userCompanyIds, Integer abCompanyId);

  List<AbUserResponse> getAbUsers(
      List<Integer> userCompanyIds,
      List<Integer> abUserIds,
      String externalAbUserId,
      Integer blUserId,
      Integer abCompanyId,
      Integer abUserGroupId,
      Instant lastModifiedDate,
      boolean includeDeleted,
      Integer skip,
      Integer limit
  );


  List<AbUserAutoResponse> getAbUsersAuto(
      List<Integer> userCompanyIds,
      String term,
      List<Integer> userTypeIds,
      List<Integer> abUserRoleIds,
      Integer abCompanyId,
      String order,
      Integer skip,
      Integer limit);


  List<AbUserTypeResponse> getAbUserTypes();
}
