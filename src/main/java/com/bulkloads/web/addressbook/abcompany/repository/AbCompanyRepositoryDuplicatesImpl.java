package com.bulkloads.web.addressbook.abcompany.repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import com.bulkloads.common.jpa.JpaCustomQueryService;
import com.bulkloads.web.addressbook.abcompany.domain.entity.AbCompany;
import org.springframework.stereotype.Repository;
import lombok.RequiredArgsConstructor;

@Repository
@RequiredArgsConstructor
public class AbCompanyRepositoryDuplicatesImpl implements AbCompanyRepositoryDuplicates {

  private static final String FIND_DUPLICATE_QUERY = """
          select abc from AbCompany abc where
      
          abc.companyName = :companyName
          and abc.userCompany.userCompanyId = :userCompanyId
      
          <% params.put("companyName", companyName) %>
          <% params.put("userCompanyId", userCompanyId) %>
      
          <% if (paramExistsAdd("abCompanyId")) { %>
              and abc.abCompanyId <> :abCompanyId
          <% } %>
      
          <% if (paramExistsAdd("companyCode")) { %>
              and abc.companyCode = :companyCode
          <% } %>
      
          <% if (paramExistsAdd("externalAbCompanyId")) { %>
              and abc.externalAbCompanyId = :externalAbCompanyId
          <% } %>
      
          <% if (paramExistsAdd("externalAbCompanyType")) { %>
              and abc.externalAbCompanyType = :externalAbCompanyType
          <% } %>
      
          <% if (paramExistsAdd("censusNum")) { %>
              and abc.censusNum = :censusNum
          <% } %>
      
          <% if (paramExistsAdd("city")) { %>
              and abc.city = :city
          <% } %>
      
          <% if (paramExistsAdd("state")) { %>
              and abc.state = :state
          <% } %>
      
          <% if (paramExistsAdd("state")) { %>
              and abc.state = :state
          <% } %>
      
          <% if (paramExistsAdd("country")) { %>
              and abc.country = :country
          <% } %>
      
          <% if (paramExistsAdd("address")) { %>
              and abc.address = :address
          <% } %>
      
           <% if (paramExists("city") && paramExists("state") && paramExists("country")) { %>
              <% if (paramExistsAdd("location")) { %>
                  and abc.location = :location
              <% } %>
           <% } %>
      
          and abc.deleted = false
      """;

  private final JpaCustomQueryService jpaCustomQueryService;

  @Override
  public Optional<AbCompany> findDuplicate(Integer userCompanyId, AbCompany abCompany) {

    Map<String, Object> params = new HashMap<>();

    Objects.requireNonNull(abCompany.getCompanyName());
    params.put("userCompanyId", userCompanyId);

    // TODO: But also require not empty, can I use assertions?
    Objects.requireNonNull(abCompany.getCompanyName());
    params.put("companyName", abCompany.getCompanyName());

    params.put("abCompanyId", abCompany.getAbCompanyId());
    params.put("companyCode", abCompany.getCompanyCode());
    params.put("externalAbCompanyId", abCompany.getExternalAbCompanyId());
    params.put("externalAbCompanyType", abCompany.getExternalAbCompanyType());
    params.put("censusNum", abCompany.getCensusNum());
    params.put("city", abCompany.getCity());
    params.put("state", abCompany.getState());
    params.put("country", abCompany.getCountry());
    params.put("address", abCompany.getAddress());
    params.put("location", abCompany.getLocation());

    List<AbCompany> duplicateCandidates = jpaCustomQueryService.query(FIND_DUPLICATE_QUERY, params, AbCompany.class);

    return duplicateCandidates.stream()
        .filter(abc -> abc.getUserTypeIds().containsAll(abCompany.getUserTypeIds()))
        .findFirst();
  }
}
