package com.bulkloads.web.addressbook.abcompany.repository;

import static com.bulkloads.common.Converters.instantToSql;
import static com.bulkloads.web.addressbook.abcompany.repository.template.GetAbCompaniesAutoQueryTemplate.GET_AB_COMPANIES_AUTO_QUERY_TEMPLATE;
import static com.bulkloads.web.addressbook.abcompany.repository.template.GetAbCompaniesFinderQueryTemplate.GET_AB_COMPANIES_FINDER_QUERY_TEMPLATE;
import static com.bulkloads.web.addressbook.abcompany.repository.template.GetAbCompaniesQueryTemplate.GET_AB_COMPANIES_QUERY_TEMPLATE;
import java.time.Instant;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.bulkloads.common.jpa.nativejpa.JpaNativeQueryService;
import com.bulkloads.web.addressbook.abcompany.service.dto.AbCompanyAutoResponse;
import com.bulkloads.web.addressbook.abcompany.service.dto.AbCompanyFinderResponse;
import com.bulkloads.web.addressbook.abcompany.service.dto.AbCompanyListResponse;
import com.bulkloads.web.addressbook.abcompany.service.dto.AbCompanyResponse;
import com.bulkloads.web.addressbook.abcompany.service.dto.transformers.AbCompanyAutoResponseTransformer;
import com.bulkloads.web.addressbook.abcompany.service.dto.transformers.AbCompanyFinderResponseTransformer;
import com.bulkloads.web.addressbook.abcompany.service.dto.transformers.AbCompanyListResponseTransformer;
import com.bulkloads.web.addressbook.abcompany.service.dto.transformers.AbCompanyResponseTransformer;
import org.springframework.stereotype.Repository;
import lombok.RequiredArgsConstructor;

@Repository
@RequiredArgsConstructor
public class AbCompanyQueryRepositoryImpl implements AbCompanyQueryRepository {

  private final JpaNativeQueryService jpaNativeQueryService;
  private final AbCompanyResponseTransformer abCompanyResponseTransformer;
  private final AbCompanyListResponseTransformer abCompanyListResponseTransformer;
  private final AbCompanyFinderResponseTransformer abCompanyFinderResponseTransformer;
  private final AbCompanyAutoResponseTransformer abCompanyAutoResponseTransformer;

  @Override
  public AbCompanyResponse getAbCompany(
      final int userCompanyId,
      final Integer abCompanyId,
      final Integer defaultBillToCompanyId) {
    final Map<String, Object> params = new HashMap<>();

    params.put("userCompanyId", userCompanyId);
    params.put("abCompanyId", abCompanyId);
    params.put("defaultBillToCompanyId", defaultBillToCompanyId);

    return jpaNativeQueryService.queryForObject(GET_AB_COMPANIES_QUERY_TEMPLATE, params, abCompanyResponseTransformer);
  }

  @Override
  public List<AbCompanyListResponse> getAbCompanies(
      final int userCompanyId,
      final String authCode,
      final String externalAbCompanyId,
      final String externalAbCompanyType,
      final Integer censusNum,
      final String hasCensusNum,
      final String mcpMonitored,
      final String term,
      final List<Integer> userTypeIds,
      final Instant lastModifiedDate,
      final Boolean includeDeleted,
      final String order,
      final Integer skip,
      final Integer limit) {

    final Map<String, Object> params = new HashMap<>();
    params.put("userCompanyId", userCompanyId);
    params.put("authCode", authCode);
    params.put("externalAbCompanyId", externalAbCompanyId);
    params.put("externalAbCompanyType", externalAbCompanyType);
    params.put("censusNum", censusNum);
    params.put("hasCensusNum", hasCensusNum);
    params.put("mcpMonitored", mcpMonitored);
    params.put("term", term);
    params.put("userTypeIds", userTypeIds);
    params.put("lastModifiedDate", instantToSql(lastModifiedDate));
    params.put("includeDeleted", includeDeleted);
    params.put("order", order);
    params.put("skip", skip);
    params.put("limit", limit);

    return jpaNativeQueryService.query(GET_AB_COMPANIES_QUERY_TEMPLATE, params, abCompanyListResponseTransformer);
  }

  @Override
  public List<AbCompanyFinderResponse> getAbCompaniesFinder(
      final int userCompanyId,
      final String term,
      final List<Integer> userTypeIds) {

    final Map<String, Object> params = new HashMap<>();
    params.put("userCompanyId", userCompanyId);
    params.put("term", term);
    params.put("userTypeIds", userTypeIds);

    return jpaNativeQueryService.query(GET_AB_COMPANIES_FINDER_QUERY_TEMPLATE, params, abCompanyFinderResponseTransformer);
  }

  @Override
  public List<AbCompanyAutoResponse> getAbCompaniesAuto(
      final int userCompanyId,
      final String term,
      final Integer defaultBillToCompanyId,
      final List<Integer> userTypeIds,
      final String order,
      final int skip,
      final int limit) {

    final Map<String, Object> params = new HashMap<>();
    params.put("userCompanyId", userCompanyId);
    params.put("defaultBillToCompanyId", defaultBillToCompanyId);
    params.put("term", term);
    params.put("userTypeIds", userTypeIds);
    params.put("order", order);
    params.put("skip", skip);
    params.put("limit", limit);

    return jpaNativeQueryService.query(
        GET_AB_COMPANIES_AUTO_QUERY_TEMPLATE,
        params,
        abCompanyAutoResponseTransformer);
  }

}
