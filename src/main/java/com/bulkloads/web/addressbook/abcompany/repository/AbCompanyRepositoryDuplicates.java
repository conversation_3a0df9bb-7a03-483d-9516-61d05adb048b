package com.bulkloads.web.addressbook.abcompany.repository;

import java.util.Optional;
import com.bulkloads.web.addressbook.abcompany.domain.entity.AbCompany;
import org.springframework.data.repository.query.Param;

public interface AbCompanyRepositoryDuplicates {

  Optional<AbCompany> findDuplicate(@Param("userCompanyId") Integer userCompanyId,
                                    @Param("abCompany") AbCompany abCompany);
}
