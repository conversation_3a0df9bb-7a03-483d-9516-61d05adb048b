package com.bulkloads.web.addressbook.abcompany.service.dto;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;
import lombok.Builder;
import lombok.Data;
import lombok.extern.jackson.Jacksonized;

@Data
@Builder
@Jacksonized
public class AbCompanyRequest {

  Optional<Boolean> active;
  Optional<String> address;
  Optional<Boolean> apptRequired;

  Optional<Integer> censusNum;
  Optional<String> city;
  Optional<String> companyCode;
  Optional<String> companyEmail;
  Optional<String> companyName;
  Optional<String> companyNotes;
  Optional<String> companyPhone;
  Optional<String> country;
  Optional<String> directions;
  Optional<String> externalAbCompanyId;
  Optional<String> externalAbCompanyType;
  Optional<Integer> facilityId;
  Optional<Integer> insCargoAmount;
  Optional<String> insCargoCompany;
  Optional<String> insCargoContact;

  Optional<LocalDate> insCargoExpDate;

  Optional<String> insCargoNotes;
  Optional<String> insCargoPhone;
  Optional<String> insCargoPolicy;
  Optional<Boolean> insCargoSameAsLiab;
  Optional<Integer> insLiabAmount;
  Optional<String> insLiabCompany;
  Optional<String> insLiabContact;

  Optional<LocalDate> insLiabExpDate;

  Optional<String> insLiabNotes;
  Optional<String> insLiabPhone;
  Optional<String> insLiabPolicy;
  Optional<String> insuranceInfo;
  Optional<Integer> insWorkAmount;
  Optional<String> insWorkCompany;
  Optional<String> insWorkContact;

  Optional<LocalDate> insWorkExpDate;

  Optional<String> insWorkNotes;
  Optional<String> insWorkPhone;
  Optional<String> insWorkPolicy;
  Optional<Boolean> insWorkSameAsLiab;
  Optional<Double> latitude;
  Optional<String> location;
  Optional<Double> longitude;
  Optional<String> mailingAddress;
  Optional<String> mailingLocation;
  Optional<String> mailingCity;
  Optional<String> mailingState;
  Optional<String> mailingZip;
  Optional<String> mailingCountry;

  Optional<Boolean> mailingSameAsPhysical;

  Optional<String> mcNum;
  Optional<Boolean> mcpMonitored;
  Optional<String> privateNotes;
  Optional<String> receivingHours;
  Optional<String> riskAssessmentOverall;
  Optional<String> state;
  Optional<String> zip;
  Optional<String> userTypeIds;
  Optional<List<RestFileRequest>> files;

  @Builder.Default
  boolean validateLocation = true;

}
