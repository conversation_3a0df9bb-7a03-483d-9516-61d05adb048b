package com.bulkloads.web.addressbook.abcompany.service.dto.transformers;

import static com.bulkloads.web.addressbook.abcompany.service.dto.transformers.AbCompanyResponseTransformer.toResponse;
import com.bulkloads.common.jpa.nativejpa.QueryParts;
import com.bulkloads.web.addressbook.abcompany.service.dto.AbCompanyListResponse;
import org.hibernate.query.TupleTransformer;
import org.springframework.stereotype.Component;
import lombok.RequiredArgsConstructor;

@Component
@RequiredArgsConstructor
public class AbCompanyListResponseTransformer implements TupleTransformer<AbCompanyListResponse> {

  @Override
  public AbCompanyListResponse transformTuple(Object[] columns, String[] aliases) {
    QueryParts parts = new QueryParts(columns, aliases);
    return toResponse(parts);
  }

}
