package com.bulkloads.web.addressbook.abcompany.service.dto.transformers;


import com.bulkloads.common.jpa.nativejpa.QueryParts;
import com.bulkloads.web.addressbook.abcompany.service.dto.AbCompanyResponse;
import org.hibernate.query.TupleTransformer;
import org.springframework.stereotype.Component;
import lombok.RequiredArgsConstructor;

@Component
@RequiredArgsConstructor
public class AbCompanyResponseTransformer implements TupleTransformer<AbCompanyResponse> {

  @Override
  public AbCompanyResponse transformTuple(Object[] columns, String[] aliases) {
    QueryParts parts = new QueryParts(columns, aliases);
    return toResponse(parts);
  }

  static AbCompanyResponse toResponse(QueryParts parts) {
    AbCompanyResponse response = new AbCompanyResponse();
    response.setAbCompanyId(parts.asInteger("ab_company_id"));
    response.setExternalAbCompanyId(parts.asString("external_ab_company_id"));
    response.setExternalAbCompanyType(parts.asString("external_ab_company_type"));
    response.setAuthCode(parts.asString("auth_code"));
    response.setCompanyName(parts.asString("company_name"));
    response.setCompanyCode(parts.asString("company_code"));
    response.setUserTypeIds(parts.asIntegerListFromCsv("user_type_ids"));
    response.setUserTypes(parts.asStringListFromCsv("user_types"));
    response.setUserCompanyId(parts.asInteger("user_company_id"));
    response.setFacilityId(parts.asInteger("facility_id"));
    response.setCensusNum(parts.asInteger("census_num"));
    response.setMcNum(parts.asString("mc_num"));
    response.setCompanyPhone(parts.asString("company_phone"));
    response.setCompanyEmail(parts.asString("company_email"));
    response.setAddress(parts.asString("address"));
    response.setLocation(parts.asString("location"));
    response.setCity(parts.asString("city"));
    response.setState(parts.asString("state"));
    response.setZip(parts.asString("zip"));
    response.setCountry(parts.asString("country"));
    response.setLongitude(parts.asDouble("longitude"));
    response.setLatitude(parts.asDouble("latitude"));
    response.setMailingSameAsPhysical(parts.asBoolean("mailing_same_as_physical"));
    response.setMailingAddress(parts.asString("mailing_address"));
    response.setMailingLocation(parts.asString("mailing_location"));
    response.setMailingCity(parts.asString("mailing_city"));
    response.setMailingState(parts.asString("mailing_state"));
    response.setMailingZip(parts.asString("mailing_zip"));
    response.setMailingCountry(parts.asString("mailing_country"));
    response.setInsuranceInfo(parts.asString("insurance_info"));
    response.setCompanyNotes(parts.asString("company_notes"));
    response.setApptRequired(parts.asBoolean("appt_required"));
    response.setReceivingHours(parts.asString("receiving_hours"));
    response.setDirections(parts.asString("directions"));
    response.setPrivateNotes(parts.asString("private_notes"));
    response.setInsLiabCompany(parts.asString("ins_liab_company"));
    response.setInsLiabPolicy(parts.asString("ins_liab_policy"));
    response.setInsLiabExpDate(parts.asLocalDate("ins_liab_exp_date"));
    response.setInsLiabPhone(parts.asString("ins_liab_phone"));
    response.setInsLiabContact(parts.asString("ins_liab_contact"));
    response.setInsLiabAmount(parts.asInteger("ins_liab_amount"));
    response.setInsLiabNotes(parts.asString("ins_liab_notes"));
    response.setInsWorkSameAsLiab(parts.asBoolean("ins_work_same_as_liab"));
    response.setInsWorkCompany(parts.asString("ins_work_company"));
    response.setInsWorkPolicy(parts.asString("ins_work_policy"));
    response.setInsWorkExpDate(parts.asLocalDate("ins_work_exp_date"));
    response.setInsWorkPhone(parts.asString("ins_work_phone"));
    response.setInsWorkContact(parts.asString("ins_work_contact"));
    response.setInsWorkAmount(parts.asInteger("ins_work_amount"));
    response.setInsWorkNotes(parts.asString("ins_work_notes"));
    response.setInsCargoSameAsLiab(parts.asBoolean("ins_cargo_same_as_liab"));
    response.setInsCargoCompany(parts.asString("ins_cargo_company"));
    response.setInsCargoPolicy(parts.asString("ins_cargo_policy"));
    response.setInsCargoExpDate(parts.asLocalDate("ins_cargo_exp_date"));
    response.setInsCargoPhone(parts.asString("ins_cargo_phone"));
    response.setInsCargoContact(parts.asString("ins_cargo_contact"));
    response.setInsCargoAmount(parts.asInteger("ins_cargo_amount"));
    response.setInsCargoNotes(parts.asString("ins_cargo_notes"));
    response.setNumberOfFiles(parts.asInteger("number_of_files"));
    response.setRiskAssessmentOverall(parts.asString("risk_assessment_overall"));
    response.setMcpMonitored(parts.asBoolean("mcp_monitored"));
    response.setMcpDetailsUrl(parts.asString("mcp_details_url"));
    response.setMcpSyncDate(parts.asInstant("mcp_sync_date"));
    response.setModifiedDate(parts.asInstant("modified_date"));
    response.setActive(parts.asBoolean("active"));
    response.setDeleted(parts.asBoolean("deleted"));
    return response;
  }

}
