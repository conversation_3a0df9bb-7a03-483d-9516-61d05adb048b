package com.bulkloads.web.addressbook.abcompany.domain.entity;

import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;
import com.bulkloads.web.addressbook.abuser.domain.entity.AbUser;
import com.bulkloads.web.common.jpa.converter.CsvIntegerListConverter;
import com.bulkloads.web.common.jpa.converter.CsvStringListConverter;
import com.bulkloads.web.quickbooks.domain.entity.QbAbCompany;
import com.bulkloads.web.user.domain.entity.User;
import com.bulkloads.web.usercompany.domain.entity.UserCompany;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "ab_companies")
@Getter
@Setter
public class AbCompany {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "ab_company_id")
  private Integer abCompanyId;

  @Size(max = 50, message = "Enter up to 50 chars")
  @Column(name = "external_ab_company_id")
  private String externalAbCompanyId = "";

  @Size(max = 100, message = "Enter up to 100 chars")
  @Column(name = "external_ab_company_type")
  private String externalAbCompanyType = "";

  @Size(max = 45, message = "Enter up to 45 chars")
  @Column(name = "source")
  private String source = "";

  @Size(max = 45, message = "Enter up to 45 chars")
  @Column(name = "source_id")
  private String sourceId = "";

  @Column(name = "facility_id")
  private Integer facilityId;

  @Size(max = 150, message = "Enter up to 150 chars")
  @NotNull
  @NotEmpty
  @Column(name = "company_name")
  private String companyName = "";

  @Size(max = 20, message = "Enter up to 20 chars")
  @Column(name = "company_code")
  private String companyCode = "";

  @Column(name = "census_num")
  private Integer censusNum;

  @Size(max = 150, message = "Enter up to 20 chars")
  @Column(name = "mc_num")
  private String mcNum = "";

  @NotEmpty(message = "Select a company type")
  @Convert(converter = CsvIntegerListConverter.class)
  @Column(name = "user_type_ids")
  private List<Integer> userTypeIds = new ArrayList<>();

  @NotEmpty(message = "Select a company type")
  @Convert(converter = CsvStringListConverter.class)
  @Column(name = "user_types")
  private List<String> userTypes = new ArrayList<>();

  @Size(max = 25, message = "Enter up to 25 chars")
  @Column(name = "company_phone")
  private String companyPhone = "";

  @Column(name = "company_phone_type")
  private String companyPhoneType = "";

  @Email(message = "Enter a valid email")
  @Column(name = "company_email")
  private String companyEmail = "";

  @Size(max = 150, message = "Enter up to 150 chars")
  @Column(name = "address")
  private String address = "";

  @Size(max = 50, message = "Enter up to 50 chars")
  @Column(name = "state")
  private String state = "";

  @Column(name = "location")
  private String location = "";

  @Size(max = 80, message = "Enter up to 80 chars")
  @Column(name = "city")
  private String city = "";

  @Size(max = 25, message = "Enter up to 25 chars")
  @Column(name = "zip")
  private String zip = "";

  @Size(max = 50, message = "Enter up to 50 chars")
  @Column(name = "country")
  private String country = "";

  @Column(name = "longitude")
  private Double longitude;

  @Column(name = "latitude")
  private Double latitude;

  @Column(name = "timezone")
  private ZoneId timezone;

  @Column(name = "mailing_same_as_physical")
  private Boolean mailingSameAsPhysical = false;

  @Size(max = 150, message = "Enter up to 150 chars")
  @Column(name = "mailing_address")
  private String mailingAddress = "";

  @Column(name = "mailing_location")
  private String mailingLocation = "";

  @Size(max = 80, message = "Enter up to 80 chars")
  @Column(name = "mailing_city")
  private String mailingCity = "";

  @Size(max = 50, message = "Enter up to 50 chars")
  @Column(name = "mailing_state")
  private String mailingState = "";

  @Size(max = 25, message = "Enter up to 25 chars")
  @Column(name = "mailing_zip")
  private String mailingZip = "";

  @Size(max = 50, message = "Enter up to 50 chars")
  @Column(name = "mailing_country")
  private String mailingCountry = "";

  @Column(name = "private_notes")
  private String privateNotes = "";

  @Column(name = "appt_required")
  private Boolean apptRequired = false;

  @Size(max = 50, message = "Enter up to 50 chars")
  @Column(name = "receiving_hours")
  private String receivingHours = "";

  @Column(name = "directions")
  private String directions = "";

  @Column(name = "company_notes")
  private String companyNotes = "";

  @Size(max = 200, message = "Enter up to 200 chars")
  @Column(name = "insurance_info")
  private String insuranceInfo = "";

  @Size(max = 100, message = "Enter up to 100 chars")
  @Column(name = "ins_liab_company")
  private String insLiabCompany = "";

  @Size(max = 50, message = "Enter up to 50 chars")
  @Column(name = "ins_liab_policy")
  private String insLiabPolicy = "";

  @Column(name = "ins_liab_exp_date")
  private LocalDate insLiabExpDate;

  @Size(max = 25, message = "Enter up to 25 chars")
  @Column(name = "ins_liab_phone")
  private String insLiabPhone = "";

  @Size(max = 100, message = "Enter up to 100 chars")
  @Column(name = "ins_liab_contact")
  private String insLiabContact = "";

  @Column(name = "ins_liab_amount")
  private Integer insLiabAmount;

  @Column(name = "ins_liab_notes")
  private String insLiabNotes = "";

  @Column(name = "ins_work_same_as_liab")
  private Boolean insWorkSameAsLiab = true;

  @Size(max = 100, message = "Enter up to 100 chars")
  @Column(name = "ins_work_company")
  private String insWorkCompany = "";

  @Size(max = 50, message = "Enter up to 50 chars")
  @Column(name = "ins_work_policy")
  private String insWorkPolicy = "";

  @Column(name = "ins_work_exp_date")
  private LocalDate insWorkExpDate;

  @Size(max = 25, message = "Enter up to 25 chars")
  @Column(name = "ins_work_phone")
  private String insWorkPhone = "";

  @Size(max = 100, message = "Enter up to 100 chars")
  @Column(name = "ins_work_contact")
  private String insWorkContact = "";

  @Column(name = "ins_work_amount")
  private Integer insWorkAmount;

  @Column(name = "ins_work_notes")
  private String insWorkNotes = "";

  @Column(name = "ins_cargo_same_as_liab")
  private Boolean insCargoSameAsLiab = true;

  @Size(max = 100, message = "Enter up to 100 chars")
  @Column(name = "ins_cargo_company")
  private String insCargoCompany = "";

  @Size(max = 50, message = "Enter up to 50 chars")
  @Column(name = "ins_cargo_policy")
  private String insCargoPolicy = "";

  @Column(name = "ins_cargo_exp_date")
  private LocalDate insCargoExpDate;

  @Size(max = 25, message = "Enter up to 25 chars")
  @Column(name = "ins_cargo_phone")
  private String insCargoPhone = "";

  @Size(max = 100, message = "Enter up to 100 chars")
  @Column(name = "ins_cargo_contact")
  private String insCargoContact = "";

  @Column(name = "ins_cargo_amount")
  private Integer insCargoAmount;

  @Column(name = "ins_cargo_notes")
  private String insCargoNotes = "";

  @Column(name = "balance")
  private Double balance = 0.00;

  @Column(name = "freq")
  private int freq = 0;

  @Column(name = "number_of_files")
  private int numberOfFiles = 0;

  @Column(name = "risk_assessment_overall")
  private String riskAssessmentOverall = "";

  @Column(name = "mcp_monitored")
  private Boolean mcpMonitored = false;

  @Column(name = "mcp_details_url")
  private String mcpDetailsUrl = "";

  @Column(name = "mcp_sync_date")
  private Instant mcpSyncDate;

  @Column(name = "auth_code")
  private String authCode = "";

  @Column(name = "date_added")
  private Instant dateAdded = Instant.now();

  @Column(name = "modified_date")
  private Instant modifiedDate;

  @Column(name = "date_deleted")
  private Instant dateDeleted;

  @Column(name = "active")
  private Boolean active = true;

  @Column(name = "deleted")
  private Boolean deleted = false;

  @OneToOne(mappedBy = "abCompany", cascade = CascadeType.ALL, orphanRemoval = true)
  private QbAbCompany accountingLink;

  @ManyToOne(cascade = {CascadeType.PERSIST, CascadeType.MERGE, CascadeType.DETACH, CascadeType.REFRESH}, fetch = FetchType.LAZY)
  @JoinColumn(name = "user_id")
  private User user;

  @ManyToOne(cascade = {CascadeType.PERSIST, CascadeType.MERGE, CascadeType.DETACH, CascadeType.REFRESH}, fetch = FetchType.LAZY)
  @JoinColumn(name = "user_company_id")
  private UserCompany userCompany;

  @OneToMany(cascade = CascadeType.ALL, mappedBy = "abCompany", fetch = FetchType.LAZY)
  private List<AbUser> abUsers = new ArrayList<>();

  @OneToMany(cascade = CascadeType.ALL, mappedBy = "abCompany", fetch = FetchType.LAZY, orphanRemoval = true)
  private List<AbCompanyFile> abCompanyFiles = new ArrayList<>();

  @Transient
  private boolean requestMcpMonitoring = false;

  @Transient
  private boolean cancelMcpMonitoring = false;

  @Transient
  private Integer censusNumToCancel = null;

}
