package com.bulkloads.web.addressbook.abcompany.repository;

import java.util.List;
import java.util.Optional;
import com.bulkloads.web.addressbook.abcompany.domain.entity.AbCompany;
import com.bulkloads.web.file.domain.entity.File;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.ListCrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface AbCompanyRepository extends
    ListCrudRepository<AbCompany, Integer>,
    AbCompanyRepositoryDuplicates,
    AbCompanyQueryRepository {

  @Query("""
      select f from AbCompany abc
        join abc.abCompanyFiles abcf
        join abcf.file f
      where
        abcf.abCompany.abCompanyId = :abCompanyId
        and abc.deleted = false
        order by abcf.fileOrder
      """)
  List<File> findFilesByAbCompanyId(@Param("abCompanyId") int abCompanyId);


  Optional<AbCompany> findByAbCompanyIdAndUserCompanyUserCompanyId(final int abCompanyId, final int userCompanyId);

  Optional<AbCompany> findByAbCompanyIdAndUserCompanyUserCompanyIdIn(final int abCompanyIds, final List<Integer> userCompanyIds);

  Optional<AbCompany> findByUserCompanyUserCompanyIdAndExternalAbCompanyId(
      Integer userCompanyId,
      String externalAbCompanyId
  );
}
