package com.bulkloads.web.addressbook.abcompany.repository.template;

public class GetAbCompaniesFinderQueryTemplate {

  public static final String GET_AB_COMPANIES_FINDER_QUERY_TEMPLATE = """
        <% if (paramIsTrue("unionBl")) { %>
        (
        <% } %>
            select
                facility_id,
                name as company_name,
                location as company_location_name,
                cast(NULL as DECIMAL) as census_num,
                '' as mc_num,

                email as company_email,
                phone_1 as company_phone,
                '40' as user_type_ids,
                'Shipper' as user_types,

                address as address,
                location as location,
                city as city,
                state as state,
                zip as zip,
                country as country,
                latitude as latitude,
                longitude as longitude,

                mailing_address as mailing_address,
                mailing_location as mailing_location,
                mailing_city as mailing_city,
                mailing_state as mailing_state,
                mailing_zip as mailing_zip,
                mailing_country as mailing_country

            from facilities
            where deleted = 0
                
              <% if (paramExists("term")) {
                  var wildTerms = term.split("\\s+")
                  for (int i = 0; i < wildTerms.length; i++) {
                      var part = "wildTerms_" + i
                      params.put(part, "%"+wildTerms[i]+"%")
                  %>
                    and (
                      name like :<% print(part) %>
                      or location like :<% print(part) %>
                    )
                <% } %>
              <% } %>
                
            order by name asc
            limit 15

        <% if (paramIsTrue("unionBl")) { %>
        )
        UNION ALL
        (
            select
                cast(NULL as DECIMAL) as facility_id,
                company_name,
                '' as company_location_name,
                c.census_num,
                c.mc_num,

                u.email as company_email,
                u.phone_1 as company_phone,
                c.user_type_ids,
                c.user_types,

                u.address,

                concat(u.city,', ',u.state,if(u.zip = '','',concat(' ',u.zip)),if(u.country = 'US','',concat(', ',u.country))) as location,
                u.city,
                u.state,
                u.zip,
                u.country,
                u.latitude,
                u.longitude,

                '' as mailing_address,
                '' as mailing_location,
                '' as mailing_city,
                '' as mailing_state,
                '' as mailing_zip,
                '' as mailing_country

            from user_company c
                INNER JOIN user_info u ON u.user_id = c.company_owner_id

            where c.merged_to is null
            and c.deletion_date is null
            
            <% if (paramExists("userTypeIds")) { %>
              AND (
                <% for (int i = 0; i < userTypeIds.size(); i++) {
                    var part = "userTypeId_" + i
                    params.put(part, userTypeIds.get(i))
                %>
                    find_in_set(:<% print(part) %>, c.user_type_ids)
                    <% if (i != userTypeIds.size() - 1) { %> OR <% } %>
                <% } %>
              )
            <% } %>

            <% if (paramExistsAdd("cId")) { %>
                and c.user_company_id not in
                    (select blocked_user_company_id from blocked_companies where user_company_id = :cId)
                and c.user_company_id not in
                    (select user_company_id from blocked_companies where blocked_user_company_id = :cId)
            <% } %>
          
            <% if (paramExists("term")) {
                var wildTerms = term.split("\\s+")
                for (int i = 0; i < wildTerms.length; i++) {
                    var part = "wildTerms_"+i
                    params.put(part, "%"+wildTerms[i]+"%")
            %>
                and c.company_name like :<% print(part) %>
                <% } %>
            <% } %>

            order by company_name
            limit 15
        )
        <% } %>
      """;


}
