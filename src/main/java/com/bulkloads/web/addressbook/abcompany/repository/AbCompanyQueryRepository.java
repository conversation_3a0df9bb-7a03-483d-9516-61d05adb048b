package com.bulkloads.web.addressbook.abcompany.repository;

import java.time.Instant;
import java.util.List;
import com.bulkloads.web.addressbook.abcompany.service.dto.AbCompanyAutoResponse;
import com.bulkloads.web.addressbook.abcompany.service.dto.AbCompanyFinderResponse;
import com.bulkloads.web.addressbook.abcompany.service.dto.AbCompanyListResponse;
import com.bulkloads.web.addressbook.abcompany.service.dto.AbCompanyResponse;
import org.springframework.stereotype.Repository;

@Repository
public interface AbCompanyQueryRepository {

  AbCompanyResponse getAbCompany(
      final int userCompanyId,
      final Integer abCompanyId,
      final Integer defaultBillToCompanyId);

  List<AbCompanyListResponse> getAbCompanies(
      final int userCompanyId,
      final String authCode,
      final String externalAbCompanyId,
      final String externalAbCompanyType,
      final Integer censusNum,
      final String hasCensusNum,
      final String mcpMonitored,
      final String term,
      final List<Integer> userTypeIds,
      final Instant lastModifiedDate,
      final Boolean includeDeleted,
      final String order,
      final Integer skip,
      final Integer limit);

  List<AbCompanyFinderResponse> getAbCompaniesFinder(
      final int userCompanyId,
      final String term,
      final List<Integer> userTypeIds);

  List<AbCompanyAutoResponse> getAbCompaniesAuto(
      final int userCompanyId,
      final String term,
      final Integer defaultBillToCompanyId,
      final List<Integer> userTypeIds,
      final String order,
      final int skip,
      final int limit);
}
