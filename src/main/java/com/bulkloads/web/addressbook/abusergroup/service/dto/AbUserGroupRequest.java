package com.bulkloads.web.addressbook.abusergroup.service.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Value;
import lombok.extern.jackson.Jacksonized;

@Value
@Builder
@Jacksonized
public class AbUserGroupRequest {

  @NotNull
  @Schema(name = "group_name", requiredMode = Schema.RequiredMode.REQUIRED)
  String groupName;

  @NotNull
  @Schema(name = "ab_user_ids", requiredMode = Schema.RequiredMode.REQUIRED)
  String abUserIds;
}
