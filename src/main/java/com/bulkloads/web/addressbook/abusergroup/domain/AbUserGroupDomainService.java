package com.bulkloads.web.addressbook.abusergroup.domain;

import static com.bulkloads.common.validation.ValidationMethod.CREATE;
import static com.bulkloads.common.validation.ValidationMethod.UPDATE;
import java.time.Instant;
import com.bulkloads.common.BaseDomainService;
import com.bulkloads.common.UserUtil;
import com.bulkloads.common.validation.Result;
import com.bulkloads.common.validation.ValidationMethod;
import com.bulkloads.web.addressbook.abusergroup.domain.data.AbUserGroupData;
import com.bulkloads.web.addressbook.abusergroup.domain.entity.AbUserGroup;
import com.bulkloads.web.addressbook.abusergroup.mapper.AbUserGroupMapper;
import com.bulkloads.web.addressbook.abusergroup.repository.AbUserGroupRepository;
import com.bulkloads.web.user.domain.entity.User;
import com.bulkloads.web.user.service.UserService;
import org.springframework.stereotype.Component;
import lombok.RequiredArgsConstructor;

@Component
@RequiredArgsConstructor
public class AbUserGroupDomainService extends BaseDomainService<AbUserGroup> {

  private final AbUserGroupRepository abUserGroupRepository;
  private final UserService userService;
  private final AbUserGroupMapper mapper;

  public Result<AbUserGroup> create(final AbUserGroupData data) {
    final AbUserGroup abUserGroup = new AbUserGroup();
    return super.validate(abUserGroup, null, data, CREATE);
  }

  public Result<AbUserGroup> update(final AbUserGroup abUserGroup, final AbUserGroupData data) {
    return super.validate(abUserGroup, null, data, UPDATE);
  }

  @Override
  public void validateDataAndMapToEntity(final Result<AbUserGroup> result,
                                         final AbUserGroup entity,
                                         final AbUserGroup existing, Object data,
                                         final ValidationMethod method) {

    final User user = userService.getLoggedInUser();
    final Instant now = Instant.now();

    if (method == CREATE) {
      entity.setUser(user);
      entity.setUserCompany(user.getUserCompany());
      entity.setDateAdded(now);
    }
  }

  @Override
  public void mapToEntityAuto(Object data, AbUserGroup entity) {
    mapper.dataToEntity((AbUserGroupData) data, entity);
  }

  @Override
  public void validateEntity(final Result<AbUserGroup> result, final AbUserGroup entity) {
    final int companyId = UserUtil.getUserCompanyIdOrThrow();

    final String groupName = entity.getGroupName();
    final Integer abUserGroupId = entity.getAbUserGroupId();
    final AbUserGroup duplicate = abUserGroupRepository.findDuplicate(companyId, groupName, abUserGroupId);

    if (duplicate != null) {
      result.addError("group_name", "You already have a group with that name");
    }
  }
}

