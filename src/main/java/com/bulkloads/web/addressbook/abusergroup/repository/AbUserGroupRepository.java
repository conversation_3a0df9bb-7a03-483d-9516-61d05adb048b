package com.bulkloads.web.addressbook.abusergroup.repository;

import java.util.List;
import java.util.Optional;
import com.bulkloads.web.addressbook.abusergroup.domain.entity.AbUserGroup;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface AbUserGroupRepository extends JpaRepository<AbUserGroup, Integer> {

  List<AbUserGroup> findAllByUserCompanyUserCompanyIdAndDeletedFalse(final int userCompanyId);

  Optional<AbUserGroup> findByAbUserGroupIdAndUserCompanyUserCompanyId(final int abUserGroupId, final int userCompanyId);

  @Query("""
          select e from AbUserGroup e
          where
              e.groupName = :groupName
              and e.deleted = false
              and e.userCompany.userCompanyId = :userCompanyId
              and ( :abUserGroupId is null or e.abUserGroupId <> :abUserGroupId )
      """)
  AbUserGroup findDuplicate(
      @Param("userCompanyId") final Integer userCompanyId,
      @Param("groupName") final String groupName,
      @Param("abUserGroupId") final Integer abUserGroupId
  );

  Optional<AbUserGroup> findById(Integer id);
}
