package com.bulkloads.web.loadinvoice.api;

import static com.bulkloads.common.mui.model.QueryParams.getFieldInfoFromClass;
import java.util.List;
import java.util.Map;
import com.bulkloads.common.mui.model.FieldInfo;
import com.bulkloads.common.mui.model.QueryParams;
import com.bulkloads.web.loadinvoice.service.LoadInvoiceService;
import com.bulkloads.web.loadinvoice.service.dto.LoadInvoicePayableByCompanyResponse;
import com.bulkloads.web.loadinvoice.service.dto.LoadInvoicePayableResponse;
import com.bulkloads.web.loadinvoice.service.dto.LoadInvoiceReceivableByCompanyResponse;
import com.bulkloads.web.loadinvoice.service.dto.LoadInvoiceReceivableResponse;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.constraints.PositiveOrZero;
import lombok.RequiredArgsConstructor;

@RestController
@Tag(name = "Loads Invoices")
@RequiredArgsConstructor
@RequestMapping("/rest/loads/invoices")
public class LoadInvoiceQueryController {

  private static final Map<String, FieldInfo> LOAD_INVOICE_PAYABLE_BY_COMPANY_FIELD_NAME_CACHE =
      getFieldInfoFromClass(LoadInvoicePayableByCompanyResponse.class);

  private static final Map<String, FieldInfo> LOAD_INVOICE_PAYABLE_FIELD_NAME_CACHE =
      getFieldInfoFromClass(LoadInvoicePayableResponse.class);

  private static final Map<String, FieldInfo> LOAD_INVOICE_RECEIVABLE_BY_COMPANY_FIELD_NAME_CACHE =
      getFieldInfoFromClass(LoadInvoiceReceivableByCompanyResponse.class);

  private static final Map<String, FieldInfo> LOAD_INVOICE_RECEIVABLE_FIELD_NAME_CACHE =
      getFieldInfoFromClass(LoadInvoiceReceivableResponse.class);

  private final LoadInvoiceService loadInvoiceService;

  @GetMapping(value = "/payables/by_company")
  public List<LoadInvoicePayableByCompanyResponse> getLoadInvoicePayablesByCompany(
      HttpServletRequest request,
      @RequestParam(value = "archived", required = false) Boolean archived,
      @Parameter(description = "the number of records to skip, defaults to 0")
      @PositiveOrZero(message = "Must be 0 or positive")
      @RequestParam(value = "skip", defaultValue = "0") int skip,
      @Parameter(description = "the number of records to return, defaults to 100")
      @PositiveOrZero(message = "Must be 0 or positive")
      @RequestParam(value = "limit", defaultValue = "100") int limit) {

    return loadInvoiceService.getLoadInvoicePayablesByCompany(
        archived,
        new QueryParams(request.getQueryString(), LOAD_INVOICE_PAYABLE_BY_COMPANY_FIELD_NAME_CACHE), skip,
        limit);
  }

  @GetMapping(value = "/payables")
  public List<LoadInvoicePayableResponse> getLoadInvoicePayables(
      HttpServletRequest request,
      @RequestParam(value = "archived", required = false) Boolean archived,
      @RequestParam(value = "user_company_id", required = false) Integer userCompanyId,
      @RequestParam(value = "user_id", required = false) Integer userId,
      @Parameter(description = "the number of records to skip, defaults to 0")
      @PositiveOrZero(message = "Must be 0 or positive")
      @RequestParam(value = "skip", defaultValue = "0") int skip,
      @Parameter(description = "the number of records to return, defaults to 100")
      @PositiveOrZero(message = "Must be 0 or positive")
      @RequestParam(value = "limit", defaultValue = "100") int limit) {

    return loadInvoiceService.getLoadInvoicePayables(archived, userCompanyId, userId,
        skip, limit, new QueryParams(request.getQueryString(), LOAD_INVOICE_PAYABLE_FIELD_NAME_CACHE));
  }

  @GetMapping(value = "/receivables/by_company")
  public List<LoadInvoiceReceivableByCompanyResponse> getLoadInvoiceReceivablesByCompany(
      HttpServletRequest request,
      @RequestParam(value = "archived", required = false) Boolean archived,
      @Parameter(description = "the number of records to skip, defaults to 0")
      @PositiveOrZero(message = "Must be 0 or positive")
      @RequestParam(value = "skip", defaultValue = "0") int skip,
      @Parameter(description = "the number of records to return, defaults to 100")
      @PositiveOrZero(message = "Must be 0 or positive")
      @RequestParam(value = "limit", defaultValue = "100") int limit) {

    return loadInvoiceService.getLoadInvoiceReceivablesByCompany(archived,
                                                                 new QueryParams(request.getQueryString(), LOAD_INVOICE_RECEIVABLE_BY_COMPANY_FIELD_NAME_CACHE),
                                                                 skip, limit);
  }

  @GetMapping(value = "/receivables")
  public List<LoadInvoiceReceivableResponse> getLoadInvoiceReceivables(
      HttpServletRequest request,
      @RequestParam(value = "archived", required = false) Boolean archived,
      @RequestParam(value = "bill_to_user_company_id", required = false)
      Integer billToUserCompanyId,
      @RequestParam(value = "bill_to_ab_company_id", required = false) Integer billToAbCompanyId,
      @Parameter(description = "the number of records to skip, defaults to 0")
      @PositiveOrZero(message = "Must be 0 or positive")
      @RequestParam(value = "skip", defaultValue = "0") int skip,
      @Parameter(description = "the number of records to return, defaults to 100")
      @PositiveOrZero(message = "Must be 0 or positive")
      @RequestParam(value = "limit", defaultValue = "100") int limit) {

    return loadInvoiceService.getLoadInvoiceReceivables(archived, billToUserCompanyId, billToAbCompanyId,
                                                        new QueryParams(request.getQueryString(), LOAD_INVOICE_RECEIVABLE_FIELD_NAME_CACHE), skip, limit);
  }
}
