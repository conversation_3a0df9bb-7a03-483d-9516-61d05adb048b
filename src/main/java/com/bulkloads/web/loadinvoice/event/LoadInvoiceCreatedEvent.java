package com.bulkloads.web.loadinvoice.event;

import static com.bulkloads.config.AppConstants.InvoiceAction.LOAD_INVOICE_CREATE;

import java.util.List;
import lombok.Getter;

@Getter
public class LoadInvoiceCreatedEvent extends LoadInvoiceEvent {

  private final String fileUrl;
  private final List<Integer> loadAssignmentIds;
  private final boolean sendLoadInvoice;
  private final String message;

  public LoadInvoiceCreatedEvent(final int loadInvoiceId,
                                 final String fileUrl,
                                 final List<Integer> loadAssignmentIds,
                                 final boolean sendLoadInvoice,
                                 final String message) {
    super(loadInvoiceId, LOAD_INVOICE_CREATE);
    this.fileUrl = fileUrl;
    this.loadAssignmentIds = loadAssignmentIds;
    this.sendLoadInvoice = sendLoadInvoice;
    this.message = message;
  }
}
