//package com.bulkloads.web.loadinvoice.handler;
//
//import static com.bulkloads.config.AppConstants.AssignmentStatus.COMPLETED;
//import static com.bulkloads.config.AppConstants.AssignmentStatus.DELIVERED;
//import static java.util.Objects.isNull;
//import static java.util.Objects.nonNull;
//import java.util.List;
//import java.util.Map;
//import com.bulkloads.common.jpa.JpaUtils;
//import com.bulkloads.web.assignment.domain.entity.Assignment;
//import com.bulkloads.web.assignment.event.BookingUpdatedEvent;
//import com.bulkloads.web.assignment.repository.AssignmentRepository;
//import com.bulkloads.web.loadinvoice.service.LoadInvoiceService;
//import org.springframework.stereotype.Component;
//import org.springframework.transaction.event.TransactionPhase;
//import org.springframework.transaction.event.TransactionalEventListener;
//import lombok.RequiredArgsConstructor;
//import lombok.extern.slf4j.Slf4j;
//
//@Slf4j
//@Component
//@RequiredArgsConstructor
//public class AssignmentLoadInvoiceEventHandler {
//
//  private final AssignmentRepository assignmentRepository;
//  private final LoadInvoiceService loadInvoiceService;
//  private final JpaUtils jpaUtils;
//
//  private static final List<String> REGENERATION_INVOICE_DOCUMENT_FIELDS = List.of(
//      "billToAbCompany",
//      "billToAbUser",
//      "rate",
//      "rateType",
//      "hauledNotes",
//      "hauledDate",
//      "billWeight",
//      "billVolume",
//      "billMiles",
//      "billHours",
//      "billQuantity",
//      "billSubtotal",
//      "billSurcharges"
//  );
//
//  @TransactionalEventListener(
//      classes = BookingUpdatedEvent.class,
//      phase = TransactionPhase.BEFORE_COMMIT)
//  public void handleBookingUpdatedEvent(final BookingUpdatedEvent event) {
//    log.trace("handleBookingUpdatedEvent: {}", event);
//    final int firstAssignmentId = event.getLoadAssignmentIds().get(0);
//    final Assignment booking = assignmentRepository.getReferenceById(firstAssignmentId);
//
//    final Integer loadInvoiceId = booking.getLoadInvoiceId();
//    final String assignmentStatus = booking.getAssignmentStatus();
//    final Map<String, Object> dirtyFields = jpaUtils.findDirtyFields(booking);
//    final boolean shouldReGenerateInvoice = shouldReGenerateInvoice(booking, dirtyFields);
//    final boolean alteredFiles = dirtyFields.containsKey("assignmentFiles");
//    final boolean statusNonDeliveredAndNonCompleted = List.of(DELIVERED, COMPLETED).contains(assignmentStatus);
//    final boolean shouldReBundleInvoice = !shouldReGenerateInvoice && statusNonDeliveredAndNonCompleted
//        && nonNull(booking.getHiringAbCompany()) && nonNull(loadInvoiceId) && alteredFiles;
//
//    if (shouldReGenerateInvoice) {
//      loadInvoiceService.reGenerateInvoiceAsync(firstAssignmentId, loadInvoiceId);
//    }
//
//    if (shouldReBundleInvoice) {
//      loadInvoiceService.reBundleInvoiceAsync(firstAssignmentId, loadInvoiceId);
//    }
//
//    if (statusNonDeliveredAndNonCompleted && isNull(booking.getLoadInvoiceId())) {
//      loadInvoiceService.createInvoiceAsync(firstAssignmentId);
//    }
//  }
//
//  private boolean shouldReGenerateInvoice(final Assignment assignment, final Map<String, Object> dirtyFields) {
//    return nonNull(assignment.getLoadInvoiceId()) && REGENERATION_INVOICE_DOCUMENT_FIELDS.stream()
//        .anyMatch(dirtyFields::containsKey);
//  }
//}
