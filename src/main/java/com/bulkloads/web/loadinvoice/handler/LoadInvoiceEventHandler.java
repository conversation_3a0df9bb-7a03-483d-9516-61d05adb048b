package com.bulkloads.web.loadinvoice.handler;

import com.bulkloads.web.loadinvoice.event.LoadInvoiceCreatedEvent;
import com.bulkloads.web.loadinvoice.service.LoadInvoiceService;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
@RequiredArgsConstructor
public class LoadInvoiceEventHandler {

  private final LoadInvoiceService loadInvoiceService;

  @Order(30)
  @TransactionalEventListener(
      classes = LoadInvoiceCreatedEvent.class,
      phase = TransactionPhase.BEFORE_COMMIT)
  public void handleLoadInvoiceCreatedEvent(final LoadInvoiceCreatedEvent event) {
    log.trace("handleLoadInvoiceCreatedEvent: {}", event);

    if (event.isSendLoadInvoice()) {
      loadInvoiceService.sendLoadInvoice(event.getLoadInvoiceId(), event.getMessage(), false);
    }
  }
}
