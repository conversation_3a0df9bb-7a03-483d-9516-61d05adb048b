package com.bulkloads.web.loadinvoice.service.dto;

import java.math.BigDecimal;
import java.time.Instant;
import lombok.Builder;
import lombok.Value;

@Value
@Builder
public class LoadInvoiceReceivableResponse {
  Integer loadInvoiceId;
  String billTo;
  String billToCompanyName;
  String email;
  Instant invoiceDate;
  BigDecimal invoiceTotal;
  String invoiceFileUrl;
  BigDecimal claimedPay;
  BigDecimal verifiedPay;
  Boolean sent;
  Boolean paid;
  Boolean hasDeleted;
}