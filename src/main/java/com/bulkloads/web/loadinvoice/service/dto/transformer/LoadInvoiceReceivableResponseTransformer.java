package com.bulkloads.web.loadinvoice.service.dto.transformer;

import com.bulkloads.common.jpa.nativejpa.QueryParts;
import com.bulkloads.web.loadinvoice.service.dto.LoadInvoiceReceivableResponse;
import org.hibernate.query.TupleTransformer;
import org.springframework.stereotype.Component;
import lombok.RequiredArgsConstructor;

@Component
@RequiredArgsConstructor
public class LoadInvoiceReceivableResponseTransformer implements TupleTransformer<LoadInvoiceReceivableResponse> {

  @Override
  public LoadInvoiceReceivableResponse transformTuple(Object[] columns, String[] aliases) {
    QueryParts parts = new QueryParts(columns, aliases);
    return LoadInvoiceReceivableResponse.builder()
        .loadInvoiceId(parts.asInteger("load_invoice_id"))
        .billTo(parts.asString("bill_to"))
        .billToCompanyName(parts.asString("bill_to_company_name"))
        .email(parts.asString("email"))
        .invoiceDate(parts.asInstant("invoice_date"))
        .invoiceTotal(parts.asBigDecimal("invoice_total"))
        .invoiceFileUrl(parts.asString("invoice_file_url"))
        .claimedPay(parts.asBigDecimal("claimed_pay"))
        .verifiedPay(parts.asBigDecimal("verified_pay"))
        .sent(parts.asBoolean("sent"))
        .paid(parts.asBoolean("paid"))
        .hasDeleted(parts.asBoolean("has_deleted"))
        .build();
  }
}
