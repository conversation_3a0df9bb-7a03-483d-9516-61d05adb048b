package com.bulkloads.web.loadinvoice.service.dto;

import java.math.BigDecimal;
import java.time.Instant;
import lombok.Builder;
import lombok.Value;

@Value
@Builder
public class LoadInvoicePayableResponse {
  Integer loadInvoiceId;
  String from;
  String companyName;
  String email;
  Instant invoiceDate;
  BigDecimal invoiceTotal;
  String invoiceFileUrl;
  BigDecimal payment;
  Boolean paid;
  Boolean hasDeleted;
  String settlementFileUrl;
}