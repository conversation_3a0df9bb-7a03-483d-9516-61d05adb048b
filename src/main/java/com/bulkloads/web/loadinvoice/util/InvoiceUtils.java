package com.bulkloads.web.loadinvoice.util;

import static com.bulkloads.common.PdfUtils.convertImageToPdf;
import static com.bulkloads.common.PdfUtils.mergePdfFiles;
import static com.bulkloads.common.StringUtil.getUuid;
import static com.bulkloads.web.file.util.FileUtils.downloadFile;
import java.io.IOException;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import com.bulkloads.common.download.FileDownload;
import com.bulkloads.common.download.TempDirectory;
import com.bulkloads.web.loadinvoice.domain.entity.LoadInvoice;
import com.bulkloads.web.loadinvoice.service.InvoicePart;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class InvoiceUtils {

  public static String buildInvoiceDocumentFileName(LoadInvoice loadInvoice) {
    Objects.requireNonNull(loadInvoice);
    Objects.requireNonNull(loadInvoice.getLoadInvoiceId());

    String filename = String.format("%s-%s_Inv_%d.pdf",
                                    getUuid(),
                                    loadInvoice.getCompanyName().replace(" ", "_"),
                                    loadInvoice.getLoadInvoiceId())
        .replaceAll("[^0-9A-Za-z-._]", "");

    if (filename.length() > 100) {
      filename = filename.substring(filename.length() - 100);
    }
    return filename;
  }

  public static void mergePdfAndImageFiles(Path pdfFilePath, List<InvoicePart> invoiceParts) {
    if (invoiceParts.isEmpty()) {
      return;
    }

    try (TempDirectory tempDirectory = new TempDirectory()) {
      log.debug("Working with temporary directory: {}", tempDirectory.getPath());

      // Limit concurrent downloads
      int maxConcurrent = Math.min(4, invoiceParts.size());
      ExecutorService executor = Executors.newFixedThreadPool(maxConcurrent);
      List<CompletableFuture<Void>> futures = new ArrayList<>();

      for (FileDownload part : invoiceParts) {
        futures.add(CompletableFuture.runAsync(
            () -> {
              Path path = downloadFile(part.getFileUrl(), tempDirectory.getPath());
              part.setFilePath(path);
            },
            executor
        ));
      }
      join(futures);
      executor.shutdown();

      List<Path> mergePaths = Stream.concat(
              Stream.of(pdfFilePath),
              invoiceParts.parallelStream()
                  .map(invoicePart -> {
                    Path attachmentPath = invoicePart.getFilePath();
                    String pdfFileName = attachmentPath.getFileName().toString().replaceAll("\\.[^.]+$", ".pdf");
                    Path imagePdfPath = attachmentPath.resolveSibling(pdfFileName);
                    if (invoicePart.isImage()) {
                      convertImageToPdf(attachmentPath, imagePdfPath);
                      return imagePdfPath;
                    }
                    return attachmentPath;
                  })
          )
          .collect(Collectors.toList());

      mergePdfFiles(mergePaths, pdfFilePath);

    } catch (IOException e) {
      throw new RuntimeException("Error managing temporary files", e);
    }
  }

  private static void join(List<CompletableFuture<Void>> futures) {
    CompletableFuture
        .allOf(futures.toArray(new CompletableFuture[0]))
        .join();
  }

}
