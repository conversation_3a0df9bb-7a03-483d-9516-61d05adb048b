package com.bulkloads.web.loadinvoice.domain.entity;

import static com.bulkloads.common.validation.ValidationUtils.exists;
import static java.util.Objects.isNull;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import com.bulkloads.web.addressbook.abcompany.domain.entity.AbCompany;
import com.bulkloads.web.addressbook.abuser.domain.entity.AbUser;
import com.bulkloads.web.common.event.DomainEvent;
import com.bulkloads.web.file.domain.entity.File;
import com.bulkloads.web.quickbooks.domain.entity.QbBill;
import com.bulkloads.web.quickbooks.domain.entity.QbInvoice;
import com.bulkloads.web.user.domain.entity.User;
import com.bulkloads.web.usercompany.domain.entity.UserCompany;
import org.hibernate.annotations.DynamicUpdate;
import org.springframework.data.domain.AbstractAggregateRoot;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Table;
import jakarta.validation.constraints.DecimalMax;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;

@Entity
@Getter
@Setter
@DynamicUpdate
@Table(name = "load_invoices")
public class LoadInvoice extends AbstractAggregateRoot<LoadInvoice> {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "load_invoice_id")
  private Integer loadInvoiceId;

  @Column(name = "load_invoice_payment_id")
  private Integer loadInvoicePaymentId;

  @NotNull
  @ManyToOne(cascade = {CascadeType.PERSIST, CascadeType.MERGE, CascadeType.DETACH, CascadeType.REFRESH}, fetch = FetchType.LAZY)
  @JoinColumn(name = "user_id")
  private User user;

  @NotNull
  @ManyToOne(cascade = {CascadeType.PERSIST, CascadeType.MERGE, CascadeType.DETACH, CascadeType.REFRESH}, fetch = FetchType.LAZY)
  @JoinColumn(name = "user_company_id")
  private UserCompany userCompany;

  @Column(name = "ab_company_id")
  private Integer abCompanyId;

  @Size(max = 30, message = "The first_name must be up to 30 chars")
  @NotNull(message = "The first_name cannot be null")
  @Column(name = "first_name")
  private String firstName = "";

  @Size(max = 45, message = "The last_name must be up to 45 chars")
  @NotNull(message = "The last_name cannot be null")
  @Column(name = "last_name")
  private String lastName = "";

  @Size(max = 150, message = "The company_name must be up to 150 chars")
  @NotNull(message = "The company_name cannot be null")
  @Column(name = "company_name")
  private String companyName = "";

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "hiring_ab_user_id")
  private AbUser hiringAbUser;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "hiring_ab_company_id")
  private AbCompany hiringAbCompany;

  @Column(name = "archived")
  private Boolean archived = false;

  @Column(name = "archived_date")
  private Instant archivedDate;

  @Column(name = "archived_by_user_id")
  private Integer archivedByUserId;

  @Column(name = "bill_to_ab_user_id")
  private Integer billToAbUserId;

  @Column(name = "bill_to_ab_company_id")
  private Integer billToAbCompanyId;

  @ManyToOne(cascade = {CascadeType.PERSIST, CascadeType.MERGE, CascadeType.DETACH, CascadeType.REFRESH}, fetch = FetchType.LAZY)
  @JoinColumn(name = "bill_to_user_id")
  private User billToUser;

  @ManyToOne(cascade = {CascadeType.PERSIST, CascadeType.MERGE, CascadeType.DETACH, CascadeType.REFRESH}, fetch = FetchType.LAZY)
  @JoinColumn(name = "bill_to_user_company_id")
  private UserCompany billToUserCompany;

  @Size(max = 30, message = "The bill_to_first_name must be up to 30 chars")
  @NotNull(message = "The bill_to_first_name cannot be null")
  @Column(name = "bill_to_first_name")
  private String billToFirstName = "";

  @Size(max = 45, message = "The bill_to_last_name must be up to 45 chars")
  @NotNull(message = "The bill_to_last_name cannot be null")
  @Column(name = "bill_to_last_name")
  private String billToLastName = "";

  @Size(max = 150, message = "The bill_to_company_name must be up to 150 chars")
  @NotNull(message = "The bill_to_company_name cannot be null")
  @Column(name = "bill_to_company_name")
  private String billToCompanyName = "";

  @Size(max = 100, message = "The bill_to_email must be up to 100 chars")
  @NotNull(message = "The bill_to_email cannot be null")
  @Column(name = "bill_to_email")
  private String billToEmail = "";

  @Size(max = 25, message = "The bill_to_phone must be up to 25 chars")
  @NotNull(message = "The bill_to_phone cannot be null")
  @Column(name = "bill_to_phone")
  private String billToPhone = "";

  @Size(max = 150, message = "The bill_to_address must be up to 150 chars")
  @NotNull(message = "The bill_to_address cannot be null")
  @Column(name = "bill_to_address")
  private String billToAddress = "";

  @Size(max = 60, message = "The bill_to_city must be up to 60 chars")
  @NotNull(message = "The bill_to_city cannot be null")
  @Column(name = "bill_to_city")
  private String billToCity = "";

  @Size(max = 2, message = "The bill_to_state must be up to 2 chars")
  @NotNull(message = "The bill_to_state cannot be null")
  @Column(name = "bill_to_state")
  private String billToState = "";

  @Size(max = 10, message = "The bill_to_zip must be up to 10 chars")
  @NotNull(message = "The bill_to_zip cannot be null")
  @Column(name = "bill_to_zip")
  private String billToZip = "";

  @Size(max = 25, message = "The bill_to_country must be up to 25 chars")
  @NotNull(message = "The bill_to_country cannot be null")
  @Column(name = "bill_to_country")
  private String billToCountry = "";

  @Size(max = 80, message = "The bill_to_location must be up to 80 chars")
  @NotNull(message = "The bill_to_location cannot be null")
  @Column(name = "bill_to_location")
  private String billToLocation = "";

  @Size(max = 1000, message = "The bill_description must be up to 1000 chars")
  @NotNull(message = "The bill_description cannot be null")
  @Column(name = "bill_description")
  private String billDescription = "";

  @Column(name = "bill_to_archived")
  private Boolean billToArchived = false;

  @Column(name = "bill_to_archived_date")
  private Instant billToArchivedDate;

  @Column(name = "bill_to_archived_by_user_id")
  private Integer billToArchivedByUserId;

  @Column(name = "revised_from_load_invoice_id")
  private Integer revisedFromLoadInvoiceId;

  @Column(name = "modified_date")
  private Instant modifiedDate;

  @Column(name = "email_queue_id")
  private Integer emailQueueId;

  @Size(max = 100, message = "The email_status must be up to 100 chars")
  @NotNull(message = "The email_status cannot be null")
  @Column(name = "email_status")
  private String emailStatus = "";

  @Column(name = "email_status_date")
  private Instant emailStatusDate;

  @DecimalMin(value = "0.0")
  @DecimalMax(value = "99999999.99")
  @Column(name = "invoice_total")
  private BigDecimal invoiceTotal;

  @Column(name = "payment_due_date")
  private Instant paymentDueDate;

  @Column(name = "invoice_date")
  private Instant invoiceDate;

  @Column(name = "added_date")
  private Instant addedDate;

  @Column(name = "deleted_date")
  private Instant deletedDate;

  @Column(name = "deleted_by_user_id")
  private Integer deletedByUserId;

  @Column(name = "deleted")
  private Boolean deleted = false;

  @ManyToOne(cascade = {CascadeType.PERSIST, CascadeType.MERGE, CascadeType.DETACH, CascadeType.REFRESH}, fetch = FetchType.LAZY)
  @JoinColumn(name = "invoice_file_id")
  private File file;

  @OneToOne(cascade = CascadeType.ALL)
  @JoinColumn(name = "load_invoice_id")
  private QbInvoice qbInvoice;

  @OneToOne(cascade = CascadeType.ALL)
  @JoinColumn(name = "load_invoice_id")
  private QbBill qbBill;

  @OneToMany(cascade = CascadeType.ALL, mappedBy = "loadInvoice", fetch = FetchType.LAZY)
  private List<LoadInvoiceItem> loadInvoiceItems = new ArrayList<>();

  public boolean isInternalAssignment() {
    return isNull(billToAbCompanyId) // not assigned to addressbook contact
        && exists(billToUserCompany)  // assigned to some user company
        && exists(billToUser);
  }

  public boolean isExternalAssignment() {
    return !isNull(billToAbCompanyId);
  }

  public void registerDomainEvent(final DomainEvent event) {
    registerEvent(event);
  }

}


