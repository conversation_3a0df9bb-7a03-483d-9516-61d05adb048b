package com.bulkloads.web.loadinvoice.repository;

import java.util.List;
import java.util.Optional;
import com.bulkloads.web.loadinvoice.domain.entity.LoadInvoice;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface LoadInvoiceRepository extends JpaRepository<LoadInvoice, Integer>, LoadInvoiceQueryRepository {

  @Query(value = """
      select load_invoice_id
      from load_invoices
      where
          deleted = 0
          and user_company_id = :user_company_id
          and bill_to_ab_user_id = :ab_user_id
          and email_status != 'sent'
          and email_status != 'delivered'
          and email_status != 'opened'
          and email_status_date is not null -- has been sent
          and email_status_date > curdate() - interval 30 day
      """, nativeQuery = true)
  List<Integer> findRecentDroppedInvoices(
      @Param("user_company_id") int userCompanyId,
      @Param("ab_user_id") int abUserId);

  @Modifying
  @Query(value = """
      update LoadInvoice li
      set li.billToEmail = :email
      where li.loadInvoiceId = :load_invoice_id
      """)
  void updateBillToEmail(
      @Param("email") String email,
      @Param("load_invoice_id") Integer loadInvoiceId);

  Optional<LoadInvoice> findByLoadInvoiceIdAndUserCompanyUserCompanyId(int loadInvoiceId, int userCompanyId);

  @Query(value = """
      select li from LoadInvoice li
      left join li.qbInvoice qbli
        where qbli.qbInvoiceId is null
        and li.archived = false and li.deleted = false
        and li.userCompany.userCompanyId = :userCompanyId
      """)
  List<LoadInvoice> findAllNonQuickbooksPostedInvoices(final int userCompanyId);

  @Query(value = """
      select li from LoadInvoice li
      left join li.qbBill qbb
        where qbb.qbBillId is null
        and li.archived = false and li.deleted = false
        and li.userCompany.userCompanyId = :userCompanyId
      """)
  List<LoadInvoice> findAllNonQuickbooksPostedBills(final int userCompanyId);
}
