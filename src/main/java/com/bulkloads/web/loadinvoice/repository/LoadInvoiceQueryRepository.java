package com.bulkloads.web.loadinvoice.repository;

import java.util.List;
import com.bulkloads.common.mui.model.QueryParams;
import com.bulkloads.web.loadinvoice.service.dto.LoadInvoicePayableByCompanyResponse;
import com.bulkloads.web.loadinvoice.service.dto.LoadInvoicePayableResponse;
import com.bulkloads.web.loadinvoice.service.dto.LoadInvoiceReceivableByCompanyResponse;
import com.bulkloads.web.loadinvoice.service.dto.LoadInvoiceReceivableResponse;

public interface LoadInvoiceQueryRepository {

  List<LoadInvoicePayableByCompanyResponse> getLoadInvoicePayablesByCompany(
      int cId,
      Boolean archived,
      QueryParams queryParams,
      int skip,
      int limit
  );

  List<LoadInvoicePayableResponse> getLoadInvoicePayables(
      int cId,
      Boolean archived,
      Integer userCompanyId,
      Integer userId,
      QueryParams queryParams,
      int skip,
      int limit
  );

  List<LoadInvoiceReceivableByCompanyResponse> getLoadInvoiceReceivablesByCompany(
      int cId,
      Boolean archived,
      QueryParams queryParams,
      int skip,
      int limit
  );

  List<LoadInvoiceReceivableResponse> getLoadInvoiceReceivables(
      int cId,
      Boolean archived,
      Integer billToUserCompanyId,
      Integer billToAbCompanyId,
      QueryParams queryParams,
      int skip,
      int limit
  );
}
