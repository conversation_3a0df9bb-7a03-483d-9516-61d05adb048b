package com.bulkloads.web.infra.email;

import static com.bulkloads.config.AppConstants.BULKLOADS_ERROR_MAIL_ADDRESS;
import static com.bulkloads.config.AppConstants.BULKLOADS_SITE_ID;
import static com.bulkloads.config.AppConstants.Category.CUSTOMER_SUPPORT;
import static com.bulkloads.config.AppConstants.Category.LOAD_ASSIGNMENTS;
import static com.bulkloads.config.AppConstants.Header.X_PRIORITY;
import static com.bulkloads.config.AppConstants.Header.X_SMTPAPI;
import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;
import static org.springframework.util.StringUtils.hasText;
import java.time.Instant;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import com.bulkloads.config.AppProperties;
import com.bulkloads.web.infra.email.domain.Attachment;
import com.bulkloads.web.infra.email.domain.EmailDetails;
import com.bulkloads.web.infra.email.dto.AttachmentDto;
import com.bulkloads.web.infra.email.dto.EmailMessageDto;
import com.bulkloads.web.infra.email.entity.Email;
import com.bulkloads.web.infra.email.entity.EmailCategory;
import com.bulkloads.web.infra.email.repository.EmailCategoryRepository;
import com.bulkloads.web.infra.email.repository.EmailRepository;
import com.bulkloads.web.infra.email.repository.UserEmailCategoryRepository;
import com.bulkloads.web.infra.messaging.consumer.MessageQueueSender;
import com.bulkloads.web.user.repository.UserRepository;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.validation.annotation.Validated;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@Validated
@Transactional
@RequiredArgsConstructor
public class EmailService {

  public static final String NEVER = "never";
  private static final List<String> WHITELISTED_EMAILS = List.of("<EMAIL>");

  private final UserEmailCategoryRepository userEmailCategoryRepository;
  private final EmailCategoryRepository emailCategoryRepository;
  private final EmailRepository emailRepository;
  private final MessageQueueSender queueSender;
  private final UserRepository userRepository;
  private final AppProperties appProperties;

  public Optional<Integer> sendEmail(final @Valid EmailDetails emailDetails) {

    if (shouldSendEmail(emailDetails)) {
      final EmailMessageDto emailMessageDto = map(emailDetails);

      final Optional<Integer> emailId = emailMessageDto.getEmailId();

      sendToQueue(emailMessageDto);

      return emailId;
    }
    return Optional.empty();
  }

  protected void sendToQueue(EmailMessageDto emailMessageDto) {

    final String queueName = appProperties.getMailing().getEmailQueueName();
    TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
      @Override
      public void afterCommit() {
        queueSender.send(queueName, emailMessageDto);
      }
    });
  }

  private boolean shouldSendEmail(final EmailDetails emailDetails) {
    return !(hasBeenEmailedInThePast(emailDetails) || hasBeenEmailRetried(emailDetails));
  }

  private boolean hasBeenEmailedInThePast(final EmailDetails emailDetails) {
    final Integer category = emailDetails.getCategory();
    if (nonNull(category)) {
      final Integer toUserId = emailDetails.getToUserId().orElse(0);
      return userEmailCategoryRepository
                 .countByIdUserIdAndIdSiteIdAndIdEmailCategoryIdAndEmailFrequency(toUserId, BULKLOADS_SITE_ID, category, NEVER) > 0;
    }
    return false;
  }

  private boolean hasBeenEmailRetried(final EmailDetails emailDetails) {
    if (!(isNull(emailDetails.getCategory())
          || emailDetails.getCategory() == CUSTOMER_SUPPORT
          || emailDetails.getCategory() == LOAD_ASSIGNMENTS
          || emailDetails.getToEmails().stream().anyMatch(WHITELISTED_EMAILS::contains))) {
      return userRepository.countByBadEmailDateIsNotNullAndEmailIn(emailDetails.getToEmails()) > 0;
    }
    return false;
  }

  private EmailMessageDto map(final EmailDetails emailDetails) {
    final EmailCategory emailCategory = Optional.ofNullable(emailDetails.getCategory())
        .flatMap(emailCategoryRepository::findById)
        .orElse(null);

    final Instant sendDate = Instant.now();

    final Optional<Integer> emailQueueId = getEmailQueueId(emailDetails, emailCategory, sendDate);
    final String emailCategoryString = Optional.ofNullable(emailCategory)
        .map(EmailCategory::getEmailCategory)
        .orElse("");

    final EmailMessageDto.EmailMessageDtoBuilder builder = EmailMessageDto.builder()
        .fromEmail(emailDetails.getFromEmail())
        .subject(emailDetails.getSubject())
        .message(emailDetails.getMessage())
        .toEmails(emailDetails.getToEmails())
        .replyToEmail(emailDetails.getReplyToEmail())
        .failTo(emailDetails.getFailTo())
        .description(emailDetails.getDescription())
        .sendDate(sendDate);

    emailQueueId.ifPresent(builder::emailId);
    emailDetails.getCc().ifPresent(builder::cc);
    emailDetails.getBcc().ifPresent(builder::bcc);

    if (nonNull(emailDetails.getAttachments())) {
      builder.attachments(map(emailDetails.getAttachments()));
    }

    builder.headers(buildHeaders(emailDetails, emailQueueId.orElse(0), emailCategoryString));

    return builder.build();
  }

  private List<AttachmentDto> map(final List<Attachment> attachments) {
    return attachments.stream()
        .map(this::map)
        .toList();
  }

  private AttachmentDto map(final Attachment attachment) {
    return AttachmentDto.builder()
        .filename(attachment.getFilename())
        .url(attachment.getUrl())
        .build();
  }

  private Map<String, String> buildHeaders(final EmailDetails emailDetails, final int emailQueueId, final String emailCategoryString) {
    final Map<String, String> headers = new HashMap<>();
    headers.put(X_SMTPAPI, getUniqueArgs(emailQueueId, emailCategoryString));

    if (emailDetails.isPrioritise()) {
      headers.put(X_PRIORITY, "1");
    }
    return headers;
  }

  private Email buildEmail(final EmailDetails emailDetails, final EmailCategory category, final Instant sendDate) {

    final String fromEmail = emailDetails.getFromEmail();
    final String replyToEmail = emailDetails.getReplyToEmail();

    final Email email = new Email();
    email.setFromEmail(fromEmail);
    email.setToEmail(emailDetails.getToEmails());
    emailDetails.getCc().ifPresent(email::setCc);
    emailDetails.getBcc().ifPresent(email::setBcc);
    email.setReplyToEmail(hasText(replyToEmail) ? replyToEmail : fromEmail);
    email.setSubject(getSubject(emailDetails.getSubject()));
    email.setSenderUserId(emailDetails.getSenderUserId().orElse(0));
    email.setEmailCategory(category);
    email.setMessage(emailDetails.getMessage());
    email.setSendDate(sendDate);
    email.setSiteId(BULKLOADS_SITE_ID);
    email.setDescription(nonNull(emailDetails.getDescription()) ? emailDetails.getDescription() : "");

    emailDetails.getToUserId().ifPresent(email::setToUserId);
    return email;
  }

  private Optional<Integer> getEmailQueueId(final EmailDetails emailDetails, final EmailCategory category, final Instant sendDate) {
    if (!emailDetails.getToEmails().contains(BULKLOADS_ERROR_MAIL_ADDRESS)) {
      final Email email = buildEmail(emailDetails, category, sendDate);
      return Optional.of(emailRepository.save(email).getId());
    }
    return Optional.empty();
  }

  private String getUniqueArgs(final int emailQueueId, final String category) {
    return """
        {"unique_args": {"email_queue_id": "%s"}, "category":"%s"}
        """.formatted(emailQueueId, category);
  }

  private String getSubject(final String subject) {
    return appProperties.isProdMode() ? subject : subject + " (TEST)";
  }
}
