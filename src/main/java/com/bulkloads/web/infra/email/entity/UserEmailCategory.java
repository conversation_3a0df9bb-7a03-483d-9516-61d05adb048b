package com.bulkloads.web.infra.email.entity;

import java.time.Instant;
import com.bulkloads.web.user.domain.entity.User;
import org.hibernate.annotations.OnDelete;
import org.hibernate.annotations.OnDeleteAction;
import jakarta.persistence.Column;
import jakarta.persistence.EmbeddedId;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.Lob;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.MapsId;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(name = "user_email_categories")
public class UserEmailCategory {

  @EmbeddedId
  private UserEmailCategoryId id;

  @MapsId("userId")
  @ManyToOne(fetch = FetchType.LAZY, optional = false)
  @OnDelete(action = OnDeleteAction.CASCADE)
  @JoinColumn(name = "user_id")
  private User user;

  @MapsId("emailCategoryId")
  @ManyToOne(fetch = FetchType.LAZY, optional = false)
  @OnDelete(action = OnDeleteAction.CASCADE)
  @JoinColumn(name = "email_category_id")
  private EmailCategory emailCategory;

  @NotNull
  @Lob
  @Column(name = "email_frequency")
  private String emailFrequency = "schedule";

  @Column(name = "changed_date")
  private Instant changedDate;

  @NotNull
  @Column(name = "send_push")
  private Byte sendPush = 0;

  @NotNull
  @Column(name = "send_sms")
  private Byte sendSms = 0;

  @Column(name = "site_id", insertable = false, updatable = false)
  private Integer siteId = 1;
}
