package com.bulkloads.web.infra.email.entity;

import java.io.Serializable;
import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import jakarta.validation.constraints.NotNull;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Embeddable
@EqualsAndHashCode
public class UserEmailCategoryId implements Serializable {

  @NotNull
  @Column(name = "user_id")
  private Integer userId;

  @NotNull
  @Column(name = "site_id")
  private Integer siteId = 1;

  @NotNull
  @Column(name = "email_category_id")
  private Integer emailCategoryId;
}
