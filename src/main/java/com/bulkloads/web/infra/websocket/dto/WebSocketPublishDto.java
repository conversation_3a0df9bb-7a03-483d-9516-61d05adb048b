package com.bulkloads.web.infra.websocket.dto;

import java.util.Map;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Value;
import lombok.extern.jackson.Jacksonized;

@Value
@Builder
@Jacksonized
public class WebSocketPublishDto {

  @NotBlank
  String channel;
  Map<@NotBlank String, Object> message;
  Map<@NotBlank String, @NotNull Object> meta;
}
