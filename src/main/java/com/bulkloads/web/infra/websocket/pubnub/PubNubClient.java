package com.bulkloads.web.infra.websocket.pubnub;

public interface PubNubClient {

  void publish(final String channel, final Object meta, final Object message);

  void signal(final String channel, final String message);

  void hereNow(final String channel, final boolean includeUuids, final boolean includeState);

  void globalHereNow(final String channel, final boolean includeUuids, final boolean includeState);
}
