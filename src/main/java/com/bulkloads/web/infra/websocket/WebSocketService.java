package com.bulkloads.web.infra.websocket;

import static com.bulkloads.config.AppConstants.WebSocket.DOMAIN;
import java.util.Map;
import com.bulkloads.common.UserUtil;
import com.bulkloads.config.AppProperties;
import com.bulkloads.web.infra.messaging.consumer.MessageQueueSender;
import com.bulkloads.web.infra.websocket.dto.WebSocketHereNowDto;
import com.bulkloads.web.infra.websocket.dto.WebSocketPublishDto;
import com.bulkloads.web.infra.websocket.dto.WebSocketSignalDto;
import org.springframework.stereotype.Component;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
@RequiredArgsConstructor
public class WebSocketService {

  private final MessageQueueSender queueSender;
  private final AppProperties appProperties;

  public void sendToWebSocket(final WebSocketPublishDto dto) {
    log.debug("Sending websocket {}", dto);
    final AppProperties.PubNub pubNub = appProperties.getPubNub();
    final String queueName = pubNub.getQueueName();
    dto.getMessage().put(DOMAIN, pubNub.getUuid());
    queueSender.send(queueName, dto);
  }

  public void sendToWebSocket(String channel, final Map<String, Object> message) {
    final Integer userCompanyId = UserUtil.getUserCompanyIdOrThrow();

    final Map<String, Object> meta = Map.of("user_company_id", userCompanyId);

    final WebSocketPublishDto publishDto = WebSocketPublishDto.builder()
        .channel(channel)
        .message(message)
        .meta(meta)
        .build();

    this.sendToWebSocket(publishDto);
  }

  public void sendToWebSocket(final WebSocketSignalDto dto) {
    final AppProperties.PubNub pubNub = appProperties.getPubNub();
    final String queueName = pubNub.getQueueName();
    dto.getMessage().put(DOMAIN, pubNub.getUuid());
    queueSender.send(queueName, dto);
  }

  public void sendToWebSocket(final WebSocketHereNowDto dto) {
    final AppProperties.PubNub pubNub = appProperties.getPubNub();
    final String queueName = pubNub.getQueueName();
    queueSender.send(queueName, dto);
  }

}
