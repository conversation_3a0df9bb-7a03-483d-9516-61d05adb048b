package com.bulkloads.web.infra.confirmation.dto;

import java.util.ArrayList;
import java.util.List;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Positive;
import lombok.Builder;
import lombok.Value;
import lombok.extern.jackson.Jacksonized;

@Value
@Builder
@Jacksonized
public class ConfirmationDto {

  @NotEmpty
  @Builder.Default
  List<@Positive Integer> loadAssignmentIds = new ArrayList<>();

  String action;
}
