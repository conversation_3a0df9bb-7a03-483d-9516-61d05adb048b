package com.bulkloads.web.infra.dynlink;

import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;
import java.util.HashMap;
import java.util.Map;
import com.bulkloads.config.AppProperties;
import com.bulkloads.exception.BulkloadsException;
import com.bulkloads.web.load.domain.template.DynamicLink;
import org.apache.http.HttpHeaders;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;

@Component
public class FirebaseDynamicLinkFacade {

  private final WebClient webClient;
  private final AppProperties.DynamicLinks dynamicLinks;

  public FirebaseDynamicLinkFacade(final AppProperties appProperties) {
    this.dynamicLinks = appProperties.getFirebase().getDynamicLinks();
    this.webClient = WebClient
        .builder()
        .baseUrl(dynamicLinks.getBaseUrl())
        .build();
  }

  public DynamicLink createDynamicLink(final String link, final boolean requireApp) {

    try {
      Map<String, Object> requestBody = Map.of("dynamicLinkInfo", buildFirebasePayload(link, requireApp));

      final String dynamicLink = webClient.post()
          .uri(uriBuilder -> uriBuilder
              .path("/shortLinks")
              .queryParam("key", dynamicLinks.getApiKey())
              .build())
          .header(HttpHeaders.CONTENT_TYPE, APPLICATION_JSON_VALUE)
          .body(BodyInserters.fromValue(requestBody))
          .retrieve()
          .bodyToMono(Map.class)
          .map(response -> (String) response.get("shortLink"))
          .block();

      return DynamicLink.builder().originalLink(link).dynamicLink(dynamicLink).build();
    } catch (Exception e) {
      throw new BulkloadsException("Failed to create dynamic link", e);
    }
  }

  private Map<String, Object> buildFirebasePayload(final String link,
                                                   final boolean requireApp) {
    Map<String, Object> dynamicLinkInfo = new HashMap<>();
    Map<String, String> androidInfo = new HashMap<>();
    Map<String, String> iosInfo = new HashMap<>();

    if (requireApp) {
      androidInfo.put("androidPackageName", dynamicLinks.getAndroidPackageName());
      iosInfo.put("iosBundleId", dynamicLinks.getIosBundleId());
      iosInfo.put("iosAppStoreId", dynamicLinks.getIosAppStoreId());
    } else {
      androidInfo.put("androidFallbackLink", link);
      iosInfo.put("iosFallbackLink", link);
    }

    dynamicLinkInfo.put("domainUriPrefix", dynamicLinks.getDomainUriPrefix());
    dynamicLinkInfo.put("link", link);

    dynamicLinkInfo.put("androidInfo", androidInfo);
    dynamicLinkInfo.put("iosInfo", iosInfo);
    return dynamicLinkInfo;
  }

}
