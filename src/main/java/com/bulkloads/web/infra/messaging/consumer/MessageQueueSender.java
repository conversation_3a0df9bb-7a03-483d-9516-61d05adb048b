package com.bulkloads.web.infra.messaging.consumer;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Component;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
@RequiredArgsConstructor
public class MessageQueueSender {

  private final RabbitTemplate template;
  private final ObjectMapper objectMapper;

  @SneakyThrows
  public void send(final String queueName, final Object messageObject) {
    template.convertAndSend(queueName, messageObject);
    log.debug("Message type {} was sent to message queue {}", messageObject, queueName);

    if (log.isDebugEnabled()) {
      final String messageJson = objectMapper
          .writerWithDefaultPrettyPrinter()
          .writeValueAsString(messageObject);
      log.debug("Message payload - {}", messageJson);
    }
  }
}
