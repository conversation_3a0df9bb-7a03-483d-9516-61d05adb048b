package com.bulkloads.web.infra.messaging.consumer;

import static com.bulkloads.config.AppConstants.InvoiceAction.LOAD_INVOICE_CREATE;
import static com.bulkloads.config.AppConstants.InvoiceAction.LOAD_INVOICE_REBUNDLE;
import static com.bulkloads.config.AppConstants.InvoiceAction.LOAD_INVOICE_REGENERATE;
import com.bulkloads.security.ImpersonationService;
import com.bulkloads.web.assignment.domain.entity.Assignment;
import com.bulkloads.web.assignment.repository.AssignmentRepository;
import com.bulkloads.web.infra.invoice.dto.InvoiceMessageDto;
import com.bulkloads.web.loadinvoice.service.LoadInvoiceService;
import com.bulkloads.web.loadinvoice.service.dto.LoadInvoiceRequest;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
@Validated
@Transactional
@RequiredArgsConstructor
public class InvoiceQueueConsumer {

  private final LoadInvoiceService loadInvoiceService;
  private final AssignmentRepository assignmentRepository;
  private final ImpersonationService impersonationService;

  @RabbitListener(queues = "${bulkloads.invoice.queue-name}", errorHandler = "bulkloadsRabbitListenerErrorHandler")
  public void receive(@Payload final InvoiceMessageDto dto) {
    log.info("Received invoice queue message {}", dto);

    final String action = dto.getAction();
    final int loadAssignmentId = dto.getLoadAssignmentId();
    final Assignment assignment = assignmentRepository.getReferenceById(loadAssignmentId);
    final int userId = assignment.getToUser().getUserId();

    impersonationService.impersonate(userId);

    if (LOAD_INVOICE_CREATE.equals(action)) {

      LoadInvoiceRequest loadInvoiceRequest = LoadInvoiceRequest.builder()
          .loadAssignmentIds(String.valueOf(loadAssignmentId))
          .build();
      loadInvoiceService.create(loadInvoiceRequest);

    } else if (LOAD_INVOICE_REBUNDLE.equals(action)) {

      dto.getLoadInvoiceId()
          .ifPresent(loadInvoiceService::rebundle);

    } else if (LOAD_INVOICE_REGENERATE.equals(action)) {

      dto.getLoadInvoiceId()
          .ifPresent(loadInvoiceService::revise);

    }
  }
}
