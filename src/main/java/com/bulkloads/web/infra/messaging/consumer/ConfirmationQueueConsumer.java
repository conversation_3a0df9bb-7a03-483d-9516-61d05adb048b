package com.bulkloads.web.infra.messaging.consumer;

import static com.bulkloads.config.AppConstants.AssignmentAction.ASSIGNMENT_CANCEL;
import static com.bulkloads.config.AppConstants.ContactMethod.BOTH;
import static com.bulkloads.config.AppConstants.ContactMethod.EMAIL;
import static com.bulkloads.config.AppConstants.ContactMethod.SMS;
import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;
import java.time.Instant;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import com.bulkloads.config.AppProperties;
import com.bulkloads.web.addressbook.abuser.domain.entity.AbUser;
import com.bulkloads.web.addressbook.abuser.repository.AbUserRepository;
import com.bulkloads.web.assignment.domain.entity.Assignment;
import com.bulkloads.web.assignment.domain.entity.AssignmentConfirmationLog;
import com.bulkloads.web.assignment.event.AssignmentSentEvent;
import com.bulkloads.web.assignment.mapper.AssignmentMapper;
import com.bulkloads.web.assignment.repository.AssignmentConfirmationLogRepository;
import com.bulkloads.web.assignment.repository.AssignmentRepository;
import com.bulkloads.web.assignment.service.AssignmentService;
import com.bulkloads.web.assignment.service.AssignmentTemplateBuilderRegistry;
import com.bulkloads.web.assignment.service.template.AssignmentTemplateBuilder;
import com.bulkloads.web.communication.CommunicationService;
import com.bulkloads.web.communication.domain.CommunicationDetails;
import com.bulkloads.web.communication.domain.CommunicationIds;
import com.bulkloads.web.infra.confirmation.dto.ConfirmationDto;
import com.bulkloads.web.load.domain.template.DynamicLink;
import com.bulkloads.web.load.domain.template.LoadAssignmentTemplateModel;
import com.bulkloads.web.user.domain.entity.User;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
@Validated
@Transactional
@RequiredArgsConstructor
@RabbitListener(queues = "${bulkloads.confirmation.queue-name}", errorHandler = "bulkloadsRabbitListenerErrorHandler")
public class ConfirmationQueueConsumer {

  public static final int EMAIL_CATEGORY_ID = 20;

  private final AssignmentConfirmationLogRepository assignmentConfirmationLogRepository;
  private final AssignmentService assignmentService;
  private final AssignmentRepository assignmentRepository;
  private final AssignmentMapper assignmentMapper;
  private final CommunicationService communicationService;
  private final AppProperties appProperties;
  private final AssignmentTemplateBuilderRegistry templateBuilderRegistry;
  private final AbUserRepository abUserRepository;
  private final ApplicationEventPublisher applicationEventPublisher;

  @RabbitHandler
  public void receive(@Valid @Payload final ConfirmationDto confirmationDto) {
    log.trace("Received confirmation queue message {}", confirmationDto);
    handle(confirmationDto);
  }

  private void handle(final ConfirmationDto confirmationDto) {
    final List<Integer> loadAssignmentIds = confirmationDto.getLoadAssignmentIds();
    final String action = confirmationDto.getAction();

    final List<Assignment> assignments = assignmentRepository.findAllById(loadAssignmentIds);
    final Assignment firstAssignment = assignments.get(0);

    final LoadAssignmentTemplateModel model = buildTemplateModel(assignments, action);
    final String confirmationSentMethod = firstAssignment.getConfirmationSentMethod();

    final AssignmentTemplateBuilder templateBuilder = templateBuilderRegistry.findBuilder(action);

    String emailMessage = null;
    String emailTitle = null;
    if (List.of(BOTH, EMAIL).contains(confirmationSentMethod)) {
      emailMessage = templateBuilder.getEmailContent(model);
      emailTitle = templateBuilder.getEmailTitle(model, null);
    }

    String smsMessage = null;
    if (List.of(BOTH, SMS).contains(confirmationSentMethod)) {
      smsMessage = templateBuilder.getSmsContent(model);
    }

    final String notificationTitle = templateBuilder.getNotificationTitle(model);
    final String notificationContent = templateBuilder.getNotificationContent(model);
    final Map<String, Object> pushData = buildPushData(firstAssignment);

    final String confirmationCcOthers = firstAssignment.getConfirmationCcOthers();
    final Integer fromUserId = getUserId(firstAssignment);

    Optional<CommunicationIds> communicationIds;

    if (nonNull(firstAssignment.getToAbUser())) {
      final Integer toAbUserId = firstAssignment.getToAbUser().getAbUserId();

      final CommunicationDetails.CommunicationDetailsBuilder builder =
          CommunicationDetails.builder().fromUserId(fromUserId).toAbUserId(toAbUserId).emailCc(confirmationCcOthers).emailTitle(emailTitle)
              .emailMessage(emailMessage).pushTitle(notificationTitle).pushMessage(notificationContent).pushData(pushData).smsMessage(smsMessage)
              .emailCategoryId(EMAIL_CATEGORY_ID);

      if (nonNull(firstAssignment.getToAbUser().getBlUser())) {
        builder.toUserId(firstAssignment.getToAbUser().getBlUser().getUserId());
      }

      final CommunicationDetails communicationDetails = builder.build();

      communicationIds = communicationService.sendToAbUser(communicationDetails);
    } else if (nonNull(firstAssignment.getToUser())) {
      final Integer toUserId = firstAssignment.getToUser().getUserId();

      final CommunicationDetails communicationDetails =
          CommunicationDetails.builder().fromUserId(fromUserId).toUserId(toUserId).emailCc(confirmationCcOthers).emailTitle(emailTitle)
              .emailMessage(emailMessage).pushTitle(notificationTitle).pushMessage(notificationContent).pushData(pushData).smsMessage(smsMessage)
              .emailCategoryId(EMAIL_CATEGORY_ID).build();

      communicationIds = communicationService.sendToUser(communicationDetails);
    } else {
      communicationIds = Optional.empty();
    }

    if (!ASSIGNMENT_CANCEL.equals(action)) {

      //Confirmation to additional ab_users
      final List<Integer> confirmationToAbUserIds = firstAssignment.getConfirmationToAbUserIds();
      final int userCompanyId = getUserCompanyId(firstAssignment);
      if (!confirmationToAbUserIds.isEmpty()) {
        emailTitle = templateBuilder.getEmailTitle(model, "Load confirmation for");
        final List<AbUser> abUsers = abUserRepository.findAllByAbUserIdInAndUserCompanyUserCompanyIdAndDeletedIsFalse(confirmationToAbUserIds, userCompanyId);
        for (final AbUser abUser : abUsers) {
          final CommunicationDetails communicationDetails =
              CommunicationDetails.builder().fromUserId(fromUserId).toAbUserId(abUser.getAbUserId()).emailCc(confirmationCcOthers).emailTitle(emailTitle)
                  .emailMessage(emailMessage).pushTitle(notificationTitle).pushMessage(notificationContent).pushData(pushData).smsMessage(smsMessage)
                  .emailCategoryId(EMAIL_CATEGORY_ID).build();

          communicationService.sendToAbUser(communicationDetails);
        }
      }

      final Instant now = Instant.now();
      assignments.forEach(assignment -> markConfirmationSent(assignment, communicationIds, now));
      saveConfirmationLog(firstAssignment, now);

      applicationEventPublisher.publishEvent(new AssignmentSentEvent(loadAssignmentIds));
    }
  }

  private Integer getUserCompanyId(final Assignment firstAssignment) {
    return nonNull(firstAssignment.getUserCompany())
        ? firstAssignment.getUserCompany().getUserCompanyId()
        : firstAssignment.getToUserCompany().getUserCompanyId();
  }

  private Integer getUserId(final Assignment firstAssignment) {
    return nonNull(firstAssignment.getUser()) ? firstAssignment.getUser().getUserId() : firstAssignment.getToUser().getUserId();
  }

  private LoadAssignmentTemplateModel buildTemplateModel(final List<Assignment> assignments, final String action) {
    final String domainUrl = appProperties.getDomainUrl();
    final List<DynamicLink> dynamicLinks = assignmentService.createDynamicLinks(assignments, action);
    if (action.contains("booking")) {
      return assignmentMapper.bookingsToFmModel(assignments, dynamicLinks, domainUrl);
    } else {
      return assignmentMapper.assignmentsToFmModel(assignments, dynamicLinks, domainUrl);
    }
  }

  private void saveConfirmationLog(final Assignment assignment, final Instant now) {
    final User user = assignment.getUser();
    final AssignmentConfirmationLog confLog = new AssignmentConfirmationLog();
    confLog.setLoadAssignmentId(assignment.getLoadAssignmentId());
    confLog.setLogDate(now);
    confLog.setConfirmationFileId(assignment.getConfirmationFile().getFileId());
    confLog.setLogByUser(user);
    confLog.setToAbUserId(isNull(assignment.getToAbUser()) ? null : assignment.getToAbUser().getAbUserId());
    confLog.setLogAction("Sent via " + assignment.getConfirmationSentMethod());
    assignmentConfirmationLogRepository.save(confLog);
  }

  private void markConfirmationSent(final Assignment assignment, final Optional<CommunicationIds> communicationIds, final Instant now) {
    final String confirmationSentMethod = assignment.getConfirmationSentMethod();
    final Integer userId = getUserId(assignment);
    assignment.setConfirmationSentDate(now);
    assignment.setConfirmationSentByUserId(userId);
    communicationIds.ifPresent(ids -> assignment.setConfirmationEmailQueueId(ids.getEmailQueueId().orElse(null)));

    if (List.of(BOTH, EMAIL).contains(confirmationSentMethod)) {
      assignment.setConfirmationEmailStatus("Sent");
    }
  }

  private Map<String, Object> buildPushData(final Assignment assignment) {
    Map<String, Object> data = new HashMap<>();
    data.put("notification_type", "Load Assignments");
    data.put("notification_subtype", "Assignments");
    data.put("load_assignment_ids", assignment.getLoadAssignmentId());
    
    if (assignment.getConfirmationFile() != null) {
      data.put("confirmation_file_id", assignment.getConfirmationFile().getFileId());
    }
    
    return data;
  }
}
