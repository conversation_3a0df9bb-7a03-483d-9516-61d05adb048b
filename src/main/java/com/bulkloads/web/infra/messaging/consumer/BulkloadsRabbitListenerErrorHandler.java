package com.bulkloads.web.infra.messaging.consumer;

import static com.bulkloads.config.AppConstants.BULKLOADS_ERROR_MAIL_ADDRESS;
import static com.bulkloads.config.AppConstants.NO_REPLY_EMAIL_ADDRESS;
import static com.bulkloads.config.AppConstants.Templates.ERROR;

import java.util.List;
import java.util.Objects;
import java.util.UUID;
import com.bulkloads.advice.ErrorTemplateModel;
import com.bulkloads.config.AppProperties;
import com.bulkloads.exception.BulkloadsExceptionUtils;
import com.bulkloads.web.infra.email.EmailService;
import com.bulkloads.web.infra.email.domain.EmailDetails;
import com.bulkloads.web.infra.template.TemplateService;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.listener.api.RabbitListenerErrorHandler;
import org.springframework.amqp.rabbit.support.ListenerExecutionFailedException;
import org.springframework.boot.info.GitProperties;
import org.springframework.messaging.MessageHeaders;
import org.springframework.stereotype.Component;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
@RequiredArgsConstructor
public class BulkloadsRabbitListenerErrorHandler implements RabbitListenerErrorHandler {

  private final EmailService emailService;
  private final TemplateService templateService;
  private final AppProperties appProperties;
  private final GitProperties gitProperties;

  @Override
  public Object handleError(final Message amqpMessage,
                            final org.springframework.messaging.Message<?> message,
                            final ListenerExecutionFailedException exception) {
    final String uuid = getUuid(message);
    log.error("Error handling MQ message: {}, uuid {}", message, uuid, exception);

    final EmailDetails emailDetails = buildErrorEmail(exception, message, uuid);
    emailService.sendEmail(emailDetails);
    throw exception;
  }

  private EmailDetails buildErrorEmail(final Exception exception,
                                       final org.springframework.messaging.Message<?> message,
                                       final String uuid) {
    return EmailDetails
        .builder()
        .fromEmail(BULKLOADS_ERROR_MAIL_ADDRESS)
        .subject((appProperties.isProdMode() ? "[PROD]" : "[TEST]") + " Error handling MQ message " + uuid)
        .message(getMessage(exception, uuid, message))
        .toEmails(appProperties.getMailing().getErrorEmailAddresses())
        .replyToEmail(NO_REPLY_EMAIL_ADDRESS)
        .failTo(NO_REPLY_EMAIL_ADDRESS)
        .build();
  }

  private String getUuid(final org.springframework.messaging.Message<?> message) {
    final MessageHeaders headers = Objects.requireNonNull(message.getHeaders());
    final UUID uuid = headers.get("id", UUID.class);
    return Objects.requireNonNull(uuid).toString();
  }

  private String getMessage(final Exception exception,
                            final String uuid,
                            final org.springframework.messaging.Message<?> message) {
    final String stackTrace = ExceptionUtils.getStackTrace(exception);
    final String causedByChain = BulkloadsExceptionUtils.getCausedByChainMessage(stackTrace);

    final List<String> headers = message.getHeaders().entrySet().stream()
        .map(entry -> entry.getKey() + ": " + entry.getValue())
        .toList();

    final ErrorTemplateModel errorTemplateModel = ErrorTemplateModel.builder()
        .uuid(uuid)
        .causedByChain(causedByChain)
        .headers(headers)
        .body(message.getPayload().toString())
        .stacktrace(stackTrace)
        .commitHash(gitProperties.getCommitId())
        .build();

    return templateService.processFromTemplateFile(ERROR, errorTemplateModel);

  }
}
