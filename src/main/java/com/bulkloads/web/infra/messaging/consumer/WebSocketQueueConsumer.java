package com.bulkloads.web.infra.messaging.consumer;

import com.bulkloads.web.infra.websocket.WebSocketFacade;
import com.bulkloads.web.infra.websocket.dto.WebSocketHereNowDto;
import com.bulkloads.web.infra.websocket.dto.WebSocketPublishDto;
import com.bulkloads.web.infra.websocket.dto.WebSocketSignalDto;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
@Validated
@RequiredArgsConstructor
@RabbitListener(queues = "${bulkloads.pubnub.queue-name}", errorHandler = "bulkloadsRabbitListenerErrorHandler")
public class WebSocketQueueConsumer {

  private final WebSocketFacade webSocketFacade;

  @RabbitHandler
  public void receive(@Valid @Payload final WebSocketPublishDto message) {
    log.trace("Received web socket queue 'publish' message {}", message);
    webSocketFacade.publish(message);
  }

  @RabbitHandler
  public void receive(@Valid @Payload final WebSocketSignalDto message) {
    log.trace("Received web socket queue 'signal' message {}", message);
    webSocketFacade.signal(message);
  }

  @RabbitHandler
  public void receive(@Valid @Payload final WebSocketHereNowDto message) {
    log.trace("Received web socket queue 'here now' message {}", message);
    webSocketFacade.hereNow(message);
  }

}
