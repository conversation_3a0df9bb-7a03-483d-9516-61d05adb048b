package com.bulkloads.web.infra.invoice.dto;

import java.util.Optional;
import jakarta.validation.constraints.NotEmpty;
import lombok.Builder;
import lombok.Value;
import lombok.extern.jackson.Jacksonized;

@Value
@Jacksonized
@Builder
public class InvoiceMessageDto {
  @NotEmpty
  String action;
  int loadAssignmentId;
  Integer loadInvoiceId;

  public Optional<Integer> getLoadInvoiceId() {
    return Optional.ofNullable(loadInvoiceId);
  }
}