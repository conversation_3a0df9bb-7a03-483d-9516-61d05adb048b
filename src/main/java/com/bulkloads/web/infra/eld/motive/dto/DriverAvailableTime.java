package com.bulkloads.web.infra.eld.motive.dto;

import java.util.List;
import com.fasterxml.jackson.annotation.JsonProperty;

public record DriverAvailableTime(
    String dutyStatus,
    int id,
    String firstName,
    String lastName,
    String username,
    String email,
    String driverCompanyId,
    String status,
    String role,
    AvailableTime availableTime,
    Recap recap,
    LastHosStatus lastHosStatus,
    LastCycleReset lastCycleReset) {

  public record AvailableTime(
      int cycle,
      int shift,
      int drive,
      @JsonProperty("break")
      int _break) {
  }

  public record Duration(
      String date,
      int duration) {
  }

  public record Recap(
      List<Duration> onDutyDuration,
      List<Duration> drivingDuration,
      int secondsAvailable,
      int secondsTomorrow) {
  }

  public record LastHosStatus(
      String status,
      String time) {
  }

  public record LastCycleReset(
      String type,
      String startTime,
      String endTime) {
  }
}