package com.bulkloads.web.loadalert.repository.template;

import org.intellij.lang.annotations.Language;

public class FindDuplicateLoadAlertQueryTemplate {


  @Language("HQL")
  public static final String FIND_DUPLICATE_LOAD_ALERTS_QUERY_TEMPLATE = """        
        SELECT la.*
        from load_alerts la
        WHERE la.deleted = 0
          and la.site_id = 1
          AND la.active = 1

        <% if (paramExistsAdd("loadAlertId")) { %>
          AND la.load_alert_id <> :loadAlertId
        <% } %>

        <% params.put("userId", binding.variables.get("userId")) %>
        AND la.user_id = :userId

        <% params.put("originCountry", binding.variables.get("originCountry")) %>
        AND la.origin_country = :originCountry

        <% params.put("originState", binding.variables.get("originState")) %>
        AND la.origin_state = :originState

        <% params.put("originCity", binding.variables.get("originCity")) %>
        AND la.origin_city = :originCity

        <% params.put("originZip", binding.variables.get("originZip")) %>
        AND la.origin_zip = :originZip

        <% if (paramExistsAdd("originLat")) { %>
          AND la.origin_lat = :originLat
        <% } else { %>
          AND la.origin_lat IS NULL
        <% } %>

        <% if (paramExistsAdd("originLong")) { %>
          AND la.origin_long = :originLong
        <% } else { %>
          AND la.origin_long IS NULL
        <% } %>

        <% if (paramExistsAdd("originRadius")) { %>
          AND la.origin_radius = :originRadius
        <% } else { %>
          AND la.origin_radius IS NULL
        <% } %>

        <% params.put("destinationCountry", binding.variables.get("destinationCountry")) %>
        AND la.destination_country = :destinationCountry

        <% params.put("destinationState", binding.variables.get("destinationState")) %>
        AND la.destination_state = :destinationState

        <% params.put("destinationCity", binding.variables.get("destinationCity")) %>
        AND la.destination_city = :destinationCity

        <% params.put("destinationZip", binding.variables.get("destinationZip")) %>
        AND la.destination_zip = :destinationZip

        <% if (paramExistsAdd("destinationLat")) { %>
          AND la.destination_lat = :destinationLat
        <% } else { %>
          AND la.destination_lat IS NULL
        <% } %>

        <% if (paramExistsAdd("destinationLong")) { %>
          AND la.destination_long = :destinationLong
        <% } else { %>
          AND la.destination_long IS NULL
        <% } %>

        <% if (paramExistsAdd("destinationRadius")) { %>
          AND la.destination_radius = :destinationRadius
        <% } else { %>
          AND la.destination_radius IS NULL
        <% } %>


        <% params.put("userCompanyIds", binding.variables.get("userCompanyIds")) %>
        AND la.user_company_ids = :userCompanyIds

        <% params.put("equipmentIds", binding.variables.get("equipmentIds")) %>
        AND la.equipment_ids = :equipmentIds

        <% params.put("rateProductCategoryIds", binding.variables.get("rateProductCategoryIds")) %>
        AND la.rate_product_category_ids = :rateProductCategoryIds

        ORDER BY la.load_alert_id
      """;
}
