package com.bulkloads.web.loadalert.service.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
@AllArgsConstructor
public class LoadAlertSearchRequest {
  Boolean active;
  Integer siteId;
  String originCountry;
  String originState;
  String originCity;
  String originZip;
  Double originLat;
  Double originLong;
  Integer originRadius;
  String destinationCountry;
  String destinationState;
  String destinationCity;
  String destinationZip;
  Double destinationLat;
  Double destinationLong;
  Integer destinationRadius;
  String userCompanyIds;
  String equipmentIds;
  String product;
  String rateProductCategoryIds;
}
