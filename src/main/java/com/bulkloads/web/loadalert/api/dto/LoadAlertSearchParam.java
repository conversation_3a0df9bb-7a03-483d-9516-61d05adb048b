package com.bulkloads.web.loadalert.api.dto;

import org.springdoc.core.annotations.ParameterObject;
import org.springframework.web.bind.annotation.BindParam;
import io.swagger.v3.oas.annotations.Parameter;
import lombok.Getter;
import lombok.Setter;

//@Data
//@AllArgsConstructor
@Getter
@Setter
@ParameterObject
public class LoadAlertSearchParam {

  @Parameter(name = "active", description = "")
  @BindParam(value = "active")
  Boolean active;

  @Parameter(name = "user_company_ids", description = "")
  @BindParam(value = "user_company_ids")
  String userCompanyIds;

  @Parameter(name = "origin_country", description = "")
  @BindParam(value = "origin_country")
  String originCountry;

  @Parameter(name = "origin_state", description = "")
  @BindParam(value = "origin_state")
  String originState;

  @Parameter(name = "origin_city", description = "")
  @BindParam(value = "origin_city")
  String originCity;

  @Parameter(name = "origin_zip", description = "")
  @BindParam(value = "origin_zip")
  String originZip;

  @Parameter(name = "origin_lat", description = "")
  @BindParam(value = "origin_lat")
  Double originLat;

  @Parameter(name = "origin_long", description = "")
  @BindParam(value = "origin_long")
  Double originLong;

  @Parameter(name = "origin_radius", description = "")
  @BindParam(value = "origin_radius")
  Integer originRadius;

  @Parameter(name = "destination_country", description = "")
  @BindParam(value = "destination_country")
  String destinationCountry;

  @Parameter(name = "destination_state", description = "")
  @BindParam(value = "destination_state")
  String destinationState;

  @Parameter(name = "destination_city", description = "")
  @BindParam(value = "destination_city")
  String destinationCity;

  @Parameter(name = "destination_zip", description = "")
  @BindParam(value = "destination_zip")
  String destinationZip;

  @Parameter(name = "destination_lat", description = "")
  @BindParam(value = "destination_lat")
  Double destinationLat;

  @Parameter(name = "destination_long", description = "")
  @BindParam(value = "destination_long")
  Double destinationLong;

  @Parameter(name = "destination_radius", description = "")
  @BindParam(value = "destination_radius")
  Integer destinationRadius;

  @Parameter(name = "equipment_ids", description = "")
  @BindParam(value = "equipment_ids")
  String equipmentIds;

  @Parameter(name = "product", description = "")
  @BindParam(value = "product")
  String product;
}
