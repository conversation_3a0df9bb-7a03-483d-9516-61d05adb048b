package com.bulkloads.web.loadalert.domain;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import com.bulkloads.common.BaseDomainService;
import com.bulkloads.common.validation.Result;
import com.bulkloads.common.validation.ValidationMethod;
import com.bulkloads.web.equipment.domain.entity.Equipment;
import com.bulkloads.web.equipment.repository.EquipmentRepository;
import com.bulkloads.web.loadalert.domain.data.LoadAlertData;
import com.bulkloads.web.loadalert.domain.entity.LoadAlert;
import com.bulkloads.web.loadalert.mapper.LoadAlertMapper;
import com.bulkloads.web.loadalert.repository.LoadAlertRepository;
import com.bulkloads.web.loadalert.service.dto.LoadAlertResponse;
import com.bulkloads.web.rate.domain.entity.RateProductCategory;
import com.bulkloads.web.rate.repository.RateProductCategoryRepository;
import com.bulkloads.web.user.domain.entity.User;
import com.bulkloads.web.user.service.UserService;
import com.bulkloads.web.usercompany.domain.entity.UserCompany;
import com.bulkloads.web.usercompany.repository.UserCompanyRepository;
import org.springframework.stereotype.Component;
import lombok.RequiredArgsConstructor;

@Component
@RequiredArgsConstructor
public class LoadAlertDomainService extends BaseDomainService<LoadAlert> {

  private final LoadAlertMapper loadAlertMapper;
  private final EquipmentRepository equipmentRepository;
  private final RateProductCategoryRepository rateProductCategoryRepository;
  private final LoadAlertRepository loadAlertRepository;
  private final UserCompanyRepository userCompanyRepository;
  private final UserService userService;

  public Result<LoadAlert> create(LoadAlertData data) {
    return validate(new LoadAlert(), null, data, ValidationMethod.CREATE);
  }

  public Result<LoadAlert> update(LoadAlert loadAlertsToUpdate, LoadAlertData data) {
    return validate(loadAlertsToUpdate, null, data, ValidationMethod.UPDATE);
  }

  public Result<LoadAlert> delete(LoadAlert loadAlertToRemove) {
    loadAlertToRemove.setDeleted(true);
    loadAlertToRemove.setDeletedDate(Instant.now());
    return new Result<>(loadAlertToRemove);
  }

  @Override
  public void validateDataAndMapToEntity(Result<LoadAlert> result,
                                         LoadAlert entity, LoadAlert existing,
                                         Object data, ValidationMethod method) {

    LoadAlertData loadAlertData = (LoadAlertData) data;

    final Instant now = Instant.now();
    final User user = userService.getLoggedInUser();

    if (method == ValidationMethod.CREATE) {
      entity.setUser(user);
      entity.setDateAdded(now);
      entity.setLastRun(now);

      // default to 0 for CREATE
      if (loadAlertData.getFrequency() == null) {
        loadAlertData.setFrequency(0);
      }
    } else {
      entity.setDateEdited(now);
    }

    // frequency validation
    if (!isValidFrequency(loadAlertData.getFrequency())) {
      result.addError("frequency", "Valid values for the frequency are 0, 15, 30, 60, 180, and 360");
    }

    // companies validation
    entity.setUserCompanyIds(new ArrayList<>());
    entity.setCompanyNames(new ArrayList<>());
    if (!loadAlertData.getUserCompanyIds().isEmpty()) {
      List<UserCompany> companies =
          userCompanyRepository.findAllByUserCompanyIdInOrderByCompanyNameAsc(loadAlertData.getUserCompanyIds());

      // convert list to a string of comma-delimited integers
      entity.setUserCompanyIds(companies.stream().map(UserCompany::getUserCompanyId).collect(Collectors.toCollection(ArrayList::new)));
      entity.setCompanyNames(companies.stream().map(UserCompany::getCompanyName).collect(Collectors.toCollection(ArrayList::new)));
    }

    // Equipments validation
    entity.setEquipmentIds(new ArrayList<>());
    entity.setEquipmentNames(new ArrayList<>());
    if (loadAlertData.getEquipmentIds() != null && !loadAlertData.getEquipmentIds().isEmpty()) {
      List<Equipment> equipments = equipmentRepository.findAllByEquipmentIdInOrderByEquipmentIdAsc(loadAlertData.getEquipmentIds());

      // find and create list of equipment ids and names
      loadAlertData.setEquipmentIds(equipments.stream().map(Equipment::getEquipmentId).collect(Collectors.toCollection(ArrayList::new)));
      loadAlertData.setEquipmentNames(equipments.stream().map(Equipment::getEquipmentName).collect(Collectors.toCollection(ArrayList::new)));
    }

    // Rate product categories validation
    entity.setProduct(new ArrayList<>());
    if (!loadAlertData.getProduct().isEmpty()) {
      List<RateProductCategory> categories =
          rateProductCategoryRepository.findAllByRateProductCategoryInOrderByRateProductCategoryAsc(loadAlertData.getProduct());

      // find and concatenate
      entity.setRateProductCategoryIds(categories.stream().map(RateProductCategory::getRateProductCategoryId).collect(Collectors.toCollection(ArrayList::new)));
      entity.setProduct(categories.stream().map(RateProductCategory::getRateProductCategory).collect(Collectors.toCollection(ArrayList::new)));
    }



    // radius validation
    if (loadAlertData.getOriginRadius() != null && (loadAlertData.getOriginLat() == null || loadAlertData.getOriginLong() == null)) {
      result.addError("origin_radius", "You must provide the origin_lat and origin_long if you specify an origin_radius");
    }

    if (loadAlertData.getDestinationRadius() != null && (loadAlertData.getDestinationLat() == null || loadAlertData.getDestinationLong() == null)) {
      result.addError("destination_radius", "You must provide the destination_lat and destination_long if you specify a destination_radius");
    }

  }

  // frequency validation
  private boolean isValidFrequency(Integer frequency) {
    return frequency != null && (frequency == 0 || frequency == 15 || frequency == 30 || frequency == 60 || frequency == 180 || frequency == 360);
  }

  @Override
  public void mapToEntityAuto(Object data, LoadAlert entity) {
    loadAlertMapper.dataToEntity((LoadAlertData) data, entity);
  }

  @Override
  public void validateEntity(Result<LoadAlert> result, LoadAlert entity) {
    // duplicate check
    final List<LoadAlertResponse> alertDuplicates = loadAlertRepository.findDuplicate(entity);

    if (!alertDuplicates.isEmpty()) {
      result.addError("general", "You already have an active alert with the same criteria");
    }
  }

}
