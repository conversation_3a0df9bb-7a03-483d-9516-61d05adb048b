package com.bulkloads.web.loadalert.domain.entity;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import com.bulkloads.common.jpa.CsvListSize;
import com.bulkloads.web.common.jpa.converter.CsvIntegerListConverter;
import com.bulkloads.web.common.jpa.converter.CsvStringListConverter;
import com.bulkloads.web.user.domain.entity.User;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.validation.constraints.DecimalMax;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import jakarta.validation.constraints.PositiveOrZero;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;

@Entity
@Getter
@Setter
@Table(name = "load_alerts")
public class LoadAlert {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "load_alert_id")
  private Integer loadAlertId;

  @NotNull
  @ManyToOne(cascade = {CascadeType.PERSIST, CascadeType.MERGE, CascadeType.DETACH, CascadeType.REFRESH}, fetch = FetchType.LAZY)
  @JoinColumn(name = "user_id")
  private User user;

  @NotNull
  @Column(name = "site_id")
  private Integer siteId = 1;

  @Size(max = 25, message = "The origin_country must be up to 25 chars")
  @NotNull(message = "The origin_country cannot be null")
  @Column(name = "origin_country")
  private String originCountry = "";

  @Size(max = 255, message = "The origin_state must be up to 255 chars")
  @NotNull(message = "The origin_state cannot be null")
  @Column(name = "origin_state")
  private String originState = "";

  @Size(max = 40, message = "The origin_city must be up to 40 chars")
  @NotNull(message = "The origin_city cannot be null")
  @Column(name = "origin_city")
  private String originCity = "";

  @Size(max = 7, message = "The origin_zip must be up to 7 chars")
  @NotNull(message = "The origin_zip cannot be null")
  @Column(name = "origin_zip")
  private String originZip = "";

  @DecimalMin(value = "-90.0")
  @DecimalMax(value = "90.0")
  @Column(name = "origin_lat")
  private Double originLat;

  @DecimalMin(value = "-180.0")
  @DecimalMax(value = "180.0")
  @Column(name = "origin_long")
  private Double originLong;

  @Positive(message = "The origin radius must be a positive number")
  @Column(name = "origin_radius")
  private Integer originRadius;

  @Size(max = 25, message = "The destination_country must be up to 25 chars")
  @NotNull(message = "The destination_country cannot be null")
  @Column(name = "destination_country")
  private String destinationCountry = "";

  @Size(max = 255, message = "The destination_state must be up to 255 chars")
  @NotNull(message = "The destination_state cannot be null")
  @Column(name = "destination_state")
  private String destinationState = "";

  @Size(max = 40, message = "The destination_city must be up to 40 chars")
  @NotNull(message = "The destination_city cannot be null")
  @Column(name = "destination_city")
  private String destinationCity = "";

  @Size(max = 7, message = "The destination_zip must be up to 7 chars")
  @NotNull(message = "The destination_zip cannot be null")
  @Column(name = "destination_zip")
  private String destinationZip = "";

  @DecimalMin(value = "-90.0")
  @DecimalMax(value = "90.0")
  @Column(name = "destination_lat")
  private Double destinationLat;

  @DecimalMin(value = "-180.0")
  @DecimalMax(value = "180.0")
  @Column(name = "destination_long")
  private Double destinationLong;

  @Positive(message = "The destination radius must be a positive number")
  @Column(name = "destination_radius")
  private Integer destinationRadius;

  @NotNull(message = "The user_company_ids must be up to 5000 chars")
  @CsvListSize(max = 300)
  @Convert(converter = CsvIntegerListConverter.class)
  @Column(name = "user_company_ids")
  private List<Integer> userCompanyIds = new ArrayList<>();

  @CsvListSize(max = 300)
  @Convert(converter = CsvStringListConverter.class)
  @Column(name = "company_names")
  private List<String> companyNames = new ArrayList<>();

  @CsvListSize(max = 300)
  @Convert(converter = CsvStringListConverter.class)
  @Column(name = "equipment_ids")
  private List<String> equipmentIds = new ArrayList<>();

  @CsvListSize(max = 300)
  @Convert(converter = CsvStringListConverter.class)
  @Column(name = "equipment_names")
  private List<String> equipmentNames = new ArrayList<>();

  @CsvListSize(max = 50)
  @Convert(converter = CsvIntegerListConverter.class)
  @Column(name = "rate_product_category_ids")
  private List<Integer> rateProductCategoryIds = new ArrayList<>();

  @CsvListSize(max = 50)
  @Convert(converter = CsvStringListConverter.class)
  @Column(name = "product")
  private List<String> product = new ArrayList<>();

  @Column(name = "date_added")
  private Instant dateAdded;

  @Column(name = "date_edited")
  private Instant dateEdited;

  @Column(name = "deleted_date")
  private Instant deletedDate;

  @Column(name = "deleted")
  private Boolean deleted = false;

  @Column(name = "active")
  private Boolean active = true;

  @NotNull(message = "The frequency must be a number")
  @PositiveOrZero(message = "The frequency must be 0 or positive number")
  @Column(name = "frequency")
  private Integer frequency;

  @Column(name = "last_run")
  private Instant lastRun;
}
