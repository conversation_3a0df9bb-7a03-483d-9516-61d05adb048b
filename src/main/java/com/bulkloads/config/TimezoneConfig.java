package com.bulkloads.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import net.iakovlev.timeshape.TimeZoneEngine;

@Configuration
public class TimezoneConfig {

  @Bean
  public TimeZoneEngine timezoneEngine() {
    // init with a north america bounding box for less memory usage
    return TimeZoneEngine.initialize(5.0, -168.0, 84.0, -52.0, false);
  }
}