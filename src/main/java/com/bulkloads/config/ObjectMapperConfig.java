package com.bulkloads.config;

import static com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS;
import com.bulkloads.common.jackson.BooleanAsNumberSerializer;
import com.bulkloads.common.jackson.BulkloadsNullSerializerProvider;
import com.bulkloads.common.jackson.TrimStringDeserializer;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.datatype.jdk8.Jdk8Module;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.module.paramnames.ParameterNamesModule;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.converter.json.Jackson2ObjectMapperBuilder;

@Configuration
public class ObjectMapperConfig {

  @Bean
  public Jackson2ObjectMapperBuilder jackson2ObjectMapperBuilder() {

    final BulkloadsNullSerializerProvider serializerProvider = new BulkloadsNullSerializerProvider();

    return new Jackson2ObjectMapperBuilder()
        .failOnUnknownProperties(false)
        .featuresToDisable(WRITE_DATES_AS_TIMESTAMPS)
        .propertyNamingStrategy(PropertyNamingStrategies.SNAKE_CASE)
        .failOnEmptyBeans(false)
        .postConfigurer(objectMapper -> objectMapper.setSerializerProvider(serializerProvider))
        .modules(new JavaTimeModule(), jdk8Module(), trimStringModule(), new ParameterNamesModule())
        .serializerByType(Boolean.class, new BooleanAsNumberSerializer());
  }

  private Jdk8Module jdk8Module() {
    final Jdk8Module jdk8module = new Jdk8Module();
    jdk8module.configureReadAbsentAsNull(false);
    return jdk8module;
  }

  private SimpleModule trimStringModule() {
    SimpleModule module = new SimpleModule();
    module.addDeserializer(String.class, new TrimStringDeserializer());
    return module;
  }
}
