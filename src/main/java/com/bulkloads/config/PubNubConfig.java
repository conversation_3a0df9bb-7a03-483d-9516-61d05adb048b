package com.bulkloads.config;

import com.pubnub.api.PNConfiguration;
import com.pubnub.api.PubNub;
import com.pubnub.api.PubNubException;
import com.pubnub.api.UserId;
import com.pubnub.api.enums.PNLogVerbosity;
import com.pubnub.api.enums.PNReconnectionPolicy;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Configuration
public class PubNubConfig {

  @Bean
  public PubNub pubNub(final AppProperties appProperties) throws PubNubException {
    final AppProperties.PubNub pubNubProps = appProperties.getPubNub();
    final String pubKey = pubNubProps.getPubKey();
    final String subKey = pubNubProps.getSubKey();
    final String uuid = pubNubProps.getUuid();

    final UserId userId = new UserId(uuid);
    final PNConfiguration pnConfiguration = new PNConfiguration(userId)
        .setPublishKey(pubKey)
        .setSubscribeKey(subKey)
        .setReconnectionPolicy(PNReconnectionPolicy.LINEAR);

    if (log.isDebugEnabled()) {
      pnConfiguration.setLogVerbosity(PNLogVerbosity.BODY);
    }

    return new PubNub(pnConfiguration);
  }

}
