package com.bulkloads.config;

import com.bulkloads.security.LoggingInterceptor;
import org.jetbrains.annotations.NotNull;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.MediaType;
import org.springframework.web.servlet.config.annotation.ContentNegotiationConfigurer;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Configuration
@RequiredArgsConstructor
public class WebConfiguration implements WebMvcConfigurer {

  private final LoggingInterceptor loggingInterceptor;

  @Override
  public void addInterceptors(final InterceptorRegistry registry) {
    registry.addInterceptor(loggingInterceptor);
  }

  @Override
  public void configureContentNegotiation(@NotNull ContentNegotiationConfigurer configurer) {
    configurer.ignoreAcceptHeader(true)
        .defaultContentType(MediaType.APPLICATION_JSON);
  }
}