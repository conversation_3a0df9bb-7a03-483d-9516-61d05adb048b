package com.bulkloads.common.api;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Value;

@Builder
@Value
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ApiResponse<E, K> {

  @Schema(description = "Message.", requiredMode = Schema.RequiredMode.REQUIRED)
  String message;
  @Schema(description = "Key.", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  K key;
  @Schema(description = "Data.", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  E data;
}
