package com.bulkloads.common.mui.model;

import static com.bulkloads.common.StringUtil.toSnakeCase;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import com.bulkloads.exception.BulkloadsException;
import com.qs.core.model.QSArray;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class FilterItem {

  private String field;

  @Builder.Default
  private List<String> values = new ArrayList<>();

  private Class<?> clasz;
  private FieldType fieldType;

  private String operator;

  public boolean isNumeric() {
    return getFieldType() == FieldType.NUMERIC && NUMERIC_OPERATORS.contains(getOperator());
  }

  public boolean isBoolean() {
    return getFieldType() == FieldType.BOOLEAN && BOOLEAN_OPERATORS.contains(getOperator());
  }

  public boolean isString() {
    return getFieldType() == FieldType.STRING && STRING_OPERATORS.contains(getOperator());
  }

  public boolean isDate() {
    return getFieldType() == FieldType.DATE && DATE_OPERATORS.contains(getOperator());
  }

  public void setField(final String field) {
    this.field = toSnakeCase(field);
  }

  public void setClasz(final Class<?> clasz) {
    this.clasz = clasz;
    setFieldType(classifyType(this.clasz));
  }

  public static final Set<String> STRING_OPERATORS = Set.of(
      "equals",
      "is", // comes from Enumerations
      "doesNotEqual",
      "isNot", // comes from Enumerations
      "contains",
      "doesNotContain",
      "startsWith",
      "endsWith",
      "isEmpty", // also in Enumerations
      "isNotEmpty", // also in Enumerations
      "isAnyOf" // also in Enumerations
  );

  public static final Set<String> NUMERIC_OPERATORS = Set.of(
      "=",
      "!=",
      ">",
      ">=",
      "<",
      "<=",
      "isEmpty",
      "isNotEmpty",
      "isAnyOf");

  public static final Set<String> BOOLEAN_OPERATORS = Set.of("is");

  public static final Set<String> DATE_OPERATORS = Set.of(
      "is",
      "not",
      "after",
      "onOrAfter",
      "before",
      "onOrBefore",
      "isEmpty",
      "isNotEmpty");

  public static FieldType classifyType(Class<?> fieldClass) {
    if (fieldClass.equals(String.class)) {
      return FieldType.STRING;
    } else if (
        fieldClass.equals(Integer.class) || fieldClass.equals(int.class)
        || fieldClass.equals(Short.class) || fieldClass.equals(short.class)
        || fieldClass.equals(Long.class) || fieldClass.equals(long.class)
        || fieldClass.equals(Double.class) || fieldClass.equals(double.class)
        || fieldClass.equals(Float.class) || fieldClass.equals(float.class)
        || fieldClass.equals(BigDecimal.class)
    ) {
      return FieldType.NUMERIC;
    } else if (fieldClass.equals(Boolean.class) || fieldClass.equals(boolean.class)) {
      return FieldType.BOOLEAN;

    } else if (fieldClass.equals(LocalDate.class)
               || fieldClass.equals(LocalDateTime.class)
               || fieldClass.equals(Instant.class)) {

      return FieldType.DATE;
    } else {
      throw new BulkloadsException("Unknown type for query field: " + fieldClass);
    }
  }

  void parseValues(Object value) {

    if ((isNumeric() || isString()) && operatorIs("isAnyOf")) {
      setValuesFromArray(value);

    } else if ((isNumeric() || isString() || isDate())
               && operatorIs("isEmpty")
               && operatorIs("isNotEmpty")) {

      // Note: no need to add a value
    } else {
      setValuesFromString(value);
    }
  }

  void setValuesFromString(Object value) {
    values = new ArrayList<>();
    if (value == null) {
      values.add("");
    } else {
      values.add((String) value);
    }
  }

  void setValuesFromArray(Object value) {
    if (value instanceof final QSArray qsArray) {
      values = new ArrayList<>();
      for (Object valueItem : qsArray) {
        if (valueItem != null) {
          values.add((String) valueItem);
        }
      }
    } else {
      values.add("");
    }
  }

  boolean operatorIs(String operatorValue) {
    return operator.equalsIgnoreCase(operatorValue);
  }

}

