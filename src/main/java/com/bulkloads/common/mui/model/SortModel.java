package com.bulkloads.common.mui.model;

import static com.bulkloads.common.StringUtil.toSnakeCase;
import static java.util.Objects.nonNull;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import com.qs.core.model.QSArray;
import com.qs.core.model.QSObject;
import lombok.AllArgsConstructor;
import lombok.Data;

@Data
@AllArgsConstructor
public class SortModel {

  private List<SortItem> sortItems;

  public static SortModel parse(QSObject qs, Map<String, FieldInfo> fields) {

    List<SortItem> sortItems = new ArrayList<>();

    QSArray sortItemsQsArray = (QSArray) qs.getOrDefault("sort", new QSArray());

    for (Object sortItemObject : sortItemsQsArray) {
      QSObject sortItemQsObject = (QSObject) sortItemObject;

      SortItem sortItem = new SortItem();
      String field = toSnakeCase((String) sortItemQsObject.get("field"));
      if (nonNull(fields) && fields.containsKey(field)) {
        sortItem.setNaturalSort(fields.get(field).isNaturalSort());
      }
      sortItem.setField(field);
      sortItem.setSort(parseSortOrder(sortItemQsObject.get("sort")));
      sortItems.add(sortItem);
    }

    return new SortModel(sortItems);
  }

  static String parseSortOrder(Object sortOrderObject) {
    String sortOrder;
    if (sortOrderObject == null) {
      sortOrder = "asc";
    } else {
      if (sortOrderObject instanceof String) {
        sortOrder = (String) sortOrderObject;
        if (!(sortOrder.equals("asc") || sortOrder.equals("desc"))) {
          sortOrder = "asc";
        }
      } else {
        sortOrder = "asc";
      }
    }
    return sortOrder;
  }

}
