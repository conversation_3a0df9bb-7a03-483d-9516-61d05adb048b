package com.bulkloads.common;

import static com.bulkloads.common.validation.ValidationUtils.isEmpty;
import static com.bulkloads.common.validation.ValidationUtils.isValidUrl;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.net.URI;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.text.ParseException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.Objects;
import java.util.UUID;
import java.util.function.BiFunction;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import com.google.common.base.CaseFormat;
import org.apache.commons.lang3.time.DateUtils;
import org.jsoup.Jsoup;
import org.jsoup.safety.Safelist;
import org.springframework.data.util.Pair;
import org.springframework.util.StringUtils;
import org.sqids.Sqids;

public class StringUtil {

  private static final Pattern URL_LINKS_PATTERN = Pattern.compile("href=\"(http[^\"]+)\"", Pattern.CASE_INSENSITIVE);

  private static final Pattern MULTIPLE_SPACES = Pattern.compile("\\s{2,}");
  private static final Pattern HEAD = Pattern.compile("<\\s*head.*?>.*?</head>", Pattern.DOTALL | Pattern.CASE_INSENSITIVE);
  private static final Pattern SCRIPT = Pattern.compile("<\\s*script.*?>.*?</script>", Pattern.DOTALL | Pattern.CASE_INSENSITIVE);
  private static final Pattern STYLE = Pattern.compile("<\\s*style.*?>.*?</style>", Pattern.DOTALL | Pattern.CASE_INSENSITIVE);
  private static final Pattern TD = Pattern.compile("<\\s*td.*?>", Pattern.CASE_INSENSITIVE);
  private static final Pattern BR = Pattern.compile("<\\s*br\\s*/?>", Pattern.CASE_INSENSITIVE);
  private static final Pattern LI = Pattern.compile("<\\s*li\\s*>", Pattern.CASE_INSENSITIVE);
  private static final Pattern DIV = Pattern.compile("<\\s*div.*?>", Pattern.CASE_INSENSITIVE);
  private static final Pattern TR = Pattern.compile("<\\s*tr.*?>", Pattern.CASE_INSENSITIVE);
  private static final Pattern P = Pattern.compile("<\\s*p.*?>", Pattern.CASE_INSENSITIVE);
  private static final Pattern TAGS = Pattern.compile("<.*?>", Pattern.DOTALL);
  private static final Pattern NBSP = Pattern.compile("&nbsp;");
  private static final Pattern BULL = Pattern.compile("&bull;");
  private static final Pattern LSAQUO = Pattern.compile("&lsaquo;");
  private static final Pattern RSAQUO = Pattern.compile("&rsaquo;");
  private static final Pattern TRADE = Pattern.compile("&trade;");
  private static final Pattern FRASL = Pattern.compile("&frasl;");
  private static final Pattern LT = Pattern.compile("&lt;");
  private static final Pattern GT = Pattern.compile("&gt;");
  private static final Pattern COPY = Pattern.compile("&copy;");
  private static final Pattern REG = Pattern.compile("&reg;");
  private static final Pattern AMP = Pattern.compile("&amp;");
  private static final Pattern QUOT = Pattern.compile("&quot;");
  private static final Pattern OTHER_ENTITIES = Pattern.compile("&(.{2,6});");

  private static final Sqids sqids = Sqids.builder()
      .alphabet("FxnX7l2RePyY8M16cuhsAVK04Tzt9fHdpimLwaUQrqSGkBNIJvjW3CoDOgb5ZE")
      .minLength(6)
      .build();

  public static String trimNewLines(String str) {
    return str.replaceAll("(?m)^\s*\n", "");
  }

  public static String toSnakeCase(String camelCase) {
    if (camelCase == null) {
      return null;
    }
    return CaseFormat.UPPER_CAMEL.to(CaseFormat.LOWER_UNDERSCORE, camelCase);
  }

  public static String toCamelCase(String snakeCase) {
    if (snakeCase == null) {
      return null;
    }
    return CaseFormat.LOWER_UNDERSCORE.to(CaseFormat.LOWER_CAMEL, snakeCase);
  }

  public static String lineTransformer(String inputString, Function<String, String> lineTransformer) {
    return Arrays.stream(inputString.split("\\r?\\n"))
        .map(lineTransformer)
        .collect(Collectors.joining("\n"));
  }

  public static String lineTransformer(String inputString, BiFunction<String, Integer, String> lineTransformer) {
    String[] lines = inputString.split("\\r?\\n");
    return IntStream.range(0, lines.length)
        .mapToObj(i -> lineTransformer.apply(lines[i], i + 1))
        .collect(Collectors.joining("\n"));
  }

  public static Pair<String, String> parseFullName(final String fullName) {
    Objects.requireNonNull(fullName);
    String name = fullName.trim();
    name = name.replaceAll("  +", " ");

    String first;
    String last;

    int index = name.indexOf(' ');

    if (index != -1) {
      first = name.substring(0, index);
      last = name.substring(index + 1);
    } else {
      first = name;
      last = "";
    }

    return Pair.of(first, last);
  }

  public static void parseFullName(
      final String fullName,
      Consumer<Pair<String, String>> consumer) {

    consumer.accept(parseFullName(fullName));
  }

  public static String properCase(String input) {
    if (input == null) {
      return null;
    }

    if (input.trim().isEmpty()) {
      return input;
    }

    String[] words = input.replaceAll("  +", " ").split(" ");
    StringBuilder result = new StringBuilder();

    for (String word : words) {
      if (!word.isEmpty()) {
        String capitalizedWord = word.substring(0, 1).toUpperCase() + word.substring(1).toLowerCase();
        result.append(capitalizedWord).append(" ");
      }
    }
    return result.toString().trim();
  }

  public static Number numberParser(String value) {
    if (isEmpty(value)) {
      return null;
    }
    NumberFormat format = NumberFormat.getInstance(Locale.US);
    try {
      return format.parse(value);
    } catch (ParseException e) {
      // throw new BulkloadsException("Could not parse " + value, e);
      return null;
    }
  }

  public static Integer parseInteger(String value) {
    Number number = numberParser(value);
    if (number == null) {
      return null;
    }
    return number.intValue();
  }

  public static Double parseDouble(String value) {
    Number number = numberParser(value);
    if (number == null) {
      return null;
    }
    return number.doubleValue();
  }

  public static String removeNewLines(String value) {
    return Arrays.stream(value.split("\\r\\n|[\\r\\n]"))
        .map(String::trim)
        .collect(Collectors.joining());
  }

  public static String intFormat(Long value) {
    DecimalFormat df = new DecimalFormat("0");
    return df.format(value);
  }

  public static String intFormat(Integer value) {
    DecimalFormat df = new DecimalFormat("0");
    return df.format(value);
  }

  // Amounts (dollarFormat) and Percentages

  public static String amountFormat(BigDecimal amount) {
    DecimalFormat df = new DecimalFormat("#,##0.00");
    df.setRoundingMode(RoundingMode.HALF_UP);
    df.setMinimumFractionDigits(2);
    df.setMaximumFractionDigits(2);
    return df.format(amount);
  }

  public static String amountFormat(Double amount) {
    DecimalFormat df = new DecimalFormat("#,##0.00");
    df.setRoundingMode(RoundingMode.HALF_UP);
    df.setMinimumFractionDigits(2);
    df.setMaximumFractionDigits(2);
    return df.format(amount);
  }

  public static String percentageFormat(BigDecimal amount) {
    return amountFormat(amount);
  }

  public static String percentageFormat(Double amount) {
    return amountFormat(amount);
  }

  public static String dollarFormat(BigDecimal amount) {
    return "$" + amountFormat(amount);
  }

  public static String dollarFormat(Double amount) {
    return "$" + amountFormat(amount);
  }

  // Weight, Volume, Miles, Hours and Rate

  public static String numberFormat(BigDecimal amount) {
    DecimalFormat df = new DecimalFormat("#,###.#####");
    df.setRoundingMode(RoundingMode.HALF_UP);

    if (amount.compareTo(BigDecimal.ZERO) != 0 && amount.abs().compareTo(BigDecimal.ONE) < 0) {
      df.setMinimumFractionDigits(2);
    }

    return df.format(amount);
  }

  public static String numberFormat(Double number) {
    DecimalFormat df = new DecimalFormat("#,###.#####");
    df.setRoundingMode(RoundingMode.HALF_UP);

    if (number != 0 && Math.abs(number) < 1) {
      df.setMinimumFractionDigits(2);
    }
    return df.format(number);
  }

  public static String weightFormat(BigDecimal weight) {
    return numberFormat(weight);
  }

  public static String weightFormat(Double weight) {
    return numberFormat(weight);
  }

  public static String volumeFormat(BigDecimal volume) {
    return numberFormat(volume);
  }

  public static String volumeFormat(Double volume) {
    return numberFormat(volume);
  }

  public static String milesFormat(BigDecimal miles) {
    return numberFormat(miles);
  }

  public static String milesFormat(Double miles) {
    return numberFormat(miles);
  }

  public static String hoursFormat(BigDecimal hours) {
    return numberFormat(hours);
  }

  public static String hoursFormat(Double hours) {
    return numberFormat(hours);
  }

  public static String rateFormat(BigDecimal rate, String rateTypeText) {
    if (rate == null) {
      return "";
    }
    return rateFormat(rate) + rateTypeText;
  }

  public static String rateFormat(BigDecimal rate) {
    return '$' + numberFormat(rate);
  }

  public static String rateFormat(Double rate) {
    return '$' + numberFormat(rate);
  }

  public static String stripHtml(String content) {
    return content.replaceAll("<[^>]*>", "");
  }

  public static String htmlToText(String source) {
    // Trim and remove multiple spaces
    String result = source.trim();
    result = MULTIPLE_SPACES.matcher(result).replaceAll(" ");

    // Remove head, scripts, styles
    result = HEAD.matcher(result).replaceAll("");
    result = SCRIPT.matcher(result).replaceAll("");
    result = STYLE.matcher(result).replaceAll("");

    // Insert tabs for <td>
    result = TD.matcher(result).replaceAll("  ");

    // Line breaks for <br>, <li>
    result = BR.matcher(result).replaceAll("\n");
    result = LI.matcher(result).replaceAll("\n");

    // Double line breaks for <div>, <tr>, <p>
    result = DIV.matcher(result).replaceAll("\n");
    result = TR.matcher(result).replaceAll("\n");
    result = P.matcher(result).replaceAll("\n");

    // Remove remaining tags
    result = TAGS.matcher(result).replaceAll("");

    // Replace special entities
    result = NBSP.matcher(result).replaceAll(" ");
    result = BULL.matcher(result).replaceAll(" * ");
    result = LSAQUO.matcher(result).replaceAll("<");
    result = RSAQUO.matcher(result).replaceAll(">");
    result = TRADE.matcher(result).replaceAll("(tm)");
    result = FRASL.matcher(result).replaceAll("/");
    result = LT.matcher(result).replaceAll("<");
    result = GT.matcher(result).replaceAll(">");
    result = COPY.matcher(result).replaceAll("(c)");
    result = REG.matcher(result).replaceAll("(r)");
    result = AMP.matcher(result).replaceAll("&");
    result = QUOT.matcher(result).replaceAll("\"");

    // Remove all other entities
    result = OTHER_ENTITIES.matcher(result).replaceAll("");

    return result;
  }

  public static String safeHtml(String htmlContent) {
    if (htmlContent == null) {
      return null;
    }
    // Define a Safelist (whitelist) to specify allowed HTML elements and attributes
    Safelist safelist = Safelist.basic(); // Allows basic HTML tags like <b>, <i>, etc.
    // Clean the HTML content
    return Jsoup.clean(htmlContent, safelist);
  }

  public static List<String> extractLinksFromHtml(String htmlContent) {
    List<String> links = new ArrayList<>();
    if (htmlContent != null) {
      Matcher matcher = URL_LINKS_PATTERN.matcher(htmlContent);
      while (matcher.find()) {
        String link = matcher.group(1);
        if (isValidUrl(link)) {
          links.add(link);
        }
      }
    }
    return links;
  }

  public static String buildLocationString(String city, String state, String zip, String country) {

    if (!isEmpty(city) && !isEmpty(state)) {
      String location = city + ", " + state;

      if (!isEmpty(zip)) {
        location += " " + zip;
      }
      if (!isEmpty(country) && !country.equals("US")) {
        location += ", " + country;
      }
      return location;
    }
    return "";
  }

  public static String currentDateTime(String dateTimeFormat) {
    return LocalDateTime.now().format(DateTimeFormatter.ofPattern(dateTimeFormat));
  }

  public static String extractFileName(String fileUrl) {
    try {
      String path = new URI(fileUrl).getPath();
      String fileName = path.substring(path.lastIndexOf('/') + 1);
      return URLDecoder.decode(fileName, StandardCharsets.UTF_8);
    } catch (Exception e) {
      throw new IllegalArgumentException("Invalid URL: " + fileUrl, e);
    }
  }

  public static String asCsString(Collection<?> values) {
    if (isEmpty(values)) {
      return "";
    }
    return values.stream()
        .map(Object::toString)
        .collect(Collectors.joining(","));
  }

  public static String getUuid() {
    return UUID.randomUUID().toString().toLowerCase();
  }

  public static String restrictMaxSize(String value, int maxSize) {
    if (value == null) {
      return null;
    }
    value = value.trim();

    if (value.length() > maxSize) {
      return value.substring(0, maxSize);
    }
    return value;
  }

  public static LocalDate parseLocalDate(String dateStr) {
    String[] possibleFormats = {
        "M/d/y", "MM/dd/yyyy", "d/M/y", "dd/MM/yyyy", "yyyy-MM-dd",
        "d-M-y", "d MMM yyyy", "MMM d, yyyy",
        "yyyy-MM-dd'T'HH:mm:ss", "yyyy-MM-dd'T'HH:mm:ss.SSS",
        "yyyy-MM-dd'T'HH:mm:ssX", "yyyy-MM-dd'T'HH:mm:ss.SSSX",
        "yyyy-MM-dd'T'HH:mm:ssXXX", "yyyy-MM-dd'T'HH:mm:ss.SSSXXX"
    };
    try {
      Date date = DateUtils.parseDate(dateStr, possibleFormats);
      return date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
    } catch (ParseException e) {
      // Try ISO 8601 parsing as fallback
      try {
        return LocalDate.parse(dateStr, DateTimeFormatter.ISO_DATE);
      } catch (Exception ex) {
        try {
          return LocalDateTime.parse(dateStr, DateTimeFormatter.ISO_DATE_TIME).toLocalDate();
        } catch (Exception exc) {
          return null;
        }
      }
    }
  }

  public static List<Integer> stringToIntegerList(final String value) {
    if (value == null || value.isEmpty()) {
      return new ArrayList<>();
    }
    return Arrays.stream(value.split(","))
        .map(String::trim) // Remove leading/trailing whitespace
        .filter(s -> s.matches("-?\\d+")) // Only allow valid integers (optional step)
        .map(Integer::parseInt) // Parse to Integer
        .toList(); // Collect to a list
  }

  public static String toTitleCase(String status) {
    if (!StringUtils.hasLength(status)) {
      return status;
    }
    return status.substring(0, 1).toUpperCase() + status.substring(1).toLowerCase();
  }

  public static String sanitizeForBooleanModeSearch(String searchTerm) {
    if (searchTerm == null || searchTerm.isEmpty()) {
      return searchTerm;
    }

    searchTerm = searchTerm
        .replaceAll("[^a-zA-Z0-9\\s_]", " ")  // Keep alphanumeric, spaces, and underscores
        .replaceAll("\\s+", " ")
        .trim();

    // If after sanitization we have an empty string, return it
    if (searchTerm.isEmpty()) {
      return searchTerm;
    }

    searchTerm = String.join("* +", searchTerm.split("\\s+"));
    searchTerm = "+" + searchTerm + "*";

    return searchTerm;
  }

  public static String bigIntegerToSqid(BigInteger id) {
    return sqids.encode(List.of(id.longValue()));
  }

  public static BigInteger sqidToBigInteger(String shortCode) {
    List<Long> decoded = sqids.decode(shortCode);
    if (decoded.isEmpty()) {
      throw new IllegalArgumentException("Invalid sqid: " + shortCode);
    }
    return BigInteger.valueOf(decoded.get(0));
  }
}

