package com.bulkloads.common.transformer;

import com.bulkloads.common.api.TotalResponse;
import com.bulkloads.common.jpa.nativejpa.QueryParts;
import org.hibernate.query.TupleTransformer;
import org.springframework.stereotype.Component;

@Component
public class TotalResponseTransformer implements TupleTransformer<TotalResponse> {

  @Override
  public TotalResponse transformTuple(Object[] columns, String[] aliases) {
    QueryParts parts = new QueryParts(columns, aliases);
    return TotalResponse.builder()
        .count(parts.asInteger("count"))
        .build();
  }
}
