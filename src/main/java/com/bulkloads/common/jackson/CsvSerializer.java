package com.bulkloads.common.jackson;

import java.io.IOException;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

public class CsvSerializer extends JsonSerializer<List<?>> {

  @Override
  public void serialize(List<?> values, JsonGenerator gen, SerializerProvider serializers) throws IOException {
    if (values != null) {
      var res = values.stream()
          .filter(Objects::nonNull)
          .map(Object::toString)
          .collect(Collectors.joining(","));

      gen.writeString(res);
    }
  }
}
