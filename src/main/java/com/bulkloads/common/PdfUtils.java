package com.bulkloads.common;

import static com.bulkloads.web.file.util.FileUtils.megaBytes;
import java.awt.Graphics2D;
import java.awt.Image;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.UncheckedIOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;
import java.util.List;
import javax.imageio.ImageIO;
import com.bulkloads.exception.BulkloadsException;
import com.openhtmltopdf.pdfboxout.PdfRendererBuilder;
import com.openhtmltopdf.util.XRLog;
import org.apache.pdfbox.multipdf.PDFMergerUtility;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.pdmodel.PDPageContentStream;
import org.apache.pdfbox.pdmodel.graphics.image.JPEGFactory;
import org.apache.pdfbox.pdmodel.graphics.image.PDImageXObject;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class PdfUtils {

  private static final float IMAGE_TO_PDF_QUALITY = 0.6f;

  public static InputStream htmlToPdf(String documentContent) {
    try {
      XRLog.setLoggingEnabled(false);
      ByteArrayOutputStream os = new ByteArrayOutputStream();
      PdfRendererBuilder builder = new PdfRendererBuilder();
      builder.useFastMode();
      builder.withHtmlContent(documentContent, "");
      builder.useDefaultPageSize(8.5f, 11f, PdfRendererBuilder.PageSizeUnits.INCHES);
      builder.toStream(os);
      builder.run();

      final byte[] byteArray = os.toByteArray();
      InputStream is = new ByteArrayInputStream(byteArray);
      os.close();

      return is;
    } catch (IOException e) {
      log.error(e.getMessage(), e);
      throw new UncheckedIOException(e);
    }
  }

  public static void htmlToPdf(String documentContent, Path outputFilePath) {
    try {
      try (InputStream pdfStream = htmlToPdf(documentContent)) {
        Files.copy(pdfStream, outputFilePath, StandardCopyOption.REPLACE_EXISTING);
      }
    } catch (IOException e) {
      throw new UncheckedIOException(e.getMessage(), e);
    }
  }

  public static void mergePdfFiles(List<Path> pdfFiles, Path outputFile) {
    try {
      PDFMergerUtility pdfMerger = new PDFMergerUtility();
      for (Path pdf : pdfFiles) {
        log.debug("Including PDF: {} size: {} MB", pdf, megaBytes(pdf));
        pdfMerger.addSource(pdf.toFile());
      }
      pdfMerger.setDestinationFileName(outputFile.toString());
      log.debug("Merging {} documents", pdfFiles.size());
      pdfMerger.mergeDocuments(null);

      log.debug("Merged PDF {} size: {} MB", outputFile, megaBytes(outputFile));

    } catch (IOException e) {
      log.error(e.getMessage(), e);
      throw new UncheckedIOException(e);
    }

  }

  public static void convertImageToPdf(final Path imagePath, Path targetPdfPath) {
    if (imagePath == null) {
      log.error("Invalid image file provided");
      throw new BulkloadsException("Invalid image file provided");
    }

    try {
      BufferedImage bimg = ImageIO.read(imagePath.toFile());

      int maxWidth = 900;
      int maxHeight = 900;
      int originalWidth = bimg.getWidth();
      int originalHeight = bimg.getHeight();

      double widthRatio = (double) maxWidth / originalWidth;
      double heightRatio = (double) maxHeight / originalHeight;
      double scale = Math.min(1.0, Math.min(widthRatio, heightRatio));

      if (scale < 1.0) {
        int newWidth = (int) (originalWidth * scale);
        int newHeight = (int) (originalHeight * scale);
        Image tmp = bimg.getScaledInstance(newWidth, newHeight, Image.SCALE_SMOOTH);
        BufferedImage scaledImage = new BufferedImage(newWidth, newHeight, BufferedImage.TYPE_INT_RGB);
        Graphics2D g2d = scaledImage.createGraphics();
        g2d.drawImage(tmp, 0, 0, null);
        g2d.dispose();
        bimg = scaledImage;
      }

      PDDocument doc = new PDDocument();
      PDPage page = new PDPage();
      doc.addPage(page);

      PDImageXObject pdImage = JPEGFactory.createFromImage(doc, bimg, IMAGE_TO_PDF_QUALITY);

      float margin = 50;
      float availableWidth = page.getMediaBox().getWidth() - 2 * margin;
      float availableHeight = page.getMediaBox().getHeight() - 2 * margin;

      float imageWidth = pdImage.getWidth();
      float imageHeight = pdImage.getHeight();
      float ratioX = availableWidth / imageWidth;
      float ratioY = availableHeight / imageHeight;
      float scaleImage = Math.min(ratioX, ratioY);

      try (PDPageContentStream contentStream = new PDPageContentStream(doc, page)) {
        float drawWidth = imageWidth * scaleImage;
        float drawHeight = imageHeight * scaleImage;
        float x = (page.getMediaBox().getWidth() - drawWidth) / 2;
        float y = (page.getMediaBox().getHeight() - drawHeight) / 2;
        contentStream.drawImage(pdImage, x, y, drawWidth, drawHeight);
      }

      doc.save(targetPdfPath.toFile());
      doc.close();

      log.debug("Image {} size: {} MB, PDF {} size: {} MB", imagePath, targetPdfPath, megaBytes(imagePath), megaBytes(targetPdfPath));

    } catch (IOException e) {
      throw new UncheckedIOException(e);
    }
  }
}
