package com.bulkloads.common.jpa.nativejpa;

import java.math.BigDecimal;
import java.sql.Date;
import java.sql.Time;
import java.sql.Timestamp;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.bulkloads.common.Converters;
import com.bulkloads.common.Parsers;
import com.fasterxml.jackson.databind.ObjectMapper;

@SuppressWarnings("unused")
public class QueryParts {

  static final ObjectMapper objectMapper = new ObjectMapper();

  private final Map<String, Object> parts = new HashMap<>();

  public QueryParts(final Object[] columns, final String[] aliases) {
    for (int i = 0; i < aliases.length; i++) {
      parts.put(aliases[i].toLowerCase(), columns[i]);
    }
  }

  public Short asShort(final String columnName) {
    final var value = parts.get(columnName);
    if (value == null) {
      return null;
    }
    return Short.parseShort(String.valueOf(value));
  }

  public Integer asInteger(final String columnName) {
    final var value = parts.get(columnName);
    if (value == null) {
      return null;
    }
    return Integer.parseInt(String.valueOf(value));
  }

  public Long asLong(final String columnName) {
    final var value = parts.get(columnName);
    if (value == null) {
      return null;
    }
    return Long.parseLong(String.valueOf(value));
  }

  public Double asDouble(final String columnName) {
    final var value = parts.get(columnName);
    if (value == null) {
      return null;
    }
    return Double.parseDouble(String.valueOf(value));
  }

  public BigDecimal asBigDecimal(final String columnName) {
    final var value = parts.get(columnName);
    if (value == null) {
      return null;
    }
    return new BigDecimal(String.valueOf(value));
  }

  public String asString(final String columnName) {
    final var value = parts.get(columnName);
    if (value == null) {
      return null;
    }
    return String.valueOf(value);
  }

  public Boolean asBoolean(final String columnName) {
    final var value = parts.get(columnName);
    if (value == null) {
      return null;
    }
    return Parsers.parseBoolean(value);
  }

  // Only for Washouts
  public String asStringBoolean(final String columnName) {
    final var dbValue = parts.get(columnName);
    if (dbValue == null) {
      return null;
    }
    String value = String.valueOf(dbValue);
    if (value == null) {
      return "";
    }

    value = value.trim();

    if (value.equalsIgnoreCase("y") || value.equalsIgnoreCase("1")) {
      return "Y";
    } else if (value.equalsIgnoreCase("n") || value.equalsIgnoreCase("0")) {
      return "N";
    } else {
      return "";
    }

  }

  public LocalDate asLocalDate(final String columnName) {
    final var value = parts.get(columnName);
    if (value instanceof Date date) {
      return date.toLocalDate();
    } else if (value instanceof Timestamp timestamp) {
      return timestamp.toLocalDateTime().toLocalDate();
    }

    return null;
  }

  public LocalTime asLocalTime(final String columnName) {
    final var value = parts.get(columnName);
    if (value instanceof Time) {
      return ((Time) value).toLocalTime();
    } else if (value instanceof java.sql.Timestamp) {
      return ((java.sql.Timestamp) value).toLocalDateTime().toLocalTime();
    } else if (value instanceof LocalTime) {
      return (LocalTime) value;
    } else if (value instanceof String) {
      return LocalTime.parse((String) value);
    }

    return null;
  }

  public Instant asInstant(final String columnName) {
    final var value = parts.get(columnName);
    if (value instanceof Timestamp timestamp) {
      return timestamp.toInstant();
    }

    return null;
  }

  public List<Integer> asIntegerListFromCsv(final String columnName) {
    final var value = parts.get(columnName);
    if (value == null) {
      return new ArrayList<>();
    }
    return Parsers.parseIntegerCsvToList(value.toString());
  }

  public List<String> asStringListFromCsv(final String columnName) {
    final var value = parts.get(columnName);
    if (value == null) {
      return new ArrayList<>();
    }
    return Parsers.parseStringCsvToList(value.toString());
  }

  public Map<String, Object> asMap(final String columnName) {
    final var value = parts.get(columnName);
    if (value == null) {
      return new HashMap<>();
    }
    if (value instanceof String) {
      return Converters.jsonStringToMap((String) value);
    }
    throw new IllegalStateException("Could not parse " + columnName + " with value " + value + " as a String");
  }

  public String asLocation(final String city, final String state, final String zip, final String country) {

    final var cityValue = parts.get(city);
    final var stateValue = parts.get(state);
    final var zipValue = parts.get(zip);
    final var countryValue = parts.get(country);

    String location = String.format("%s, %s %s", cityValue, stateValue, zipValue).trim();

    if (countryValue != null && !countryValue.equals("USA") && !countryValue.equals("US")) {
      location = String.format("%s, %s", location, countryValue).trim();
    }

    return location;

  }
}
