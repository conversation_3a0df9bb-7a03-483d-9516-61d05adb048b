package com.bulkloads.common.jpa;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import jakarta.validation.Constraint;
import jakarta.validation.Payload;

@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = CsvListMaxSize.class)
public @interface CsvListSize {

  String message() default "Value exceeds maximum allowed size";

  Class<?>[] groups() default {};

  Class<? extends Payload>[] payload() default {};

  int max() default Integer.MAX_VALUE;
}