package com.bulkloads.common.jpa;

import static java.time.Instant.now;
import java.time.Instant;
import org.hibernate.annotations.SQLRestriction;
import jakarta.persistence.Column;
import jakarta.persistence.MappedSuperclass;
import lombok.Getter;
import lombok.Setter;

@SQLRestriction("deleted = false")
@Getter
@Setter
@MappedSuperclass
public class Auditable {

  @Column(name = "created_by_user_id")
  private Integer createdByUserId;

  @Column(name = "created_date")
  private Instant createdDate;

  @Column(name = "modified_by_user_id")
  private Integer modifiedByUserId;

  //@Version
  @Column(name = "modified_date")
  private Instant modifiedDate;

  @Column(name = "deleted_by_user_id")
  private Integer deletedByUserId;

  @Column(name = "deleted")
  private Boolean deleted = false;

  @Column(name = "deleted_date")
  private Instant deletedDate;

  public void markCreatedBy(int userId) {
    Instant now = now();
    this.setCreatedByUserId(userId);
    this.setCreatedDate(now);
  }

  public void markModifiedBy(int userId) {
    Instant now = now();
    this.setModifiedByUserId(userId);
    this.setModifiedDate(now);
  }

  public void markDeletedBy(int userId) {
    Instant now = now();
    this.setDeleted(true);
    this.setDeletedByUserId(userId);
    this.setDeletedDate(now);
  }

}
