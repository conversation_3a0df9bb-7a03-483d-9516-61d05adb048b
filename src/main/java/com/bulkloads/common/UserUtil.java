package com.bulkloads.common;

import java.util.Optional;
import com.bulkloads.exception.UnauthorizedActionException;
import com.bulkloads.security.Actor;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.client.authentication.OAuth2AuthenticationToken;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class UserUtil {

  public static Optional<Integer> getUserId() {
    return getActor().flatMap(Actor::getUserId);
  }

  public static int getUserIdOrThrow() {
    return getUserId().orElseThrow(UnauthorizedActionException::new);
  }

  public static Optional<Integer> getUserCompanyId() {
    return getActor().flatMap(Actor::getUserCompanyId);
  }

  public static int getUserCompanyIdOrThrow() {
    return getUserCompanyId().orElseThrow(UnauthorizedActionException::new);
  }

  public static Optional<Integer> getAbUserId() {
    return getActor().flatMap(Actor::getAbUserId);
  }

  public static int getAbUserIdOrThrow() {
    return getAbUserId().orElseThrow(UnauthorizedActionException::new);
  }

  public static boolean isSiteAdmin() {
    return getActor()
        .map(Actor::isSiteAdmin)
        .orElse(false);
  }

  public static Actor getActorOrThrow() {
    return getActor().orElseThrow(UnauthorizedActionException::new);
  }

  public static Optional<Actor> getActor() {
    final Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
    if (authentication instanceof OAuth2AuthenticationToken) {
      return Optional.empty();
    }
    return Optional.ofNullable(authentication)
        .flatMap(auth -> Optional.ofNullable((Actor) auth.getPrincipal()));
  }

  public static Optional<String> getAppName() {
    return getActor().map(Actor::getAppName);
  }
}
