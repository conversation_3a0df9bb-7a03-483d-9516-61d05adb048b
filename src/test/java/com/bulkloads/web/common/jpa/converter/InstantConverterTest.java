package com.bulkloads.web.common.jpa.converter;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import org.junit.jupiter.api.Test;

class InstantConverterTest {

  private final InstantConverter converter = new InstantConverter();

  @Test
  void convertToDatabaseColumn_withValidInstant_returnsLocalDateTime() {
    Instant input = Instant.now();
    final ZoneOffset offset = OffsetDateTime.now().getOffset();
    LocalDateTime expected = input.atOffset(offset).toLocalDateTime();
    LocalDateTime actual = converter.convertToDatabaseColumn(input);
    assertEquals(expected, actual);
  }

  @Test
  void convertToDatabaseColumn_withNull_returnsNull() {
    LocalDateTime actual = converter.convertToDatabaseColumn(null);
    assertNull(actual);
  }

  @Test
  void convertToEntityAttribute_withValidLocalDateTime_returnsInstant() {
    LocalDateTime input = LocalDateTime.now();
    ZoneOffset offset = OffsetDateTime.now().getOffset();
    Instant expected = input.toInstant(offset);
    Instant actual = converter.convertToEntityAttribute(input);
    assertEquals(expected, actual);
  }

  @Test
  void convertToEntityAttribute_withNull_returnsNull() {
    Instant actual = converter.convertToEntityAttribute(null);
    assertNull(actual);
  }
}