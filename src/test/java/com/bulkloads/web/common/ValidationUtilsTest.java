package com.bulkloads.web.common;

import static com.bulkloads.common.validation.ValidationUtils.existsAndIsEmpty;
import static com.bulkloads.common.validation.ValidationUtils.existsAndIsFalse;
import static com.bulkloads.common.validation.ValidationUtils.existsAndIsNotEmpty;
import static com.bulkloads.common.validation.ValidationUtils.existsAndIsTrue;
import static com.bulkloads.common.validation.ValidationUtils.hasChange;
import static com.bulkloads.common.validation.ValidationUtils.isEmpty;
import static com.bulkloads.common.validation.ValidationUtils.isMissingOrIsEmpty;
import static org.assertj.core.api.Assertions.assertThat;
import java.util.Optional;
import org.junit.jupiter.api.Test;

class ValidationUtilsTest {

  @Test
  void isEmptyTest() {
    assertThat(isEmpty(null)).isTrue();

    // Optional
    assertThat(isEmpty(Optional.empty())).isTrue();
    assertThat(isEmpty(Optional.of(new Object()))).isFalse();

    // String and Optional<String>
    assertThat(isEmpty("")).isTrue();
    assertThat(isEmpty("non empty string")).isFalse();
    assertThat(isEmpty(Optional.of(""))).isTrue();
    assertThat(isEmpty(Optional.of("non empty string"))).isFalse();

    // the rest of isEmpty() comes from ObjectUtils (assuming already tested)
  }

  @Test
  void existsAndIsEmptyTest() {
    assertThat(existsAndIsEmpty(null)).isFalse();
    assertThat(existsAndIsEmpty(Optional.empty())).isTrue();

    assertThat(existsAndIsEmpty(Optional.of("non empty string"))).isFalse();
    assertThat(existsAndIsEmpty(Optional.of(new Object()))).isFalse();
  }

  @Test
  void existsAndIsNotEmptyTest() {
    assertThat(existsAndIsNotEmpty(null)).isFalse();
    assertThat(existsAndIsNotEmpty(Optional.empty())).isFalse();

    assertThat(existsAndIsNotEmpty(Optional.of(""))).isFalse();
    assertThat(existsAndIsNotEmpty(Optional.of("non empty string"))).isTrue();
    assertThat(existsAndIsNotEmpty(Optional.of(new Object()))).isTrue();
  }

  @Test
  void existsAndIsTrueTest() {
    assertThat(existsAndIsTrue(null)).isFalse();
    assertThat(existsAndIsTrue(Optional.empty())).isFalse();
    assertThat(existsAndIsTrue(Optional.of(true))).isTrue();
    assertThat(existsAndIsTrue(Optional.of(false))).isFalse();
  }

  @Test
  void existsAndIsFalseTest() {
    assertThat(existsAndIsFalse(null)).isFalse();
    assertThat(existsAndIsFalse(Optional.empty())).isFalse();
    assertThat(existsAndIsFalse(Optional.of(true))).isFalse();
    assertThat(existsAndIsFalse(Optional.of(false))).isTrue();
  }

  @Test
  void isMissingOrIsEmptyTest() {
    assertThat(isMissingOrIsEmpty(null)).isTrue();
    assertThat(isMissingOrIsEmpty(Optional.of(new Object()))).isFalse();
    assertThat(isMissingOrIsEmpty(Optional.empty())).isTrue();
    assertThat(isMissingOrIsEmpty(Optional.of(""))).isTrue();
    assertThat(isMissingOrIsEmpty(Optional.of("non empty string"))).isFalse();
  }

  @Test
  void testHasChange_WhenNewValueIsNull() {
    final Optional<String> newValue = null;
    assertThat(hasChange("oldValue", newValue)).isFalse();
  }

  @Test
  void testHasChange_WhenNewValueAndOldValueAreBothNull() {
    final String oldValue = null;
    final Optional<String> newValue = Optional.empty();
    assertThat(hasChange(oldValue, newValue)).isFalse();
  }

  @Test
  void testHasChange_WhenNewValueEmpty() {
    final String oldValue = "oldValue";
    final Optional<String> newValue = Optional.empty();
    assertThat(hasChange(oldValue, newValue)).isTrue();
  }

  @Test
  void testHasChange_WhenNewValueEqualsOldValue() {
    Optional<String> newValue = Optional.of("oldValue");
    assertThat(hasChange("oldValue", newValue)).isFalse();
  }

  @Test
  void testHasChange_WhenNewValueDiffersFromOldValue() {
    Optional<String> newValue = Optional.of("newValue");
    assertThat(hasChange("oldValue", newValue)).isTrue();
  }

  @Test
  void testHasChange_WhenOldValueIsNull() {
    Optional<String> newValue = Optional.of("newValue");
    final String oldValue = null;
    assertThat(hasChange(oldValue, newValue)).isTrue();
  }
}