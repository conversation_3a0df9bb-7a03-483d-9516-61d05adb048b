package com.bulkloads.web.city.repository;

//class CityRepositoryTest extends RepositoryTest {
//
//  @Autowired
//  private CityRepository cityRepository;
//
//  @Test
//  void testGetCityZip() {
//    final String term = "Mayo, FL 32066";
//    final City result = cityRepository.getCityZip(term);
//    assertNotNull(result);
//  }
//
//  @Test
//  void testGetCityByStateAndCity() {
//    final String originState = "NH";
//    final String originCity = "Manchester";
//    final City result = cityRepository.getCityByStateAndCity(originState, originCity);
//    assertNotNull(result);
//  }
//
//}