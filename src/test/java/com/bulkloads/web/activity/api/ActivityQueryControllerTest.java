package com.bulkloads.web.activity.api;

import static com.bulkloads.config.AppConstants.UserRole.ROLE_USER;
import static com.bulkloads.web.confg.TestConstants.DUMMY_ROLE;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoMoreInteractions;
import static org.mockito.Mockito.when;
import static org.springframework.restdocs.mockmvc.RestDocumentationRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import java.util.List;
import com.bulkloads.config.security.WithMockActor;
import com.bulkloads.web.ControllerTest;
import com.bulkloads.web.activity.service.ActivityQueryService;
import com.bulkloads.web.activity.service.dto.ActivityListResponse;
import com.bulkloads.web.activity.service.dto.ActivityTypeListResponse;
import com.bulkloads.web.confg.TestConstants;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import lombok.SneakyThrows;

@WebMvcTest(ActivityQueryController.class)
@AutoConfigureMockMvc(addFilters = false)
class ActivityQueryControllerTest extends ControllerTest {

  private static final String URL = TestConstants.ACTIVITIES_URL;

  @MockBean
  ActivityQueryService activityQueryService;

  @AfterEach
  void tearDown() {
    super.tearDownMocks();
    verifyNoMoreInteractions(activityQueryService);
  }

  @Test
  @SneakyThrows
  @WithMockActor(roles = ROLE_USER)
  void givenAuthenticatedUserWithRightRole_whenGetActivities_thenShouldBeOk() {
    final List<ActivityListResponse> response = List.of();
    final String userIds = "1,2";
    final Integer activityTypeId = 1;
    final Integer pastDays = 7;
    final Integer skip = 0;
    final Integer limit = 100;

    when(activityQueryService.getActivities(userIds, activityTypeId, pastDays, skip, limit))
        .thenReturn(response);

    mockMvc.perform(get(URL)
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON)
            .param("user_ids", userIds)
            .param("activity_type_id", "1")
            .param("past_days", "7")
            .param("skip", "0")
            .param("limit", "100"))
        .andExpect(status().isOk())
        .andExpect(content().json("[]"));

    verify(activityQueryService).getActivities(userIds, activityTypeId, pastDays, skip, limit);
  }

  @Test
  @SneakyThrows
  @WithMockActor(roles = ROLE_USER)
  void givenAuthenticatedUserWithRightRole_whenGetActivitiesWithDefaults_thenShouldBeOk() {
    final List<ActivityListResponse> response = List.of();

    when(activityQueryService.getActivities(null, null, 7, 0, 100))
        .thenReturn(response);

    mockMvc.perform(get(URL)
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andExpect(content().json("[]"));

    verify(activityQueryService).getActivities(null, null, 7, 0, 100);
  }

  @Test
  @SneakyThrows
  @WithMockActor(roles = DUMMY_ROLE)
  void givenAuthenticatedUserWithWrongRole_whenGetActivities_thenShouldBeForbidden() {
    mockMvc.perform(get(URL)
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isForbidden());
  }

  @Test
  @SneakyThrows
  void givenUnauthenticatedUser_whenGetActivities_thenShouldBeUnauthorized() {
    mockMvc.perform(get(URL)
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isUnauthorized());
  }

  @Test
  @SneakyThrows
  void givenAnyUser_whenGetActivityTypes_thenShouldBeOk() {
    final List<ActivityTypeListResponse> response = List.of();

    when(activityQueryService.getActivityTypes()).thenReturn(response);

    mockMvc.perform(get(URL + "/types")
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andExpect(content().json("[]"));

    verify(activityQueryService).getActivityTypes();
  }
}
