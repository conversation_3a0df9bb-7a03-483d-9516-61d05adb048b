package com.bulkloads.web.loadinvoice.service;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import com.bulkloads.web.load.domain.template.AssignmentTemplateModel;
import com.bulkloads.web.loadinvoice.domain.template.LoadInvoiceItemTemplateModel;
import com.bulkloads.web.loadinvoice.domain.template.LoadInvoiceTemplateModel;
import com.bulkloads.web.rate.domain.template.RateTypeTemplateModel;

class LoadInvoiceTemplateBuilderData {

  public static BigDecimal b100 = BigDecimal.valueOf(100);
  public static BigDecimal b200 = BigDecimal.valueOf(200);
  public static BigDecimal b300 = BigDecimal.valueOf(300);
  public static BigDecimal b400 = BigDecimal.valueOf(400);
  public static BigDecimal b500 = BigDecimal.valueOf(500);
  public static BigDecimal b600 = BigDecimal.valueOf(600);
  public static BigDecimal b700 = BigDecimal.valueOf(700);
  public static BigDecimal b800 = BigDecimal.valueOf(800);
  public static BigDecimal b900 = BigDecimal.valueOf(900);

  public static String desc1 = "desc1";
  public static String desc2 = "desc2";
  public static String desc3 = "desc3";
  public static String desc4 = "desc4";
  public static String desc5 = "desc5";
  public static String desc6 = "desc6";
  public static String desc7 = "desc7";
  public static String desc8 = "desc8";
  public static String desc9 = "desc9";

  public static AssignmentTemplateModel la1 = AssignmentTemplateModel.builder().loadAssignmentId(1).build();
  public static AssignmentTemplateModel la2 = AssignmentTemplateModel.builder().loadAssignmentId(2).build();
  public static AssignmentTemplateModel la3 = AssignmentTemplateModel.builder().loadAssignmentId(3).build();
  public static AssignmentTemplateModel la4 = AssignmentTemplateModel.builder().loadAssignmentId(4).build();

  public static RateTypeTemplateModel rateType100 = RateTypeTemplateModel.builder()
      .rateType("100")
      .rateTypeText("per cwt (100 lbs)")
      .rateTypeTextMedium("/cwt (100 lbs)")
      .rateTypeTextMediumPlural("cwts (100 lbs)")
      .rateTypeTextAbbr("/cwt")
      .sortOrder(10)
      .isWeight(true)
      .rateTypeChs("Cwt")
      .build();

  public static RateTypeTemplateModel rateType1000 = RateTypeTemplateModel.builder()
      .rateType("1000")
      .rateTypeText("per metric ton (kg)")
      .rateTypeTextMedium("/metric ton (kg)")
      .rateTypeTextMediumPlural("metric tons (kg)")
      .rateTypeTextAbbr("/tonne (kg)")
      .sortOrder(3)
      .isWeight(true)
      .rateTypeChs("Tonne (kg)")
      .build();

  public static RateTypeTemplateModel rateType2000 = RateTypeTemplateModel.builder()
      .rateType("2000")
      .rateTypeText("per ton")
      .rateTypeTextMedium("/ton")
      .rateTypeTextMediumPlural("tons")
      .rateTypeTextAbbr("/ton")
      .sortOrder(1)
      .isWeight(true)
      .rateTypeChs("Ton")
      .build();

  public static RateTypeTemplateModel rateType2204 = RateTypeTemplateModel.builder()
      .rateType("2204.62")
      .rateTypeText("per metric ton (lbs)")
      .rateTypeTextMedium("/metric ton (lbs)")
      .rateTypeTextMediumPlural("metric tons (lbs)")
      .rateTypeTextAbbr("/tonne (lbs)")
      .sortOrder(2)
      .isWeight(true)
      .rateTypeChs("Tonne")
      .build();

  public static RateTypeTemplateModel rateTypeBushel56 = RateTypeTemplateModel.builder()
      .rateType("56")
      .rateTypeText("per bushel (56 lbs)")
      .rateTypeTextMedium("/bushel (56 lbs)")
      .rateTypeTextMediumPlural("bushels (56 lbs)")
      .rateTypeTextAbbr("/bushel (56)")
      .sortOrder(5)
      .isWeight(true)
      .rateTypeChs("Bushel (56 lbs)")
      .build();

  public static RateTypeTemplateModel rateTypeFlat = RateTypeTemplateModel.builder()
      .rateType("flat")
      .rateTypeText("flat rate")
      .rateTypeTextMedium("flat rate")
      .rateTypeTextMediumPlural(" flat rate")
      .rateTypeTextAbbr("")
      .sortOrder(13)
      .isWeight(false)
      .rateTypeChs("")
      .build();

  public static RateTypeTemplateModel rateTypeGallon = RateTypeTemplateModel.builder()
      .rateType("gallon")
      .rateTypeText("per gallon")
      .rateTypeTextMedium("/gallon")
      .rateTypeTextMediumPlural("gallons")
      .rateTypeTextAbbr("/gal")
      .sortOrder(14)
      .isWeight(false)
      .rateTypeChs("per gallon")
      .build();

  public static RateTypeTemplateModel rateTypeHour = RateTypeTemplateModel.builder()
      .rateType("hour")
      .rateTypeText("per hour")
      .rateTypeTextMedium("/hour")
      .rateTypeTextMediumPlural("hours")
      .rateTypeTextAbbr("/h")
      .sortOrder(12)
      .isWeight(false)
      .rateTypeChs("per hour")
      .build();

  public static RateTypeTemplateModel rateTypeLiter = RateTypeTemplateModel.builder()
      .rateType("liter")
      .rateTypeText("per liter")
      .rateTypeTextMedium("/liter")
      .rateTypeTextMediumPlural("liters")
      .rateTypeTextAbbr("/lt")
      .sortOrder(15)
      .isWeight(false)
      .rateTypeChs("per liter")
      .build();

  public static LoadInvoiceItemTemplateModel invoiceItem(
      AssignmentTemplateModel loadAssignment,
      String loadAssignmentNumber,
      boolean isSurcharge,
      String pickupCompanyName,
      String dropCompanyName,
      String commodity,
      String billRateMessage,
      RateTypeTemplateModel billRateType,
      BigDecimal previousItemAmount,
      BigDecimal itemAmount,
      String itemDescription,
      Double billWeight) {

    return LoadInvoiceItemTemplateModel.builder()
        .loadAssignment(loadAssignment)
        .loadAssignmentNumber(loadAssignmentNumber)
        .isSurcharge(isSurcharge)
        .pickupCompanyName(pickupCompanyName)
        .dropCompanyName(dropCompanyName)
        .commodity(commodity)
        .billRateMessage(billRateMessage)
        .billRateType(billRateType)
        .previousItemAmount(previousItemAmount)
        .itemAmount(itemAmount)
        .itemDescription(itemDescription)
        .hauledDate(LocalDate.now())
        .billWeight(billWeight)
        .build();
  }

  static LoadInvoiceTemplateModel getInvoiceModel() {

    List<LoadInvoiceItemTemplateModel> items = new ArrayList<>();

    items.add(invoiceItem(la1, "la1", false, "p1", "d1", "c1", "brm1", rateType100, b100, b100, desc1, 100.0));
    items.add(invoiceItem(la1, "la1", true, "p2", "d2", "c2", "brm2", rateType1000, b200, b200, desc2, 101.0));
    items.add(invoiceItem(la1, "la1", true, "p3", "d3", "c3", "brm3", rateType2000, b300, b300, desc3, 102.0));
    items.add(invoiceItem(la1, "la1", true, "p4", "d4", "c4", "brm4", rateType2204, b400, b400, desc4, 103.0));
    items.add(invoiceItem(la1, "la1", true, "p5", "d5", "c5", "brm5", rateTypeBushel56, b500, b500, desc5, 104.0));
    items.add(invoiceItem(la1, "la1", true, "p6", "d6", "c6", "brm6", rateTypeFlat, b600, b600, desc6, 105.0));
    items.add(invoiceItem(la1, "la1", true, "p7", "d7", "c7", "brm7", rateTypeGallon, b700, b700, desc7, 106.0));
    items.add(invoiceItem(la1, "la1", true, "p8", "d8", "c8", "brm8", rateTypeHour, b800, b800, desc8, 107.0));
    items.add(invoiceItem(la1, "la1", true, "p9", "d9", "c9", "brm9", rateTypeLiter, b900, b900, desc9, 108.0));

    items.add(invoiceItem(la2, "la1", false, "p1", "d1", "c1", "brm1", rateType100, b100, b100, desc1, 100.0));
    items.add(invoiceItem(la2, "la1", true, "p2", "d2", "c2", "brm2", rateType1000, b200, b200, desc2, 101.0));
    items.add(invoiceItem(la2, "la1", true, "p3", "d3", "c3", "brm3", rateType2000, b300, b300, desc3, 102.0));
    items.add(invoiceItem(la2, "la1", true, "p4", "d4", "c4", "brm4", rateType2204, b400, b400, desc4, 103.0));
    items.add(invoiceItem(la2, "la1", true, "p5", "d5", "c5", "brm5", rateTypeBushel56, b500, b500, desc5, 104.0));
    items.add(invoiceItem(la2, "la1", true, "p6", "d6", "c6", "brm6", rateTypeFlat, b600, b600, desc6, 105.0));
    items.add(invoiceItem(la2, "la1", true, "p7", "d7", "c7", "brm7", rateTypeGallon, b700, b700, desc7, 106.0));
    items.add(invoiceItem(la2, "la1", true, "p8", "d8", "c8", "brm8", rateTypeHour, b800, b800, desc8, 107.0));
    items.add(invoiceItem(la2, "la1", true, "p9", "d9", "c9", "brm9", rateTypeLiter, b900, b900, desc9, 108.0));

    return LoadInvoiceTemplateModel.builder()
        .loadInvoiceId(1)
        .userId(12304)
        .userCompanyId(1230)
        .companyName("companyName")
        .firstName("firstName")
        .lastName("lastName")
        .billToCompanyName("billToCompanyName")
        .loadInvoiceItems(items)
        .invoiceDate(Instant.now())
        .invoiceTotal(BigDecimal.valueOf(4500))
        .invoiceFileUrl("http://theinvoice.url")
        .build();
  }

}
