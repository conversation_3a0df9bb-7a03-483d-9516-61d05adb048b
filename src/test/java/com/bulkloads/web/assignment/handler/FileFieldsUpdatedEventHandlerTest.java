package com.bulkloads.web.assignment.handler;

import static com.bulkloads.web.file.service.FileOcrService.OCR_FIELD_GROSS_WEIGHT;
import static com.bulkloads.web.file.service.FileOcrService.OCR_FIELD_TARE_WEIGHT;
import static com.bulkloads.web.file.service.FileOcrService.OCR_FIELD_TICKET_DATE;
import static com.bulkloads.web.file.service.FileOcrService.OCR_FIELD_TICKET_NUMBER;
import static com.bulkloads.web.file.service.FileOcrService.OCR_FIELD_VOLUME;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.time.LocalDate;
import java.util.Collections;
import java.util.List;
import com.bulkloads.web.assignment.domain.entity.Assignment;
import com.bulkloads.web.assignment.repository.AssignmentRepository;
import com.bulkloads.web.file.domain.entity.File;
import com.bulkloads.web.file.domain.entity.FileField;
import com.bulkloads.web.file.event.FileFieldsUpdatedEvent;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;


class FileFieldsUpdatedEventHandlerTest {

  private FileFieldsUpdatedEventHandler handler;

  @Mock
  private AssignmentRepository assignmentRepository;

  @Mock
  private File file;

  @Mock
  private Assignment loadingAssignment;

  @Mock
  private Assignment unloadingAssignment;

  @BeforeEach
  void setUp() {
    MockitoAnnotations.openMocks(this);
    handler = new FileFieldsUpdatedEventHandler(assignmentRepository);
    
    when(file.getFileId()).thenReturn(123);
  }

  @Test
  void testHandleEventWithNoFields() {
    // Test with null fields
    FileFieldsUpdatedEvent event = new FileFieldsUpdatedEvent(file, null);
    handler.handleFileFieldsUpdatedEvent(event);
    
    // Test with empty fields list
    event = new FileFieldsUpdatedEvent(file, Collections.emptyList());
    handler.handleFileFieldsUpdatedEvent(event);
    
    verify(assignmentRepository, never()).save(any(Assignment.class));
  }

  @Test
  void testHandleEventWithNoAssignments() {
    // Set up file fields
    FileField ticketNumber = new FileField();
    ticketNumber.setFieldName(OCR_FIELD_TICKET_NUMBER);
    ticketNumber.setFieldValue("T12345");
    
    FileField tareWeight = new FileField();
    tareWeight.setFieldName(OCR_FIELD_TARE_WEIGHT);
    tareWeight.setFieldValue("1000.5");
    
    List<FileField> fields = List.of(ticketNumber, tareWeight);
    
    // No assignments found for this file
    when(assignmentRepository.findAllByLoadingTicketFileId(123)).thenReturn(Collections.emptyList());
    when(assignmentRepository.findAllByUnloadingTicketFileId(123)).thenReturn(Collections.emptyList());
    
    FileFieldsUpdatedEvent event = new FileFieldsUpdatedEvent(file, fields);
    handler.handleFileFieldsUpdatedEvent(event);
    
    verify(assignmentRepository, never()).save(any(Assignment.class));
  }

  @Test
  void testHandleEventForLoadingTicket() {
    // Set up file fields
    FileField ticketNumber = new FileField();
    ticketNumber.setFieldName(OCR_FIELD_TICKET_NUMBER);
    ticketNumber.setFieldValue("T12345");
    
    FileField grossWeight = new FileField();
    grossWeight.setFieldName(OCR_FIELD_GROSS_WEIGHT);
    grossWeight.setFieldValue("2000.5");
    
    FileField tareWeight = new FileField();
    tareWeight.setFieldName(OCR_FIELD_TARE_WEIGHT);
    tareWeight.setFieldValue("1000.5");
    
    FileField volume = new FileField();
    volume.setFieldName(OCR_FIELD_VOLUME);
    volume.setFieldValue("500.25");
    
    FileField ticketDate = new FileField();
    ticketDate.setFieldName(OCR_FIELD_TICKET_DATE);
    ticketDate.setFieldValue("2025-03-07");
    
    List<FileField> fields = List.of(ticketNumber, grossWeight, tareWeight, volume, ticketDate);
    
    // Assignment found for loading ticket
    when(assignmentRepository.findAllByLoadingTicketFileId(123)).thenReturn(List.of(loadingAssignment));
    when(assignmentRepository.findAllByUnloadingTicketFileId(123)).thenReturn(Collections.emptyList());
    
    FileFieldsUpdatedEvent event = new FileFieldsUpdatedEvent(file, fields);
    handler.handleFileFieldsUpdatedEvent(event);
    
    // Verify OCR fields are set
    verify(loadingAssignment).setLoadingTicketNumberOcr("T12345");
    
    // Verify regular fields are set when they are null
    verify(loadingAssignment).setLoadingTicketNumber("T12345");
    verify(loadingAssignment).setHauledDate(LocalDate.parse("2025-03-07"));
    verify(loadingAssignment).setLoadedWeightOcr(1000.0);
    verify(loadingAssignment).setLoadedVolumeOcr(500.25);
    
    verify(assignmentRepository).save(loadingAssignment);
  }

  @Test
  void testHandleEventForUnloadingTicket() {
    // Set up file fields
    FileField ticketNumber = new FileField();
    ticketNumber.setFieldName(OCR_FIELD_TICKET_NUMBER);
    ticketNumber.setFieldValue("T12345");
    
    FileField grossWeight = new FileField();
    grossWeight.setFieldName(OCR_FIELD_GROSS_WEIGHT);
    grossWeight.setFieldValue("2000.5");
    
    FileField tareWeight = new FileField();
    tareWeight.setFieldName(OCR_FIELD_TARE_WEIGHT);
    tareWeight.setFieldValue("1000.5");
    
    FileField volume = new FileField();
    volume.setFieldName(OCR_FIELD_VOLUME);
    volume.setFieldValue("500.25");
    
    List<FileField> fields = List.of(ticketNumber, grossWeight, tareWeight, volume);
    
    // Assignment found for unloading ticket
    when(assignmentRepository.findAllByLoadingTicketFileId(123)).thenReturn(Collections.emptyList());
    when(assignmentRepository.findAllByUnloadingTicketFileId(123)).thenReturn(List.of(unloadingAssignment));
    
    FileFieldsUpdatedEvent event = new FileFieldsUpdatedEvent(file, fields);
    handler.handleFileFieldsUpdatedEvent(event);
    
    // Verify OCR fields are set
    verify(unloadingAssignment).setUnloadingTicketNumberOcr("T12345");
    
    // Verify regular fields are set when they are null
    verify(unloadingAssignment).setUnloadingTicketNumber("T12345");
    verify(unloadingAssignment).setUnloadWeightOcr(1000.0);
    verify(unloadingAssignment).setUnloadVolumeOcr(500.25);
    
    verify(assignmentRepository).save(unloadingAssignment);
  }

  @Test
  void testHandleEventForBothTickets() {
    // Set up file fields
    FileField ticketNumber = new FileField();
    ticketNumber.setFieldName(OCR_FIELD_TICKET_NUMBER);
    ticketNumber.setFieldValue("T12345");
    
    List<FileField> fields = List.of(ticketNumber);
    
    // Assignments found for both loading and unloading tickets
    when(assignmentRepository.findAllByLoadingTicketFileId(123)).thenReturn(List.of(loadingAssignment));
    when(assignmentRepository.findAllByUnloadingTicketFileId(123)).thenReturn(List.of(unloadingAssignment));
    
    FileFieldsUpdatedEvent event = new FileFieldsUpdatedEvent(file, fields);
    handler.handleFileFieldsUpdatedEvent(event);
    
    verify(loadingAssignment).setLoadingTicketNumberOcr("T12345");
    verify(loadingAssignment).setLoadingTicketNumber("T12345");
    
    verify(unloadingAssignment).setUnloadingTicketNumberOcr("T12345");
    verify(unloadingAssignment).setUnloadingTicketNumber("T12345");
    
    verify(assignmentRepository, times(2)).save(any(Assignment.class));
  }

  @Test
  void testInvalidTareWeightFormat() {
    // Set up file field with invalid tare weight
    FileField tareWeight = new FileField();
    tareWeight.setFieldName(OCR_FIELD_TARE_WEIGHT);
    tareWeight.setFieldValue("not-a-number");
    
    List<FileField> fields = List.of(tareWeight);
    
    when(assignmentRepository.findAllByLoadingTicketFileId(123)).thenReturn(List.of(loadingAssignment));
    
    FileFieldsUpdatedEvent event = new FileFieldsUpdatedEvent(file, fields);
    handler.handleFileFieldsUpdatedEvent(event);
    
    // Assignment should still be saved with modification date
    verify(assignmentRepository).save(loadingAssignment);
  }
}