package com.bulkloads.web.companyequipment.api;

import static com.bulkloads.config.AppConstants.UserPermission.MANAGE_EQUIPMENTS;
import static com.bulkloads.web.companyequipment.UserCompanyEquipmentLogUtils.CREATE_USER_COMPANY_EQUIPMENT_LOG_REQUEST_JSON;
import static com.bulkloads.web.companyequipment.UserCompanyEquipmentLogUtils.UPDATE_USER_COMPANY_EQUIPMENT_LOG_REQUEST_JSON;
import static com.bulkloads.web.companyequipment.UserCompanyEquipmentLogUtils.USER_COMPANY_EQUIPMENT_LOG_RESPONSE_JSON;
import static com.bulkloads.web.companyequipment.UserCompanyEquipmentLogUtils.buildUserCompanyEquipmentLogResponse;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoMoreInteractions;
import static org.mockito.Mockito.when;
import static org.springframework.http.MediaType.APPLICATION_JSON;
import static org.springframework.restdocs.mockmvc.RestDocumentationRequestBuilders.delete;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;
import com.bulkloads.config.security.WithMockActor;
import com.bulkloads.web.ControllerTest;
import com.bulkloads.web.companyequipment.service.UserCompanyEquipmentLogService;
import com.bulkloads.web.companyequipment.service.dto.UserCompanyEquipmentLogRequest;
import com.bulkloads.web.companyequipment.service.dto.UserCompanyEquipmentLogResponse;
import com.bulkloads.web.confg.TestConstants;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import lombok.SneakyThrows;

@WebMvcTest(UserCompanyEquipmentLogController.class)
@AutoConfigureMockMvc(addFilters = false)
class UserCompanyEquipmentLogControllerTest extends ControllerTest {

  private static final String URL = TestConstants.USER_COMPANY_EQUIPMENTS_URL;

  @MockBean
  UserCompanyEquipmentLogService service;

  @AfterEach
  void tearDown() {
    super.tearDownMocks();
    verifyNoMoreInteractions(service);
  }

  @Test
  @SneakyThrows
  @WithMockActor(permissions = MANAGE_EQUIPMENTS)
  void givenAuthenticatedUserWithRightPermissions_whenCreateUserCompanyEquipmentLog_thenShouldBeOk() {

    final int equipmentId = 1;
    final UserCompanyEquipmentLogRequest request =
        objectMapper.readValue(CREATE_USER_COMPANY_EQUIPMENT_LOG_REQUEST_JSON, UserCompanyEquipmentLogRequest.class);

    final UserCompanyEquipmentLogResponse response = buildUserCompanyEquipmentLogResponse();
    response.setUserCompanyEquipmentLogId(1);

    final String responseJson = """
        {
          "message" : "The equipment maintenance log has been added",
          "key" : 1,
          "data" : %s
        }""".formatted(USER_COMPANY_EQUIPMENT_LOG_RESPONSE_JSON);

    when(service.create(equipmentId, request)).thenReturn(response);

    mockMvc.perform(post(URL + "/{user_company_equipment_id}/logs", equipmentId)
                        .contentType(APPLICATION_JSON)
                        .accept(APPLICATION_JSON)
                        .content(CREATE_USER_COMPANY_EQUIPMENT_LOG_REQUEST_JSON))
        .andExpect(status().isOk())
        .andExpect(content().string(prettyPrintJson(responseJson)));

    verify(service).create(equipmentId, request);
  }

  @Test
  @SneakyThrows
  void givenUnauthenticatedUser_whenCreateUserCompanyEquipmentLog_thenShouldBeUnauthorized() {
    final int equipmentId = 1;
    mockMvc.perform(post(URL + "/{user_company_equipment_id}/logs", equipmentId)
                        .contentType(APPLICATION_JSON)
                        .accept(APPLICATION_JSON)
                        .content(CREATE_USER_COMPANY_EQUIPMENT_LOG_REQUEST_JSON))
        .andExpect(status().isUnauthorized())
        .andReturn();
  }

  @Test
  @SneakyThrows
  @WithMockActor(roles = MANAGE_EQUIPMENTS)
  void givenAuthenticatedUserWithRightPermissions_whenUpdateUserCompanyEquipmentLog_thenShouldBeOk() {

    final int equipmentId = 1;
    final int logId = 1;
    final UserCompanyEquipmentLogRequest request =
        objectMapper.readValue(UPDATE_USER_COMPANY_EQUIPMENT_LOG_REQUEST_JSON, UserCompanyEquipmentLogRequest.class);

    final UserCompanyEquipmentLogResponse response = buildUserCompanyEquipmentLogResponse();
    response.setUserCompanyEquipmentLogId(logId);

    final String responseJson = """
        {
          "message" : "The equipment maintenance log has been updated",
          "key" : 1,
          "data" : %s
        }""".formatted(USER_COMPANY_EQUIPMENT_LOG_RESPONSE_JSON);

    when(service.update(equipmentId, logId, request)).thenReturn(response);

    mockMvc.perform(put(URL + "/{user_company_equipment_id}/logs/{user_company_equipment_log_id}", equipmentId, logId)
                        .contentType(APPLICATION_JSON)
                        .accept(APPLICATION_JSON)
                        .content(UPDATE_USER_COMPANY_EQUIPMENT_LOG_REQUEST_JSON))
        .andExpect(status().isOk())
        .andExpect(content().string(prettyPrintJson(responseJson)));

    verify(service).update(equipmentId, logId, request);
  }

  @Test
  @SneakyThrows
  void givenUnauthenticatedUser_whenUpdateUserCompanyEquipmentLog_thenShouldBeUnauthorized() {
    final int equipmentId = 1;
    final int logId = 1;
    mockMvc.perform(put(URL + "/{user_company_equipment_id}/logs/{user_company_equipment_log_id}", equipmentId, logId)
                        .contentType(APPLICATION_JSON)
                        .accept(APPLICATION_JSON)
                        .content(UPDATE_USER_COMPANY_EQUIPMENT_LOG_REQUEST_JSON))
        .andExpect(status().isUnauthorized())
        .andReturn();
  }

  @Test
  @SneakyThrows
  @WithMockActor(roles = MANAGE_EQUIPMENTS)
  void givenANegativeLogId_whenUpdateUserCompanyEquipmentLog_thenShouldBeBadRequest() {
    final int equipmentId = -1;
    final int logId = -1;
    mockMvc.perform(put(URL + "/{user_company_equipment_id}/logs/{user_company_equipment_log_id}", equipmentId, logId)
                        .contentType(APPLICATION_JSON)
                        .accept(APPLICATION_JSON)
                        .content(UPDATE_USER_COMPANY_EQUIPMENT_LOG_REQUEST_JSON))
        .andExpect(status().isBadRequest());
  }

  @Test
  @SneakyThrows
  @WithMockActor(roles = MANAGE_EQUIPMENTS)
  void givenAuthenticatedUserWithRightPermissions_whenDeleteUserCompanyEquipmentLog_thenShouldBeOk() {
    final int equipmentId = 1;
    final int logId = 1;

    final String responseJson = """
        {
          "message" : "Equipment maintenance log deleted"
        }""";

    doNothing().when(service).remove(equipmentId, logId);

    mockMvc.perform(delete(URL + "/{user_company_equipment_id}/logs/{user_company_equipment_log_id}", equipmentId, logId)
                        .contentType(APPLICATION_JSON)
                        .accept(APPLICATION_JSON))
        .andExpect(status().isOk())
        .andExpect(content().string(prettyPrintJson(responseJson)));

    verify(service).remove(equipmentId, logId);
  }

  @Test
  @SneakyThrows
  @WithMockActor(roles = MANAGE_EQUIPMENTS)
  void givenANegativeLogId_whenDeleteUserCompanyEquipmentLog_thenShouldBeBadRequest() {
    final int equipmentId = -1;
    final int logId = -1;
    mockMvc.perform(delete(URL + "/{user_company_equipment_id}/logs/{user_company_equipment_log_id}", equipmentId, logId)
                        .contentType(APPLICATION_JSON)
                        .accept(APPLICATION_JSON))
        .andExpect(status().isBadRequest());
  }

  @Test
  @SneakyThrows
  void givenUnauthenticatedUser_whenDeleteUserCompanyEquipmentLog_thenShouldBeUnauthorized() {
    final int equipmentId = 1;
    final int logId = 1;
    mockMvc.perform(delete(URL + "/{user_company_equipment_id}/logs/{user_company_equipment_log_id}", equipmentId, logId)
                        .contentType(APPLICATION_JSON)
                        .accept(APPLICATION_JSON))
        .andExpect(status().isUnauthorized());
  }
}