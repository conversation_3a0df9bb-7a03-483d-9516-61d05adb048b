package com.bulkloads.web.companyequipment.api;

import static com.bulkloads.config.AppConstants.UserPermission.MANAGE_EQUIPMENTS;
import static com.bulkloads.web.companyequipment.UserCompanyEquipmentUtils.CREATE_USER_COMPANY_EQUIPMENT_REQUEST_JSON;
import static com.bulkloads.web.companyequipment.UserCompanyEquipmentUtils.UPDATE_USER_COMPANY_EQUIPMENT_REQUEST_JSON;
import static com.bulkloads.web.companyequipment.UserCompanyEquipmentUtils.USER_COMPANY_EQUIPMENT_RESPONSE_JSON;
import static com.bulkloads.web.companyequipment.UserCompanyEquipmentUtils.buildUserCompanyEquipmentResponse;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoMoreInteractions;
import static org.mockito.Mockito.when;
import static org.springframework.restdocs.mockmvc.RestDocumentationRequestBuilders.delete;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;
import com.bulkloads.config.security.WithMockActor;
import com.bulkloads.web.ControllerTest;
import com.bulkloads.web.companyequipment.service.UserCompanyEquipmentService;
import com.bulkloads.web.companyequipment.service.dto.UserCompanyEquipmentRequest;
import com.bulkloads.web.companyequipment.service.dto.UserCompanyEquipmentResponse;
import com.bulkloads.web.confg.TestConstants;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import lombok.SneakyThrows;

@WebMvcTest(UserCompanyEquipmentController.class)
@AutoConfigureMockMvc(addFilters = false)
class UserCompanyEquipmentControllerTest extends ControllerTest {

  private static final String URL = TestConstants.USER_COMPANY_EQUIPMENTS_URL;

  @MockBean
  UserCompanyEquipmentService service;

  @AfterEach
  void tearDown() {
    super.tearDownMocks();
    verifyNoMoreInteractions(service);
  }

  @Test
  @SneakyThrows
  @WithMockActor(permissions = MANAGE_EQUIPMENTS)
  void givenAuthenticatedUserWithRightPermissions_whenCreateUserCompanyEquipment_thenShouldBeOk() {

    final UserCompanyEquipmentRequest request =
        objectMapper.readValue(CREATE_USER_COMPANY_EQUIPMENT_REQUEST_JSON, UserCompanyEquipmentRequest.class);

    final UserCompanyEquipmentResponse response = buildUserCompanyEquipmentResponse();

    final String responseJson = """
        {
          "message" : "The equipment has been added to your company",
          "key" : 1,
          "data" : %s
        }""".formatted(USER_COMPANY_EQUIPMENT_RESPONSE_JSON);

    when(service.create(request)).thenReturn(response);

    mockMvc.perform(post(URL)
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON)
                        .content(CREATE_USER_COMPANY_EQUIPMENT_REQUEST_JSON))
        .andExpect(status().isOk())
        .andExpect(content().string(prettyPrintJson(responseJson)));

    verify(service).create(request);
  }

  @Test
  @SneakyThrows
  void givenUnauthenticatedUser_whenCreateUserCompanyEquipment_thenShouldBeUnauthorized() {
    mockMvc.perform(post(URL)
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON)
                        .content(CREATE_USER_COMPANY_EQUIPMENT_REQUEST_JSON))
        .andExpect(status().isUnauthorized())
        .andReturn();
  }

  @Test
  @SneakyThrows
  @WithMockActor(roles = MANAGE_EQUIPMENTS)
  void givenAuthenticatedUserWithRightPermissions_whenUpdateUserCompanyEquipment_thenShouldBeOk() {

    final int equipmentId = 1;
    final UserCompanyEquipmentRequest request =
        objectMapper.readValue(UPDATE_USER_COMPANY_EQUIPMENT_REQUEST_JSON, UserCompanyEquipmentRequest.class);

    final UserCompanyEquipmentResponse response = buildUserCompanyEquipmentResponse();

    final String responseJson = """
        {
          "message" : "The equipment has been updated",
          "key" : 1,
          "data" : %s
        }""".formatted(USER_COMPANY_EQUIPMENT_RESPONSE_JSON);

    when(service.update(equipmentId, request)).thenReturn(response);

    mockMvc.perform(put(URL + "/{user_company_equipment_id}", equipmentId)
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON)
                        .content(UPDATE_USER_COMPANY_EQUIPMENT_REQUEST_JSON))
        .andExpect(status().isOk())
        .andExpect(content().string(prettyPrintJson(responseJson)));

    verify(service).update(equipmentId, request);
  }

  @Test
  @SneakyThrows
  void givenUnauthenticatedUser_whenUpdateUserCompanyEquipment_thenShouldBeUnauthorized() {
    final int equipmentId = 1;
    mockMvc.perform(put(URL + "/{user_company_equipment_id}", equipmentId)
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON)
                        .content(CREATE_USER_COMPANY_EQUIPMENT_REQUEST_JSON))
        .andExpect(status().isUnauthorized())
        .andReturn();
  }

  @Test
  @SneakyThrows
  @WithMockActor(roles = MANAGE_EQUIPMENTS)
  void givenANegativeEquipmentId_whenUpdateUserCompanyEquipment_thenShouldBeBadRequest() {
    final int equipmentId = -1;
    mockMvc.perform(put(URL + "/{user_company_equipment_id}", equipmentId)
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON)
                        .content(CREATE_USER_COMPANY_EQUIPMENT_REQUEST_JSON))
        .andExpect(status().isBadRequest());
  }

  @Test
  @SneakyThrows
  @WithMockActor(roles = MANAGE_EQUIPMENTS)
  void givenAuthenticatedUserWithRightPermissions_whenDeleteUserCompanyEquipment_thenShouldBeOk() {
    final int equipmentId = 1;

    final String responseJson = """
        {
          "message" : "Equipment deleted"
        }""";

    doNothing().when(service).remove(equipmentId);

    mockMvc.perform(delete(URL + "/{user_company_equipment_id}", equipmentId)
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andExpect(content().string(prettyPrintJson(responseJson)));

    verify(service).remove(equipmentId);
  }

  @Test
  @SneakyThrows
  @WithMockActor(roles = MANAGE_EQUIPMENTS)
  void givenANegativeEquipmentId_whenDeleteUserCompanyEquipment_thenShouldBeBadRequest() {
    final int equipmentId = -1;
    mockMvc.perform(delete(URL + "/{user_company_equipment_id}", equipmentId)
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest());
  }

  @Test
  @SneakyThrows
  void givenUnauthenticatedUser_whenDeleteUserCompanyEquipment_thenShouldBeUnauthorized() {
    final int equipmentId = 1;
    mockMvc.perform(delete(URL + "/{user_company_equipment_id}", equipmentId)
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isUnauthorized());
  }
}