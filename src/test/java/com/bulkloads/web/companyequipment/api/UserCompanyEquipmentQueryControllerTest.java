package com.bulkloads.web.companyequipment.api;

import static com.bulkloads.config.AppConstants.UserRole.ROLE_USER;
import static com.bulkloads.web.companyequipment.UserCompanyEquipmentUtils.USER_COMPANY_EQUIPMENT_RESPONSE_JSON;
import static com.bulkloads.web.companyequipment.UserCompanyEquipmentUtils.buildUserCompanyEquipmentResponse;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoMoreInteractions;
import static org.mockito.Mockito.when;
import static org.springframework.http.MediaType.APPLICATION_JSON;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;
import java.util.List;
import com.bulkloads.config.security.WithMockActor;
import com.bulkloads.web.ControllerTest;
import com.bulkloads.web.companyequipment.service.UserCompanyEquipmentService;
import com.bulkloads.web.companyequipment.service.dto.UserCompanyEquipmentListResponse;
import com.bulkloads.web.companyequipment.service.dto.UserCompanyEquipmentResponse;
import com.bulkloads.web.confg.TestConstants;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import lombok.SneakyThrows;

@WebMvcTest(UserCompanyEquipmentQueryController.class)
@AutoConfigureMockMvc(addFilters = false)
class UserCompanyEquipmentQueryControllerTest extends ControllerTest {

  private static final String URL = TestConstants.USER_COMPANY_EQUIPMENTS_URL;

  @MockBean
  UserCompanyEquipmentService service;

  @AfterEach
  void tearDown() {
    super.tearDownMocks();
    verifyNoMoreInteractions(service);
  }

  @Test
  @SneakyThrows
  @WithMockActor(roles = ROLE_USER)
  void givenAuthenticatedUserWithRightRole_whenGetEquipmentById_thenShouldBeOk() {
    final int equipmentId = 1;
    final UserCompanyEquipmentResponse response = buildUserCompanyEquipmentResponse();

    when(service.getUserCompanyEquipmentById(equipmentId)).thenReturn(response);

    mockMvc.perform(get(URL + "/{user_company_equipment_id}", equipmentId)
                        .accept(APPLICATION_JSON))
        .andExpect(status().isOk())
        .andExpect(content().string(prettyPrintJson(USER_COMPANY_EQUIPMENT_RESPONSE_JSON)));

    verify(service).getUserCompanyEquipmentById(equipmentId);
  }

  @Test
  @SneakyThrows
  void givenUnauthenticatedUser_whenGetEquipmentById_thenShouldBeUnauthorized() {
    final int equipmentId = 1;
    mockMvc.perform(get(URL + "/{user_company_equipment_id}", equipmentId)
                        .accept(APPLICATION_JSON))
        .andExpect(status().isUnauthorized());
  }

  @Test
  @SneakyThrows
  @WithMockActor(roles = ROLE_USER)
  void givenANegativeEquipmentId_whenGetEquipmentById_thenShouldBeBadRequest() {
    final int equipmentId = -1;
    mockMvc.perform(get(URL + "/{user_company_equipment_id}", equipmentId)
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest());
  }

  @Test
  @SneakyThrows
  @WithMockActor(roles = ROLE_USER)
  void givenAuthenticatedUserWithRightRole_whenGetEquipments_thenShouldBeOk() {
    final List<UserCompanyEquipmentListResponse> responseList = List.of(buildUserCompanyEquipmentResponse());

    when(service.getUserCompanyEquipments(null, null, null, null, null, null, null, null, null, null)).thenReturn(responseList);

    mockMvc.perform(get(URL)
                        .accept(APPLICATION_JSON))
        .andExpect(status().isOk())
        .andExpect(content().json("[" + USER_COMPANY_EQUIPMENT_RESPONSE_JSON + "]"));

    verify(service).getUserCompanyEquipments(null, null, null, null, null, null, null, null, null, null);
  }

  @Test
  @SneakyThrows
  void givenUnauthenticatedUser_whenGetEquipments_thenShouldBeUnauthorized() {
    mockMvc.perform(get(URL)
                        .accept(APPLICATION_JSON))
        .andExpect(status().isUnauthorized());
  }
}