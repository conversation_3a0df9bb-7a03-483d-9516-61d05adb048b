package com.bulkloads.web.companyequipment;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Collections;
import com.bulkloads.web.companyequipment.domain.entity.EquipmentStatus;
import com.bulkloads.web.companyequipment.service.dto.UserCompanyEquipmentResponse;
import com.bulkloads.web.file.service.dto.FileResponse;

public final class UserCompanyEquipmentUtils {

  public static final String CREATE_USER_COMPANY_EQUIPMENT_REQUEST_JSON = """
      {
          "equipment_type": "TRUCK",
          "external_equipment_id": "12345",
          "make": "Ford",
          "model": "F-150",
          "trailer_type": "Flatbed",
          "year": "2020",
          "vin": "1FTFW1E50LKE12345",
          "value": 30000.00,
          "estimated_mileage": 50000,
          "license_plate_number": "ABC123",
          "license_plate_expiration_date": "2024-12-31",
          "dot_expiration_date": "2023-12-31",
          "insurance_expiration_date": "2023-12-31",
          "insurance_company": "XYZ Insurance",
          "inspection_expiration_date": "2023-12-31",
          "registration_expiration_date": "2023-12-31",
          "notes": "Regular maintenance required",
          "status": "ACTIVE",
          "default_assigned_user_id": 1,
          "default_assigned_ab_user_id": 2,
          "files": [
            {
              "file_name": "document.pdf",
              "file_url": "http://example.com/document.pdf"
            }
          ]
        }
      """;
  public static final String UPDATE_USER_COMPANY_EQUIPMENT_REQUEST_JSON = """
      {
         "estimated_mileage": 50001
      }
      """;
  public static final String USER_COMPANY_EQUIPMENT_RESPONSE_JSON = """
      {
          "user_company_equipment_id" : 1,
          "equipment_type" : "TRUCK",
          "external_equipment_id" : "12345",
          "make" : "Ford",
          "model" : "F-150",
          "trailer_type" : "Flatbed",
          "year" : "2020",
          "vin" : "1FTFW1E50LKE12345",
          "value" : 30000.0,
          "estimated_mileage" : 50000,
          "license_plate_number" : "ABC123",
          "license_plate_expiration_date" : "2024-12-31",
          "dot_expiration_date" : "2023-12-31",
          "insurance_expiration_date" : "2023-12-31",
          "insurance_company" : "XYZ Insurance",
          "inspection_expiration_date" : "2023-12-31",
          "registration_expiration_date" : "2023-12-31",
          "notes" : "Regular maintenance required",
          "default_assigned_user_id" : 1,
          "default_assigned_ab_user_id" : 2,
          "default_assigned_driver_name" : "",
          "status" : "active",
          "user_company_equipment_logs" : null,
          "files" : [ {
            "file_id" : null,
            "file_url" : "http://example.com/document.pdf",
            "file_type_id" : null,
            "file_type" : "",
            "thumb_url" : "",
            "mime_type" : "",
            "is_image" : null,
            "is_audio" : null,
            "size" : null,
            "extension" : "",
            "filename" : "document.pdf",
            "number_of_pages" : null,
            "caption" : ""
          }]
        }""";

  public static UserCompanyEquipmentResponse buildUserCompanyEquipmentResponse() {
    UserCompanyEquipmentResponse response = new UserCompanyEquipmentResponse();

    response.setUserCompanyEquipmentId(1);
    response.setEquipmentType("TRUCK");
    response.setExternalEquipmentId("12345");
    response.setMake("Ford");
    response.setModel("F-150");
    response.setTrailerType("Flatbed");
    response.setYear("2020");
    response.setVin("1FTFW1E50LKE12345");
    response.setValue(BigDecimal.valueOf(30000.00));
    response.setEstimatedMileage(50000L);
    response.setLicensePlateNumber("ABC123");
    response.setLicensePlateExpirationDate(LocalDate.parse("2024-12-31"));
    response.setDotExpirationDate(LocalDate.parse("2023-12-31"));
    response.setInsuranceExpirationDate(LocalDate.parse("2023-12-31"));
    response.setInsuranceCompany("XYZ Insurance");
    response.setInspectionExpirationDate(LocalDate.parse("2023-12-31"));
    response.setRegistrationExpirationDate(LocalDate.parse("2023-12-31"));
    response.setNotes("Regular maintenance required");
    response.setStatus(EquipmentStatus.active);
    response.setDefaultAssignedUserId(1);
    response.setDefaultAssignedAbUserId(2);

    FileResponse file = FileResponse.builder()
        .filename("document.pdf")
        .fileUrl("http://example.com/document.pdf")
        .build();
    response.setFiles(Collections.singletonList(file));

    return response;
  }
}
