package com.bulkloads.web.companyequipment;

import java.time.LocalDate;
import java.util.List;
import com.bulkloads.web.companyequipment.service.dto.UserCompanyEquipmentLogResponse;

public final class UserCompanyEquipmentLogUtils {

  public static final LocalDate LOCAL_DATE = LocalDate.parse("2024-03-19");
  public static final String CREATE_USER_COMPANY_EQUIPMENT_LOG_REQUEST_JSON = """
      {
            "log_type": "Maintenance",
            "log_date": "2023-10-01",
            "mileage": 15000,
            "notes": "Changed oil and filters",
            "expense": 200.00,
            "files": [
              {
                "file_name": "receipt.pdf",
                "file_url": "http://example.com/receipt.pdf"
              }
            ]
          }
      """;
  public static final String UPDATE_USER_COMPANY_EQUIPMENT_LOG_REQUEST_JSON = """
      {
         "notes": "Changed oil and filters updated"
      }
      """;
  public static final String USER_COMPANY_EQUIPMENT_LOG_RESPONSE_JSON = """
      {
        "user_company_equipment_log_id": 1,
        "log_type": "Maintenance",
        "log_date": "2024-03-19",
        "mileage": 15000,
        "notes": "Changed oil and filters",
        "expense": null,
        "files": []
      }""";

  public static UserCompanyEquipmentLogResponse buildUserCompanyEquipmentLogResponse() {
    UserCompanyEquipmentLogResponse response = new UserCompanyEquipmentLogResponse();

    response.setUserCompanyEquipmentLogId(1);
    response.setLogType("Maintenance");
    response.setLogDate(LOCAL_DATE);
    response.setMileage(15000);
    response.setNotes("Changed oil and filters");
    response.setExpense(null);
    response.setFiles(List.of());

    return response;
  }
}
