package com.bulkloads.web.commons;

import static com.bulkloads.common.jpa.nativejpa.JpaNativeQueryService.BASIC_GROOVY_QUERY_FUNCTIONS;
import static org.assertj.core.api.Assertions.assertThat;
import java.util.HashMap;
import java.util.Map;
import org.junit.jupiter.api.Test;
import groovy.text.SimpleTemplateEngine;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

@Slf4j
class GroovyTemplatingTests {

  private Map<String, Object> getBindingForBasicValues() {
    HashMap<String, Object> binding = new HashMap<>();
    binding.put("params", new HashMap<>());
    binding.put("prop-with-value", "propertyValue");
    binding.put("prop-with-null", null);
    binding.put("prop-with-empty-str", "");
    binding.put("prop-as-bool-false", false);
    binding.put("prop-as-bool-true", true);
    binding.put("prop-as-str-0", "0");
    binding.put("prop-as-str-1", "1");
    binding.put("prop-as-int-0", 0);
    binding.put("prop-as-int-1", 1);
    return binding;
  }

  @SneakyThrows
  String renderExpression(String expression) {
    return new SimpleTemplateEngine()
        .createTemplate(BASIC_GROOVY_QUERY_FUNCTIONS + "<% print(" + expression + ") %>")
        .make(getBindingForBasicValues())
        .toString()
        .trim();
  }

  @Test
  void existingPropertyWithValueTest() {
    assertThat(renderExpression("'prop-with-value' in binding.variables")).isEqualTo("true");
    assertThat(renderExpression("binding.variables.containsKey('prop-with-value')")).isEqualTo("true");
    assertThat(renderExpression("paramIsTrue('prop-with-value')")).isEqualTo("false");
    assertThat(renderExpression("paramExists('prop-with-value')")).isEqualTo("true");
    assertThat(renderExpression("paramExistsAdd('prop-with-value')")).isEqualTo("true");

    assertThat(renderExpression("paramDefined('prop-with-value')")).isEqualTo("true");
    assertThat(renderExpression("paramDefinedAdd('prop-with-value')")).isEqualTo("true");

  }

  @Test
  void existingPropertyWithoutValueTest() {
    assertThat(renderExpression("'prop-with-null' in binding.variables")).isEqualTo("false");
    assertThat(renderExpression("binding.variables.containsKey('prop-with-null')")).isEqualTo("true");
    assertThat(renderExpression("paramIsTrue('prop-with-null')")).isEqualTo("false");
    assertThat(renderExpression("paramExists('prop-with-null')")).isEqualTo("false");
    assertThat(renderExpression("paramExistsAdd('prop-with-null')")).isEqualTo("false");

    assertThat(renderExpression("paramDefined('prop-with-null')")).isEqualTo("true");
    assertThat(renderExpression("paramDefinedAdd('prop-with-null')")).isEqualTo("true");

  }

  @Test
  void existingPropertyWithEmptyStringValueTest() {
    assertThat(renderExpression("'prop-with-empty-str' in binding.variables")).isEqualTo("false");
    assertThat(renderExpression("binding.variables.containsKey('prop-with-empty-str')")).isEqualTo("true");
    assertThat(renderExpression("paramIsTrue('prop-with-empty-str')")).isEqualTo("false");
    assertThat(renderExpression("paramExists('prop-with-empty-str')")).isEqualTo("false");
    assertThat(renderExpression("paramExistsAdd('prop-with-empty-str')")).isEqualTo("false");

    assertThat(renderExpression("paramDefined('prop-with-empty-str')")).isEqualTo("true");
    assertThat(renderExpression("paramDefinedAdd('prop-with-empty-str')")).isEqualTo("true");

  }

  @Test
  void missingPropertyTest() {
    assertThat(renderExpression("'missing-property-name' in binding.variables")).isEqualTo("false");
    assertThat(renderExpression("binding.variables.containsKey('missing-property-name')")).isEqualTo("false");
    assertThat(renderExpression("paramIsTrue('missing-property-name')")).isEqualTo("false");
    assertThat(renderExpression("paramExists('missing-property-name')")).isEqualTo("false");
    assertThat(renderExpression("paramExistsAdd('missing-property-name')")).isEqualTo("false");

    assertThat(renderExpression("paramDefined('missing-property-name')")).isEqualTo("false");
    assertThat(renderExpression("paramDefinedAdd('missing-property-name')")).isEqualTo("false");

  }

  @Test
  void paramIsTrueTest() {
    assertThat(renderExpression("paramIsTrue('prop-as-bool-false')")).isEqualTo("false");
    assertThat(renderExpression("paramIsTrue('prop-as-bool-true')")).isEqualTo("true");
    assertThat(renderExpression("paramIsTrue('prop-as-int-0')")).isEqualTo("false");
    assertThat(renderExpression("paramIsTrue('prop-as-int-1')")).isEqualTo("false");
    assertThat(renderExpression("paramIsTrue('prop-as-str-0')")).isEqualTo("false");
    assertThat(renderExpression("paramIsTrue('prop-as-str-1')")).isEqualTo("false");
  }

  @Test
  @SneakyThrows
  void iterateTest() {
    HashMap<String, Object> binding = new HashMap<>();
    HashMap<String, Object> params = new HashMap<>();
    binding.put("params", params);
    binding.put("term", "one two three");

    String expr = """
        <%
        def likeEveryTokenOf(String term, Closure closure) {
            if (term == null || term.isEmpty()) {
                return ''
            }
            def tokens = term.split("\\s+")
            def counter = 0
            def sqlFragments = tokens.collect { token ->
                def paramName = "like_terms_${counter++}"
                params[paramName] = "%$token%"
                closure.call(token, paramName)
            }
            return sqlFragments.join('\\n')
        }%>

        <% out.print likeEveryTokenOf(term) { token, paramName ->
             return " AND company_name LIKE :$paramName"
        } %>
        """;

    String template = new SimpleTemplateEngine()
        .createTemplate(BASIC_GROOVY_QUERY_FUNCTIONS + " " + expr)
        .make(binding)
        .toString()
        .trim();

    String expected = """
          AND company_name LIKE :like_terms_0
          AND company_name LIKE :like_terms_1
          AND company_name LIKE :like_terms_2
        """;

    assertThat(normalize(template)).isEqualTo(normalize(expected));
    // TODO: make assertions for params

  }

  String normalize(String input) {
    return input.replaceAll("\\s+", " ").trim();
  }

}
