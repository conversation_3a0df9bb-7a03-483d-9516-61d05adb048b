package com.bulkloads.web.routing.service;

import static com.bulkloads.web.routing.util.RouteTestUtil.buildDestination;
import static com.bulkloads.web.routing.util.RouteTestUtil.buildOrigin;
import static com.bulkloads.web.routing.util.RouteTestUtil.buildRouteDto;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoMoreInteractions;
import static org.mockito.Mockito.when;
import java.util.Optional;
import com.bulkloads.web.routing.domain.entity.Route;
import com.bulkloads.web.routing.domain.vo.Location;
import com.bulkloads.web.routing.mapper.RouteMapper;
import com.bulkloads.web.routing.provider.RoutingProviderClient;
import com.bulkloads.web.routing.provider.RoutingProviderFactory;
import com.bulkloads.web.routing.repository.RouteRepository;
import com.bulkloads.web.routing.service.dto.RouteDto;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class RouteServiceTest {

  @Mock
  RoutingProviderFactory routingProviderFactory;
  @Mock
  RoutingProviderClient routingProviderClient;
  @Mock
  RouteMapper routeMapper;
  @Mock
  RouteRepository routeRepository;
  @Spy
  @InjectMocks
  RouteService routeService;

  @AfterEach
  void tearDown() {
    verifyNoMoreInteractions(routeRepository);
    verifyNoMoreInteractions(routeMapper);
    verifyNoMoreInteractions(routingProviderClient);
  }

  @Test
  void findRoute_shouldReturnRouteFromDbWhenPresent() {
    final Location origin = buildOrigin();
    final Location destination = buildDestination();
    final RouteDto routeDto = buildRouteDto(origin, destination);

    doReturn(Optional.of(routeDto)).when(routeService).findRouteFromDb(origin, destination);

    final Optional<RouteDto> result = routeService.findRoute(origin, destination);

    assertTrue(result.isPresent());
    assertEquals(routeDto, result.get());

    verify(routeService).findRouteFromDb(origin, destination);
  }

  @Test
  void findRoute_shouldReturnRouteFromRemoteApiWhenNotInDb() {
    final Location origin = buildOrigin();
    final Location destination = buildDestination();
    final RouteDto routeDto = buildRouteDto(origin, destination);

    doReturn(Optional.empty()).when(routeService).findRouteFromDb(origin, destination);
    doReturn(Optional.of(routeDto)).when(routeService).findRouteFromRemoteApi(origin, destination);
    final Route route = new Route();
    when(routeMapper.routeDtoToEntity(routeDto)).thenReturn(route);

    when(routeRepository.save(route)).thenReturn(route);

    when(routeMapper.entityToRouteDto(route)).thenReturn(routeDto);

    final Optional<RouteDto> result = routeService.findRoute(origin, destination);

    assertTrue(result.isPresent());
    assertEquals(routeDto, result.get());

    verify(routeService).findRouteFromDb(origin, destination);
    verify(routeService).findRouteFromRemoteApi(origin, destination);
    verify(routeMapper).routeDtoToEntity(routeDto);
    verify(routeRepository).save(route);
    verify(routeMapper).entityToRouteDto(route);
  }

  @Test
  void findRoute_shouldReturnEmptyWhenNotInDbAndRemoteApiFails() {
    final Location origin = buildOrigin();
    final Location destination = buildDestination();

    doReturn(Optional.empty()).when(routeService).findRouteFromDb(origin, destination);
    doReturn(Optional.empty()).when(routeService).findRouteFromRemoteApi(origin, destination);

    final Optional<RouteDto> result = routeService.findRoute(origin, destination);

    assertTrue(result.isEmpty());

    verify(routeService).findRouteFromDb(origin, destination);
    verify(routeService).findRouteFromRemoteApi(origin, destination);
  }

  @Test
  void findRouteFromDb_shouldFindRoute() {
    final Location origin = buildOrigin();
    final Location destination = buildDestination();
    final Route routeEntity = new Route();
    final RouteDto routeDto = buildRouteDto(origin, destination);

    when(routeRepository.findCached(origin, destination)).thenReturn(Optional.of(routeEntity));

    when(routeMapper.entityToRouteDto(routeEntity)).thenReturn(routeDto);

    final Optional<RouteDto> result = routeService.findRouteFromDb(origin, destination);

    assertTrue(result.isPresent());
    assertEquals(routeDto, result.get());

    verify(routeRepository).findCached(origin, destination);
    verify(routeMapper).entityToRouteDto(routeEntity);

  }

  @Test
  void findRouteFromDb_shouldReturnEmptyWhenNotFound() {
    final Location origin = buildOrigin();
    final Location destination = buildDestination();

    when(routeRepository.findCached(origin, destination)).thenReturn(Optional.empty());

    final Optional<RouteDto> result = routeService.findRouteFromDb(origin, destination);

    assertTrue(result.isEmpty());

    verify(routeRepository).findCached(origin, destination);
  }

  @Test
  void findRouteFromRemoteApi_shouldReturnRoute() {
    final Location origin = buildOrigin();
    final Location destination = buildDestination();
    final RouteDto routeDto = buildRouteDto(origin, destination);

    when(routingProviderFactory.getClient()).thenReturn(routingProviderClient);
    when(routingProviderClient.getRoute(origin, destination)).thenReturn(Optional.of(routeDto));

    final Optional<RouteDto> result = routeService.findRouteFromRemoteApi(origin, destination);

    assertTrue(result.isPresent());
    assertEquals(routeDto, result.get());

    verify(routingProviderFactory).getClient();
    verify(routingProviderClient).getRoute(origin, destination);
  }

  @Test
  void findRouteFromRemoteApi_shouldReturnEmptyWhenApiFails() {
    final Location origin = buildOrigin();
    final Location destination = buildDestination();

    when(routingProviderFactory.getClient()).thenReturn(routingProviderClient);
    when(routingProviderClient.getRoute(origin, destination)).thenReturn(Optional.empty());

    final Optional<RouteDto> result = routeService.findRouteFromRemoteApi(origin, destination);

    assertTrue(result.isEmpty());

    verify(routingProviderFactory).getClient();
    verify(routingProviderClient).getRoute(origin, destination);
  }
}
