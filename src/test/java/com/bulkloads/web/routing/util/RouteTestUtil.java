package com.bulkloads.web.routing.util;

import static com.bulkloads.web.routing.provider.GoogleMapsProviderClient.GOOGLE;
import java.math.BigDecimal;
import java.time.Instant;
import com.bulkloads.web.routing.domain.vo.Coordinates;
import com.bulkloads.web.routing.domain.vo.Location;
import com.bulkloads.web.routing.service.dto.RouteDto;

public final class RouteTestUtil {

  public static Location buildOrigin() {
    return Location.builder()
        .address("address1")
        .city("city1")
        .state("state1")
        .zip("zip1")
        .country("country1")
        .coordinates(Coordinates.builder().latitude(1.0).longitude(1.0).build())
        .build();
  }

  public static Location buildDestination() {
    return Location.builder()
        .address("address2")
        .city("city2")
        .state("state2")
        .zip("zip2")
        .country("country2")
        .coordinates(Coordinates.builder().latitude(2.0).longitude(2.0).build())
        .build();
  }

  public static RouteDto buildRouteDto(final Location origin, final Location destination) {
    return new RouteDto()
        .setStartAddress(origin.getAddress())
        .setStartCity(origin.getCity())
        .setStartState(origin.getState())
        .setStartZip(origin.getZip())
        .setStartCountry(origin.getCountry())
        .setEndAddress(destination.getAddress())
        .setEndCity(destination.getCity())
        .setEndState(destination.getState())
        .setEndZip(destination.getZip())
        .setEndCountry(destination.getCountry())
        .setStartLat(origin.getCoordinates().getLatitude())
        .setStartLng(origin.getCoordinates().getLongitude())
        .setEndLat(destination.getCoordinates().getLatitude())
        .setEndLng(destination.getCoordinates().getLongitude())
        .setBearing(45.0)
        .setBearingDirection("NE")
        .setErrorMessage("OK")
        .setMiles(BigDecimal.ONE)
        .setDuration(1L)
        .setDurationText("1")
        .setDateCreated(Instant.now())
        .setStops(0)
        .setProvider(GOOGLE);
  }
}
