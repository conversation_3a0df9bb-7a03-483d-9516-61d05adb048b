package com.bulkloads.web.file.service;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import com.bulkloads.web.aws.service.AmazonS3Service;
import com.bulkloads.web.file.domain.data.FileData;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

class FileServiceTest {

  @InjectMocks
  private FileService fileService;

  @Mock
  private AmazonS3Service amazonS3Service;

  public FileServiceTest() {
    MockitoAnnotations.openMocks(this);
  }

  @Test
  void shouldHandleUnknownMimeTypeWhenBuildingFileData() throws IOException {

    Path tempFile = Files.createTempFile("test", ".xyz123");
    byte[] randomBytes = {0x1, 0x2, 0x3, 0x4, 0x5};
    Files.write(tempFile, randomBytes);

    String fileUrl = "https://example.com/test.xyz123";
    String thumbUrl = "https://example.com/test_thumb.xyz123";

    try {
      FileData fileData = FileService.buildFileDataFromPath(tempFile, fileUrl, thumbUrl);

      assertThat(fileData.getMimeType())
          .isPresent()
          .hasValue("application/octet-stream");

      assertThat(fileData.getFilename())
          .isPresent()
          .matches(name -> name.get().endsWith(".xyz123"));

      assertThat(fileData.getExtension())
          .isPresent()
          .hasValue("xyz123");

      assertThat(fileData.getSize())
          .isPresent()
          .hasValue((long) randomBytes.length);

    } finally {
      Files.deleteIfExists(tempFile);
    }
  }

  @Test
  void shouldHandleUnknownMimeTypeWhenPreparingSingleFile() throws IOException {

    Path tempFile = Files.createTempFile("test", ".xyz123");
    byte[] randomBytes = {0x1, 0x2, 0x3, 0x4, 0x5};
    Files.write(tempFile, randomBytes);

    String s3FolderName = "test-folder";

    // Mock S3 service to avoid actual upload
    when(amazonS3Service.put(any(Path.class), eq(s3FolderName)))
        .thenReturn("https://example.com/test.xyz123");

    try {
      FileData fileData = fileService.prepareSingleFile(tempFile, s3FolderName);

      assertThat(fileData.getMimeType())
          .isPresent()
          .hasValue("application/octet-stream");

      verify(amazonS3Service).put(any(Path.class), eq(s3FolderName));

    } finally {
      Files.deleteIfExists(tempFile);
    }
  }
}
