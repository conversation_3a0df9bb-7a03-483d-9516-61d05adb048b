package com.bulkloads.web.file.util;

import static com.bulkloads.web.file.util.FileUtils.deleteFileIfExists;
import static com.bulkloads.web.file.util.ImageUtils.detectImageFormat;
import static com.bulkloads.web.file.util.ImageUtils.resample;
import static com.bulkloads.web.file.util.ImageUtils.writeImageToPath;
import java.awt.Color;
import java.awt.Graphics2D;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Comparator;
import javax.imageio.ImageIO;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class ScaleDownImageTest {

  private Path tempDir;

  @BeforeEach
  void setUp() throws IOException {
    tempDir = Files.createTempDirectory("scale-image-test");
  }

  @AfterEach
  void tearDown() throws IOException {
    if (tempDir != null && Files.exists(tempDir)) {
      Files.walk(tempDir)
          .sorted(Comparator.reverseOrder())
          .forEach(p -> {
            log.debug("Deleting {}", p);
            deleteFileIfExists(p);
          });
    }
  }

  @Test
  void testGivenAnImageWithinBoundsItWillRespectDimensions() throws IOException {

    int originalWidth = 100;
    int originalHeight = 100;

    Path outputImagePath = tempDir.resolve("image-100x100.jpeg");
    BufferedImage image = createTestImage(originalWidth, originalHeight, Color.RED);
    writeImageToPath(image, 1.0f, "jpeg", outputImagePath);

    resample(outputImagePath, 1.0f, 1000, 1000);

    String fmt = detectImageFormat(outputImagePath);
    Assertions.assertEquals("jpeg", fmt);

    BufferedImage outputImage = ImageIO.read(outputImagePath.toFile());
    Assertions.assertEquals(100, outputImage.getWidth());
    Assertions.assertEquals(100, outputImage.getHeight());
  }

  @Test
  void testGivenAnImageWithinBoundsItWillRespectDimensionsButRetypeToJpeg() throws IOException {
    int originalWidth = 1000;
    int originalHeight = 1000;

    Path imagePath = tempDir.resolve("image-100x100.png");
    BufferedImage image = createTestImage(originalWidth, originalHeight, Color.RED);

    writeImageToPath(image, 1.0f, "png", imagePath);

    resample(imagePath, 1.0f, 100, 100);

    String fmt = detectImageFormat(imagePath);

    BufferedImage outputImage = ImageIO.read(imagePath.toFile());
    Assertions.assertEquals(100, outputImage.getWidth());
    Assertions.assertEquals(100, outputImage.getHeight());
  }

  private static BufferedImage createTestImage(int width, int height, Color color) {
    BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
    Graphics2D g2d = image.createGraphics();
    g2d.setColor(color);
    g2d.fillRect(0, 0, width, height);
    g2d.dispose();
    return image;
  }

}