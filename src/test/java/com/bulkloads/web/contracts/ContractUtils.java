package com.bulkloads.web.contracts;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import com.bulkloads.web.contracts.service.dto.ContractResponse;

public final class ContractUtils {

  public static final LocalDate LOCAL_DATE = LocalDate.parse("2024-03-19");
  public static final Instant INSTANT = Instant.parse("2024-03-19T12:26:45.988Z");
  public static final String CREATE_CONTRACT_REQUEST_JSON = """
      {
         "external_contract_id" : "",
         "buy_sell" : "Sell",
         "contract_number" : "CN123",
         "rate" : 1,
         "rate_type" : "",
         "freight_rate" : 1,
         "freight_rate_type" : "2000",
         "number_of_loads" : 1,
         "quantity" : 1,
         "commodity_id" : "3192",
         "ship_from" : "2024-03-19T12:26:45.988Z",
         "ship_to" : "2024-03-19T12:26:45.988Z",
         "contact_info" : "string",
         "notes" : "string",
         "pickup_ab_company_id" : 1,
         "drop_ab_company_id" : 2
       }
      """;
  public static final String UPDATE_CONTRACT_REQUEST_JSON = """
      {
         "buy_sell" : "Sell"
      }
      """;
  public static final String CONTRACT_RESPONSE_JSON = """
      {
        "contract_id" : 1,
        "user_id" : 1,
        "user_company_id" : 1,
        "contract_status" : "open",
        "external_contract_id" : "externalContractId",
        "buy_sell" : "buy",
        "contract_number" : "contractNumber",
        "rate" : 1,
        "rate_type" : "1000",
        "freight_rate" : 1,
        "freight_rate_type" : "freightRateType",
        "freight_rate_per_mile" : 1,
        "number_of_loads" : 1,
        "remaining_number_of_loads" : 1,
        "quantity" : 1,
        "contract_total" : 1,
        "remaining_quantity" : 1,
        "weight" : 1,
        "remaining_weight" : 1,
        "commodity_id" : 1,
        "ship_from" : "2024-03-19",
        "ship_to" : "2024-03-19",
        "pickup_ab_company_id" : 1,
        "pickup_company_name" : "pickupCompanyName",
        "pickup_address" : "pickupAddress",
        "pickup_location" : "pickupLocation",
        "pickup_city" : "pickupCity",
        "pickup_state" : "pickupState",
        "pickup_zip" : "pickupZip",
        "pickup_country" : "pickupCountry",
        "pickup_lat" : 1.0,
        "pickup_long" : 1.0,
        "drop_ab_company_id" : 1,
        "drop_company_name" : "dropCompanyName",
        "drop_address" : "dropAddress",
        "drop_location" : "dropLocation",
        "drop_city" : "dropCity",
        "drop_state" : "dropState",
        "drop_zip" : "dropZip",
        "drop_country" : "dropCountry",
        "drop_lat" : 1.0,
        "drop_long" : 1.0,
        "miles" : 1,
        "contact_info" : "contactInfo",
        "notes" : "notes",
        "added_date" : "2024-03-19T12:26:45.988Z",
        "closed_date" : "2024-03-19T12:26:45.988Z",
        "modified_date" : "2024-03-19T12:26:45.988Z",
        "deleted" : 0,
        "commodity" : "commodityName"
      }""";
  public static final String CONTRACT_FULL_RESPONSE_JSON = """
      {
        "contract_id" : 1,
        "user_id" : 1,
        "user_company_id" : 1,
        "contract_status" : "open",
        "external_contract_id" : "externalContractId",
        "buy_sell" : "buy",
        "contract_number" : "contractNumber",
        "rate" : 1,
        "rate_type" : "1000",
        "freight_rate" : 1,
        "freight_rate_type" : "freightRateType",
        "freight_rate_per_mile" : 1,
        "number_of_loads" : 1,
        "remaining_number_of_loads" : 1,
        "quantity" : 1,
        "contract_total" : 1,
        "remaining_quantity" : 1,
        "weight" : 1,
        "remaining_weight" : 1,
        "commodity_id" : 1,
        "ship_from" : "2024-03-19",
        "ship_to" : "2024-03-19",
        "pickup_ab_company_id" : 1,
        "pickup_company_name" : "pickupCompanyName",
        "pickup_address" : "pickupAddress",
        "pickup_location" : "pickupLocation",
        "pickup_city" : "pickupCity",
        "pickup_state" : "pickupState",
        "pickup_zip" : "pickupZip",
        "pickup_country" : "pickupCountry",
        "pickup_lat" : 1.0,
        "pickup_long" : 1.0,
        "drop_ab_company_id" : 1,
        "drop_company_name" : "dropCompanyName",
        "drop_address" : "dropAddress",
        "drop_location" : "dropLocation",
        "drop_city" : "dropCity",
        "drop_state" : "dropState",
        "drop_zip" : "dropZip",
        "drop_country" : "dropCountry",
        "drop_lat" : 1.0,
        "drop_long" : 1.0,
        "miles" : 1,
        "contact_info" : "contactInfo",
        "notes" : "notes",
        "added_date" : "2024-03-19T12:26:45.988Z",
        "closed_date" : "2024-03-19T12:26:45.988Z",
        "modified_date" : "2024-03-19T12:26:45.988Z",
        "deleted" : 0,
        "commodity" : "commodityName"
      }""";

  public static ContractResponse buildContractResponse() {
    return buildContractResponse(LOCAL_DATE, LOCAL_DATE);
  }

  public static ContractResponse buildContractResponse(LocalDate shipFrom,
                                                       LocalDate shipTo) {

    return ContractResponse.builder()
        .contractId(1)
        .contractStatus("open")
        .userId(1)
        .userCompanyId(1)
        .contractStatus("open")
        .externalContractId("externalContractId")
        .buySell("buy")
        .contractNumber("contractNumber")
        .rate(BigDecimal.ONE)
        .rateType("1000")
        .freightRate(BigDecimal.ONE)
        .freightRateType("freightRateType")
        .freightRatePerMile(BigDecimal.ONE)
        .numberOfLoads(1)
        .remainingNumberOfLoads(1)
        .quantity(BigDecimal.ONE)
        .contractTotal(BigDecimal.ONE)
        .remainingQuantity(BigDecimal.ONE)
        .weight(BigDecimal.ONE)
        .remainingWeight(BigDecimal.ONE)
        .commodityId(1)
        .commodityName("commodityName")
        .shipFrom(shipFrom)
        .shipTo(shipTo)
        .pickupAbCompanyId(1)
        .pickupCompanyName("pickupCompanyName")
        .pickupAddress("pickupAddress")
        .pickupLocation("pickupLocation")
        .pickupCity("pickupCity")
        .pickupState("pickupState")
        .pickupZip("pickupZip")
        .pickupCountry("pickupCountry")
        .pickupLat(1.0)
        .pickupLong(1.0)
        .dropAbCompanyId(1)
        .dropCompanyName("dropCompanyName")
        .dropAddress("dropAddress")
        .dropLocation("dropLocation")
        .dropCity("dropCity")
        .dropState("dropState")
        .dropZip("dropZip")
        .dropCountry("dropCountry")
        .dropLat(1.0)
        .dropLong(1.0)
        .miles(BigDecimal.ONE)
        .contactInfo("contactInfo")
        .notes("notes")
        .addedDate(INSTANT)
        .closedDate(INSTANT)
        .modifiedDate(INSTANT)
        .deleted(false)
        .build();
  }
}
