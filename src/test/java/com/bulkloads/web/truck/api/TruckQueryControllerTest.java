package com.bulkloads.web.truck.api;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;
import java.time.Instant;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import com.bulkloads.web.truck.service.TruckService;
import com.bulkloads.web.truck.service.dto.MyTruckResponse;
import com.bulkloads.web.truck.service.dto.TruckResponse;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * Unit tests for the {@link TruckController} class.
 */
@ExtendWith(MockitoExtension.class)
class TruckQueryControllerTest {

  @Mock
  private TruckService truckService;

  @InjectMocks
  private TruckQueryController truckQueryController;

  @Test
  void shouldReturnListOfTrucks() {
    List<MyTruckResponse> myTruckRespons = new ArrayList<>();

    myTruckRespons.add(MyTruckResponse.builder()
        .truckId(3)
        .comments("FLAT BED")
        .contactName("")
        .contactNumber("")
        .equipmentName("")
        .repostDays(5)
        .postDate(Instant.now())
        .dateAvailable(LocalDate.now())
        .timeAvailable("6 PM")
        .originLat(41.3394)
        .originLong(54.987)
        .destination("")
        .origin("")
        .expiresOn(LocalDate.now())
        .build()
    );

    // Mock the behavior of truckService.getMyTrucks()
    when(truckService.getMyTrucks("Newest", 0, 100)).thenReturn(myTruckRespons);

    // Call the method under test
    List<MyTruckResponse> result = truckQueryController.getMyTrucks("Newest", 0, 100);

    // Assert the result
    assertEquals(myTruckRespons, result);
  }

  @Test
  void shouldReturn_EmptyResponse() {
    List<MyTruckResponse> emptyTruckResponses = new ArrayList<>();
    when(truckService.getMyTrucks("Newest", 0, 100)).thenReturn(emptyTruckResponses);
    List<MyTruckResponse> result = truckQueryController.getMyTrucks("Newest", 0, 100);

    assertTrue(result.isEmpty(), "Expected an empty list of truck responses");
  }

  @Test
  void testGetTrucks() {
    // Given
    List<TruckResponse> expectedTrucks = Collections.singletonList(TruckResponse.builder().build());
    when(truckService.getTrucks(anyString(), anyString(), anyInt(), anyString(), any(LocalDate.class), any(LocalDate.class),
        anyInt(), anyString(), anyInt(), anyInt())).thenReturn(expectedTrucks);

    // When
    List<TruckResponse> actualTrucks = truckQueryController.getTrucks(
        "FL",
        "LIVE OAK",
        5,
        "equipment type",
        LocalDate.now(),
        LocalDate.now(),
        5,
        "Newest",
        0,
        100);

    // Then
    assertEquals(expectedTrucks, actualTrucks);
  }
}
