package com.bulkloads.web.usercompany.repository;

import java.util.List;
import com.bulkloads.web.common.TestUsersProvider;
import com.bulkloads.web.usercompany.domain.entity.UserType;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

class UserTypeRepositoryTest extends TestUsersProvider {

  @Autowired
  UserTypeRepository userTypeRepository;

  @Test
  void searchByUserTypeIdsOrderByUserTypeIdAsc() {

    List<UserType> types = userTypeRepository.searchByUserTypeIdsOrderByUserTypeIdAsc(List.of());
    Assertions.assertThat(types).isEmpty();

    types = userTypeRepository.searchByUserTypeIdsOrderByUserTypeIdAsc(List.of(20));
    Assertions.assertThat(types).hasSize(1);

    types = userTypeRepository.searchByUserTypeIdsOrderByUserTypeIdAsc(List.of(20, 30));
    Assertions.assertThat(types).hasSize(2);

    var foundTypes = userTypeRepository.searchByUserTypeIdsOrderByUserTypeIdAsc(
        List.of(10, 20, 40, 50, 60, 70, 80, 90, 100));
    Assertions.assertThat(foundTypes).hasSize(4);
  }

}