package com.bulkloads.web;

import static org.mockito.Mockito.verifyNoMoreInteractions;
import com.bulkloads.advice.ErrorHandler;
import com.bulkloads.config.AppConfiguration;
import com.bulkloads.config.AppProperties;
import com.bulkloads.config.ObjectMapperConfig;
import com.bulkloads.security.BulkLoadsJwtTokenService;
import com.bulkloads.security.BulkLoadsUserDetailsService;
import com.bulkloads.security.SecurityConfiguration;
import com.bulkloads.security.oauth.Oauth2AuthenticationSuccessHandler;
import com.bulkloads.web.apikey.repository.ApiKeyRepository;
import com.bulkloads.web.requestlog.RequestLogRepository;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import org.junit.jupiter.api.BeforeEach;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;
import org.springframework.test.web.servlet.MockMvc;
import lombok.SneakyThrows;

@Import({AppConfiguration.class, SecurityConfiguration.class, ObjectMapperConfig.class})
public abstract class ControllerTest {

  @MockBean
  protected BulkLoadsJwtTokenService bulkLoadsJwtTokenService;
  @MockBean
  protected BulkLoadsUserDetailsService userDetailsService;
  @MockBean
  protected ApiKeyRepository apiKeyRepository;
  @MockBean
  protected ErrorHandler errorHandler;
  @MockBean
  protected RequestLogRepository requestLogRepository;
  @MockBean
  protected Oauth2AuthenticationSuccessHandler oauth2AuthenticationSuccessHandler;
  @MockBean
  protected AppProperties appProperties;

  @Autowired
  protected MockMvc mockMvc;
  @Autowired
  protected ObjectMapper objectMapper;

  @BeforeEach
  void setUp() {
    objectMapper.configure(SerializationFeature.INDENT_OUTPUT, true);
  }

  protected void tearDownMocks() {
    verifyNoMoreInteractions(bulkLoadsJwtTokenService);
    verifyNoMoreInteractions(userDetailsService);
    verifyNoMoreInteractions(apiKeyRepository);
  }

  @SneakyThrows
  protected String prettyPrintJson(final String json) {
    final Object jsonObject = objectMapper.readValue(json, Object.class);
    return objectMapper.writeValueAsString(jsonObject);
  }

}
