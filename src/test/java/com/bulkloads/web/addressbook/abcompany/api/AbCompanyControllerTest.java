package com.bulkloads.web.addressbook.abcompany.api;

import static com.bulkloads.config.AppConstants.UserRole.ROLE_USER;
import static com.bulkloads.web.addressbook.AbCompanyUtils.AB_COMPANY_RESPONSE_JSON;
import static com.bulkloads.web.addressbook.AbCompanyUtils.CREATE_AB_COMPANY_REQUEST_JSON;
import static com.bulkloads.web.addressbook.AbCompanyUtils.UPDATE_AB_COMPANY_REQUEST_JSON;
import static com.bulkloads.web.addressbook.AbCompanyUtils.buildAbCompanyResponse;
import static com.bulkloads.web.confg.TestConstants.DUMMY_PERMISSION;
import static com.bulkloads.web.confg.TestConstants.DUMMY_ROLE;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoMoreInteractions;
import static org.mockito.Mockito.when;
import static org.springframework.restdocs.mockmvc.RestDocumentationRequestBuilders.delete;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;
import com.bulkloads.config.security.WithMockActor;
import com.bulkloads.web.ControllerTest;
import com.bulkloads.web.addressbook.abcompany.service.AbCompanyService;
import com.bulkloads.web.addressbook.abcompany.service.dto.AbCompanyRequest;
import com.bulkloads.web.addressbook.abcompany.service.dto.AbCompanyResponse;
import com.bulkloads.web.confg.TestConstants;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import lombok.SneakyThrows;

@WebMvcTest(AbCompanyController.class)
@AutoConfigureMockMvc(addFilters = false)
class AbCompanyControllerTest extends ControllerTest {

  private static final String URL = TestConstants.AB_COMPANIES_URL;

  @MockBean
  AbCompanyService service;

  @AfterEach
  void tearDown() {
    super.tearDownMocks();
    verifyNoMoreInteractions(service);
  }

  @Test
  @SneakyThrows
  @WithMockActor(roles = ROLE_USER)
  void givenAuthenticatedUserWithRightRole_whenCreateAbCompany_thenShouldBeOk() {

    final AbCompanyRequest request = objectMapper.readValue(CREATE_AB_COMPANY_REQUEST_JSON, AbCompanyRequest.class);
    final AbCompanyResponse response = buildAbCompanyResponse();

    final String responseJson = """
       {
         "message" : "The company was added to your address book",
         "ab_company_id" : 1,
         "key" : 1,
         "data" : %s
       }""".formatted(AB_COMPANY_RESPONSE_JSON);

    when(service.create(request)).thenReturn(response);

    mockMvc.perform(post(URL)
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON)
            .content(CREATE_AB_COMPANY_REQUEST_JSON))
        .andExpect(status().isOk())
        .andExpect(content().string(prettyPrintJson(responseJson)));

    verify(service).create(request);
  }

  @Test
  @SneakyThrows
  @WithMockActor(roles = DUMMY_ROLE)
  void givenAuthenticatedUserWithWrongRole_whenCreateAbCompany_thenShouldBeForbidden() {
    mockMvc.perform(post(URL)
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON)
            .content(CREATE_AB_COMPANY_REQUEST_JSON))
        .andExpect(status().isForbidden())
        .andReturn();
  }

  @Test
  @SneakyThrows
  void givenUnauthenticatedUser_whenCreateAbCompany_thenShouldBeUnauthorized() {
    mockMvc.perform(post(URL)
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON)
            .content(CREATE_AB_COMPANY_REQUEST_JSON))
        .andExpect(status().isUnauthorized())
        .andReturn();
  }

  @Test
  @SneakyThrows
  @WithMockActor(roles = ROLE_USER)
  void givenAuthenticatedUserWithRightRole_whenUpdateAbCompany_thenShouldBeOk() {

    final int abCompanyId = 1;
    final AbCompanyRequest request =
        objectMapper.readValue(UPDATE_AB_COMPANY_REQUEST_JSON, AbCompanyRequest.class);
    final AbCompanyResponse response = buildAbCompanyResponse();

    final String responseJson = """
       {
         "message" : "Company details updated",
         "ab_company_id" : 1,
         "key" : 1,
         "data" : %s
       }""".formatted(AB_COMPANY_RESPONSE_JSON);

    when(service.update(abCompanyId, request)).thenReturn(response);

    mockMvc.perform(put(URL + "/{ab_company_id}", abCompanyId)
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON)
            .content(UPDATE_AB_COMPANY_REQUEST_JSON))
        .andExpect(status().isOk())
        .andExpect(content().string(prettyPrintJson(responseJson)));

    verify(service).update(abCompanyId, request);
  }

  @Test
  @SneakyThrows
  @WithMockActor(roles = DUMMY_PERMISSION)
  void givenAuthenticatedUserWithWrongPermissions_whenUpdateAbCompany_thenShouldBeForbidden() {
    final int abCompanyId = 1;
    mockMvc.perform(put(URL + "/{ab_company_id}", abCompanyId)
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON)
            .content(UPDATE_AB_COMPANY_REQUEST_JSON))
        .andExpect(status().isForbidden());
  }

  @Test
  @SneakyThrows
  void givenUnauthenticatedUser_whenUpdateAbCompany_thenShouldBeUnauthorized() {
    final int abCompanyId = 1;
    mockMvc.perform(put(URL + "/{ab_company_id}", abCompanyId)
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON)
            .content(UPDATE_AB_COMPANY_REQUEST_JSON))
        .andExpect(status().isUnauthorized());
  }

  @Test
  @SneakyThrows
  @WithMockActor(roles = ROLE_USER)
  void givenAuthenticatedUserWithRightRole_whenDeleteAbCompany_thenShouldBeOk() {

    final int abCompanyId = 1;

    final String responseJson = """
        {
          "message" : "Company deleted"
        }""";

    doNothing().when(service).remove(abCompanyId);

    mockMvc.perform(delete(URL + "/{ab_company_id}", abCompanyId)
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andExpect(content().string(prettyPrintJson(responseJson)));

    verify(service).remove(abCompanyId);
  }

  @Test
  @SneakyThrows
  @WithMockActor(roles = DUMMY_PERMISSION)
  void givenAuthenticatedUserWithWrongRole_whenDeleteAbCompany_thenShouldBeForbidden() {
    final int abCompanyId = 1;
    mockMvc.perform(delete(URL + "/{ab_company_id}", abCompanyId)
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isForbidden());
  }

  @Test
  @SneakyThrows
  void givenUnauthenticatedUser_whenDeleteAbCompany_thenShouldBeUnauthorized() {
    final int abCompanyId = 1;
    mockMvc.perform(delete(URL + "/{ab_company_id}", abCompanyId)
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isUnauthorized());
  }
}
