package com.bulkloads.web.addressbook.abcompany.api;

import static com.bulkloads.config.AppConstants.UserRole.ROLE_USER;
import static com.bulkloads.web.addressbook.AbCompanyUtils.AB_COMPANY_AUTO_RESPONSE_JSON;
import static com.bulkloads.web.addressbook.AbCompanyUtils.AB_COMPANY_FINDER_RESPONSE_JSON;
import static com.bulkloads.web.addressbook.AbCompanyUtils.AB_COMPANY_RESPONSE_JSON;
import static com.bulkloads.web.addressbook.AbCompanyUtils.buildAbCompanyAutoResponse;
import static com.bulkloads.web.addressbook.AbCompanyUtils.buildAbCompanyFinderResponse;
import static com.bulkloads.web.addressbook.AbCompanyUtils.buildAbCompanyResponse;
import static com.bulkloads.web.confg.TestConstants.DUMMY_ROLE;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoMoreInteractions;
import static org.mockito.Mockito.when;
import static org.springframework.restdocs.mockmvc.RestDocumentationRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;
import java.util.List;
import com.bulkloads.common.Parsers;
import com.bulkloads.config.security.WithMockActor;
import com.bulkloads.web.ControllerTest;
import com.bulkloads.web.addressbook.abcompany.service.AbCompanyService;
import com.bulkloads.web.addressbook.abcompany.service.dto.AbCompanyAutoResponse;
import com.bulkloads.web.addressbook.abcompany.service.dto.AbCompanyFinderResponse;
import com.bulkloads.web.addressbook.abcompany.service.dto.AbCompanyResponse;
import com.bulkloads.web.confg.TestConstants;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import lombok.SneakyThrows;

@WebMvcTest(AbCompanyQueryController.class)
@AutoConfigureMockMvc(addFilters = false)
class AbCompanyQueryControllerTest extends ControllerTest {

  private static final String URL = TestConstants.AB_COMPANIES_URL;

  @MockBean
  AbCompanyService service;

  @AfterEach
  void tearDown() {
    super.tearDownMocks();
    verifyNoMoreInteractions(service);
  }

  @Test
  @SneakyThrows
  @WithMockActor(roles = ROLE_USER)
  void givenAuthenticatedUserWithRightRole_whenGetAbCompanyById_thenShouldBeOk() {
    final int abCompanyId = 1;
    final AbCompanyResponse response = buildAbCompanyResponse();

    when(service.getAbCompany(abCompanyId)).thenReturn(response);

    mockMvc.perform(get(URL + "/{ab_company_id}", abCompanyId)
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andExpect(content().string(prettyPrintJson(AB_COMPANY_RESPONSE_JSON)));

    verify(service).getAbCompany(abCompanyId);
  }

  @Test
  @SneakyThrows
  @WithMockActor(roles = ROLE_USER)
  void givenANegativeAbCompanyId_whenGetAbCompanyById_thenShouldBeBadRequest() {
    final int abCompanyId = -1;
    mockMvc.perform(get(URL + "/{ab_company_id}", abCompanyId)
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest());
  }

  @Test
  @SneakyThrows
  @WithMockActor(roles = DUMMY_ROLE)
  void givenAuthenticatedUserWithWrongRole_whenGetAbCompanyById_thenShouldBeForbidden() {
    final int abCompanyId = 1;
    mockMvc.perform(get(URL + "/{ab_company_id}", abCompanyId)
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isForbidden())
        .andReturn();
  }

  @Test
  @SneakyThrows
  void givenUnauthenticatedUser_whenGetAbCompanyById_thenShouldBeUnauthorized() {
    final int abCompanyId = 1;
    mockMvc.perform(get(URL + "/{ab_company_id}", abCompanyId)
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isUnauthorized())
        .andReturn();
  }

  //TODO test getAbCompanies

  @Test
  @SneakyThrows
  @WithMockActor(roles = ROLE_USER)
  void givenAuthenticatedUserWithRightRole_whenGetCompaniesFinder_thenShouldBeOk() {
    final List<AbCompanyFinderResponse> response = List.of(buildAbCompanyFinderResponse());

    final String term = "term";
    final String userTypeIdsString = "123,234";
    final List<Integer> userTypeIds = Parsers.parseIntegerCsvToList(userTypeIdsString);

    when(service.getCompaniesFinder(term, userTypeIds)).thenReturn(response);

    mockMvc.perform(get(URL + "/finder")
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON)
            .param("term", term)
            .param("user_type_ids", userTypeIdsString))
        .andExpect(status().isOk())
        .andExpect(content().string(prettyPrintJson("[ " + AB_COMPANY_FINDER_RESPONSE_JSON + " ]")));

    verify(service).getCompaniesFinder(term, userTypeIds);
  }

  @Test
  @SneakyThrows
  @WithMockActor(roles = DUMMY_ROLE)
  void givenAuthenticatedUserWithWrongRole_whenGetCompaniesFinder_thenShouldBeForbidden() {
    mockMvc.perform(get(URL + "/finder")
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON)
            .param("user_type_ids", "1,2"))
        .andExpect(status().isForbidden())
        .andReturn();
  }

  @Test
  @SneakyThrows
  void givenUnauthenticatedUser_whenGetCompaniesFinder_thenShouldBeUnauthorized() {
    mockMvc.perform(get(URL + "/finder")
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON)
            .param("user_type_ids", "1,2"))
        .andExpect(status().isUnauthorized())
        .andReturn();
  }

  @Test
  @SneakyThrows
  @WithMockActor(roles = ROLE_USER)
  void givenAuthenticatedUserWithRightRole_whenGetAbCompaniesAuto_thenShouldBeOk() {
    final List<AbCompanyAutoResponse> response = List.of(buildAbCompanyAutoResponse());

    final String term = "term";
    final Boolean includeBillToData = true;
    final String userTypeIdsString = "123,234";
    final String order = "ASC";
    final int skip = 1;
    final int limit = 10;
    final List<Integer> userTypeIds = Parsers.parseIntegerCsvToList(userTypeIdsString);

    when(service.getAbCompaniesAuto(term, includeBillToData, userTypeIds, order, skip, limit))
        .thenReturn(response);

    mockMvc.perform(get(URL + "/auto")
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON)
            .param("term", term)
            .param("include_bill_to_data", "true")
            .param("user_type_ids", userTypeIdsString)
            .param("order", order)
            .param("skip", "" + skip)
            .param("limit", "" + limit))
        .andExpect(status().isOk())
        .andExpect(content().string(prettyPrintJson("[ " + AB_COMPANY_AUTO_RESPONSE_JSON + " ]")));

    verify(service).getAbCompaniesAuto(term, includeBillToData, userTypeIds, order, skip, limit);
  }

  @Test
  @SneakyThrows
  @WithMockActor(roles = ROLE_USER)
  void givenInvalidParams_whenGetAbCompaniesAuto_thenShouldBeBadRequest() {
    mockMvc.perform(get(URL + "/auto")
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON)
            .param("skip", "-1")
            .param("limit", "-1"))
        .andExpect(status().isBadRequest());
  }

  @Test
  @SneakyThrows
  @WithMockActor(roles = DUMMY_ROLE)
  void givenAuthenticatedUserWithWrongRole_whenGetAbCompaniesAuto_thenShouldBeForbidden() {
    mockMvc.perform(get(URL + "/auto")
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON)
            .param("user_type_ids", "1,2"))
        .andExpect(status().isForbidden())
        .andReturn();
  }

  @Test
  @SneakyThrows
  void givenUnauthenticatedUser_whenGetAbCompaniesAuto_thenShouldBeUnauthorized() {
    mockMvc.perform(get(URL + "/auto")
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON)
            .param("user_type_ids", "1,2"))
        .andExpect(status().isUnauthorized())
        .andReturn();
  }
}