package com.bulkloads.web.addressbook.common.api;

import static com.bulkloads.web.confg.TestConstants.AB_BOOK_URL;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoMoreInteractions;
import static org.mockito.Mockito.when;
import static org.springframework.restdocs.mockmvc.RestDocumentationRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import java.util.List;
import com.bulkloads.web.ControllerTest;
import com.bulkloads.web.addressbook.abuser.domain.entity.AbUserRole;
import com.bulkloads.web.addressbook.abuser.service.AbUserService;
import com.bulkloads.web.addressbook.abuser.service.dto.AbUserTypeResponse;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import lombok.SneakyThrows;

@WebMvcTest(AbCommonQueryController.class)
@AutoConfigureMockMvc(addFilters = false)
class AbCommonQueryControllerTest extends ControllerTest {

  private static final String URL = AB_BOOK_URL;

  private static final String AB_USER_ROLE_JSON = """
      {
        "ab_user_role_id": 1,
        "ab_user_role": "Admin",
        "description" : "Dummy description"
      }""";

  private static final String AB_USER_TYPE_RESPONSE_JSON = """
      {
        "user_type_id": 1,
        "user_type": "Shipper"
      }""";

  @MockBean
  AbUserService abUserService;

  @AfterEach
  void tearDown() {
    super.tearDownMocks();
    verifyNoMoreInteractions(abUserService);
  }

  @Test
  @SneakyThrows
  void whenGetAbUserRoles_thenShouldBeOk() {
    final List<AbUserRole> response = List.of(createAbUserRole());

    when(abUserService.getAbUserRoles()).thenReturn(response);

    mockMvc.perform(get(URL + "/user_roles")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andExpect(content().string(prettyPrintJson("[ " + AB_USER_ROLE_JSON + " ]")));

    verify(abUserService).getAbUserRoles();
  }

  @Test
  @SneakyThrows
  void whenGetAbUserTypes_thenShouldBeOk() {
    final List<AbUserTypeResponse> response = List.of(createAbUserTypeResponse());

    when(abUserService.getAbUserTypes()).thenReturn(response);

    mockMvc.perform(get(URL + "/user_types")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andExpect(content().string(prettyPrintJson("[ " + AB_USER_TYPE_RESPONSE_JSON + " ]")));

    verify(abUserService).getAbUserTypes();
  }

  private AbUserRole createAbUserRole() {
    AbUserRole role = new AbUserRole();
    role.setAbUserRoleId(1);
    role.setAbUserRole("Admin");
    role.setDescription("Dummy description");
    return role;
  }

  private AbUserTypeResponse createAbUserTypeResponse() {
    return AbUserTypeResponse.builder()
        .userTypeId(1)
        .userType("Shipper").build();
  }
}
