package com.bulkloads.web.infra.websocket;

import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import java.util.HashMap;
import java.util.Map;
import com.bulkloads.config.AppProperties;
import com.bulkloads.web.infra.websocket.dto.WebSocketHereNowDto;
import com.bulkloads.web.infra.websocket.dto.WebSocketPublishDto;
import com.bulkloads.web.infra.websocket.dto.WebSocketSignalDto;
import com.bulkloads.web.infra.websocket.pubnub.PubNubClient;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import lombok.SneakyThrows;

@ExtendWith(MockitoExtension.class)
class WebSocketFacadeTest {

  static final String CHANNEL = "test-channel";
  static final String TEST_CHANNEL = "test-channel_test";
  static final String JSON_MESSAGE = "{\"key\":\"value\"}";

  @Mock
  PubNubClient pubNubClient;

  @Mock
  ObjectMapper objectMapper;

  @Mock
  AppProperties appProperties;

  @InjectMocks
  WebSocketFacade webSocketFacade;

  Map<String, Object> message;
  Map<String, Object> meta;

  @BeforeEach
  void setUp() {
    message = new HashMap<>();
    message.put("key", "value");

    meta = new HashMap<>();
    meta.put("user_id", 1);

    when(appProperties.isProdMode()).thenReturn(false);
  }

  @Test
  void publish_shouldAddTestSuffixInNonProdMode() {
    WebSocketPublishDto publishDto = WebSocketPublishDto.builder()
        .channel(CHANNEL)
        .message(message)
        .meta(meta)
        .build();

    webSocketFacade.publish(publishDto);

    verify(pubNubClient).publish(TEST_CHANNEL, meta, message);
  }

  @Test
  void publish_shouldNotAddTestSuffixInProdMode() {
    when(appProperties.isProdMode()).thenReturn(true);
    WebSocketPublishDto publishDto = WebSocketPublishDto.builder()
        .channel(CHANNEL)
        .message(message)
        .meta(meta)
        .build();

    webSocketFacade.publish(publishDto);

    verify(pubNubClient).publish(CHANNEL, meta, message);
  }

  @SneakyThrows
  @Test
  void signal_shouldAddTestSuffixInNonProdMode() {
    WebSocketSignalDto signalDto = WebSocketSignalDto.builder()
        .channel(CHANNEL)
        .message(message)
        .build();

    when(objectMapper.writeValueAsString(message)).thenReturn(JSON_MESSAGE);

    webSocketFacade.signal(signalDto);

    verify(pubNubClient).signal(TEST_CHANNEL, JSON_MESSAGE);
  }

  @SneakyThrows
  @Test
  void signal_shouldNotAddTestSuffixInProdMode() {
    when(appProperties.isProdMode()).thenReturn(true);
    WebSocketSignalDto signalDto = WebSocketSignalDto.builder()
        .channel(CHANNEL)
        .message(message)
        .build();

    when(objectMapper.writeValueAsString(message)).thenReturn(JSON_MESSAGE);

    webSocketFacade.signal(signalDto);

    verify(pubNubClient).signal(CHANNEL, JSON_MESSAGE);
  }

  @Test
  void hereNow_shouldNotAddTestSuffixInProdMode() {
    when(appProperties.isProdMode()).thenReturn(true);
    WebSocketHereNowDto hereNowDto = WebSocketHereNowDto.builder()
        .channel(CHANNEL)
        .includeUuids(true)
        .includeState(false)
        .build();

    webSocketFacade.hereNow(hereNowDto);

    verify(pubNubClient).hereNow(CHANNEL, true, false);
  }
}
