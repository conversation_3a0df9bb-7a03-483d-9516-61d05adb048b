package com.bulkloads.security;

import static com.bulkloads.config.AppConstants.UserPermission.MANAGE_ADDRESS_BOOK;
import static com.bulkloads.config.AppConstants.UserPermission.MANAGE_CONTRACTS;
import static com.bulkloads.web.confg.TestConstants.DEFAULT_APP_NAME;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoMoreInteractions;
import static org.mockito.Mockito.when;
import java.util.List;
import java.util.Optional;
import com.bulkloads.web.addressbook.abcompany.domain.entity.AbCompany;
import com.bulkloads.web.addressbook.abuser.domain.entity.AbUser;
import com.bulkloads.web.addressbook.abuser.repository.AbUserRepository;
import com.bulkloads.web.user.domain.entity.User;
import com.bulkloads.web.user.domain.entity.UserPermission;
import com.bulkloads.web.user.model.UserPermissionRepository;
import com.bulkloads.web.user.repository.UserRepository;
import com.bulkloads.web.usercompany.domain.entity.UserCompany;
import com.bulkloads.web.usercompany.domain.entity.UserType;
import com.bulkloads.web.usercompany.domain.entity.UserTypeValue;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class BulkLoadsUserDetailsServiceTest {

  @Mock
  UserRepository userRepository;
  @Mock
  UserPermissionRepository userPermissionRepository;
  @Mock
  AbUserRepository abUserRepository;
  @InjectMocks
  BulkLoadsUserDetailsService userDetailsService;

  @AfterEach
  void tearDown() {
    verifyNoMoreInteractions(userRepository);
    verifyNoMoreInteractions(userPermissionRepository);
    verifyNoMoreInteractions(abUserRepository);
  }

  @Test
  void givenUsername_whenLoadUserByUsername_thenShouldLoadActor() {
    final String username = "<EMAIL>";

    final User user = new User();
    user.setFirstName("firstName");
    user.setLastName("lastName");
    user.setUsername(username);

    final UserCompany userCompany = new UserCompany();
    userCompany.getUserTypes().add(UserType.of(UserTypeValue.ADMIN));
    user.setUserCompany(userCompany);

    when(userRepository.searchByEmail(username)).thenReturn(Optional.of(user));

    final Actor actual = userDetailsService.loadUserByUsername(username);
    final Actor expected = Actor.fromUser(user, null);

    assertEquals(expected, actual);

    verify(userRepository).searchByEmail(username);
  }

  @Test
  void givenUserId_whenLoadUserByIdWithPermissions_thenShouldLoadActorWithPermissions() {
    final int userId = 1;
    final String username = "<EMAIL>";

    final User user = new User();
    user.setUserId(userId);
    user.setFirstName("firstName");
    user.setLastName("lastName");
    user.setUsername(username);

    final UserCompany userCompany = new UserCompany();
    userCompany.getUserTypes().add(UserType.of(UserTypeValue.ADMIN));
    user.setUserCompany(userCompany);

    final UserPermission manageContractsPermission = createPermission(MANAGE_CONTRACTS);
    final UserPermission manageAddressBookPermission = createPermission(MANAGE_ADDRESS_BOOK);

    final List<UserPermission> permissions =
        List.of(manageContractsPermission, manageAddressBookPermission);

    when(userRepository.searchByIdWithCompanyAndSettings(userId)).thenReturn(Optional.of(user));
    when(userPermissionRepository.findAllByUserId(userId)).thenReturn(permissions);

    final Actor actual = userDetailsService.loadUserByIdWithPermissions(userId, DEFAULT_APP_NAME);
    final Actor expected =
        Actor.fromUser(user, userDetailsService.getPermissionsAsAuthorities(permissions), DEFAULT_APP_NAME);

    assertEquals(expected, actual);

    verify(userRepository).searchByIdWithCompanyAndSettings(userId);
    verify(userPermissionRepository).findAllByUserId(userId);
  }

  @Test
  void testLoadAbUserById() {
    final int abUserId = 1;
    final AbUser abUser = new AbUser();
    AbCompany abCompany = new AbCompany();
    abCompany.setCompanyName("Test Company");
    abCompany.setUserTypes(List.of("ADMIN"));
    abCompany.setUserTypeIds(List.of(1));
    abUser.setAbCompany(abCompany);

    when(abUserRepository.findByIdWithCompany(abUserId)).thenReturn(Optional.of(abUser));

    final Actor actual = userDetailsService.loadAbUserById(abUserId, DEFAULT_APP_NAME);
    final Actor expected = Actor.fromGuest(abUser, DEFAULT_APP_NAME);

    assertEquals(expected, actual);

    verify(abUserRepository).findByIdWithCompany(abUserId);
  }

  private UserPermission createPermission(final String permissionName) {
    final UserPermission manageContractsPermission = new UserPermission();
    manageContractsPermission.setPermission(permissionName);
    return manageContractsPermission;
  }

}
