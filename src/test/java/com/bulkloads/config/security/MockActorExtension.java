package com.bulkloads.config.security;

import static org.springframework.core.annotation.AnnotatedElementUtils.isAnnotated;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import com.bulkloads.security.Actor;
import org.junit.jupiter.api.extension.AfterTestExecutionCallback;
import org.junit.jupiter.api.extension.BeforeTestExecutionCallback;
import org.junit.jupiter.api.extension.ExtensionContext;
import org.springframework.security.authentication.TestingAuthenticationToken;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;

public class MockActorExtension implements BeforeTestExecutionCallback, AfterTestExecutionCallback {

  private static List<GrantedAuthority> getPermissionsAsAuthorities(final String[] permissions) {
    return Arrays.stream(permissions).map(SimpleGrantedAuthority::new).collect(Collectors.toList());
  }

  @Override
  public void beforeTestExecution(final ExtensionContext context) {
    if (!shouldMockUser(context)) {
      return;
    }
    final Method testMethod = context.getRequiredTestMethod();
    final WithMockActor mockActor = testMethod.getAnnotation(WithMockActor.class);

    final Actor actor = buildMockActor(mockActor);
    final String[] permissions = Stream.concat(
        Arrays.stream(mockActor.permissions()),
        Arrays.stream(mockActor.roles())).toArray(String[]::new);
    final List<GrantedAuthority> permissionsAsAuthorities = getPermissionsAsAuthorities(permissions);

    final TestingAuthenticationToken authentication = new TestingAuthenticationToken(actor, "N/A", permissionsAsAuthorities);

    SecurityContextHolder.getContext().setAuthentication(authentication);
  }

  @Override
  public void afterTestExecution(final ExtensionContext context) {
    SecurityContextHolder.clearContext();
  }

  private Actor buildMockActor(final WithMockActor mockUser) {
    final List<GrantedAuthority> authorities = getPermissionsAsAuthorities(mockUser.permissions());

    return Actor.builder()
        .userId(mockUser.userId())
        .username(mockUser.username())
        .firstName(mockUser.firstName())
        .lastName(mockUser.lastName())
        .email(mockUser.email())
        .userCompanyId(mockUser.userCompanyId())
        .userCompanyName(mockUser.userCompanyName())
        .userTypes(Arrays.asList(mockUser.userTypes()))
        .userTypeIds(Arrays.stream(mockUser.userTypeIds()).boxed().collect(Collectors.toList()))
        .abUserId(mockUser.abUserId())
        .isPro(mockUser.isPro())
        .authorities(authorities)
        .build();
  }

  private boolean shouldMockUser(final ExtensionContext context) {
    return context.getElement().map(el -> isAnnotated(el, WithMockActor.class)).orElse(false);
  }
}
