package com.bulkloads.common.validation;

import static com.bulkloads.common.validation.ValidationUtils.hasChange;
import static org.assertj.core.api.Assertions.assertThat;
import java.util.Optional;
import java.util.stream.Stream;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import lombok.AllArgsConstructor;
import lombok.Data;

class ValidationUtilsTest {

  @Test
  @DisplayName("hasChange should handle null Optional")
  void hasChange_withNullOptional_returnsFalse() {
    assertThat(hasChange("any value", null)).isFalse();
  }

  @ParameterizedTest(name = "oldValue={0}, newValue={1}, expectedResult={2}")
  @MethodSource("provideTestCases")
  void hasChange_shouldDetectChangesCorrectly(Object oldValue, Optional<?> newValue, boolean expectedResult) {
    assertThat(hasChange(oldValue, newValue)).isEqualTo(expectedResult);
  }

  private static Stream<Arguments> provideTestCases() {
    return Stream.of(
        // Test case format: oldValue, newValue, expectedResult

        // String cases
        Arguments.of("test", Optional.of("test"), false),
        Arguments.of("test", Optional.of("different"), true),
        Arguments.of("test", Optional.empty(), true),
        Arguments.of(null, Optional.of("test"), true),
        Arguments.of(null, Optional.empty(), false),

        // Integer cases
        Arguments.of(123, Optional.of(123), false),
        Arguments.of(123, Optional.of(456), true),
        Arguments.of(123, Optional.empty(), true),
        Arguments.of(null, Optional.of(123), true),

        // Object cases
        Arguments.of(new TestObject("test"), Optional.of(new TestObject("test")), false),
        Arguments.of(new TestObject("test"), Optional.of(new TestObject("different")), true),
        Arguments.of(new TestObject("test"), Optional.empty(), true),
        Arguments.of(null, Optional.of(new TestObject("test")), true),

        // Edge cases
        Arguments.of("", Optional.of(""), false),
        Arguments.of("", Optional.empty(), true),
        Arguments.of(null, Optional.of(""), true),
        Arguments.of(null, Optional.ofNullable(null), false),
        Arguments.of(null, Optional.empty(), false)
    );
  }

  // Test helper class
  @Data
  @AllArgsConstructor
  private static class TestObject {
    private final String value;
  }
}