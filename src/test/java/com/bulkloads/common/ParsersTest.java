package com.bulkloads.common;

import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;

class ParsersTest {

  @Test
  public void parseBooleanTest() {

    Assertions.assertThat(Parsers.parseBoolean(null)).isNull();

    Assertions.assertThat(Parsers.parseBoolean("")).isFalse();
    Assertions.assertThat(Parsers.parseBoolean(" ")).isFalse();

    Assertions.assertThat(Parsers.parseBoolean("true")).isTrue();
    Assertions.assertThat(Parsers.parseBoolean("false")).isFalse();

    Assertions.assertThat(Parsers.parseBoolean("True")).isTrue();
    Assertions.assertThat(Parsers.parseBoolean("False")).isFalse();

    Assertions.assertThat(Parsers.parseBoolean("TRUE")).isTrue();
    Assertions.assertThat(Parsers.parseBoolean("FALSE")).isFalse();

    Assertions.assertThat(Parsers.parseBoolean("X")).isFalse();

    Assertions.assertThat(Parsers.parseBoolean(-1)).isFalse();
    Assertions.assertThat(Parsers.parseBoolean(0)).isFalse();
    Assertions.assertThat(Parsers.parseBoolean(1)).isTrue();
    Assertions.assertThat(Parsers.parseBoolean(2)).isTrue();

  }

}