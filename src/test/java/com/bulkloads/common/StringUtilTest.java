package com.bulkloads.common;

import static com.bulkloads.common.StringUtil.amountFormat;
import static com.bulkloads.common.StringUtil.intFormat;
import static com.bulkloads.common.StringUtil.numberFormat;
import static com.bulkloads.common.StringUtil.parseDouble;
import static com.bulkloads.common.StringUtil.parseFullName;
import static com.bulkloads.common.StringUtil.parseInteger;
import static com.bulkloads.common.StringUtil.removeNewLines;
import static com.bulkloads.common.StringUtil.sanitizeForBooleanModeSearch;
import static com.bulkloads.common.StringUtil.toSnakeCase;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import java.math.BigInteger;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.springframework.data.util.Pair;

class StringUtilTest {

  @ParameterizedTest
  @CsvSource({
      "123, 123",
      "abc, abc",
      "ABC, a_b_c",
      "camelCase, camel_case",
      "impossibleCase, impossible_case",
      "ImpossibleCase, impossible_case",
      "camelCase_camelCase, camel_case_camel_case"})
  void snakeCaseTests(final String input, final String expected) {
    assertThat(toSnakeCase(input)).isEqualTo(expected);
  }

  @Test
  void testSingleName() {
    assertThat(parseFullName("").getFirst()).isEmpty();
    assertThat(parseFullName("").getSecond()).isEmpty();

    assertThat(parseFullName("Prince").getFirst()).isEqualTo("Prince");
    assertThat(parseFullName("Prince").getSecond()).isEmpty();

    assertThat(parseFullName("The Prince").getFirst()).isEqualTo("The");
    assertThat(parseFullName("The Prince").getSecond()).isEqualTo("Prince");

    assertThat(parseFullName("Sigmud Jr. Freud").getFirst()).isEqualTo("Sigmud");
    assertThat(parseFullName("Sigmud Jr. Freud").getSecond()).isEqualTo("Jr. Freud");
  }

  @Test
  void testProperCase() {
    assertThat(StringUtil.properCase("")).isEmpty();
    assertThat(StringUtil.properCase("  ")).isEqualTo("  ");
    assertThat(StringUtil.properCase("john")).isEqualTo("John");
    assertThat(StringUtil.properCase("    john      ")).isEqualTo("John");
    assertThat(StringUtil.properCase("john blake")).isEqualTo("John Blake");
    assertThat(StringUtil.properCase("john blake smith")).isEqualTo("John Blake Smith");
    assertThat(StringUtil.properCase("a b c d e")).isEqualTo("A B C D E");
    assertThat(StringUtil.properCase("Trompone Records")).isEqualTo("Trompone Records");
    assertThat(StringUtil.properCase(" Trompone         Records        ")).isEqualTo("Trompone Records");
  }

  @Test
  void testParseInt() {
    assertThat(parseInteger("100")).isEqualTo(100);
    assertThat(parseInteger("1,000,000")).isEqualTo(1_000_000);

    assertThat(parseDouble("100")).isEqualTo(100.0);
    assertThat(parseDouble("1,000,000.323")).isEqualTo(1000000.323);
  }

  @Test
  void remoteNewLinesTest() {
    assertThat(removeNewLines("")).isEmpty();
    assertThat(removeNewLines(" ")).isEmpty();
    assertThat(removeNewLines("  ")).isEmpty();
    assertThat(removeNewLines("  \n ")).isEmpty();
    assertThat(removeNewLines("  \r ")).isEmpty();
    assertThat(removeNewLines("  \r\n ")).isEmpty();
    assertThat(removeNewLines("  \r \n ")).isEmpty();
    assertThat(removeNewLines("  \r\n \n\r \r")).isEmpty();

    assertThat(removeNewLines(" a \r\n b \n\r c\r d")).isEqualTo("abcd");

    String inp = """
        abc\s\s\s
        def\s
        ghej\s
        """;

    assertThat(removeNewLines(inp)).isEqualTo("abcdefghej");
  }

  @Test
  void numberFormatTests() {

    assertThat(numberFormat(0.0)).isEqualTo("0");
    assertThat(numberFormat(0.00)).isEqualTo("0");
    assertThat(numberFormat(0.000)).isEqualTo("0");

    assertThat(numberFormat(0.1)).isEqualTo("0.10");
    assertThat(numberFormat(0.10)).isEqualTo("0.10");
    assertThat(numberFormat(0.100)).isEqualTo("0.10");

    assertThat(numberFormat(0.01)).isEqualTo("0.01");
    assertThat(numberFormat(0.010)).isEqualTo("0.01");
    assertThat(numberFormat(0.0100)).isEqualTo("0.01");

    assertThat(numberFormat(1.0)).isEqualTo("1");
    assertThat(numberFormat(1.00)).isEqualTo("1");
    assertThat(numberFormat(1.000)).isEqualTo("1");

    assertThat(numberFormat(1.1)).isEqualTo("1.1");
    assertThat(numberFormat(1.10)).isEqualTo("1.1");
    assertThat(numberFormat(1.100)).isEqualTo("1.1");

    assertThat(numberFormat(1.01)).isEqualTo("1.01");
    assertThat(numberFormat(1.010)).isEqualTo("1.01");
    assertThat(numberFormat(1.0100)).isEqualTo("1.01");

    // do we need the rounding here or truncate?
    assertThat(numberFormat(1.123456789)).isEqualTo("1.12346");

    assertThat(numberFormat(1000.0)).isEqualTo("1,000");
    assertThat(numberFormat(1000000.0)).isEqualTo("1,000,000");

    assertThat(numberFormat(1000000.00100)).isEqualTo("1,000,000.001");
  }

  @Test
  void amountFormatTests() {

    assertThat(amountFormat(0.0)).isEqualTo("0.00");
    assertThat(amountFormat(0.00)).isEqualTo("0.00");
    assertThat(amountFormat(0.000)).isEqualTo("0.00");

    assertThat(amountFormat(0.1)).isEqualTo("0.10");
    assertThat(amountFormat(0.10)).isEqualTo("0.10");
    assertThat(amountFormat(0.100)).isEqualTo("0.10");

    assertThat(amountFormat(0.01)).isEqualTo("0.01");
    assertThat(amountFormat(0.010)).isEqualTo("0.01");
    assertThat(amountFormat(0.0100)).isEqualTo("0.01");

    assertThat(amountFormat(1.0)).isEqualTo("1.00");
    assertThat(amountFormat(1.00)).isEqualTo("1.00");
    assertThat(amountFormat(1.000)).isEqualTo("1.00");

    assertThat(amountFormat(1.1)).isEqualTo("1.10");
    assertThat(amountFormat(1.10)).isEqualTo("1.10");
    assertThat(amountFormat(1.100)).isEqualTo("1.10");

    assertThat(amountFormat(1.01)).isEqualTo("1.01");
    assertThat(amountFormat(1.010)).isEqualTo("1.01");
    assertThat(amountFormat(1.0100)).isEqualTo("1.01");

    assertThat(amountFormat(1000.0)).isEqualTo("1,000.00");
    assertThat(amountFormat(1000000.0)).isEqualTo("1,000,000.00");

    assertThat(amountFormat(1000000.00100)).isEqualTo("1,000,000.00");
  }

  @Test
  void integerFormatTests() {
    assertThat(intFormat(0)).isEqualTo("0");
    assertThat(intFormat(123456)).isEqualTo("123456");
  }

  @Test
  void sanitizeForBooleanModeSearchTest() {
    // Test null and empty inputs
    assertThat(sanitizeForBooleanModeSearch(null)).isNull();
    assertThat(sanitizeForBooleanModeSearch("")).isEmpty();
    assertThat(sanitizeForBooleanModeSearch("   ")).isEmpty();

    // Test single word inputs
    assertThat(sanitizeForBooleanModeSearch("test")).isEqualTo("+test*");
    assertThat(sanitizeForBooleanModeSearch("hello")).isEqualTo("+hello*");

    // Test multi-word inputs
    assertThat(sanitizeForBooleanModeSearch("hello world")).isEqualTo("+hello* +world*");
    assertThat(sanitizeForBooleanModeSearch("search term example")).isEqualTo("+search* +term* +example*");

    // Test inputs with extra spaces
    assertThat(sanitizeForBooleanModeSearch("  hello   world  ")).isEqualTo("+hello* +world*");

    // Test inputs with underscores
    assertThat(sanitizeForBooleanModeSearch("hello_world")).isEqualTo("+hello_world*");
    assertThat(sanitizeForBooleanModeSearch("hello_world_example")).isEqualTo("+hello_world_example*");

    // Test inputs with special characters
    assertThat(sanitizeForBooleanModeSearch("hello@world")).isEqualTo("+hello* +world*");
    assertThat(sanitizeForBooleanModeSearch("test+plus-minus")).isEqualTo("+test* +plus* +minus*");
    assertThat(sanitizeForBooleanModeSearch("(parentheses)")).isEqualTo("+parentheses*");
    assertThat(sanitizeForBooleanModeSearch("brackets[test]")).isEqualTo("+brackets* +test*");
    assertThat(sanitizeForBooleanModeSearch("curly{braces}")).isEqualTo("+curly* +braces*");

    // Test inputs with MySQL Boolean operators
    assertThat(sanitizeForBooleanModeSearch("+required -excluded")).isEqualTo("+required* +excluded*");
    assertThat(sanitizeForBooleanModeSearch("\"exact phrase\"")).isEqualTo("+exact* +phrase*");
    assertThat(sanitizeForBooleanModeSearch("wild*card~fuzzy")).isEqualTo("+wild* +card* +fuzzy*");

    // Test inputs with mixed special characters and spaces
    assertThat(sanitizeForBooleanModeSearch("<EMAIL>")).isEqualTo("+email* +example* +com*");
    assertThat(sanitizeForBooleanModeSearch("first-name last&name")).isEqualTo("+first* +name* +last* +name*");

    // Test inputs with numbers and alphanumeric combinations
    assertThat(sanitizeForBooleanModeSearch("user123")).isEqualTo("+user123*");
    assertThat(sanitizeForBooleanModeSearch("mc#1234")).isEqualTo("+mc* +1234*");

    // Test inputs that would be completely sanitized away
    assertThat(sanitizeForBooleanModeSearch("@#$%^&*()")).isEmpty();
    assertThat(sanitizeForBooleanModeSearch("+ - * ? ~ ( )")).isEmpty();

    // Test inputs with periods and common punctuation
    assertThat(sanitizeForBooleanModeSearch("example.com")).isEqualTo("+example* +com*");
    assertThat(sanitizeForBooleanModeSearch("Mr. Smith")).isEqualTo("+Mr* +Smith*");

    // Test inputs with apostrophes and quotes
    assertThat(sanitizeForBooleanModeSearch("John's book")).isEqualTo("+John* +s* +book*");
    assertThat(sanitizeForBooleanModeSearch("\"quoted text\"")).isEqualTo("+quoted* +text*");
  }

  @Test
  void testParseFullName_TwoWords() {
    Pair<String, String> result = StringUtil.parseFullName("John Doe");
    assertEquals("John", result.getFirst());
    assertEquals("Doe", result.getSecond());
  }

  @Test
  void testParseFullName_SingleWord() {
    Pair<String, String> result = StringUtil.parseFullName("John");
    assertEquals("John", result.getFirst());
    assertEquals("", result.getSecond());
  }

  @Test
  void testParseFullName_ExtraSpaces() {
    Pair<String, String> result = StringUtil.parseFullName("  John   Doe  ");
    assertEquals("John", result.getFirst());
    assertEquals("Doe", result.getSecond());
  }

  @Test
  void testBigIntegerToSqid() {
    // Zero
    assertThat(StringUtil.bigIntegerToSqid(BigInteger.ZERO)).hasSize(6);
    // One
    assertThat(StringUtil.bigIntegerToSqid(BigInteger.ONE)).hasSize(6);
    // Large values
    BigInteger big = new BigInteger("1234567890");
    String sqid = StringUtil.bigIntegerToSqid(big);
    assertThat(sqid).isNotEmpty();
    assertThat(StringUtil.sqidToBigInteger(sqid)).isEqualTo(big);
  }
}
