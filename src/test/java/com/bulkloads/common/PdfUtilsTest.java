package com.bulkloads.common;

import static com.bulkloads.common.PdfUtils.convertImageToPdf;
import static com.bulkloads.common.PdfUtils.mergePdfFiles;
import static com.bulkloads.common.Utils.getFileFromResources;
import static com.bulkloads.common.Utils.openFileWithDefaultApp;
import java.io.InputStream;
import java.nio.file.Path;
import java.util.List;
import org.apache.commons.io.FileUtils;
import org.intellij.lang.annotations.Language;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import lombok.SneakyThrows;

class PdfUtilsTest {

  @Disabled
  @Test
  @SneakyThrows
  public void htmlToPdfTest() {
    @Language("HTML")
    String content = """
        <!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
        <html style="max-width: 900px; margin: 0 auto;" lang="en">
        <head>
            <title>Sample Document Title</title>
            <style type="text/css">
                @page {
                    margin: 0.6in 0.6in 0.6in 0.6in;
                }
                body {
                    margin: 0;
                }
              </style>
        </head>
        <body>
          <p>Hello World!</p>
        </body>
        </html>
        """;
    InputStream pdfInputStream = PdfUtils.htmlToPdf(content);
    Path outputFile = Path.of("ee7db60a7cf909.pdf");
    FileUtils.copyInputStreamToFile(pdfInputStream, outputFile.toFile());
    openFileWithDefaultApp(outputFile);
  }

  @Disabled
  @Test
  @SneakyThrows
  public void mergePdfFilesTest() {

    List<Path> pdfFiles = List.of(
        getFileFromResources("test_files/pdf/1.pdf"),
        getFileFromResources("test_files/pdf/2.pdf"),
        getFileFromResources("test_files/pdf/3.pdf")
    );
    Path outputPath = Path.of("9a50c51dee50a328.pdf");
    mergePdfFiles(pdfFiles, outputPath);
    openFileWithDefaultApp(outputPath);
  }

  @Disabled
  @Test
  @SneakyThrows
  public void convertImageTest() {
    String path = "test_files/jpg/1.jpg";
    Path imageFile = getFileFromResources(path);
    Path outputPath = Path.of("9a50c51dee50a328.pdf");
    convertImageToPdf(imageFile, outputPath);
    openFileWithDefaultApp(outputPath);
  }

}
