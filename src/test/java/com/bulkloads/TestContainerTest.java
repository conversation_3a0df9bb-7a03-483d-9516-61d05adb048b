package com.bulkloads;

import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;
import org.testcontainers.containers.MariaDBContainer;
import org.testcontainers.junit.jupiter.Testcontainers;
import org.testcontainers.utility.DockerImageName;

@Testcontainers
public abstract class TestContainerTest {

  protected static final MariaDBContainer<?> DB_CONTAINER;

  static {
    DB_CONTAINER = new MariaDBContainer<>(DockerImageName.parse("mariadb:10.5.5"))
        .withUsername("root")
        .withPassword("root");

    DB_CONTAINER.start();
  }

  @DynamicPropertySource
  static void configureTestProperties(DynamicPropertyRegistry registry) {
    registry.add("spring.datasource.web.url", DB_CONTAINER::getJdbcUrl);
    registry.add("spring.datasource.web.username", DB_CONTAINER::getUsername);
    registry.add("spring.datasource.web.password", DB_CONTAINER::getPassword);
    registry.add("spring.datasource.web.driverClassName", () -> "org.mariadb.jdbc.Driver");
  }
}
