-- liquibase formatted sql

-- changeset system:1729142640066-1

insert into user_type (user_type_id, user_type)
values (20, 'Carrier'),
       (30, 'Broker'),
       (40, '<PERSON><PERSON>'),
       (50, 'Other'),
       (60, 'Pickup / Drop Facility'),
       (100, 'Admin');

insert into cities
(`name`, `city`, `state`, `zip`, `region`, `country`, `Latitude`, `Longitude`, `freq`)
values ('Mayo, FL 32066', 'Mayo', 'FL', '32066', 'South East', 'US', 30.054896, -83.175212, 15),
       ('Monroe, MI 48162', 'Monroe', 'MI', '48162', 'South East', 'US', 41.916433, -83.397941, 13),
       ('Baltimore, MD 21231', 'Baltimore', 'MD', '21231', 'South East', 'US', 39.283064, -76.595851, 536),
       ('Baton Rouge, LA 70809', 'Baton Rouge', 'LA', '70809', 'South', 'US', 30.438926, -91.106672, 278),
       ('Rockville, MD 20847', 'Rockville', 'MD', '20847', 'South East', 'US', 39.083744, -77.152963, 13),
       ('Manchester, NH', 'Manchester', 'NH', '', 'South East', 'US', 42.99556, -71.45528, 13),
       ('Osceola, AR', 'Osceola', 'AR', '', 'South', 'US', 35.705, -89.96944, 24),
       ('Bay City, MI 48708', 'Bay City', 'MI', '48708', 'South East', 'US', 43.575166, -83.884676, 33),
       ('Merced, CA 95341', 'Merced', 'CA', '95341', 'West', 'US', 37.295163, -120.480237, 22),
       ('San Antonio, TX 78234', 'San Antonio', 'TX', '78234', 'South', 'US', 29.459295, -98.436641, 857),
       ('Conway, AR', 'Conway', 'AR', '', 'South', 'US', 35.08861, -92.44194, 13),
       ('Alexandria, MN', 'Alexandria', 'MN', '', 'North', 'US', 45.88528, -95.37722, 75),
       ('Trenton, NJ 08666', 'Trenton', 'NJ', '08666', 'South East', 'US', 40.21687, -74.742992, 21),
       ('New York, NY 10018', 'New York', 'NY', '10018', 'South East', 'US', 40.75631, -73.993451, 54),
       ('La Salle, IL 61301', 'La Salle', 'IL', '61301', 'South East', 'US', 41.340052, -89.092039, 31),
       ('Springdale, AR 72762', 'Springdale', 'AR', '72762', 'South', 'US', 36.17369, -94.138049, 323),
       ('Milwaukee, WI 53218', 'Milwaukee', 'WI', '53218', 'North', 'US', 43.110153, -87.991144, 243),
       ('McCook, NE 69001', 'McCook', 'NE', '69001', 'North', 'US', 40.205005, -100.628431, 113),
       ('Ashburn, GA', 'Ashburn', 'GA', '', 'South East', 'US', 31.70583, -83.65333, 32),
       ('Spiritwood, ND', 'Spiritwood', 'ND', '', 'North', 'US', 46.93611, -98.49528, 11),
       ('Cooperton, OK', 'Cooperton', 'OK', '', 'South', 'US', 34.8675, -98.86639, 22),
       ('Ohiowa, NE', 'Ohiowa', 'NE', '', 'North', 'US', 40.41444, -97.4525, 11),
       ('Denver, CO 80299', 'Denver', 'CO', '80299', 'South', 'US', 39.713664, -104.904652, 1240),
       ('Buffalo, NY 14227', 'Buffalo', 'NY', '14227', 'South East', 'US', 42.901399, -78.752117, 192),
       ('Boston, MA 02120', 'Boston', 'MA', '02120', 'South East', 'US', 42.328486, -71.107668, 161),
       ('Griffin, GA 30224', 'Griffin', 'GA', '30224', 'South East', 'US', 33.246775, -84.264091, 44),
       ('Hoven, SD', 'Hoven', 'SD', '', 'North', 'US', 45.24361, -99.77667, 18),
       ('New Braunfels, TX 78133', 'New Braunfels', 'TX', '78133', 'South', 'US', 29.8781434, -98.2431747, 26),
       ('Appleton, WI 54911', 'Appleton', 'WI', '54911', 'North', 'US', 44.271848, -88.412898, 53),
       ('Richfield, UT', 'Richfield', 'UT', '', 'West', 'US', 38.7725, -112.08333, 11),
       ('Conroe, TX 77304', 'Conroe', 'TX', '77304', 'South', 'US', 30.324218, -95.482878, 34),
       ('Dallas, TX 75270', 'Dallas', 'TX', '75270', 'South', 'US', 32.785206, -96.773197, 1896),
       ('Philadelphia, PA 19149', 'Philadelphia', 'PA', '19149', 'South East', 'US', 40.037045, -75.064123, 204),
       ('Columbia, SC 29217', 'Columbia', 'SC', '29217', 'South East', 'US', 34.0090267, -81.03578569999999, 249),
       ('PHILA, PA 19111', 'PHILA', 'PA', '19111', 'South East', 'US', 40.064245, -75.07684, 16),
       ('Houston, TX 77007', 'Houston', 'TX', '77007', 'South', 'US', 29.764155, -95.412736, 2524),
       ('Corp Christi, TX 78401', 'Corp Christi', 'TX', '78401', 'South', 'US', 27.771706, -97.429322, 36),
       ('Middletown, OH 45044', 'Middletown', 'OH', '45044', 'South East', 'US', 39.490307, -84.38673, 44),
       ('Springfield, CO', 'Springfield', 'CO', '', 'South', 'US', 37.40833, -102.61389, 11),
       ('Eaton, OH', 'Eaton', 'OH', '', 'South East', 'US', 39.74389, -84.63667, 14),
       ('Fairless Hills, PA 19030', 'Fairless Hills', 'PA', '19030', 'South East', 'US', 40.161537, -74.865292, 36),
       ('COLORADO SPGS, CO 80938', 'COLORADO SPGS', 'CO', '80938', 'South', 'US', 38.909252, -104.673513, 16),
       ('Springfield, MA 01152', 'Springfield', 'MA', '01152', 'South East', 'US', 42.099644, -72.591513, 20),
       ('Tampa, FL 33618', 'Tampa', 'FL', '33618', 'South East', 'US', 28.073123, -82.481877, 499),
       ('Friendly, WV 26146', 'Friendly', 'WV', '26146', 'South East', 'US', 39.515546, -81.060397, 17),
       ('Adrian, MN 56110', 'Adrian', 'MN', '56110', 'North', 'US', 43.634966, -95.932794, 11),
       ('Pueblo, CO 81010', 'Pueblo', 'CO', '81010', 'South', 'US', 38.278993, -104.577529, 77),
       ('Estill Springs, TN', 'Estill Springs', 'TN', '', 'South East', 'US', 35.27056, -86.12806, 11),
       ('Sacramento, CA 95814', 'Sacramento', 'CA', '95814', 'West', 'US', 38.591754, -121.486541, 190),
       ('Montgomery, AL 36109', 'Montgomery', 'AL', '36109', 'South East', 'US', 32.391858, -86.221714, 331);

insert into states
(state_id, state, abbreviation, country, region, longitude, latitude, color_code)
values (1, 'Alabama', 'AL', 'US', 4, -86.660000000000000, 32.840000000000000, 'fefe80'),
       (3, 'Arizona', 'AZ', 'US', 1, -111.970000000000000, 34.530000000000000, 'd980fe'),
       (4, 'Arkansas', 'AR', 'US', 3, -92.550000000000000, 34.670000000000000, '9180fe'),
       (5, 'California', 'CA', 'US', 1, -120.230000000000000, 36.810000000000000, '80a6fe'),
       (6, 'Colorado', 'CO', 'US', 3, -105.730000000000000, 38.960000000000000, '86fbff'),
       (7, 'Connecticut', 'CT', 'US', 4, -72.550000000000000, 41.640000000000000, '86ffbf'),
       (8, 'Delaware', 'DE', 'US', 4, -75.490000000000000, 38.822000000000000, '86ff8c'),
       (9, 'Florida', 'FL', 'US', 4, -81.830000000000000, 28.380000000000000, 'bfff86'),
       (10, 'Georgia', 'GA', 'US', 4, -83.410000000000000, 32.730000000000000, 'd9ff86'),
       (12, 'Idaho', 'ID', 'US', 1, -114.430000000000000, 43.420000000000000, 'f2ff86'),
       (13, 'Illinois', 'IL', 'US', 4, -89.300000000000000, 40.010000000000000, 'ffea86'),
       (14, 'Indiana', 'IN', 'US', 4, -86.180000000000000, 40.040000000000000, 'ffd086'),
       (15, 'Iowa', 'IA', 'US', 2, -93.690000000000000, 42.010000000000000, 'ffb186'),
       (16, 'Kansas', 'KS', 'US', 3, -98.310000000000000, 38.510000000000000, 'ff8f86'),
       (17, 'Kentucky', 'KY', 'US', 4, -84.770000000000000, 37.440000000000000, 'f6d8fe'),
       (18, 'Louisiana', 'LA', 'US', 3, -92.640000000000000, 31.470000000000000, 'ebd8fe'),
       (19, 'Maine', 'ME', 'US', 4, -69.210000000000000, 45.240000000000000, 'e1d8fe'),
       (20, 'Maryland', 'MD', 'US', 4, -76.620000000000000, 39.310000000000000, 'd8d8fe'),
       (21, 'Massachusetts', 'MA', 'US', 4, -72.050000000000000, 42.370000000000000, 'd8e6fe'),
       (22, 'Michigan', 'MI', 'US', 4, -84.710000000000000, 43.100000000000000, 'd8f5fe'),
       (23, 'Minnesota', 'MN', 'US', 2, -94.750000000000000, 45.690000000000000, 'd8fef8'),
       (24, 'Mississippi', 'MS', 'US', 4, -89.740000000000000, 32.950000000000000, 'd8fee3'),
       (25, 'Missouri', 'MO', 'US', 3, -92.550000000000000, 38.600000000000000, 'ebfed8'),
       (26, 'Montana', 'MT', 'US', 2, -109.470000000000000, 47.100000000000000, 'fafed8'),
       (27, 'Nebraska', 'NE', 'US', 2, -99.890000000000000, 41.640000000000000, 'fef3d8'),
       (28, 'Nevada', 'NV', 'US', 1, -116.740000000000000, 39.620000000000000, 'fcdab1'),
       (29, 'New Hampshire', 'NH', 'US', 4, -71.720000000000000, 43.480000000000000, 'f2b1fc'),
       (30, 'New Jersey', 'NJ', 'US', 4, -74.630000000000000, 39.940000000000000, 'dbb1fc'),
       (31, 'New Mexico', 'NM', 'US', 3, -106.040000000000000, 34.600000000000000, 'bdb1fc'),
       (32, 'New York', 'NY', 'US', 4, -75.670000000000000, 43.070000000000000, 'b1bffc'),
       (33, 'North Carolina', 'NC', 'US', 4, -79.280000000000000, 35.680000000000000, 'b1cdfc'),
       (34, 'North Dakota', 'ND', 'US', 2, -100.500000000000000, 47.580000000000000, 'b1e1fc'),
       (35, 'Ohio', 'OH', 'US', 4, -82.840000000000000, 40.250000000000000, 'b1f1fc'),
       (36, 'Oklahoma', 'OK', 'US', 3, -97.120000000000000, 35.600000000000000, 'b1fce4'),
       (37, 'Oregon', 'OR', 'US', 1, -120.890000000000000, 43.990000000000000, 'b1fcca'),
       (41, 'Pennsylvania', 'PA', 'US', 4, -77.780000000000000, 40.980000000000000, 'bafcb1'),
       (42, 'Rhode Island', 'RI', 'US', 4, -71.370000000000000, 41.510000000000000, 'cdfcb1'),
       (43, 'South Carolina', 'SC', 'US', 4, -80.770000000000000, 33.850000000000000, 'ebfcb1'),
       (44, 'South Dakota', 'SD', 'US', 2, -100.420000000000000, 44.470000000000000, 'fcf8b1'),
       (45, 'Tennessee', 'TN', 'US', 4, -86.310000000000000, 35.890000000000000, 'beb5ef'),
       (46, 'Texas', 'TX', 'US', 3, -98.880000000000000, 31.240000000000000, 'b5c3ef'),
       (47, 'Utah', 'UT', 'US', 1, -111.670000000000000, 39.370000000000000, 'b5ddef'),
       (48, 'Vermont', 'VT', 'US', 4, -72.770000000000000, 44.070000000000000, 'b5efe7'),
       (49, 'Virginia', 'VA', 'US', 4, -78.530000000000000, 37.440000000000000, 'b5efc5'),
       (50, 'Washington', 'WA', 'US', 1, -120.540000000000000, 47.430000000000000, 'bcefb5'),
       (51, 'West Virginia', 'WV', 'US', 4, -80.840000000000000, 38.530000000000000, 'd6efb5'),
       (52, 'Wisconsin', 'WI', 'US', 2, -89.940000000000000, 44.610000000000000, 'ecefb5'),
       (53, 'Wyoming', 'WY', 'US', 2, -107.450000000000000, 43.070000000000000, 'efd4b5'),
       (70, 'Washington, Dc', 'DC', 'US', 4, NULL, NULL, 'FFFFFF'),
       (71, 'North', 'NORTH', 'US', 2, NULL, NULL, 'FFFFFF'),
       (72, 'South', 'SOUTH', 'US', 3, NULL, NULL, 'FFFFFF'),
       (73, 'East', 'EAST', 'US', 4, NULL, NULL, 'FFFFFF'),
       (74, 'West', 'WEST', 'US', 1, NULL, NULL, 'FFFFFF'),
       (75, 'Unknown', 'UNKNOWN', 'US', 0, NULL, NULL, 'FFFFFF'),
       (76, 'Alberta', 'AB', 'CA', 5, -112.980000000000000, 51.260000000000000, 'fc61c0'),
       (77, 'British Columbia', 'BC', 'CA', 5, -121.110000000000000, 51.120000000000000, '61cbfc'),
       (78, 'Manitoba', 'MB', 'CA', 5, -98.350000000000000, 51.430000000000000, '61fcef'),
       (79, 'Ontario', 'ON', 'CA', 5, -91.140000000000000, 50.090000000000000, '61fca6'),
       (80, 'Saskatchewan', 'SK', 'CA', 5, -105.910000000000000, 51.810000000000000, '9bfc61'),
       (81, 'Yukon Territory', 'YT', 'CA', 5, NULL, NULL, 'FFFFFF'),
       (105, 'New Brunswick', 'NB', 'CA', 5, NULL, NULL, 'FFFFFF'),
       (106, 'Newfoundland', 'NL', 'CA', 5, NULL, NULL, 'FFFFFF'),
       (107, 'Northwest Territories', 'NT', 'CA', 5, NULL, NULL, 'FFFFFF'),
       (108, 'Nova Scotia', 'NS', 'CA', 5, NULL, NULL, 'FFFFFF'),
       (109, 'Nunavut', 'NU', 'CA', 5, NULL, NULL, 'FFFFFF'),
       (110, 'Prince Edward Island', 'PE', 'CA', 5, NULL, NULL, 'FFFFFF'),
       (111, 'Quebec', 'QC', 'CA', 5, NULL, NULL, 'FFFFFF');

INSERT INTO activity_types (activity_type_id, activity_type, activity_metadata)
VALUES (1, 'Loads', '{ "load_ids": "string" }'),
       (2, 'Load Assignments', '{ "load_assignment_ids": "string" }'),
       (3, 'Load Bookings', '{ "load_assignment_ids": "string" }'),
       (4, 'Load Offers Sent', '{ "load_ids": "string", "offer_ids": "string" }'),
       (5, 'Load Offers Received', '{ "load_ids": "string", "offer_ids": "string" }'),
       (6, 'Load Invoices Sent', '{ "load_invoice_ids": "string", "invoice_file_urls": "string" }'),
       (7, 'Load Invoices Received', '{ "load_invoice_ids": "string","invoice_file_urls": "string" }'),
       (8, 'Contracts', '{ "contract_ids": "string" }');

INSERT INTO rate_types (rate_type, rate_type_text, rate_type_text_medium, rate_type_text_abbr, sort_order, is_weight,
                        rate_type_chs, rate_type_text_medium_plural)
VALUES ('100', 'per cwt (100 lbs)', '/cwt (100 lbs)', '/cwt', 8, 1, 'Cwt', ''),
       ('2000', 'per ton', '/ton', '/ton', 1, 1, 'Ton', ''),
       ('2204.62', 'per metric ton', '/metric ton', '/tonne', 2, 1, 'Tonne', ''),
       ('32', 'per bushel (32 lbs)', '/bushel (32 lbs)', '/bushel (32)', 7, 1, 'Bushel (32 lbs)', ''),
       ('48', 'per bushel (48 lbs)', '/bushel (48 lbs)', '/bushel (48)', 6, 1, 'Bushel (48 lbs)', ''),
       ('50', 'per bushel (50 lbs)', '/bushel (50 lbs)', '/bushel (50)', 5, 1, 'Bushel (50 lbs)', ''),
       ('56', 'per bushel (56 lbs)', '/bushel (56 lbs)', '/bushel (56)', 4, 1, 'Bushel (56 lbs)', ''),
       ('60', 'per bushel (60 lbs)', '/bushel (60 lbs)', '/bushel (60)', 3, 1, 'Bushel (60 lbs)', ''),
       ('flat', 'flat rate', ' flat rate', '', 11, 0, '', ''),
       ('hour', 'per hour', '/hour', '/h', 10, 0, 'per hour', ''),
       ('mile', 'per mile', '/mile', '/mi', 9, 0, 'per mile', '');
