digraph test_env {
    label = "Test Environment";
    
    graph [fontname = "Helvetica"; rankdir = LR; compound = true;];
    node [shape = box; fontname = "Helvetica";];
    edge [fontname = "Helvetica";];
    
    subgraph cluster_test_server {
        label = "test.bulkloads.com / 10.8.85.30 / s08194";
        style = filled;
        color = lightgrey;
        node [shape = component;];
        
        subgraph cluster_github_runner {
            label = "Github Runner";
            style = "filled";
            color = lightblue;
            pencolor = black;

            github_runner;
        }
        


        subgraph cluster_docker {
            label = "Docker";
            style = filled;
            color = lightblue;
            
            spring [label = "Spring Boot :9000";];
            rabbitmq [label = "RabbitMQ";];
        }
        
        subgraph cluster_pm2 {
            label = "PM2";
            style = "filled";
            
            color = lightsteelblue;
            nextjs [label = "NextJS :3000";];
        }
        
        nginx [label = "NGINX";URL = "";];
        static_files [label = "/var/www/tms";shape = "folder";];
    }
    
    subgraph cluster_cf_server {
        label = "coldfusion / 10.8.84.10 / s05502";
        style = filled;
        color = lightgrey;
        node [shape = component;];
        coldfusion [label = "ColdFusion";];
    }
    
    subgraph cluster_mysql_server {
        label = "10.8.84.10 / s08194";
        style = filled;
        color = lightgrey;
        node [shape = component;];
        mysql [label = "DB";shape = cylinder;];
    }
    
    
    spring -> rabbitmq [label = "";];
    spring -> mysql [label = ""; lhead = cluster_mysql_server;];
    coldfusion -> mysql [label = ""; lhead = cluster_mysql_server;];
    
    nginx -> spring [label = "";];
    nginx -> nextjs [label = "";];
    nginx -> static_files [label = "";];
    
    
    nginx -> coldfusion [style = dashed; color = "#ff6600"; arrowhead = vee; penwidth = 2; lhead = cluster_cf_server;];
}