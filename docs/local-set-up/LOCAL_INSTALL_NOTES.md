# Local setup 

The following guide is a step-by-step guide to setup a local development 
environment for the BulkLoads application. it consists of the following architecture:

- ColdFusion 2018 (Docker)
- MySQL 5.7 (Docker)
- BulkLoads API - Java/Spring (Running locally)
- <PERSON>inx (Docker)

The high level diagram of the setup is as follows:

# TBD

## Prerequisite steps

### 1. Clone the code repositories.

```bash
mkdir ~/bulkloads
cd ~/bulkloads
<NAME_EMAIL>:bulkloads/bulkloads-web.git #coldfusion codebase
<NAME_EMAIL>:bulkloads/bulkloads-tms.git #ui codebase
<NAME_EMAIL>:bulkloads/bulkloads-v2.git #spring/java codebase
```

### 2. Setup MySQL
```bash 
docker compose up -d bulkloads-db
```
and import the database schema shared to you by the team. Skip if you already have set up this step.

### 3. Setup ColdFusion:
```bash
docker compose up -d coldfusion
```

and follow the steps in the [setup-conf-guide.md](../../../bulkloads-web/docs/tutorials/setup-coldfusion-guide/setup-conf-guide.md) file in the `bulkloads-web/docs/tutorials/setup-coldfusion-guide/` directory of the `bulkloads-web` repository.

Additionally, you will need to add `-Dcom.sun.security.enableAIAcaIssuers=true` JVM argument under `Java and JVM` section.

Note that the mounted volume containing the certificates is located at `/opt/additional/` in the container.

Refer to the [official documentation](https://helpx.adobe.com/in/coldfusion/using/docker-images-coldfusion.html) for more information on setting up ColdFusion in Docker.

### 4. Start the BulkLoads-V2 api

From the `bulkloads-v2` repository-directory you can start the BulkLoads-V2 API.

A few options are to
* run the intellij shared configuration `WebApplication`
* run the gradle task `bootRun`
* start the jar file via `java -jar build/libs/web-*.jar`

Note: no need to run the `bulkloads-v2` in docker container.

### 5. Setup Nginx
Start the Nginx container via:
```bash
docker compose up -d nginx
```
### 6. Verify site is working

Link the domain `local.bulkloads.com` to `127.0.0.1` by adding the following line to your `/etc/hosts` (or windows equivalent `c:\windows\system32\drivers\etc\hosts`) file:
```bash
# Add the following line
127.0.0.1 local.bulkloads.com
````
Then, you should be able to access the site at [https://local.bulkloads.com](https://local.bulkloads.com) and the API at `/rest` i.e. [https://local.bulkloads.com/rest/washouts](https://local.bulkloads.com/rest/washouts).

## Running the end-to-end test suite

As a prerequisite, you need to follow the steps in the [README.md](../../../bulkloads-tms/README.md) file in the root directory of the `bulkloads-tms` repository.

Once this is done you can run the end-to-end test suite by running the following command in the `bulkloads-tms` repository:

for specific tests:

```bash
yarn test forum_simple
```

or whole suite:
```bash
yarn test
```
