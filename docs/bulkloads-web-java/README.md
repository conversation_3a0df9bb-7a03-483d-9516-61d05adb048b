# Instructions on JAVA Web Application Deployment with Github Actions

To have the java application deployed on `test`, `staging` and `production` environments, 
we need to have the following steps in place:

## Install Java 
Java must be installed for the app to start
in cmd: java --version
Download v19
https://www.oracle.com/java/technologies/javase/jdk19-archive-downloads.html

## Install curl
Download & install curl and add its /bin folder to the path
https://curl.se/windows/
Unzip into C:\Program Files\curl-8.4.0_7-win64-mingw
Add to System PATH
C:\Program Files\curl-8.4.0_7-win64-mingw\bin

## Step: Create a service on the target machine using `winsw`

- We are going to user an existing administrator user like `blnadmin`
- Create the directory `C:\bulkloads-web-java` and grant permissions to the newly created account
- Copy the contents of the `doc/bulkloads-web-java` from the repository to `C:\bulkloads-web-java` on the target machine

We need the files:

- `bulkloads-web-java.exe`
- `bulkloads-web-java.xml`
- `application.yml`

Configure the database user's username & password in the `application.yml` file.

Create the java jar once and copy into dir. From the command line, go to the project and:
./gradlew -x test build

Find the jar in [java project]/build/libs/web-0.0.1.jar and copy it to C:\bulkloads-web-java

Rename the jar to:
web-0.0.1.jar -> bulkloads-web-java.jar

Open an command line terminal and install the service:

```
cd /d C:\bulkloads-web-java && bulkloads-web-java.exe install
```


Test the service:
Go to Services, start the Bulkloads-web (java) service
Go to http://localhost:9000/actuator/health

( Test the service from the command line:
    Test with `psexec` that the user can start the service: 

    Start a prompt with `psexec -i -u "DOMAIN\USERNAME" cmd.exe` and then execute

    ```
    cd /d C:\bulkloads-web-java && bulkloads-web-java.exe start
    ```
)

## Create a github runner on the target machine
https://github.com/bulkloads/bulkloads-v2/settings/actions/runners
Click New self-hosted runner

# Execute from PowerShell as Admin
# create a folder under the drive root
$ cd \; mkdir actions-runner; cd actions-runner
# Download the latest runner package
$ Invoke-WebRequest -Uri https://github.com/actions/runner/releases/download/v2.311.0/actions-runner-win-x64-2.311.0.zip -OutFile actions-runner-win-x64-2.311.0.zip
# Optional: Validate the hash
$ if((Get-FileHash -Path actions-runner-win-x64-2.311.0.zip -Algorithm SHA256).Hash.ToUpper() -ne 'e629628ce25c1a7032d845f12dfe3dced630ca13a878b037dde77f5683b039dd'.ToUpper()){ throw 'Computed checksum did not match' }
# Extract the installer
$ Add-Type -AssemblyName System.IO.Compression.FileSystem ; [System.IO.Compression.ZipFile]::ExtractToDirectory("$PWD/actions-runner-win-x64-2.311.0.zip", "$PWD")
# Configure
# Create the runner and start the configuration experience
$ ./config.cmd --url https://github.com/bulkloads/bulkloads-v2 --token AMWQFAJAIFXEPZOTTJNGHOTFIIQWE

Enter the name of the runner group to add this runner to: [press Enter for Default] [Enter]

Enter the name of runner: [press Enter for S05502] [Enter]

This runner will have the following labels: 'self-hosted', 'Windows', 'X64'
Enter any additional labels (ex. label-1,label-2): [press Enter to skip] test-server

√ Runner successfully added
√ Runner connection is good

# Runner settings

Enter name of work folder: [press Enter for _work] [Enter]

√ Settings Saved.

Would you like to run the runner as service? (Y/N) [press Enter for N] y
User account to use for the service [press Enter for NT AUTHORITY\NETWORK SERVICE] blnadmin
Password for the account S05502\blnadmin **************************
Granting file permissions to 'S05502\blnadmin'.
Service actions.runner.bulkloads-bulkloads-v2.S05502 successfully installed
Service actions.runner.bulkloads-bulkloads-v2.S05502 successfully set recovery option
Service actions.runner.bulkloads-bulkloads-v2.S05502 successfully set to delayed auto start
Service actions.runner.bulkloads-bulkloads-v2.S05502 successfully configured
Waiting for service to start...
Service actions.runner.bulkloads-bulkloads-v2.S05502 started successfully


# Configure the github action runner workflow in /.github/workflows/deploy-test.yml
- Add the runner label `test-server` to the runner





