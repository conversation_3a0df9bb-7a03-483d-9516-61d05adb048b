{"swagger": "2.0", "info": {"version": "v1", "title": "My Carrier Packets Api"}, "host": "api.mycarrierpackets.com", "schemes": ["https"], "paths": {"/api/v1/Carrier/PreviewCarrier": {"post": {"tags": ["CarrierController"], "summary": "Preview carrier", "description": "Remarks", "operationId": "CarrierController_PreviewCarrier", "consumes": [], "produces": ["application/json", "text/json", "application/xml", "text/xml"], "parameters": [{"name": "DOTNumber", "in": "query", "description": "DOT number of the carrier. For example: 12345", "required": false, "type": "integer", "format": "int32"}, {"name": "docketNumber", "in": "query", "description": "Docket number of the carrier. Example: MC12345", "required": false, "type": "string"}, {"name": "Authorization", "in": "header", "description": "bearer access_token", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/MyCarrierPacketsBusiness.Services.FMCSACarrierServices.Data.PreviewCarrier"}}}}}}, "/api/v1/Carrier/GetCarrierData": {"post": {"tags": ["CarrierController"], "summary": "Get customer's packet with FMCSA data.", "description": "Remarks", "operationId": "CarrierController_GetCarrierData", "consumes": [], "produces": ["application/json", "text/json", "application/xml", "text/xml"], "parameters": [{"name": "DOTNumber", "in": "query", "description": "DOT number of the carrier. Example: 12345", "required": false, "type": "integer", "format": "int32"}, {"name": "DocketNumber", "in": "query", "description": "Docket number of the carrier. Example: MC12345", "required": false, "type": "string"}, {"name": "Authorization", "in": "header", "description": "bearer access_token", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CarrierDto"}}}}}, "/api/v1/Carrier/GetCarrierContacts": {"post": {"tags": ["CarrierController"], "summary": "Carrier Contacts", "description": "Remarks", "operationId": "CarrierController_GetCarrierContacts", "consumes": [], "produces": ["application/json", "text/json", "application/xml", "text/xml"], "parameters": [{"name": "DOTNumber", "in": "query", "description": "DOT number of the carrier. For example: 12345", "required": false, "type": "integer", "format": "int32"}, {"name": "docketNumber", "in": "query", "description": "Docket number of the carrier. Example: MC12345", "required": false, "type": "string"}, {"name": "Authorization", "in": "header", "description": "bearer access_token", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/MyCarrierPacketsBusiness.Services.FMCSACarrierServices.Data.GetCarrierContactsResponse"}}}}}}, "/api/v1/Carrier/GetMonitoredCarrierContactsData": {"post": {"tags": ["CarrierController"], "summary": "Monitored Carrier Contacts Data", "operationId": "CarrierController_GetMonitoredCarrierContactsData", "consumes": [], "produces": ["application/json", "text/json", "application/xml", "text/xml"], "parameters": [{"name": "pageNumber", "in": "query", "description": "The current page number.", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSize", "in": "query", "description": "The number of carriers returned per page. The recommended and default value is 250. The max is 500.", "required": false, "type": "integer", "format": "int32"}, {"name": "Authorization", "in": "header", "description": "bearer access_token", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/MonitoredCarriersContactsDataDto"}}}}}, "/api/v1/Carrier/GetDocument": {"post": {"tags": ["CarrierController"], "summary": "Download the carrier agreement, W9 form, W8 ben form, W8 bene form, company agreement and insurance certificates.", "description": "Remarks", "operationId": "CarrierController_GetDocument", "consumes": [], "produces": ["application/json", "text/json"], "parameters": [{"name": "name", "in": "query", "description": "The name of the document. Example: company-agreement/12/f9f10ed2-799a-4521-a09b-19fb088e16c2", "required": true, "type": "string"}, {"name": "Authorization", "in": "header", "description": "bearer access_token", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "object"}}}}}, "/api/v1/Carrier/EmailPacketInvitation": {"post": {"tags": ["CarrierController"], "summary": "Email out the packet's invitation", "description": "Remarks", "operationId": "CarrierController_EmailPacketInvitation", "consumes": [], "produces": ["application/json", "text/json"], "parameters": [{"name": "carrierEmail", "in": "query", "description": "Email of the carrier. Example: <EMAIL>", "required": true, "type": "string"}, {"name": "dotNumber", "in": "query", "description": "DOT number of the carrier. Example: 12345", "required": false, "type": "integer", "format": "int32"}, {"name": "docketNumber", "in": "query", "description": "Docket number of the carrier. Example: MC12345", "required": false, "type": "string"}, {"name": "userName", "in": "query", "description": "Username of the customer. Optional parameter. Example: abc", "required": false, "type": "string"}, {"name": "Authorization", "in": "header", "description": "bearer access_token", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "object"}}}}}, "/api/v1/Carrier/RequestUserVerification": {"post": {"tags": ["CarrierController"], "summary": "Request a user verification", "description": "Remarks", "operationId": "CarrierController_RequestUserVerification", "consumes": [], "produces": ["application/json", "text/json"], "parameters": [{"name": "userVerificationEmail", "in": "query", "description": "Email of the user. Example: <EMAIL>", "required": true, "type": "string"}, {"name": "dotNumber", "in": "query", "description": "DOT number of the carrier. Example: 12345", "required": false, "type": "integer", "format": "int32"}, {"name": "docketNumber", "in": "query", "description": "Docket number of the carrier. Example: MC12345", "required": false, "type": "string"}, {"name": "userName", "in": "query", "description": "Username of the customer. Optional parameter. Example: abc", "required": false, "type": "string"}, {"name": "Authorization", "in": "header", "description": "bearer access_token", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "object"}}}}}, "/api/v1/Carrier/RequestVINVerification": {"post": {"tags": ["CarrierController"], "summary": "Request a VIN verification", "description": "Remarks", "operationId": "CarrierController_RequestVINVerification", "consumes": [], "produces": ["application/json", "text/json"], "parameters": [{"name": "deliveryOption", "in": "query", "description": "Method for sending the request (1 or 2). 1 = Email, 2 = PhoneNumber", "required": true, "type": "integer", "format": "int32", "enum": [1, 2]}, {"name": "vinVerificationEmail", "in": "query", "description": "Email to send the request to. Example: <EMAIL>", "required": false, "type": "string"}, {"name": "vinVerificationPhoneNumber", "in": "query", "description": "Phone number to send the request to", "required": false, "type": "string"}, {"name": "dotNumber", "in": "query", "description": "DOT number of the carrier. Example: 12345", "required": false, "type": "integer", "format": "int32"}, {"name": "docketNumber", "in": "query", "description": "Docket number of the carrier. Example: MC12345", "required": false, "type": "string"}, {"name": "userName", "in": "query", "description": "Username of the customer. Optional parameter. Example: abc", "required": false, "type": "string"}, {"name": "Authorization", "in": "header", "description": "bearer access_token", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "object"}}}}}, "/api/v1/Carrier/CompletedPackets": {"post": {"tags": ["CarrierController"], "summary": "Get the list of all complete packets in some time period.", "description": "Remarks", "operationId": "CarrierController_CompletedPackets", "consumes": [], "produces": ["application/json", "text/json", "application/xml", "text/xml"], "parameters": [{"name": "fromDate", "in": "query", "description": "From Date. Example: 2021-07-01T01:00:00", "required": true, "type": "string", "format": "date-time"}, {"name": "toDate", "in": "query", "description": "To Date. Required parameter. Example: 2021-07-01T14:00:00", "required": true, "type": "string", "format": "date-time"}, {"name": "Authorization", "in": "header", "description": "bearer access_token", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/CompletedPacketDto"}}}}}}, "/api/v1/Carrier/CarriersChanges": {"post": {"tags": ["CarrierController"], "summary": "Get the list of all changes in some time period.", "description": "If pagination parameters are not supplied, all carrier changes are returned. Otherwise, the number of carrier changes returned is limited to the pageSize.\r\nIf paging is requested, the response header will contain an \"X-Pagination\" item containing the details of the paged response.", "operationId": "CarrierController_CarriersChanges", "consumes": [], "produces": ["application/json", "text/json", "application/xml", "text/xml"], "parameters": [{"name": "fromDate", "in": "query", "description": "From Date. Required parameter. Example: 2021-07-01T01:00:00", "required": true, "type": "string", "format": "date-time"}, {"name": "toDate", "in": "query", "description": "To Date. Required parameter. Example: 2021-07-01T23:00:00", "required": true, "type": "string", "format": "date-time"}, {"name": "pageNumber", "in": "query", "description": "The current page number.", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSize", "in": "query", "description": "The number of carriers returned per page. The recommended and default value is 250. The max is 500.", "required": false, "type": "integer", "format": "int32"}, {"name": "Authorization", "in": "header", "description": "bearer access_token", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CarrierChangesResultDto"}, "headers": {"X-Pagination": {"description": "Pagination data formatted as {\"pageNumber\":1,\"pageSize\":250,\"totalPages\":10,\"totalCount\":2500}", "type": "string"}}}}}}, "/api/v1/Carrier/RequestMonitoring": {"post": {"tags": ["CarrierController"], "summary": "Request monitoring of a carrier", "description": "Remarks", "operationId": "CarrierController_RequestMonitoring", "consumes": [], "produces": ["application/json", "text/json", "application/xml", "text/xml"], "parameters": [{"name": "DOTNumber", "in": "query", "description": "DOT Number", "required": false, "type": "integer", "format": "int32"}, {"name": "DocketNumber", "in": "query", "description": "Docket number or MC Number", "required": false, "type": "string"}, {"name": "IntrastateNumber", "in": "query", "description": "Intrastate Number", "required": false, "type": "string"}, {"name": "Authorization", "in": "header", "description": "bearer access_token", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/RequestMonitoringOutput"}}}}}, "/api/v1/Carrier/CancelMonitoring": {"post": {"tags": ["CarrierController"], "summary": "Cancel monitoring of a carrier", "description": "Remarks", "operationId": "CarrierController_CancelMonitoring", "consumes": [], "produces": ["application/json", "text/json", "application/xml", "text/xml"], "parameters": [{"name": "DOTNumber", "in": "query", "description": "DOT Number", "required": false, "type": "integer", "format": "int32"}, {"name": "DocketNumber", "in": "query", "description": "Docket number or MC Number", "required": false, "type": "string"}, {"name": "IntrastateNumber", "in": "query", "description": "Intrastate Number", "required": false, "type": "string"}, {"name": "Authorization", "in": "header", "description": "bearer access_token", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CancelMonitoringOutput"}}}}}, "/api/v1/Carrier/BlockCarrier": {"post": {"tags": ["CarrierController"], "summary": "Block a carrier", "description": "Remarks", "operationId": "CarrierController_BlockCarrier", "consumes": [], "produces": ["application/json", "text/json", "application/xml", "text/xml"], "parameters": [{"name": "DOTNumber", "in": "query", "description": "DOT Number", "required": false, "type": "integer", "format": "int32"}, {"name": "DocketNumber", "in": "query", "description": "Docket number or MC Number", "required": false, "type": "string"}, {"name": "IntrastateNumber", "in": "query", "description": "Intrastate Number", "required": false, "type": "string"}, {"name": "Authorization", "in": "header", "description": "bearer access_token", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/BlockCarrierOutput"}}}}}, "/api/v1/Carrier/UnblockCarrier": {"post": {"tags": ["CarrierController"], "summary": "Unblock a carrier", "description": "Remarks", "operationId": "CarrierController_UnblockCarrier", "consumes": [], "produces": ["application/json", "text/json", "application/xml", "text/xml"], "parameters": [{"name": "DOTNumber", "in": "query", "description": "DOT Number", "required": false, "type": "integer", "format": "int32"}, {"name": "DocketNumber", "in": "query", "description": "Docket number or MC Number", "required": false, "type": "string"}, {"name": "IntrastateNumber", "in": "query", "description": "Intrastate Number", "required": false, "type": "string"}, {"name": "Authorization", "in": "header", "description": "bearer access_token", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/UnblockCarrierOutput"}}}}}, "/api/v1/Carrier/MonitoredCarriers": {"post": {"tags": ["CarrierController"], "summary": "Get the list of all monitored carriers.", "description": "If pagination parameters are not supplied, all monitored carriers are returned. Otherwise, the number of monitored carriers returned is limited to the pageSize.\r\nIf paging is requested, the response header will contain an \"X-Pagination\" item containing the details of the paged response.", "operationId": "CarrierController_MonitoredCarriers", "consumes": [], "produces": ["application/json", "text/json", "application/xml", "text/xml"], "parameters": [{"name": "pageNumber", "in": "query", "description": "The current page number.", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSize", "in": "query", "description": "The number of carriers returned per page. The recommended and default value is 2500. The max is 5000.", "required": false, "type": "integer", "format": "int32"}, {"name": "Authorization", "in": "header", "description": "bearer access_token", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/MonitoredCarrierDto"}}, "headers": {"X-Pagination": {"description": "Pagination data formatted as {\"pageNumber\":1,\"pageSize\":2500,\"totalPages\":10,\"totalCount\":25000}", "type": "string"}}}}}}, "/api/v1/Carrier/MonitoredCarrierData": {"post": {"tags": ["CarrierController"], "summary": "Monitored Carrier Data", "operationId": "CarrierController_MonitoredCarrierData", "consumes": [], "produces": ["application/json", "text/json", "application/xml", "text/xml"], "parameters": [{"name": "pageNumber", "in": "query", "description": "The current page number.", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSize", "in": "query", "description": "The number of carriers returned per page. The recommended and default value is 250. The max is 500.", "required": false, "type": "integer", "format": "int32"}, {"name": "Authorization", "in": "header", "description": "bearer access_token", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/MonitoredCarrierDataDto"}}}}}, "/api/v1/Carrier/BlockedCarriers": {"post": {"tags": ["CarrierController"], "summary": "Get the list of all blocked carriers.", "description": "If pagination parameters are not supplied, all blocked carriers are returned. Otherwise, the number of blocked carriers returned is limited to the pageSize.\r\nIf paging is requested, the response header will contain an \"X-Pagination\" item containing the details of the paged response.", "operationId": "CarrierController_BlockedCarriers", "consumes": [], "produces": ["application/json", "text/json", "application/xml", "text/xml"], "parameters": [{"name": "pageNumber", "in": "query", "description": "The current page number.", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSize", "in": "query", "description": "The number of carriers returned per page. The recommended and default value is 2500. The max is 5000.", "required": false, "type": "integer", "format": "int32"}, {"name": "Authorization", "in": "header", "description": "bearer access_token", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/CustomerBlockedCarrierDto"}}, "headers": {"X-Pagination": {"description": "Pagination data formatted as {\"pageNumber\":1,\"pageSize\":2500,\"totalPages\":10,\"totalCount\":25000}", "type": "string"}}}}}}}, "definitions": {"MyCarrierPacketsBusiness.Services.FMCSACarrierServices.Data.PreviewCarrier": {"type": "object", "properties": {"CarrierID": {"format": "int32", "type": "integer"}, "DotNumber": {"format": "int32", "type": "integer"}, "DocketNumber": {"type": "string"}, "CompanyName": {"type": "string"}, "DBAName": {"type": "string"}, "Street": {"type": "string"}, "City": {"type": "string"}, "State": {"type": "string"}, "ZipCode": {"type": "string"}, "Country": {"type": "string"}, "Phone": {"type": "string"}, "Status": {"type": "string"}, "InProcessState": {"type": "string"}, "PossibleFraud": {"type": "string"}, "DoubleBrokering": {"type": "string"}, "FraudCallNumber": {"type": "string"}, "HasSaferWatchKey": {"type": "boolean"}, "WatchdogReports": {"type": "string"}, "OnCurrentCustomerAgreement": {"type": "boolean"}, "CarrierRating": {"$ref": "#/definitions/MyCarrierPacketsBusiness.Carriers.CarrierRating"}, "RiskAssessment": {"$ref": "#/definitions/MyCarrierPacketsBusiness.Services.FMCSACarrierServices.Data.RiskAssessment"}, "RiskAssessmentDetails": {"$ref": "#/definitions/MyCarrierPacketsBusiness.Services.FMCSACarrierServices.Data.RiskAssessmentDetails"}, "CertData": {"$ref": "#/definitions/MyCarrierPacketsBusiness.Services.FMCSACarrierServices.Data.CertData"}, "Emails": {"type": "array", "items": {"$ref": "#/definitions/MyCarrierPacketsBusiness.Services.FMCSACarrierServices.Data.CarrierEmail"}}, "Source": {"format": "int32", "enum": [0, 1, 2, 3], "type": "integer"}, "IsIntrastateCarrier": {"type": "boolean"}, "IsMonitored": {"type": "boolean"}, "IsBlocked": {"type": "boolean"}}}, "MyCarrierPacketsBusiness.Carriers.CarrierRating": {"type": "object", "properties": {"CarrierID": {"format": "int32", "type": "integer"}, "CustomerID": {"format": "int32", "type": "integer"}, "CustomerRating": {"format": "int32", "type": "integer"}, "RatingSum": {"format": "int32", "type": "integer"}, "TotalRatings": {"format": "int32", "type": "integer"}, "LowRatings": {"format": "int32", "type": "integer"}, "TotalRatingPercent": {"format": "int32", "type": "integer", "readOnly": true}, "CustomerRatingPercent": {"format": "int32", "type": "integer", "readOnly": true}, "RatingValue": {"format": "double", "type": "number", "readOnly": true}, "RatingValueText": {"type": "string", "readOnly": true}, "AvgRatingText": {"type": "string", "readOnly": true}, "AvgRatingBasisText": {"type": "string", "readOnly": true}, "AvgRatingTextPlusRatingBasisText": {"type": "string", "readOnly": true}, "CustomerRatingText": {"type": "string", "readOnly": true}, "HasCompletedPacket": {"type": "boolean"}}}, "MyCarrierPacketsBusiness.Services.FMCSACarrierServices.Data.RiskAssessment": {"type": "object", "properties": {"Overall": {"type": "string"}, "Authority": {"type": "string"}, "Insurance": {"type": "string"}, "Safety": {"type": "string"}, "Operation": {"type": "string"}, "Other": {"type": "string"}}}, "MyCarrierPacketsBusiness.Services.FMCSACarrierServices.Data.RiskAssessmentDetails": {"type": "object", "properties": {"IsIntrastateCarrier": {"type": "boolean"}, "TotalPoints": {"format": "int32", "type": "integer"}, "OverallRating": {"type": "string"}, "Authority": {"$ref": "#/definitions/MyCarrierPacketsBusiness.Services.FMCSACarrierServices.Data.RiskAssessmentDetail"}, "Insurance": {"$ref": "#/definitions/MyCarrierPacketsBusiness.Services.FMCSACarrierServices.Data.RiskAssessmentDetail"}, "Safety": {"$ref": "#/definitions/MyCarrierPacketsBusiness.Services.FMCSACarrierServices.Data.RiskAssessmentDetail"}, "Operation": {"$ref": "#/definitions/MyCarrierPacketsBusiness.Services.FMCSACarrierServices.Data.RiskAssessmentDetail"}, "Other": {"$ref": "#/definitions/MyCarrierPacketsBusiness.Services.FMCSACarrierServices.Data.RiskAssessmentDetail"}}}, "MyCarrierPacketsBusiness.Services.FMCSACarrierServices.Data.CertData": {"type": "object", "properties": {"Status": {"type": "string"}, "Certificates": {"type": "array", "items": {"$ref": "#/definitions/MyCarrierPacketsBusiness.Services.FMCSACarrierServices.Data.Certificate"}}}}, "MyCarrierPacketsBusiness.Services.FMCSACarrierServices.Data.CarrierEmail": {"type": "object", "properties": {"EmailType": {"format": "int32", "enum": [1, 2, 3], "type": "integer"}, "Description": {"type": "string"}, "Email": {"type": "string"}}}, "MyCarrierPacketsBusiness.Services.FMCSACarrierServices.Data.RiskAssessmentDetail": {"type": "object", "properties": {"TotalPoints": {"format": "int32", "type": "integer"}, "OverallRating": {"type": "string"}, "Infractions": {"type": "array", "items": {"$ref": "#/definitions/MyCarrierPacketsBusiness.Services.FMCSACarrierServices.Data.RiskAssessmentInfraction"}}}}, "MyCarrierPacketsBusiness.Services.FMCSACarrierServices.Data.Certificate": {"type": "object", "properties": {"CertificateID": {"type": "string"}, "ProducerName": {"type": "string"}, "ProducerAddress": {"type": "string"}, "ProducerCity": {"type": "string"}, "ProducerState": {"type": "string"}, "ProducerZip": {"type": "string"}, "ProducerPhone": {"type": "string"}, "ProducerFax": {"type": "string"}, "ProducerEmail": {"type": "string"}, "PaidFor": {"type": "string"}, "Coverages": {"type": "array", "items": {"$ref": "#/definitions/MyCarrierPacketsBusiness.Services.FMCSACarrierServices.Data.Coverage"}}}}, "MyCarrierPacketsBusiness.Services.FMCSACarrierServices.Data.RiskAssessmentInfraction": {"type": "object", "properties": {"Points": {"format": "int32", "type": "integer"}, "RiskLevel": {"type": "string"}, "RuleText": {"type": "string"}, "RuleOutput": {"type": "string"}}}, "MyCarrierPacketsBusiness.Services.FMCSACarrierServices.Data.Coverage": {"type": "object", "properties": {"InsurerName": {"type": "string"}, "Type": {"type": "string"}, "PolicyNumber": {"type": "string"}, "ExpirationDate": {"type": "string"}, "CoverageLimit": {"type": "string"}, "Deductible": {"type": "string"}, "ReferBreakdown": {"type": "string"}, "ReferBreakDeduct": {"type": "string"}, "CancellationDate": {"type": "string"}}}, "CarrierDto": {"type": "object", "properties": {"DOTNumber": {"format": "int32", "type": "integer"}, "LegalName": {"type": "string"}, "DBAName": {"type": "string"}, "Address1": {"type": "string"}, "Address2": {"type": "string"}, "City": {"type": "string"}, "Zipcode": {"type": "string"}, "State": {"type": "string"}, "Country": {"type": "string"}, "CellPhone": {"type": "string"}, "Phone": {"type": "string"}, "Fax": {"type": "string"}, "FreePhone": {"type": "string"}, "EmergencyPhone": {"type": "string"}, "Email": {"type": "string"}, "FraudIdentityTheftStatus": {"type": "string"}, "MCNumber": {"type": "string"}, "SCAC": {"type": "string"}, "MailingAddress1": {"type": "string"}, "MailingAddress2": {"type": "string"}, "MailingCity": {"type": "string"}, "MailingState": {"type": "string"}, "MailingZipcode": {"type": "string"}, "MailingCountry": {"type": "string"}, "AfterHrsWkDaySupportName": {"type": "string"}, "AfterHrsWkDaySupportPhone": {"type": "string"}, "AfterHrsWkDaySupportFax": {"type": "string"}, "AfterHrsWkDaySupportFrom": {"type": "string"}, "AfterHrsWkDaySupportTo": {"type": "string"}, "AfterHrsWkEndSupportName": {"type": "string"}, "AfterHrsWkEndSupportPhone": {"type": "string"}, "AfterHrsWkEndSupportFax": {"type": "string"}, "AfterHrsWkEndSupportFrom": {"type": "string"}, "AfterHrsWkEndSupportTo": {"type": "string"}, "Website": {"type": "string"}, "OperationManagerName": {"type": "string"}, "OnlineAccessToAvailableLoads": {"type": "boolean"}, "AvailableLoadsEmail": {"type": "string"}, "DriverLogsSafeyDeptManagerName": {"type": "string"}, "DriverLogsSafeyDeptManagerPhone": {"type": "string"}, "Dispatchers": {"type": "string"}, "ClaimsContactName": {"type": "string"}, "ClaimsContactPhone": {"type": "string"}, "ClaimsContactEmail": {"type": "string"}, "DispatchServiceUsed": {"type": "boolean"}, "DispatchServiceName": {"type": "string"}, "DispatchServicePhone": {"type": "string"}, "BrokerOutExtraFreight": {"type": "boolean"}, "References1": {"type": "string"}, "References2": {"type": "string"}, "References3": {"type": "string"}, "DriversTrackedBy": {"type": "string"}, "AccessOnlineGPSTracking": {"type": "boolean"}, "DriversTrackedByOtherMethod": {"type": "string"}, "CreatedDateTime": {"format": "date-time", "type": "string"}, "ModifiedDateTime": {"format": "date-time", "type": "string"}, "CarrierCustomerAgreements": {"type": "array", "items": {"$ref": "#/definitions/CarrierCustomerAgreementDto"}}, "CarrierCustomerPacketStatuses": {"type": "array", "items": {"$ref": "#/definitions/CarrierCustomerPacketStatusDto"}}, "CarrierCargoHauled": {"$ref": "#/definitions/CarrierCargoHauledDto"}, "CarrierCompanyClassification": {"$ref": "#/definitions/CarrierCompanyClassificationDto"}, "CarrierDrivers": {"type": "array", "items": {"$ref": "#/definitions/CarrierDriverDto"}}, "CarrierDispatchers": {"type": "array", "items": {"$ref": "#/definitions/CarrierDispatcherDto"}}, "CarrierLane": {"$ref": "#/definitions/CarrierLaneDto"}, "CarrierOperationalDetail": {"$ref": "#/definitions/CarrierOperationalDetailDto"}, "CarrierPaymentInfo": {"$ref": "#/definitions/CarrierPaymentInfoDto"}, "CarrierRemit": {"$ref": "#/definitions/CarrierRemitDto"}, "FactoringRemit": {"$ref": "#/definitions/FactoringRemitDto"}, "CarrierBank": {"$ref": "#/definitions/CarrierBankDto"}, "CarrierPaymentTerms": {"type": "array", "items": {"$ref": "#/definitions/CarrierPaymentTermDto"}}, "CarrierPaymentTypes": {"type": "array", "items": {"$ref": "#/definitions/CarrierPaymentTypeDto"}}, "CarrierPayerType": {"$ref": "#/definitions/PayerTypeDto"}, "CarrierTruckClass": {"$ref": "#/definitions/CarrierTruckClassDto"}, "CarrierTruckType": {"$ref": "#/definitions/CarrierTruckTypeDto"}, "CarrierW9Forms": {"type": "array", "items": {"$ref": "#/definitions/CarrierW9FormDto"}}, "CarrierCertification": {"$ref": "#/definitions/CarrierCertificationDto"}, "AssureAdvantage": {"type": "array", "items": {"$ref": "#/definitions/MyCarrierPacketsApi.FMCSA.FMCSACarrier"}}, "CarrierMode": {"$ref": "#/definitions/CarrierModeDto"}, "CarrierELDProvider": {"$ref": "#/definitions/CarrierELDProviderDto"}, "OwnerContactName": {"type": "string"}, "OwnerContactPhone": {"type": "string"}, "OwnerContactEmail": {"type": "string"}, "CarrierTINMatchings": {"type": "array", "items": {"$ref": "#/definitions/CarrierTINMatchingDto"}}, "Message": {"type": "string"}}}, "CarrierCustomerAgreementDto": {"type": "object", "properties": {"SignatureDate": {"format": "date-time", "type": "string"}, "SignaturePerson": {"type": "string"}, "SignaturePersonTitle": {"type": "string"}, "SignaturePersonUserName": {"type": "string"}, "SignaturePersonEmail": {"type": "string"}, "SignaturePersonPhoneNumber": {"type": "string"}, "CustomerAgreement": {"$ref": "#/definitions/CustomerAgreementDto"}, "CarrierCustomerAgreementImages": {"type": "array", "items": {"$ref": "#/definitions/CarrierCustomerAgreementImageDto"}}, "IsActive": {"type": "boolean"}, "IPAddress": {"type": "string"}}}, "CarrierCustomerPacketStatusDto": {"type": "object", "properties": {"Customer": {"$ref": "#/definitions/CustomerDto"}, "CarrierPacketStatus": {"type": "string"}, "CustomerID": {"format": "int32", "type": "integer"}}}, "CarrierCargoHauledDto": {"type": "object", "properties": {"GeneralFreight": {"type": "boolean"}, "LiquidsGas": {"type": "boolean"}, "Chemicals": {"type": "boolean"}, "HouseholdGoods": {"type": "boolean"}, "IntermodalContainers": {"type": "boolean"}, "CommoditiesDryBulk": {"type": "boolean"}, "MetalSheetsCoilsRolls": {"type": "boolean"}, "Passengers": {"type": "boolean"}, "RefrigeratedFood": {"type": "boolean"}, "MotorVehicles": {"type": "boolean"}, "OilfieldEquipment": {"type": "boolean"}, "Beverages": {"type": "boolean"}, "DrivewayTowaway": {"type": "boolean"}, "LivestockContainers": {"type": "boolean"}, "PaperProducts": {"type": "boolean"}, "LogsPolesBeamsLumber": {"type": "boolean"}, "GrainFeedHay": {"type": "boolean"}, "Utility": {"type": "boolean"}, "BuildingMaterials": {"type": "boolean"}, "CoalCoke": {"type": "boolean"}, "FarmSupplies": {"type": "boolean"}, "MobileHomes": {"type": "boolean"}, "Meat": {"type": "boolean"}, "Construction": {"type": "boolean"}, "MachineryLargeObjects": {"type": "boolean"}, "GarbageRefuseTrash": {"type": "boolean"}, "WaterWell": {"type": "boolean"}, "FreshProduce": {"type": "boolean"}, "USMail": {"type": "boolean"}, "Other": {"type": "string"}}}, "CarrierCompanyClassificationDto": {"type": "object", "properties": {"AuthForHire": {"type": "boolean"}, "Migrant": {"type": "boolean"}, "IndianNation": {"type": "boolean"}, "ExemptForHire": {"type": "boolean"}, "USMail": {"type": "boolean"}, "PrivateProperty": {"type": "boolean"}, "FederalGovernment": {"type": "boolean"}, "PrivPassBusiness": {"type": "boolean"}, "StateGovernment": {"type": "boolean"}, "PrivPassNonBusiness": {"type": "boolean"}, "LocalGovernment": {"type": "boolean"}, "WOSB": {"type": "boolean"}, "VOSB": {"type": "boolean"}, "MBE": {"type": "boolean"}, "AsianPacificAmerican": {"type": "boolean"}, "SubcontinentAmerican": {"type": "boolean"}, "NOB": {"type": "boolean"}, "HispanicAmerican": {"type": "boolean"}, "AfricanAmerican": {"type": "boolean"}, "WBE": {"type": "boolean"}, "DBE": {"type": "boolean"}, "SBA8a": {"type": "boolean"}, "EDWOSB": {"type": "boolean"}, "SDVOSB": {"type": "boolean"}, "HUBZone": {"type": "boolean"}, "LGBTQIA": {"type": "boolean"}, "Other": {"type": "string"}}}, "CarrierDriverDto": {"type": "object", "properties": {"DriverName": {"type": "string"}, "CellPhone": {"type": "string"}, "ComCheck": {"type": "boolean"}, "FuelAdvance": {"type": "boolean"}}}, "CarrierDispatcherDto": {"type": "object", "properties": {"DispatcherName": {"type": "string"}, "PhoneNumber": {"type": "string"}, "Email": {"type": "string"}}}, "CarrierLaneDto": {"type": "object", "properties": {"UnitedStates": {"type": "boolean"}, "Mexico": {"type": "boolean"}, "Canada": {"type": "boolean"}, "NortheastRegion": {"type": "boolean"}, "MidwestRegion": {"type": "boolean"}, "SouthRegion": {"type": "boolean"}, "WestRegion": {"type": "boolean"}, "Alabama": {"type": "boolean"}, "Alaska": {"type": "boolean"}, "Arizona": {"type": "boolean"}, "Arkansas": {"type": "boolean"}, "California": {"type": "boolean"}, "Colorado": {"type": "boolean"}, "Delaware": {"type": "boolean"}, "Florida": {"type": "boolean"}, "Georgia": {"type": "boolean"}, "Hawaii": {"type": "boolean"}, "Idaho": {"type": "boolean"}, "Illinois": {"type": "boolean"}, "Indiana": {"type": "boolean"}, "Iowa": {"type": "boolean"}, "Kansas": {"type": "boolean"}, "Kentucky": {"type": "boolean"}, "Louisiana": {"type": "boolean"}, "Maine": {"type": "boolean"}, "Maryland": {"type": "boolean"}, "Massachusetts": {"type": "boolean"}, "Michigan": {"type": "boolean"}, "Minnesota": {"type": "boolean"}, "Mississippi": {"type": "boolean"}, "Missouri": {"type": "boolean"}, "Montana": {"type": "boolean"}, "Nebraska": {"type": "boolean"}, "Nevada": {"type": "boolean"}, "NewHampshire": {"type": "boolean"}, "NewJersey": {"type": "boolean"}, "NewMexico": {"type": "boolean"}, "NewYork": {"type": "boolean"}, "NorthCarolina": {"type": "boolean"}, "NorthDakota": {"type": "boolean"}, "Ohio": {"type": "boolean"}, "Oklahoma": {"type": "boolean"}, "Oregon": {"type": "boolean"}, "Pennsylvania": {"type": "boolean"}, "RhodeIsland": {"type": "boolean"}, "SouthCarolina": {"type": "boolean"}, "SouthDakota": {"type": "boolean"}, "Tennessee": {"type": "boolean"}, "Utah": {"type": "boolean"}, "Vermont": {"type": "boolean"}, "Virginia": {"type": "boolean"}, "Washington": {"type": "boolean"}, "WashingtonDC": {"type": "boolean"}, "WestVirginia": {"type": "boolean"}, "Wisconsin": {"type": "boolean"}, "Wyoming": {"type": "boolean"}, "Connecticut": {"type": "boolean"}, "Texas": {"type": "boolean"}, "Alberta": {"type": "boolean"}, "BritishColumbia": {"type": "boolean"}, "Manitoba": {"type": "boolean"}, "NewBrunswick": {"type": "boolean"}, "NewfoundlandAndLabrador": {"type": "boolean"}, "NorthwestTerritories": {"type": "boolean"}, "NovaScotia": {"type": "boolean"}, "Nunavut": {"type": "boolean"}, "Ontario": {"type": "boolean"}, "PrinceEdwardIsland": {"type": "boolean"}, "Quebec": {"type": "boolean"}, "Saskatchewan": {"type": "boolean"}, "YukonTerritory": {"type": "boolean"}}}, "CarrierOperationalDetailDto": {"type": "object", "properties": {"FleetSize": {"format": "int32", "type": "integer"}, "TotalPowerUnits": {"format": "int32", "type": "integer"}, "NumberOfVehicles": {"format": "int32", "type": "integer"}, "ReeferEquipment": {"type": "boolean"}, "VanEquipment": {"type": "boolean"}, "FlatbedStepDeckEquipment": {"type": "boolean"}, "OwnedTractors": {"format": "int32", "type": "integer"}, "OwnedTrucks": {"format": "int32", "type": "integer"}, "OwnedTrailers": {"format": "int32", "type": "integer"}, "TermLeasedTractors": {"format": "int32", "type": "integer"}, "TermLeasedTrucks": {"format": "int32", "type": "integer"}, "TermLeasedTrailers": {"format": "int32", "type": "integer"}, "TripLeasedTractors": {"format": "int32", "type": "integer"}, "TripLeasedTrucks": {"format": "int32", "type": "integer"}, "TripLeasedTrailers": {"format": "int32", "type": "integer"}, "InterstateAndIntrastateDrivers": {"format": "int32", "type": "integer"}, "CDLEmployedDrivers": {"format": "int32", "type": "integer"}, "MonthlyAverageLeasedDrivers": {"format": "int32", "type": "integer"}, "InterstateDriversTotal": {"format": "int32", "type": "integer"}, "InterstateDriversGT100Miles": {"format": "int32", "type": "integer"}, "InterstateDriversLT100Miles": {"format": "int32", "type": "integer"}, "IntrastateDriversTotal": {"format": "int32", "type": "integer"}, "IntrastateDriversGT100Miles": {"format": "int32", "type": "integer"}, "IntrastateDriversLT100Miles": {"format": "int32", "type": "integer"}, "PowerOnly": {"type": "boolean"}, "SatelliteEquipment": {"type": "boolean"}, "TeamDrivers": {"type": "boolean"}, "DropTrailer": {"type": "boolean"}, "ELDCompliant": {"type": "boolean"}, "ELDCompliantBy": {"type": "string"}, "ELDIdentifier": {"type": "string"}, "NumberOfTractors": {"format": "int32", "type": "integer"}, "NumberOfVans": {"format": "int32", "type": "integer"}, "NumberOfReefers": {"format": "int32", "type": "integer"}, "NumberOfFlats": {"format": "int32", "type": "integer"}, "NumberOfStepDecks": {"format": "int32", "type": "integer"}, "NumberOfTanks": {"format": "int32", "type": "integer"}}}, "CarrierPaymentInfoDto": {"type": "object", "properties": {"BankRoutingNumber": {"type": "string"}, "BankAccountNumber": {"type": "string"}, "BankAccountName": {"type": "string"}, "BankName": {"type": "string"}, "BankAddress": {"type": "string"}, "BankPhone": {"type": "string"}, "BankFax": {"type": "string"}, "FactoringCompanyName": {"type": "string"}, "RemitAddress1": {"type": "string"}, "RemitAddress2": {"type": "string"}, "RemitCity": {"type": "string"}, "RemitZipCode": {"type": "string"}, "BankAccountType": {"type": "string"}, "RemitState": {"type": "string"}, "RemitCountry": {"type": "string"}, "RemitEmail": {"type": "string"}, "Require1099": {"type": "boolean"}, "EpayManagerID": {"format": "int32", "type": "integer"}, "RemitCurrency": {"type": "string"}, "PayAdvanceOptionID": {"format": "int32", "type": "integer"}, "PayAdvanceOptionType": {"type": "string"}}}, "CarrierRemitDto": {"type": "object", "properties": {"CarrierRemitEmail": {"type": "string"}, "CarrierRemitAddress1": {"type": "string"}, "CarrierRemitAddress2": {"type": "string"}, "CarrierRemitCity": {"type": "string"}, "CarrierRemitCountry": {"type": "string"}, "CarrierRemitStateProvince": {"type": "string"}, "CarrierRemitZipCode": {"type": "string"}}}, "FactoringRemitDto": {"type": "object", "properties": {"FactoringCompanyID": {"format": "int32", "type": "integer"}, "FactoringCompanyName": {"type": "string"}, "FactoringRemitEmail": {"type": "string"}, "FactoringRemitAddress": {"type": "string"}, "FactoringRemitAddress2": {"type": "string"}, "FactoringRemitCity": {"type": "string"}, "FactoringRemitCountry": {"type": "string"}, "FactoringRemitStateProvince": {"type": "string"}, "FactoringRemitZipcode": {"type": "string"}, "FactoringPhone": {"type": "string"}, "BankRoutingNumber": {"type": "string"}, "BankAccountNumber": {"type": "string"}}}, "CarrierBankDto": {"type": "object", "properties": {"CarrierBankRoutingNumber": {"type": "string"}, "CarrierBankAccountNumber": {"type": "string"}, "CarrierBankAccountName": {"type": "string"}, "CarrierBankName": {"type": "string"}, "CarrierBankAddress": {"type": "string"}, "CarrierBankPhone": {"type": "string"}, "CarrierBankFax": {"type": "string"}, "CarrierBankAccountType": {"type": "string"}}}, "CarrierPaymentTermDto": {"type": "object", "properties": {"PaymentTerm": {"$ref": "#/definitions/CustomerPaymentTermDto"}}}, "CarrierPaymentTypeDto": {"type": "object", "properties": {"PaymentType": {"$ref": "#/definitions/CustomerPaymentTypeDto"}}}, "PayerTypeDto": {"type": "object", "properties": {"PayerTypeID": {"format": "int32", "type": "integer"}, "Name": {"type": "string"}}}, "CarrierTruckClassDto": {"type": "object", "properties": {"Conestoga": {"type": "boolean"}, "Containers": {"type": "boolean"}, "DecksSpecialized": {"type": "boolean"}, "DecksStandard": {"type": "boolean"}, "DryBulk": {"type": "boolean"}, "Flatbeds": {"type": "boolean"}, "HazardousMaterials": {"type": "boolean"}, "Reefers": {"type": "boolean"}, "Tankers": {"type": "boolean"}, "VansSpecialized": {"type": "boolean"}, "VansStandard": {"type": "boolean"}, "Other": {"type": "string"}}}, "CarrierTruckTypeDto": {"type": "object", "properties": {"AutoCarrier": {"type": "boolean"}, "BTrain": {"type": "boolean"}, "Conestoga": {"type": "boolean"}, "Container": {"type": "boolean"}, "ContainerInsulated": {"type": "boolean"}, "ContainerRefrigerated": {"type": "boolean"}, "Conveyor": {"type": "boolean"}, "DoubleDrop": {"type": "boolean"}, "DropDeckLandoll": {"type": "boolean"}, "DumpTrailer": {"type": "boolean"}, "Flatbed": {"type": "boolean"}, "FlatbedAirRide": {"type": "boolean"}, "FlatbedConestoga": {"type": "boolean"}, "FlatbedDouble": {"type": "boolean"}, "FlatbedHazMat": {"type": "boolean"}, "FlatbedHotshot": {"type": "boolean"}, "FlatbedMaxi": {"type": "boolean"}, "FlatbedOrStepDeck": {"type": "boolean"}, "FlatbedOverdimension": {"type": "boolean"}, "FlatbedWithchains": {"type": "boolean"}, "FlatbedWithSides": {"type": "boolean"}, "FlatbedWithTarps": {"type": "boolean"}, "FlatbedWithTeam": {"type": "boolean"}, "FlatbedVanReefer": {"type": "boolean"}, "HopperBottom": {"type": "boolean"}, "InsulatedVanOrReefer": {"type": "boolean"}, "Lowboy": {"type": "boolean"}, "LowboyOrRemGooseneck": {"type": "boolean"}, "LowboyOverdimension": {"type": "boolean"}, "MovingVan": {"type": "boolean"}, "Pneumatic": {"type": "boolean"}, "PowerOnly": {"type": "boolean"}, "Reefer": {"type": "boolean"}, "ReeferAirRide": {"type": "boolean"}, "ReeferDouble": {"type": "boolean"}, "ReeferHazMat": {"type": "boolean"}, "ReeferIntermodal": {"type": "boolean"}, "ReeferLogistics": {"type": "boolean"}, "ReeferOrVentedVan": {"type": "boolean"}, "ReeferPalletExchange": {"type": "boolean"}, "ReeferWithTeam": {"type": "boolean"}, "RemovableGooseneck": {"type": "boolean"}, "StepDeck": {"type": "boolean"}, "StepDeckOrRemGooseneck": {"type": "boolean"}, "StepdeckConestoga": {"type": "boolean"}, "StraightBoxTruck": {"type": "boolean"}, "StretchTrailer": {"type": "boolean"}, "TankerAluminum": {"type": "boolean"}, "TankerIntermodal": {"type": "boolean"}, "TankerSteel": {"type": "boolean"}, "TruckAndTrailer": {"type": "boolean"}, "Van": {"type": "boolean"}, "VanAirRide": {"type": "boolean"}, "VanBlanketWrap": {"type": "boolean"}, "VanConestoga": {"type": "boolean"}, "VanDouble": {"type": "boolean"}, "VanHazMat": {"type": "boolean"}, "VanHotshot": {"type": "boolean"}, "VanInsulated": {"type": "boolean"}, "VanIntermodal": {"type": "boolean"}, "VanLiftGate": {"type": "boolean"}, "VanLogistics": {"type": "boolean"}, "VanOpenTop": {"type": "boolean"}, "VanOrFlatbed": {"type": "boolean"}, "VanOrFlatbedwTarps": {"type": "boolean"}, "VanOrReefer": {"type": "boolean"}, "VanPalletExchange": {"type": "boolean"}, "VanRollerBed": {"type": "boolean"}, "VanTriple": {"type": "boolean"}, "VanVented": {"type": "boolean"}, "VanWithCurtains": {"type": "boolean"}, "VanWithTeam": {"type": "boolean"}, "OneToTwoCarHauler": {"type": "boolean"}, "ThreeCarHauler": {"type": "boolean"}, "FourToFiveCarHauler": {"type": "boolean"}, "SixToSevenCarHauler": {"type": "boolean"}, "EightToTenCarHauler": {"type": "boolean"}, "Chassis": {"type": "boolean"}, "PintleHitch": {"type": "boolean"}, "VanSprinter": {"type": "boolean"}, "Toter": {"type": "boolean"}}}, "CarrierW9FormDto": {"type": "object", "properties": {"FullName": {"type": "string"}, "BusinessName": {"type": "string"}, "IndividualOrSingleMemberLLC": {"type": "boolean"}, "CCorporation": {"type": "boolean"}, "SCorporation": {"type": "boolean"}, "Partnership": {"type": "boolean"}, "RequesterNameAddress": {"type": "string"}, "TrustOrEstate": {"type": "boolean"}, "LimitedLiabilityCompany": {"type": "boolean"}, "TaxClassification": {"type": "string"}, "Other": {"type": "boolean"}, "OtherDetail": {"type": "string"}, "ExemptPayeeCode": {"type": "string"}, "ExemptionFATCACode": {"type": "string"}, "Address": {"type": "string"}, "CityStateZipCode": {"type": "string"}, "ListAccountNumber": {"type": "string"}, "SSN": {"type": "string"}, "EIN": {"type": "string"}, "SignatureDate": {"format": "date-time", "type": "string"}, "SignaturePerson": {"type": "string"}, "IsActive": {"type": "boolean"}, "City": {"type": "string"}, "State": {"type": "string"}, "ZipCode": {"type": "string"}, "CarrierW9FormImages": {"type": "array", "items": {"$ref": "#/definitions/CarrierW9FormImageDto"}}}}, "CarrierCertificationDto": {"type": "object", "properties": {"Hazmat": {"type": "boolean"}, "HazmatNumber": {"type": "string"}, "SmartWay": {"type": "boolean"}, "CARB": {"type": "boolean"}, "TWIC": {"type": "boolean"}, "CTPATCertified": {"type": "boolean"}, "CTPATSVINumber": {"type": "string"}, "TankerEndorsed": {"type": "boolean"}, "TankerEndorsedNumOfDrivers": {"format": "int32", "type": "integer"}, "CBP": {"type": "boolean"}, "CBSA": {"type": "boolean"}, "ANAM": {"type": "boolean"}, "ACE": {"type": "boolean"}, "ACI": {"type": "boolean"}, "CSA": {"type": "boolean"}, "FAST": {"type": "boolean"}, "PIP": {"type": "boolean"}}}, "MyCarrierPacketsApi.FMCSA.FMCSACarrier": {"type": "object", "properties": {"CarrierDetails": {"$ref": "#/definitions/MyCarrierPacketsApi.FMCSA.CarrierDetails"}, "ResponseDO": {"$ref": "#/definitions/ResponseDO"}}}, "CarrierModeDto": {"type": "object", "properties": {"LessThanTruckLoad": {"type": "boolean"}, "Partial": {"type": "boolean"}, "Truckload": {"type": "boolean"}, "Rail": {"type": "boolean"}, "Intermodal": {"type": "boolean"}, "Air": {"type": "boolean"}, "Expedite": {"type": "boolean"}, "Ocean": {"type": "boolean"}, "Drayage": {"type": "boolean"}}}, "CarrierELDProviderDto": {"type": "object", "properties": {"ComplianceStatusID": {"format": "int32", "type": "integer"}, "ComplianceStatus": {"type": "string"}, "ProviderName": {"type": "string"}, "ProviderIdentifier": {"type": "string"}, "ExemptionID": {"format": "int32", "type": "integer"}, "Exemption": {"type": "string"}, "CompliantBy": {"type": "string"}}}, "CarrierTINMatchingDto": {"type": "object", "properties": {"TINTypeID": {"format": "int32", "type": "integer"}, "TIN": {"type": "string"}, "TINName": {"type": "string"}, "TINMatchingStatusID": {"format": "int32", "type": "integer"}, "TINMatchingResultID": {"format": "int32", "type": "integer"}, "CreatedOnUtc": {"format": "date-time", "type": "string"}, "SubmittedOnUtc": {"format": "date-time", "type": "string"}, "ProcessedOnUtc": {"format": "date-time", "type": "string"}, "ContactEmail": {"type": "string"}, "ContactPhoneNumber": {"type": "string"}, "MatchingResult": {"type": "string"}, "MatchingStatus": {"type": "string"}}}, "CustomerAgreementDto": {"type": "object", "properties": {"AgreementName": {"type": "string"}, "CreatedDate": {"format": "date-time", "type": "string"}, "CreatedBy": {"type": "string"}, "BlobName": {"type": "string"}, "Customer": {"$ref": "#/definitions/CustomerDto"}}}, "CarrierCustomerAgreementImageDto": {"type": "object", "properties": {"BlobName": {"type": "string"}, "CreatedDate": {"format": "date-time", "type": "string"}}}, "CustomerDto": {"type": "object", "properties": {"CustomerID": {"format": "int32", "type": "integer"}, "Title": {"type": "string"}, "FirstName": {"type": "string"}, "MiddleName": {"type": "string"}, "LastName": {"type": "string"}, "CompanyName": {"type": "string"}, "TypeCompany": {"type": "string"}, "CellPhone": {"type": "string"}, "Phone": {"type": "string"}, "Fax": {"type": "string"}, "Address1": {"type": "string"}, "Address2": {"type": "string"}, "Apartment": {"type": "string"}, "City": {"type": "string"}, "State": {"type": "string"}, "Zipcode": {"type": "string"}, "Country": {"type": "string"}, "CustomerKey": {"type": "string"}, "PacketCompletionNotificationType": {"format": "int32", "type": "integer"}, "PacketCompletionNotificationEmail": {"type": "string"}}}, "CustomerPaymentTermDto": {"type": "object", "properties": {"PaymentTermID": {"format": "int32", "type": "integer"}, "Days": {"format": "int32", "type": "integer"}, "Term": {"type": "string"}, "QuickPay": {"type": "boolean"}, "PaymentFeeType": {"type": "string"}, "PaymentFeeAmount": {"format": "double", "type": "number"}, "CustomerID": {"format": "int32", "type": "integer"}}}, "CustomerPaymentTypeDto": {"type": "object", "properties": {"PaymentTypeID": {"format": "int32", "type": "integer"}, "Type": {"type": "string"}, "CustomerID": {"format": "int32", "type": "integer"}, "PaymentType": {"$ref": "#/definitions/PaymentTypeDto"}}}, "CarrierW9FormImageDto": {"type": "object", "properties": {"BlobName": {"type": "string"}, "CreatedDate": {"format": "date-time", "type": "string"}, "FileName": {"type": "string"}, "CreatedBy": {"type": "string"}}}, "MyCarrierPacketsApi.FMCSA.CarrierDetails": {"type": "object", "properties": {"docketNumber": {"type": "string"}, "dotNumber": {"$ref": "#/definitions/MyCarrierPacketsApi.FMCSA.DotNumber"}, "carrierType": {"type": "string"}, "isMonitored": {"type": "boolean"}, "isBlocked": {"type": "boolean"}, "Identity": {"$ref": "#/definitions/MyCarrierPacketsApi.FMCSA.Identity"}, "Authority": {"$ref": "#/definitions/MyCarrierPacketsApi.FMCSA.Authority"}, "FMCSAInsurance": {"$ref": "#/definitions/MyCarrierPacketsApi.FMCSA.FMCSAInsurance"}, "CertData": {"$ref": "#/definitions/MyCarrierPacketsApi.FMCSA.CertData"}, "Safety": {"$ref": "#/definitions/MyCarrierPacketsApi.FMCSA.Safety"}, "Inspection": {"$ref": "#/definitions/MyCarrierPacketsApi.FMCSA.Inspection"}, "Crash": {"$ref": "#/definitions/MyCarrierPacketsApi.FMCSA.Crash"}, "Review": {"$ref": "#/definitions/MyCarrierPacketsApi.FMCSA.Review"}, "Operation": {"$ref": "#/definitions/MyCarrierPacketsApi.FMCSA.Operation"}, "Cargo": {"$ref": "#/definitions/MyCarrierPacketsApi.FMCSA.Cargo"}, "Drivers": {"$ref": "#/definitions/MyCarrierPacketsApi.FMCSA.Drivers"}, "Equipment": {"$ref": "#/definitions/MyCarrierPacketsApi.FMCSA.Equipment"}, "Other": {"$ref": "#/definitions/MyCarrierPacketsApi.FMCSA.Other"}, "RiskAssessment": {"$ref": "#/definitions/MyCarrierPacketsApi.FMCSA.RiskAssessment"}, "RiskAssessmentDetails": {"$ref": "#/definitions/MyCarrierPacketsApi.FMCSA.RiskAssessmentDetails"}, "CarrierRatings": {"$ref": "#/definitions/MyCarrierPacketsApi.FMCSA.CarrierRatings"}, "LatestInvitation": {"$ref": "#/definitions/MyCarrierPacketsApi.FMCSA.LatestInvitation"}}}, "ResponseDO": {"type": "object", "properties": {"status": {"type": "string"}, "action": {"type": "string"}, "code": {"type": "string"}, "displayMsg": {"type": "string"}, "techMsg": {"type": "string"}}}, "PaymentTypeDto": {"type": "object", "properties": {"Name": {"type": "string"}}}, "MyCarrierPacketsApi.FMCSA.DotNumber": {"type": "object", "properties": {"status": {"type": "string"}, "Value": {"type": "string"}}}, "MyCarrierPacketsApi.FMCSA.Identity": {"type": "object", "properties": {"legalName": {"type": "string"}, "dbaName": {"type": "string"}, "businessStreet": {"type": "string"}, "businessCity": {"type": "string"}, "businessState": {"type": "string"}, "businessZipCode": {"type": "string"}, "businessColonia": {"type": "string"}, "businessCountry": {"type": "string"}, "businessPhone": {"type": "string"}, "businessFax": {"type": "string"}, "mailingStreet": {"type": "string"}, "mailingCity": {"type": "string"}, "mailingState": {"type": "string"}, "mailingZipCode": {"type": "string"}, "mailingColonia": {"type": "string"}, "mailingCountry": {"type": "string"}, "mailingPhone": {"type": "string"}, "mailingFax": {"type": "string"}, "undeliverableMail": {"type": "string"}, "companyRep1": {"type": "string"}, "companyRep2": {"type": "string"}, "cellPhone": {"type": "string"}, "emailAddress": {"type": "string"}, "dunBradstreetNum": {"type": "string"}, "organization": {"type": "string"}}}, "MyCarrierPacketsApi.FMCSA.Authority": {"type": "object", "properties": {"authGrantDate": {"type": "string"}, "commonAuthority": {"type": "string"}, "commonAuthorityPending": {"type": "string"}, "commonAuthorityRevocation": {"type": "string"}, "contractAuthority": {"type": "string"}, "contractAuthorityPending": {"type": "string"}, "contractAuthorityRevocation": {"type": "string"}, "brokerAuthority": {"type": "string"}, "brokerAuthorityPending": {"type": "string"}, "brokerAuthorityRevocation": {"type": "string"}, "freight": {"type": "string"}, "passenger": {"type": "string"}, "householdGoods": {"type": "string"}, "private": {"type": "string"}, "enterprise": {"type": "string"}}}, "MyCarrierPacketsApi.FMCSA.FMCSAInsurance": {"type": "object", "properties": {"bipdRequired": {"type": "string"}, "bipdOnFile": {"type": "string"}, "cargoRequired": {"type": "string"}, "cargoOnFile": {"type": "string"}, "bondSuretyRequired": {"type": "string"}, "bondSuretyOnFile": {"type": "string"}, "PolicyList": {"type": "array", "items": {"$ref": "#/definitions/PolicyOutput"}}}}, "MyCarrierPacketsApi.FMCSA.CertData": {"type": "object", "properties": {"status": {"type": "string"}, "Certificate": {"type": "array", "items": {"$ref": "#/definitions/CertificateDto"}}}}, "MyCarrierPacketsApi.FMCSA.Safety": {"type": "object", "properties": {"rating": {"type": "string"}, "ratingDate": {"type": "string"}, "unsafeDrvPCT": {"type": "string"}, "unsafeDrvOT": {"type": "string"}, "unsafeDrvSV": {"type": "string"}, "unsafeDrvAlert": {"type": "string"}, "unsafeDrvTrend": {"type": "string"}, "unsafeDrvCNT": {"format": "int32", "type": "integer"}, "hosPCT": {"type": "string"}, "hosOT": {"type": "string"}, "hosSV": {"type": "string"}, "hosAlert": {"type": "string"}, "hosTrend": {"type": "string"}, "hosCNT": {"format": "int32", "type": "integer"}, "drvFitPCT": {"type": "string"}, "drvFitOT": {"type": "string"}, "drvFitSV": {"type": "string"}, "drvFitAlert": {"type": "string"}, "drvFitTrend": {"type": "string"}, "drvFitCNT": {"format": "int32", "type": "integer"}, "controlSubPCT": {"type": "string"}, "controlSubOT": {"type": "string"}, "controlSubSV": {"type": "string"}, "controlSubAlert": {"type": "string"}, "controlSubTrend": {"type": "string"}, "controlSubCNT": {"format": "int32", "type": "integer"}, "vehMaintPCT": {"type": "string"}, "vehMaintOT": {"type": "string"}, "vehMaintSV": {"type": "string"}, "vehMaintAlert": {"type": "string"}, "vehMaintTrend": {"type": "string"}, "vehMaintCNT": {"format": "int32", "type": "integer"}, "hazMatPCT": {"type": "string"}, "hazMatOT": {"type": "string"}, "hazMatSV": {"type": "string"}, "hazMatAlert": {"type": "string"}, "hazMatTrend": {"type": "string"}, "hazMatCNT": {"format": "int32", "type": "integer"}}}, "MyCarrierPacketsApi.FMCSA.Inspection": {"type": "object", "properties": {"inspectVehUS": {"type": "string"}, "inspectVehOOSUS": {"type": "string"}, "inspectVehOOSPctUS": {"type": "string"}, "inspectDrvUS": {"type": "string"}, "inspectDrvOOSUS": {"type": "string"}, "inspectDrvOOSPctUS": {"type": "string"}, "inspectHazUS": {"type": "string"}, "inspectHazOOSUS": {"type": "string"}, "inspectHazOOSPctUS": {"type": "string"}, "inspectIEPUS": {"type": "string"}, "inspectIEPOOSUS": {"type": "string"}, "inspectIEPOOSPctUS": {"type": "string"}, "inspectTotalIEPUS": {"type": "string"}, "inspectTotalUS": {"type": "string"}, "inspectVehCAN": {"type": "string"}, "inspectVehOOSCAN": {"type": "string"}, "inspectVehOOSPctCAN": {"type": "string"}, "inspectDrvCAN": {"type": "string"}, "inspectDrvOOSCAN": {"type": "string"}, "inspectDrvOOSPctCAN": {"type": "string"}, "inspectTotalCAN": {"type": "string"}}}, "MyCarrierPacketsApi.FMCSA.Crash": {"type": "object", "properties": {"crashFatalUS": {"type": "string"}, "crashInjuryUS": {"type": "string"}, "crashTowUS": {"type": "string"}, "crashTotalUS": {"type": "string"}, "crashFatalCAN": {"type": "string"}, "crashInjuryCAN": {"type": "string"}, "crashTowCAN": {"type": "string"}, "crashTotalCAN": {"type": "string"}}}, "MyCarrierPacketsApi.FMCSA.Review": {"type": "object", "properties": {"reviewType": {"type": "string"}, "reviewDate": {"type": "string"}, "reviewDocNum": {"type": "string"}, "reviewMiles": {"type": "string"}, "mcs150Date": {"type": "string"}, "mcs150MileYear": {"type": "string"}, "mcs150Miles": {"type": "string"}, "accidentRate": {"type": "string"}, "accidentRatePrevent": {"type": "string"}}}, "MyCarrierPacketsApi.FMCSA.Operation": {"type": "object", "properties": {"dotAddDate": {"type": "string"}, "carrierOperation": {"type": "string"}, "shipperOperation": {"type": "string"}, "mxOperationType": {"type": "string"}, "mxRFCNumber": {"type": "string"}, "outOfService": {"type": "string"}, "outOfServiceDate": {"type": "string"}, "outOfServiceReason": {"type": "string"}, "entityCarrier": {"type": "string"}, "entityShipper": {"type": "string"}, "entityBroker": {"type": "string"}, "entityFreightFowarder": {"type": "string"}, "entityCargoTank": {"type": "string"}, "classAuthForHire": {"type": "string"}, "classMigrant": {"type": "string"}, "classIndianNation": {"type": "string"}, "classExemptForHire": {"type": "string"}, "classUSMail": {"type": "string"}, "classPrivateProperty": {"type": "string"}, "classFederalGovernment": {"type": "string"}, "classPrivPassBusiness": {"type": "string"}, "classStateGovernment": {"type": "string"}, "classPrivPassNonBusiness": {"type": "string"}, "classLocalGovernment": {"type": "string"}, "classOther": {"type": "string"}, "operatingStatus": {"type": "string"}}}, "MyCarrierPacketsApi.FMCSA.Cargo": {"type": "object", "properties": {"hazmatIndicator": {"type": "string"}, "cargoGenFreight": {"type": "string"}, "cargoHousehold": {"type": "string"}, "cargoMetal": {"type": "string"}, "cargoMotorVeh": {"type": "string"}, "cargoDriveTow": {"type": "string"}, "cargoLogPole": {"type": "string"}, "cargoBldgMaterial": {"type": "string"}, "cargoMobileHome": {"type": "string"}, "cargoMachLarge": {"type": "string"}, "cargoProduce": {"type": "string"}, "cargoLiqGas": {"type": "string"}, "cargoIntermodal": {"type": "string"}, "cargoPassengers": {"type": "string"}, "cargoOilfield": {"type": "string"}, "cargoLivestock": {"type": "string"}, "cargoGrainfeed": {"type": "string"}, "cargoCoalcoke": {"type": "string"}, "cargoMeat": {"type": "string"}, "cargoGarbage": {"type": "string"}, "cargoUSMail": {"type": "string"}, "cargoChemicals": {"type": "string"}, "cargoDryBulk": {"type": "string"}, "cargoRefrigerated": {"type": "string"}, "cargoBeverages": {"type": "string"}, "cargoPaperProd": {"type": "string"}, "cargoUtilities": {"type": "string"}, "cargoFarmSupplies": {"type": "string"}, "cargoConstruction": {"type": "string"}, "cargoWaterwell": {"type": "string"}, "cargoOther": {"type": "string"}, "cargoOtherDesc": {"type": "string"}}}, "MyCarrierPacketsApi.FMCSA.Drivers": {"type": "object", "properties": {"driversTotal": {"type": "string"}, "driversAvgLeased": {"type": "string"}, "driversCDL": {"type": "string"}, "driversInter": {"type": "string"}, "driversInterLT100": {"type": "string"}, "driversInterGT100": {"type": "string"}, "driversIntra": {"type": "string"}, "driversIntraLT100": {"type": "string"}, "driversIntraGT100": {"type": "string"}}}, "MyCarrierPacketsApi.FMCSA.Equipment": {"type": "object", "properties": {"trucksTotal": {"type": "string"}, "totalPower": {"type": "string"}, "fleetsize": {"type": "string"}, "trucksOwned": {"type": "string"}, "trucksTerm": {"type": "string"}, "trucksTrip": {"type": "string"}, "trailersOwned": {"type": "string"}, "trailersTerm": {"type": "string"}, "trailersTrip": {"type": "string"}, "tractorsOwned": {"type": "string"}, "tractorsTerm": {"type": "string"}, "tractorsTrip": {"type": "string"}}}, "MyCarrierPacketsApi.FMCSA.Other": {"type": "object", "properties": {"carbTru": {"type": "string"}, "smartway": {"type": "string"}, "watchdogReports": {"type": "string"}}}, "MyCarrierPacketsApi.FMCSA.RiskAssessment": {"type": "object", "properties": {"Overall": {"type": "string"}, "Authority": {"type": "string"}, "Insurance": {"type": "string"}, "Safety": {"type": "string"}, "Operation": {"type": "string"}, "Other": {"type": "string"}}}, "MyCarrierPacketsApi.FMCSA.RiskAssessmentDetails": {"type": "object", "properties": {"IsIntrastateCarrier": {"type": "boolean"}, "TotalPoints": {"format": "int32", "type": "integer"}, "OverallRating": {"type": "string"}, "Authority": {"$ref": "#/definitions/MyCarrierPacketsApi.FMCSA.RiskAssessmentDetail"}, "Insurance": {"$ref": "#/definitions/MyCarrierPacketsApi.FMCSA.RiskAssessmentDetail"}, "Safety": {"$ref": "#/definitions/MyCarrierPacketsApi.FMCSA.RiskAssessmentDetail"}, "Operation": {"$ref": "#/definitions/MyCarrierPacketsApi.FMCSA.RiskAssessmentDetail"}, "Other": {"$ref": "#/definitions/MyCarrierPacketsApi.FMCSA.RiskAssessmentDetail"}}}, "MyCarrierPacketsApi.FMCSA.CarrierRatings": {"type": "object", "properties": {"myRating": {"format": "int32", "type": "integer"}, "totalRatings": {"format": "int32", "type": "integer"}, "lowRatings": {"format": "int32", "type": "integer"}, "avgRating": {"format": "double", "type": "number"}}}, "MyCarrierPacketsApi.FMCSA.LatestInvitation": {"type": "object", "properties": {"InvitedByUserName": {"type": "string"}, "InvitedByEmail": {"type": "string"}, "InvitedByFirstName": {"type": "string"}, "InvitedByLastName": {"type": "string"}, "InvitationSentDate": {"format": "date-time", "type": "string"}, "InvitationRecipient": {"type": "string"}}}, "PolicyOutput": {"type": "object", "properties": {"companyName": {"type": "string"}, "attnToName": {"type": "string"}, "address": {"type": "string"}, "city": {"type": "string"}, "stateCode": {"type": "string"}, "postalCode": {"type": "string"}, "countryCode": {"type": "string"}, "phone": {"type": "string"}, "fax": {"type": "string"}, "insuranceType": {"type": "string"}, "policyNumber": {"type": "string"}, "postedDate": {"type": "string"}, "effectiveDate": {"type": "string"}, "cancelationDate": {"type": "string"}, "coverageFrom": {"type": "string"}, "coverageTo": {"type": "string"}, "amBestRating": {"type": "string"}}}, "CertificateDto": {"type": "object", "properties": {"certificateID": {"type": "string"}, "producerName": {"type": "string"}, "producerAddress": {"type": "string"}, "producerCity": {"type": "string"}, "producerState": {"type": "string"}, "producerZip": {"type": "string"}, "producerPhone": {"type": "string"}, "producerFax": {"type": "string"}, "producerEmail": {"type": "string"}, "paidFor": {"type": "string"}, "BlobName": {"type": "string"}, "Coverage": {"type": "array", "items": {"$ref": "#/definitions/CoverageDto"}}}}, "MyCarrierPacketsApi.FMCSA.RiskAssessmentDetail": {"type": "object", "properties": {"TotalPoints": {"format": "int32", "type": "integer"}, "OverallRating": {"type": "string"}, "Infractions": {"type": "array", "items": {"$ref": "#/definitions/MyCarrierPacketsApi.FMCSA.RiskAssessmentInfraction"}}}}, "CoverageDto": {"type": "object", "properties": {"insurerName": {"type": "string"}, "insurerAMBestRating": {"type": "string"}, "type": {"type": "string"}, "policyNumber": {"type": "string"}, "expirationDate": {"type": "string"}, "coverageLimit": {"type": "string"}, "deductable": {"type": "string"}, "referBreakdown": {"type": "string"}, "referBreakDeduct": {"type": "string"}}}, "MyCarrierPacketsApi.FMCSA.RiskAssessmentInfraction": {"type": "object", "properties": {"Points": {"format": "int32", "type": "integer"}, "RiskLevel": {"type": "string"}, "RuleText": {"type": "string"}, "RuleOutput": {"type": "string"}}}, "MyCarrierPacketsBusiness.Services.FMCSACarrierServices.Data.GetCarrierContactsResponse": {"type": "object", "properties": {"Success": {"type": "boolean"}, "Message": {"type": "string"}, "Carrier": {"$ref": "#/definitions/MyCarrierPacketsDomain.CarrierContacts.Carrier"}}}, "MyCarrierPacketsDomain.CarrierContacts.Carrier": {"type": "object", "properties": {"DOTNumber": {"format": "int32", "type": "integer"}, "DocketNumber": {"type": "string"}, "LegalName": {"type": "string"}, "DBAName": {"type": "string"}, "Contacts": {"type": "array", "items": {"$ref": "#/definitions/MyCarrierPacketsDomain.CarrierContacts.CarrierContact"}}}}, "MyCarrierPacketsDomain.CarrierContacts.CarrierContact": {"type": "object", "properties": {"FirstName": {"type": "string"}, "LastName": {"type": "string"}, "Title": {"type": "string"}, "Phone": {"type": "string"}, "Email": {"type": "string"}, "AuthorizedForPackets": {"type": "boolean"}, "VerificationStatus": {"type": "string"}}}, "MonitoredCarriersContactsDataDto": {"type": "object", "properties": {"pageNumber": {"format": "int32", "type": "integer"}, "pageSize": {"format": "int32", "type": "integer"}, "totalPages": {"format": "int32", "type": "integer"}, "totalCount": {"format": "int32", "type": "integer"}, "succeeded": {"type": "boolean"}, "message": {"type": "string"}, "data": {"type": "array", "items": {"$ref": "#/definitions/MonitoredCarriersContacts"}}}}, "MonitoredCarriersContacts": {"type": "object", "properties": {"DOTNumber": {"format": "int32", "type": "integer"}, "DocketNumber": {"type": "string"}, "LegalName": {"type": "string"}, "DBAName": {"type": "string"}, "CarriersContacts": {"type": "array", "items": {"$ref": "#/definitions/MonitoredCarriersContact"}}, "Success": {"type": "boolean"}, "Messages": {"type": "array", "items": {"type": "string"}}}}, "MonitoredCarriersContact": {"type": "object", "properties": {"FirstName": {"type": "string"}, "LastName": {"type": "string"}, "Title": {"type": "string"}, "Phone": {"type": "string"}, "Email": {"type": "string"}, "AuthorizedForPackets": {"type": "boolean"}, "VerificationStatus": {"type": "string"}}}, "CompletedPacketDto": {"type": "object", "properties": {"DOTNumber": {"format": "int32", "type": "integer"}, "DocketNumber": {"type": "string"}, "LegalName": {"type": "string"}, "CompletedDate": {"format": "date-time", "type": "string"}}}, "CarrierChangesResultDto": {"type": "object", "properties": {"FromDate": {"format": "date-time", "type": "string"}, "ToDate": {"format": "date-time", "type": "string"}, "RequestDateTimeUtc": {"format": "date-time", "type": "string"}, "InsuranceChangeCount": {"format": "int32", "type": "integer"}, "FMCSAChangeCount": {"format": "int32", "type": "integer"}, "RiskAssessmentChangeCount": {"format": "int32", "type": "integer"}, "CarrierCount": {"format": "int32", "type": "integer"}, "CarrierList": {"type": "array", "items": {"$ref": "#/definitions/MyCarrierPacketsApi.FMCSA.FMCSACarrierChanged"}}}}, "MyCarrierPacketsApi.FMCSA.FMCSACarrierChanged": {"type": "object", "properties": {"ChangeDateTime": {"format": "date-time", "type": "string"}, "ChangeCategories": {"type": "array", "items": {"type": "string"}}, "CarrierDetails": {"$ref": "#/definitions/MyCarrierPacketsApi.FMCSA.CarrierDetails"}, "ResponseDO": {"$ref": "#/definitions/ResponseDO"}}}, "RequestMonitoringOutput": {"type": "object", "properties": {"MonitoringID": {"format": "int64", "type": "integer"}, "RequestDate": {"format": "date-time", "type": "string"}}}, "CancelMonitoringOutput": {"type": "object", "properties": {"MonitoringID": {"format": "int64", "type": "integer"}, "CancelDate": {"format": "date-time", "type": "string"}}}, "BlockCarrierOutput": {"type": "object", "properties": {"Result": {"type": "boolean"}, "Message": {"type": "string"}}}, "UnblockCarrierOutput": {"type": "object", "properties": {"Result": {"type": "boolean"}, "Message": {"type": "string"}}}, "MonitoredCarrierDto": {"type": "object", "properties": {"DOTNumber": {"format": "int32", "type": "integer"}, "DocketNumber": {"type": "string"}, "IntrastateNumber": {"type": "string"}, "CreatedDate": {"format": "date-time", "type": "string"}, "CreatedBy": {"type": "string"}, "LastModifiedDate": {"format": "date-time", "type": "string"}, "LastModifiedBy": {"type": "string"}}}, "MonitoredCarrierDataDto": {"type": "object", "properties": {"pageNumber": {"format": "int32", "type": "integer"}, "pageSize": {"format": "int32", "type": "integer"}, "totalPages": {"format": "int32", "type": "integer"}, "totalCount": {"format": "int32", "type": "integer"}, "succeeded": {"type": "boolean"}, "message": {"type": "string"}, "data": {"type": "array", "items": {"$ref": "#/definitions/MyCarrierPacketsApi.FMCSA.CarrierDetails"}}}}, "CustomerBlockedCarrierDto": {"type": "object", "properties": {"DOTNumber": {"format": "int32", "type": "integer"}, "DocketNumber": {"type": "string"}, "IntrastateNumber": {"type": "string"}, "CreatedDate": {"format": "date-time", "type": "string"}, "CreatedBy": {"type": "string"}, "LastModifiedDate": {"format": "date-time", "type": "string"}, "LastModifiedBy": {"type": "string"}}}}}