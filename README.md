[![Deploy to Test Server](https://github.com/bulkloads/bulkloads-v2/actions/workflows/deploy-test.yml/badge.svg?branch=test)](https://github.com/bulkloads/bulkloads-v2/actions/workflows/deploy-test.yml)

# Bulkloads-v2 / Bulkloads-server Java

## Description
Bulkloads-v2 backend application.

## Open API

After you have started the server the Open API documentation can be accessed on:

* [/api-docs/swagger-ui/index.html](http://localhost:9000/api-docs/swagger-ui/index.html)
* [/api-docs/api](http://localhost:9000/api-docs/api)

## Intellij configurations

### Checkstyle setup

The project is following the Checkstyle configuration found [here](./config/checkstyle).

It ensures that new code follows the desired convention and anything non-compliant fails the build!

To integrate with Intellij
* install Checkstyle [plugin](https://plugins.jetbrains.com/plugin/1065-checkstyle-idea/versions#tabs).
* import to Intellij. ![./docs/readme/img.png](docs/readme/img.png)
* make sure to change the `Hard wrap at` to `160`.
* to be able to have Intellij warning you about Checkstyle violations, you need to enable the following:
![./docs/readme/img_1.png](docs/readme/img_1.png)
* and then you will be able to see the violations in the checkstyle plugin view:
![./docs/readme/img_2.png](docs/readme/img_2.png)
* finally, to comply to the checkstyle check for `EmptyLineSeparator#allowMultipleEmptyLines=false` you can configure Intellij as shown below:
![./docs/readme/img_3.png](docs/readme/img_3.png)

# Infrastructure

The application is deployed in three different environments: 

- production. [www.bulkloads.com](www.bulkloads.com)
- test [test.bulkloads.com](test.bulkloads.com)
- local [local.bulkloads.com](local.bulkloads.com) 

Each environment has its own infrastructure setup.

## Test Environment

- **URL**: test.bulkloads.com

![Test Environment Infrastructure](docs/infra/test.png)

## Production Environment
- **URL**: bulkloads.com
- **Servers**: Two production nodes (prod1 and prod2)

![Production Environment Infrastructure](docs/infra/prod.png)


## Local Environment

The following guide is a step-by-step guide to set up a local development
environment for the BulkLoads application. It consists of the following architecture:

- ColdFusion 2018 (Docker or Local) (NOTE: Dockerized ColdFusion is not yet supported)
- MySQL (Docker or Local)
- Java/Spring Boot (Running locally)
- Nginx (Docker)

The high level diagram of the setup is as follows:

### Configure your SSH keys / GIT and clone repositories.

Install GIT and configure you ssh keys. Make sure you can clone repositories from the bulkloads organization:

- Coldfusion `<NAME_EMAIL>:bulkloads/bulkloads-web.git`
- Java `<NAME_EMAIL>:bulkloads/bulkloads-v2.git`
- Bulkloads Devops `<NAME_EMAIL>:bulkloads/bulkloads-devops.git`

### Java & docker

- Install JAVA (openjdk 17.0.14 2025-01-21) and configure `JAVA_HOME` (some JDK installers do this automatically).
- Install Docker (if on Windows install first WSL and a linux distro like Ubuntu)

### Generate a GITHUB token 

- Github -> Settings -> Developer Settings -> Personal access tokens

then create a classic token without expiration and with the repo scope. 

### Coldusion / IIS

- follow information in the [setup-conf-guide.md](https://github.com/bulkloads/bulkloads-web/blob/test/docs/tutorials/setup-coldfusion-guide/setup-conf-guide.md) file in the `bulkloads-web/docs/tutorials/setup-coldfusion-guide/` directory of the `bulkloads-web` repository.
- In IIS use ports *7080* and *7443* for both `local.bulkloads.com` and `localold.bulkloads.com` bindings.
- Remove any reference from `web.config` that might cause a circular reference.  

### MySQL

You may either use a local Mysql 8.x server instance or the dockerized version provided in the `docker-compose.yml` file,
however the dockerized version won't provide any data.

To use the dockerized version run `docker compose up -d bulkloads-db` from within the bulkloads-v2 repository. 

### Before starting the Java project 

- Add a variable `GITHUB_TOKEN` with your github token value to the file `env.vars` in the root of the java project.
- Create a copy of the `nginx_config-sample.yaml` and name it `nginx_config.yaml` with all the necessary changes for your environment

### 4. Start the Spring Boot Project

Start using IntelliJ.

### Start the containers

- `docker compose up -d rabbitmq`
- `docker compose up -d mail`
- `docker compose up -d nextjs`
- `docker compose up -d nginx`


![Local Environment Infrastructure](docs/infra/local.png)

### Updating local NGINX configuration

The Nginx container is executing internally an `ansible playbook` responsible for configuring the nginx server.
This playbook is responsible for configuring the java routes via [rest_java_routes.map](https://github.com/bulkloads/bulkloads-devops/blob/test/playbooks/templates/nginx/conf.d/rest_java_routes.map).

The Nginx container can use: 

[1] A local copy of the `bulkloads-devops` repository (by mounting it as a volume). 
This option allows to edit locally the `rest_java_routes.map` file for testing purposes:

```
  nginx:
    ...
    environment:
      - USE_MOUNTED_DEVOPS=1
    volumes:
      - ../bulkloads-devops:/opt/bulkloads-devops:ro
```

The `bulkloads-devops` must be checked out in the same directory as the `bulkloads-v2` repository.


[2] A remote copy of the `bulkloads-devops` repository (by cloning it from a specific branch)

```
  nginx:
    ...
    environment:
      - DEVOPS_BRANCH=test
```

Uses the same configuration as on `test` of the `bulkloads-devops` branch.
No local changes possible as the container will fetch the repository upon container start.

