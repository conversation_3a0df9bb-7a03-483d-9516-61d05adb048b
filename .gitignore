.env
nginx_config.yaml

.vscode/
.idea/


env.vars
.jpib/

docs/tools/pytools/src/__pycache__/
docs/tools/pytools/src/mysql2jpa.egg-info/

application.yml
application.yaml

application.pid

/src/main/generated/**
/src/main/resources/git.properties

.gradle
build/
!gradle/wrapper/gradle-wrapper.jar
!**/src/main/**/build/
!**/src/test/**/build/

docker-data

### STS ###
.apt_generated
.classpath
.factorypath
.project
.settings
.springBeans
.sts4-cache
bin/
!**/src/main/**/bin/
!**/src/test/**/bin/

### IntelliJ IDEA ###
.idea/*
.idea/scopes/*
*.iws
*.iml
*.ipr
out/
!**/src/main/**/out/
!**/src/test/**/out/

### NetBeans ###
/nbproject/private/
/nbbuild/
/dist/
/nbdist/
/.nb-gradle/

### VS Code ###
.vscode/

### Docker ###
docker-data
/src/main/generated/
/mysql-connector-java-8.0.25.jar
/test_master.mysql.sql
/src/main/java/com/bulkloads/web/file/util/update booking.http
