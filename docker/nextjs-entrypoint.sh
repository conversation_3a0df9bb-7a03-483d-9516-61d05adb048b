#!/usr/bin/env sh

set -euo pipefail
[ "${DEBUG:-}" = "1" ] && set -x
: "${GITHUB_TOKEN:?GITHUB_TOKEN is not set – aborting.}"

BRANCH="${NEXTJS_BRANCH:-main}"
REPO_DIR="/app"

# Configure git to use HTTPS with token for all github.com repositories
git config --global url."https://${GITHUB_TOKEN}@github.com/".insteadOf "**************:"
git config --global url."https://${GITHUB_TOKEN}@github.com/".insteadOf "ssh://**************/"

repo_url="https://${GITHUB_TOKEN}@github.com/bulkloads/bulkloads-nextjs.git"


if [ -d "$REPO_DIR/.git" ]; then
  echo "▶ Repo exists – pulling latest"
  git -C "$REPO_DIR" remote set-url origin "$repo_url"
  git -C "$REPO_DIR" fetch origin "$BRANCH"
  git -C "$REPO_DIR" reset --hard "origin/$BRANCH"
else
  echo "▶ First‑time clone into [$REPO_DIR]"
  git clone --depth 1 --branch "$BRANCH" "$repo_url" "$REPO_DIR"
fi

cd "$REPO_DIR"

echo "▶ Installing dependencies"
yarn install --frozen-lockfile

echo "▶ Starting NextJS development server"
exec yarn dev